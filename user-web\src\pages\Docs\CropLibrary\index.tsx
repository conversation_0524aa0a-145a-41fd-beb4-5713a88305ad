import FallbackComponent from '@/components/FallbackContent';
import { toLowerCaseNonAccentVietnamese } from '@/services/utils';
import { Plant } from '@/types/plant.type';
import { ReloadOutlined } from '@ant-design/icons';
import { PageContainer, TableSkeleton } from '@ant-design/pro-components';
import { Access, useAccess, useIntl, useModel } from '@umijs/max';
import { Button, Card, Col, Input, List, Row, Space, Spin } from 'antd';
import { debounce, isArray } from 'lodash';
import { FC, ReactNode, Suspense, useEffect, useState } from 'react';
import ImgCard from './components/ImgCard';
import CreateCrop from './CreateCrop';

interface CropLibraryProps {
  children?: ReactNode;
}

const CropLibrary: FC<CropLibraryProps> = ({ children }) => {
  const { myPlant, reload, loadingResource, isAccessEditDocs } = useModel('Docs');
  const [filteredPlants, setFilteredPlants] = useState<Plant[]>([]);
  const intl = useIntl();
  useEffect(() => {
    reload();
  }, []);
  useEffect(() => {
    if (isArray(myPlant)) {
      setFilteredPlants(myPlant.sort((a, b) => (a?.label || '').localeCompare(b?.label || '')));
    }
  }, [myPlant]);

  const debounceSearch = debounce((searchQuery) => {
    setFilteredPlants(
      myPlant.filter((plant) =>
        toLowerCaseNonAccentVietnamese(plant.label || '').includes(
          toLowerCaseNonAccentVietnamese(searchQuery),
        ),
      ),
    );
  }, 400);

  const handleSearch = (e: any) => {
    const searchQuery = e.target.value;
    debounceSearch(searchQuery);
  };
  const access = useAccess();
  const canCreatePlant = isAccessEditDocs;
  return (
    <Access accessible={true} fallback={<FallbackComponent />}>
      <PageContainer>
        <Space direction="vertical" size="middle" style={{ display: 'flex' }}>
          <Card bordered>
            <Row justify={'space-between'} gutter={16} align="middle">
              <Col span={8} flex={'1 0 25%'}>
                <Input
                  addonBefore={intl.formatMessage({ id: 'common.docs' })}
                  onChange={handleSearch}
                />
              </Col>
              <Space>
                {canCreatePlant && (
                  <Col span={8} style={{ textAlign: 'right' }}>
                    <CreateCrop onSuccess={reload} />
                  </Col>
                )}
                <Col>
                  <Button icon={<ReloadOutlined />} onClick={reload} />
                </Col>
              </Space>
            </Row>
          </Card>
          <Suspense fallback={<TableSkeleton active />}>
            <Spin spinning={loadingResource}>
              <List
                grid={{
                  column: 3,
                  gutter: 10,
                  md: 2,
                  sm: 2,
                  xs: 1,
                }}
                dataSource={filteredPlants}
                renderItem={(item) => (
                  <List.Item>
                    <ImgCard
                      onDeleteSuccess={reload}
                      image={item.image}
                      title={item.label}
                      id={item.name}
                    />
                  </List.Item>
                )}
              />
            </Spin>
          </Suspense>
        </Space>
      </PageContainer>
    </Access>
  );
};
export default CropLibrary;
