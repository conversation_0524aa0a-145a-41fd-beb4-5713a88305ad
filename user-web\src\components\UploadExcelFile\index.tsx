import { UploadOutlined } from '@ant-design/icons';
import { ProForm } from '@ant-design/pro-components';
import { useIntl } from '@umijs/max';
import { Button, Form, message, Upload, UploadFile, UploadProps } from 'antd';
import { RcFile } from 'antd/es/upload';
import { FC, useState } from 'react';
import * as XLSX from 'xlsx';

interface Props {
  formItemName: string | string[];
  label?: string | React.ReactElement;
  isReadonly?: boolean;
  onValueChange?: (value: string) => void;
  maxSize?: number; // mb
  onExcelDataLoaded?: (data: any[]) => void; // New prop
  disabled?: boolean;
}

const UploadExcelFileImportExportVoucher: FC<Props> = ({
  formItemName,
  label,
  isReadonly,
  onValueChange,
  maxSize,
  onExcelDataLoaded, // Nhận dữ liệu Excel từ prop
}) => {
  const [fileList, setFileList] = useState<UploadFile[]>([]);
  const form = Form.useFormInstance();
  const { formatMessage } = useIntl();

  const handleChange: UploadProps['onChange'] = ({ fileList: newFileList }) => {
    if (newFileList.length > 1) {
      message.error(formatMessage({ id: 'common.upload-error-one-file' }));
      return;
    }

    setFileList(newFileList);

    if (newFileList.length === 1) {
      const file = newFileList[0].originFileObj as RcFile;
      const reader = new FileReader();

      reader.onload = (e) => {
        const data = e.target?.result as ArrayBuffer;
        const workbook = XLSX.read(data, { type: 'array' });

        // Lấy tên của sheet đầu tiên
        const firstSheet = workbook.SheetNames[0];

        // Chuyển đổi sheet thành mảng các hàng
        const worksheet = workbook.Sheets[firstSheet];
        let rows: any = XLSX.utils.sheet_to_json(worksheet, { header: 1 });

        // Kiểm tra và xóa các hàng đầu tiên nếu nó không có dữ liệu
        while (
          rows.length > 0 &&
          rows[0].every((cell: any) => cell === null || cell === undefined || cell === '')
        ) {
          rows.shift();
        }

        // Kiểm tra và xóa hàng đầu tiên nếu chứa "Mã vật tư"
        if (rows.length > 0 && rows[0].includes('Mã vật tư')) {
          rows.shift();
        }

        // Kiểm tra hàng kế tiếp có chứa đủ 3 key "item_name", "quantity", và "valuation_rate"
        if (rows.length > 0) {
          const headerRow = rows[0];
          const requiredKeys = ['item_name', 'quantity', 'valuation_rate'];
          const hasAllKeys = requiredKeys.every((key) => headerRow.includes(key));

          if (!hasAllKeys) {
            message.error(formatMessage({ id: 'common.upload-excel-error-missing-keys' }));
            return;
          }
        }

        // Chuyển đổi mảng các hàng còn lại thành worksheet
        const newWorksheet = XLSX.utils.aoa_to_sheet(rows);

        // Chuyển đổi worksheet thành JSON
        const jsonData = XLSX.utils.sheet_to_json(newWorksheet);

        console.log('Excel content as JSON:', jsonData);

        // Gọi prop onExcelDataLoaded với dữ liệu từ file Excel
        onExcelDataLoaded?.(jsonData);

        // Lưu JSON vào form field
        form.setFieldValue(formItemName, jsonData);
        onValueChange?.(JSON.stringify(jsonData));

        // Debug form value
        console.log('Form value:', formItemName, form.getFieldValue(formItemName));
      };

      reader.readAsArrayBuffer(file);
    } else {
      form?.setFieldValue(formItemName, null);
    }
  };

  return (
    <>
      <ProForm.Item name={formItemName} style={{ display: 'none' }} />
      <ProForm.Item label={label}>
        <Upload
          fileList={fileList}
          maxCount={1}
          onChange={handleChange}
          multiple={false}
          disabled={isReadonly}
          beforeUpload={(file) => {
            if (maxSize) {
              const isLtSize = file.size / 1024 / 1024 <= maxSize;
              if (!isLtSize) {
                message.error(
                  formatMessage({
                    id: 'common.upload-error-file-big',
                  }) + ` ${maxSize}MB`,
                );
                return Upload.LIST_IGNORE;
              }
            }
            return false; // Ngăn chặn upload tự động
          }}
          // Không đặt prop action để ngăn việc upload tự động
          action={undefined}
        >
          <Button disabled={isReadonly} icon={<UploadOutlined />} type="primary">
            {formatMessage({
              id: 'common.upload',
            })}
          </Button>
        </Upload>
      </ProForm.Item>
    </>
  );
};

export default UploadExcelFileImportExportVoucher;
