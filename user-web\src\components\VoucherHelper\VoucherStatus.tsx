import { STATUS_TAG_COLOR } from '@/common/contanst/constanst';
import { useIntl } from '@umijs/max';
import { Tag } from 'antd';
import React from 'react';

const getVoucherDocStatus = (
  docStatus: number,
  formatMessage: (descriptor: { id: string }) => string,
) => {
  console.log('docStatus', docStatus);
  switch (docStatus) {
    case 0:
      return <Tag color={STATUS_TAG_COLOR.Draft}>{formatMessage({ id: 'common.draft' })}</Tag>;
    case 1:
      return (
        <Tag color={STATUS_TAG_COLOR['To Bill']}>{formatMessage({ id: 'common.submitted' })}</Tag>
      );
    case 2:
      return (
        <Tag color={STATUS_TAG_COLOR['Cancelled']}>{formatMessage({ id: 'common.cancel' })}</Tag>
      );
    default:
      return <></>;
  }
};

interface VoucherStatusProps {
  docStatus: number;
}

const VoucherStatus: React.FC<VoucherStatusProps> = ({ docStatus }) => {
  const { formatMessage } = useIntl();
  return getVoucherDocStatus(docStatus, formatMessage);
};

export default VoucherStatus;
