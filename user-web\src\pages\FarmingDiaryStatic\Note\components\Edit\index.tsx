import { ProForm } from '@ant-design/pro-components';
import { useIntl } from '@umijs/max';
import { Spin } from 'antd';
import { FC, ReactNode, useEffect } from 'react';
import useDetail from '../../hooks/useDetail';
import useUpdate from '../../hooks/useUpdate';
import Attachment from './Attachment';
import DetailedInfo from './DetailedInfo';

interface StageOfCountEditProps {
  children?: ReactNode;
  id: string;
  onSuccess?: () => void;
  setIsFormDirty: (dirty: boolean) => void;
}

const PestEdit: FC<StageOfCountEditProps> = ({
  children,
  id,
  onSuccess,
  setIsFormDirty = () => {},
}) => {
  const { formatMessage } = useIntl();
  const [form] = ProForm.useForm();
  const { run } = useUpdate({
    onSuccess,
  });
  const { data, loading } = useDetail({
    id: id,
    onSuccess(data) {
      form.setFieldsValue({
        ...data,
        state_id: data?.states?.[0]?.name,
      });
    },
  });
  useEffect(() => {
    setIsFormDirty(false);
  }, [data]);
  return (
    <Spin spinning={loading}>
      <ProForm
        onValuesChange={() => setIsFormDirty(true)}
        submitter={{
          searchConfig: {
            // resetText: formatMessage({ id: 'common.reset' }),
            // submitText: formatMessage({ id: 'common.submit' }),
          },
          render: (_, dom) => {
            return (
              <div style={{ textAlign: 'right', margin: 24 }}>
                {dom.map((item, index) => (
                  <span key={index} style={{ marginRight: index === 0 ? 8 : 0 }}>
                    {item}
                  </span>
                ))}
              </div>
            );
          },
        }}
        loading={loading}
        form={form}
        onFinish={async (values) => {
          await run({
            name: id,
            label: values.label,
            description: values.description,
            image: values.image,
            file: values.file,
            product_id: values.product_id,
            states: [
              {
                name: values.state_id,
              },
            ],
          });
          onSuccess?.();
          setIsFormDirty(false);
        }}
      >
        <div className="mb-4 space-y-4">
          <DetailedInfo initialImage={data?.image} />
          {/* <Task /> */}
          <Attachment initialFile={data?.file} />
        </div>
      </ProForm>
    </Spin>
  );
};

export default PestEdit;
