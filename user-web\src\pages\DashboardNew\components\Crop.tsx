import { DEFAULT_PAGE_SIZE_ALL } from '@/common/contanst/constanst';
import { DEFAULT_FALLBACK_IMG } from '@/common/contanst/img';
import { getCropManagementInfoList, ICropManagerInfo } from '@/services/cropManager';
import { dayjsUtil } from '@/utils/date';
import { genDownloadUrl } from '@/utils/file';
import { Link, useIntl, useRequest } from '@umijs/max';
import { Avatar, Button, Card, Progress, Skeleton } from 'antd';
import { FC, ReactNode, useMemo } from 'react';
import { useGetCropStage } from '../hooks/useGetCropStage';
import { Tag } from './Devices';

interface CropProps {
  children?: ReactNode;
}
const CropItem: FC<{
  data: ICropManagerInfo;
}> = ({ data }) => {
  const totalDate = useMemo(
    () => dayjsUtil(data.end_date).diff(data.start_date, 'd'),
    [data.start_date, data.end_date],
  );
  const daysLeft = useMemo(() => dayjsUtil(data.end_date).diff(dayjsUtil(), 'd'), [data.end_date]);
  const absDayLeft = Math.abs(daysLeft);
  const isDone = useMemo(() => data.status === 'Done', [data.status]);
  const { formatMessage } = useIntl();
  const { data: currentState, loading } = useGetCropStage({
    cropId: data.name,
  });
  return (
    <Link to={`/farming-management/seasonal-management/detail/${data.name}`}>
      <div className="border bg-white p-3 flex gap-3 items-start hover:border-[#9DDBB1] hover:shadow-sm transition-all duration-300">
        <div className="flex-none">
          <Avatar
            shape="square"
            size={44}
            src={data.avatar ? genDownloadUrl(data.avatar) : DEFAULT_FALLBACK_IMG}
            className="!bg-gray-50"
          />
        </div>
        <div className="flex-1 min-w-0">
          <div className="flex items-center gap-2 mb-1">
            {' '}
            {/* Giảm margin-bottom từ 2 xuống 1 */}
            <Tag isOffline={isDone}>
              {data.status === 'In progress'
                ? formatMessage({ id: 'new-dashboard.ongoing' })
                : formatMessage({ id: 'new-dashboard.completed' })}
            </Tag>
          </div>
          <div className="flex flex-col gap-1">
            {' '}
            {/* Giảm gap từ 2 xuống 1 */}
            <div className="font-medium text-base truncate">{data.label}</div>
            {loading ? (
              <Skeleton.Input size="small" active />
            ) : (
              <div className="text-sm text-gray-500 truncate">
                {currentState?.label
                  ? `${formatMessage({ id: 'new-dashboard.stage' })}: ${currentState.label}`
                  : '-'}
              </div>
            )}
          </div>
          <div className="flex items-center gap-3 mt-1">
            {' '}
            {/* Giảm margin-top và gap */}
            <Progress
              percent={100}
              strokeColor={['#a1ffbc']}
              success={{
                percent: (absDayLeft / totalDate) * 100,
              }}
              className="flex-1 [&_.ant-progress-outer]:!rounded-none [&_.ant-progress-bg]:!rounded-none [&_.ant-progress-success-bg]:!rounded-none"
            />
            <div className="flex-none whitespace-nowrap">
              <span className="font-medium">{absDayLeft}</span>/{totalDate}{' '}
              {formatMessage({ id: 'common.date' })}
            </div>
          </div>
        </div>
      </div>
    </Link>
  );
};
const Crop: FC<CropProps> = ({ children }) => {
  const { data } = useRequest(async () => {
    const res = await getCropManagementInfoList({
      page: 1,
      size: DEFAULT_PAGE_SIZE_ALL,
      // status: JSON.stringify(['In progress']),
    } as any);

    return {
      data: {
        data: res.data,
        success: true,
        total: res?.pagination?.totalElements,
      },
    };
  });
  const { formatMessage } = useIntl();
  return (
    <Card
      className="border-0 shadow-sm"
      title={formatMessage({ id: 'new-dashboard.all-crop' })}
      extra={
        <Link to="/farming-management/seasonal-management?tab=all">
          <Button type="link">{formatMessage({ id: 'new-dashboard.see-all' })}</Button>
        </Link>
      }
    >
      <div className="flex flex-col gap-2 overflow-y-auto max-h-[40rem] scrollbar-thin scrollbar-thumb-gray-200 scrollbar-track-transparent p-3">
        {data?.data?.map((item) => (
          <CropItem data={item} key={item.name} />
        ))}
      </div>
    </Card>
  );
};

export default Crop;
