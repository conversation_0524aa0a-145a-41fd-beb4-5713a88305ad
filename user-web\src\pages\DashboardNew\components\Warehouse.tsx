import HighChartComponentCommon from '@/components/HighChartComponentCommon';
import {
  getImportExportWarehouse,
  getTotalImportExportWarehouseQty,
} from '@/services/stock/dashboard';
import { dayjsUtil } from '@/utils/date';
import { formatMoney } from '@/utils/format';
import { Link, useIntl } from '@umijs/max';
import { useRequest } from 'ahooks';
import { Avatar, Button, Card } from 'antd';
import { FC, ReactNode, useMemo, useState } from 'react';

interface WarehouseProps {
  children?: ReactNode;
}
type OptionItemProps = {
  icon: string;
  title: string;
  value: string;
  textColor?: string;
};
const OptionItem: FC<OptionItemProps> = ({ icon, title, value, textColor }) => {
  return (
    <div className="p-2">
      <div className="flex gap-2 items-center">
        <div>
          <Avatar size={'large'} shape="square" src={icon} />
        </div>
        <div>
          <div className="text-base font-semibold">{title}</div>
          <div
            className="text-slate-400 font-semibold"
            style={{
              color: textColor,
            }}
          >
            {value} VNĐ
          </div>
        </div>
      </div>
    </div>
  );
};
const startTime = dayjsUtil().add(-1, 'year').format('YYYY-MM-DD');
const endTime = dayjsUtil().format('YYYY-MM-DD');
const Warehouse: FC<WarehouseProps> = ({ children }) => {
  const [groupBy, setGroupBy] = useState<string>('month');
  const { formatMessage } = useIntl();
  const { data } = useRequest(async () => {
    const res = await getImportExportWarehouse({
      warehouse: undefined,
      start_date: startTime,
      end_date: endTime,
      group_by: groupBy,
    });
    function formatTimestamps(timestamps: string[]) {
      const formattedTimestamps = [];
      for (const timestamp of timestamps) {
        // Parse the timestamp string
        const dt = dayjsUtil(timestamp);

        // Format based on groupBy attribute
        let formattedString;
        switch (groupBy) {
          case 'week':
            // Get the week number (Sunday as first day of the week)

            formattedString = `${dt.startOf('week')} - ${dt.endOf('week')}`;
            break;
          case 'month':
            formattedString = dt.format('MMMM YYYY');
            break;
          case 'year':
            formattedString = dt.format('YYYY');
            break;
          default:
            formattedString = timestamp;
        }

        formattedTimestamps.push(formattedString);
      }

      return formattedTimestamps;
    }
    const formattedData = {
      labels: formatTimestamps(res.result.at(0).map((item: any) => item[groupBy])),
      datasets: [
        {
          type: 'bar' as const,
          label: 'Nhập',
          data: res.result.at(0).map((item: any) => item.total_price),
          backgroundColor: '#4793AF',
        },
        {
          type: 'bar' as const,
          label: 'Xuất',
          data: res.result.at(1).map((item: any) => item.total_price),
          backgroundColor: '#DD5746',
        },
      ],
    };
    const total = await getTotalImportExportWarehouseQty({
      start_date: startTime,
      end_date: endTime,
    });
    const totalExport = total.result.deliveryNoteTotal.total_qty;
    const totalImport = total.result.purchaseReceiptTotal.total_price;
    return {
      data: {
        chart: formattedData,
        totalExport,
        totalImport,
      },
    };
  });
  const maxColumn = useMemo(() => {
    const a = data?.data?.chart.datasets?.[0]?.data?.length || 1;
    const b = data?.data?.chart.datasets?.[1]?.data?.length || 1;
    return Math.max(a, b);
  }, [data?.data?.chart.datasets]);
  return (
    <Card
      bordered={false}
      title={formatMessage({
        id: 'new-dashboard.import-and-export-warehouse',
      })}
      extra={
        <Link to="/warehouse-management-v3/inventory">
          <Button type="link">
            {formatMessage({
              id: 'new-dashboard.see-all',
            })}
          </Button>
        </Link>
      }
    >
      <HighChartComponentCommon
        chartProps={{
          chart: {
            type: 'column',
            scrollablePlotArea: {
              minWidth: maxColumn * 70,
              // nr of interval data x (40 + 30) where 40 are column width pixels and
              // 30 is additional distancing between columns;
              // Increase spacing pixels if needed
              scrollPositionX: 1,
            },
          },
          exporting: {
            enabled: false,
          },
          title: {
            // text: 'Corn vs wheat estimated production for 2020',
            text: '',
          },
          navigator: {
            enabled: false,
          },
          subtitle: {
            // text:
            //   'Source: <a target="_blank" ' +
            //   'href="https://www.indexmundi.com/agriculture/?commodity=corn">indexmundi</a>',
            // align: 'left',
          },

          xAxis: {
            type: 'category',
            categories: data?.data.chart.labels,
            crosshair: true,
            accessibility: {
              description: 'Time',
            },
          },
          yAxis: {
            min: 0,
            title: {
              text: 'VNĐ',
            },
          },
          tooltip: {
            valueSuffix: ' (VNĐ)',
          },
          plotOptions: {
            column: {
              pointPadding: 0.2,
              borderWidth: 0,
              pointRange: 20,
              borderRadius: 4,
              dataLabels: {
                enabled: false,
              },
            },
          },
          series: [
            {
              type: 'column',
              name: formatMessage({
                id: 'new-dashboard.export-warehouse',
              }),
              data: data?.data?.chart?.datasets?.[1]?.data,
              color: '#ffcf00',
            },
            {
              type: 'column',
              name: formatMessage({
                id: 'new-dashboard.import-warehouse',
              }),
              data: data?.data?.chart.datasets?.[0]?.data,
              color: '#47b494',
            },
          ],
        }}
      />
      <div className="mt-2 flex items-center gap-2 justify-between">
        <OptionItem
          textColor={'#ffc66b'}
          icon="/images/new-dashboard/export.png"
          title={formatMessage({
            id: 'new-dashboard.export-warehouse',
          })}
          value={formatMoney(data?.data.totalExport)}
        />
        <OptionItem
          textColor="#27ae60"
          icon="/images/new-dashboard/import.png"
          title={formatMessage({
            id: 'new-dashboard.import-warehouse',
          })}
          value={formatMoney(data?.data.totalImport)}
        />
      </div>
    </Card>
  );
};

export default Warehouse;
