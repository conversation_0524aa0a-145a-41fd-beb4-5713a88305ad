import { controlDevice, getLatestDataFunction } from '@/services/devices';
import { sscript } from '@/services/sscript';
import { IIotProductionFunction } from '@/types/IIotProductionFunction';
import { PageContainer } from '@ant-design/pro-components';
import { AxiosError, useModel } from '@umijs/max';
import { Button, Card, Col, ConfigProvider, DatePicker, Input, message, Result, Row, Select, Spin, Switch, Tabs } from 'antd';
import React, { useEffect, useMemo, useState } from 'react';


const FuncRenderEnum = ({ fnc }: { fnc: IIotProductionFunction }) => {
    const [loading, setLoading] = useState(false);
    const { latestDeviceValue, selectedDeviceThingsboard } = useModel('MyResource');
    const [fncValue, setFncValue] = useState();

    useMemo(() => {
        if (latestDeviceValue?.length) {
            const match_key = latestDeviceValue.filter((d: any) => {
                return d.key === fnc.identifier;
            });
            if (match_key.length) {
                let value = match_key[match_key.length - 1];
                setFncValue(value.value);
            }
        }
    }, [latestDeviceValue]);

    const setValueFnc = async (val: string) => {
        try {
            setLoading(true);
            console.log("val", val);
            let params = {};
            params[fnc.identifier || ""] = parseInt(val.toString());
            await controlDevice({
                device_id_thingsboard: selectedDeviceThingsboard,
                method: "set_state",
                params: params
            });
        } catch (error) {
            message.error("Có lỗi xảy ra, vui lòng thử lại!");
        } finally {
            setLoading(false);
        }
    }
    console.log("Enum", fnc.enum_value)
    return (
        <Row gutter={[5, 5]}>
            <Col md={12}>
                {fnc.label}
            </Col>
            <Col md={12}>
                <Select
                    disabled={fnc.data_permission === "r"}
                    loading={loading}
                    style={{
                        width: '100%'
                    }}
                    options={fnc.enum_value?.split(",").map((d: string, index: number) => {
                        return {
                            label: d,
                            value: index
                        }
                    })}
                    onChange={setValueFnc}
                    value={fncValue}
                >
                </Select>
            </Col>
        </Row>
    );
};

export default FuncRenderEnum;
