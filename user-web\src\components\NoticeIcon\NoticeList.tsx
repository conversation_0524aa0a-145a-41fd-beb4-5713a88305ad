import { Avatar, Divider, List, Skeleton } from 'antd';
import { createStyles } from 'antd-use-styles';
import classNames from 'classnames';
import React from 'react';
import { Virtuoso } from 'react-virtuoso';
import ResizeObserver from 'resize-observer-polyfill';
import styles from './NoticeList.less';
if (!window.ResizeObserver) window.ResizeObserver = ResizeObserver;
export type NoticeIconTabProps = {
  count?: number;
  showClear?: boolean;
  showViewMore?: boolean;
  style?: React.CSSProperties;
  title: string;
  tabKey: API.NoticeIconItemType;
  onClick?: (item: API.NoticeIconItem) => void;
  onClear?: () => void;
  emptyText?: string;
  clearText?: string;
  viewMoreText?: React.ReactNode;
  list: API.NoticeIconItem[];
  onViewMore?: (e: any) => void;
  hasLoadMore?: boolean;
  loadMoreData?: () => any;
};
const useStyles = createStyles(({ token }) => ({
  item: {
    '&:hover': {
      backgroundColor: token.controlItemBgHover,
    },
  },
}));

const NoticeList: React.FC<NoticeIconTabProps> = ({
  list = [],
  onClick,
  onClear,
  title,
  onViewMore,
  emptyText,
  showClear = true,
  clearText,
  viewMoreText,
  showViewMore = false,
  hasLoadMore,
  loadMoreData,
}) => {
  const stylesToken = useStyles();

  const RenderFooter = () => {
    if (!hasLoadMore)
      return (
          <Divider plain>{`Đã tải hết thông báo!`}</Divider>
      );
    return (
      <div style={{ minHeight: 10 }}>
        {Array.from(new Array(2)).map((_item, index) => (
          <List.Item key={index} className={styles.item}>
            <Skeleton avatar paragraph={{ rows: 1 }} active />
          </List.Item>
        ))}
      </div>
    );
  };
  if (!list || list.length === 0) {
    return (
      <div className={styles.notFound}>
        <img
          src="https://gw.alipayobjects.com/zos/rmsportal/sAuJeJzSKbUmHfBQRzmZ.svg"
          alt="not found"
        />
        <div>{emptyText}</div>
      </div>
    );
  }

  return (
    <>
      <List className={styles.list}>
        <Virtuoso
          className={'scrollbar-thin'}
          style={{ height: 350 }}
          data={list}
          overscan={500}
          endReached={loadMoreData}
          itemContent={(index, item) => {
            const itemCls = classNames(styles.item, stylesToken.item, {
              [styles.read]: item.read,
            });
            // eslint-disable-next-line no-nested-ternary
            const leftIcon = item.avatar ? (
              typeof item.avatar === 'string' ? (
                <Avatar className={styles.avatar} src={item.avatar} />
              ) : (
                <span className={styles.iconElement}>{item.avatar}</span>
              )
            ) : null;

            return (
              <div
                key={item.id}
                onClick={() => {
                  onClick?.(item);
                }}
              >
                <List.Item className={itemCls}>
                  <List.Item.Meta
                    className={styles.meta}
                    avatar={leftIcon}
                    title={
                      <div className={styles.title}>
                        {item.title}
                        <div className={styles.extra}>{item.extra}</div>
                      </div>
                    }
                    description={
                      <div>
                        <div className={styles.description}>{item.description}</div>
                        <div className={styles.datetime}>{item.datetime}</div>
                      </div>
                    }
                  />
                </List.Item>
              </div>
            );
          }}
          components={{ Footer: RenderFooter }}
        />
      </List>
      <div className={styles.bottomBar}>
        {showClear ? <div onClick={onClear}>{clearText}</div> : null}
        {showViewMore ? (
          <div
            onClick={(e) => {
              if (onViewMore) {
                onViewMore(e);
              }
            }}
          >
            {viewMoreText}
          </div>
        ) : null}
      </div>
    </>
  );
};

export default NoticeList;
