import { FC, useMemo, useState } from 'react';

export interface TriggerFormModalProps<T = any> {
  trigger?: JSX.Element;
  triggerRender?: ({}: { changeOpen: (open: boolean) => void; open?: boolean }) => JSX.Element;
  open?: boolean;
  onOpenChange?: (open: boolean) => void;
  onSuccess?: () => void;
  modalProps?: T;
  disabled?: boolean;
  refreshFnc?: () => void;
  buttonType?: 'primary' | 'link';
}

const withTriggerFormModal = <T = any,>({
  defaultTrigger: DefaultTrigger,
  contentRender,
}: {
  defaultTrigger: ({
    changeOpen,
    disabled,
    buttonType,
  }: {
    changeOpen: (open: boolean) => void;
    disabled?: boolean;
    buttonType?: 'primary' | 'link';
  }) => React.ReactNode;
  contentRender: FC<TriggerFormModalProps<T>>;
}) => {
  const Component: FC<TriggerFormModalProps<T>> = ({
    open,
    trigger,
    triggerRender,
    onOpenChange,
    onSuccess,
    modalProps,
    disabled,
    buttonType,
  }) => {
    const [_open, _setOpen] = useState(false);
    const openActive = typeof open === 'boolean' ? open : _open;
    const onOpenChangeActive = typeof onOpenChange === 'function' ? onOpenChange : _setOpen;
    const TriggerRender = triggerRender;
    const ContentRender = useMemo(() => contentRender, [contentRender]);
    if (!ContentRender) return null;
    return (
      <>
        {TriggerRender ? (
          <TriggerRender changeOpen={_setOpen} open={open} />
        ) : (
          trigger ||
          (DefaultTrigger ? (
            <DefaultTrigger disabled={disabled} changeOpen={_setOpen} buttonType={buttonType} />
          ) : null)
        )}
        {openActive && (
          <ContentRender
            open={openActive}
            trigger={trigger}
            onOpenChange={onOpenChangeActive}
            onSuccess={onSuccess}
            modalProps={modalProps}
          />
        )}
      </>
    );
  };
  return Component;
};

export default withTriggerFormModal;
