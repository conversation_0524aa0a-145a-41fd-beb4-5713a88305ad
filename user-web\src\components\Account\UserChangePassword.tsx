import { userResetPassword } from '@/services/auth';
import { FormattedMessage } from '@umijs/max';
import { Button, Col, Form, Input, message, Row } from 'antd';
const { Item } = Form;

import { useState } from 'react';

export const UserChangePassword = () => {
  const [loading, setLoading] = useState(false);

  const validatePassword = (rule: any, value: any, callback: (arg0?: string) => void) => {
    if (value !== document.getElementById('newPassword')?.value) {
      callback('Password and confirm password do not match!');
    } else {
      callback();
    }
  };

  return (
    <>
      <Form
        title="Change Password"
        onFinish={async (formData: any) => {
          try {
            setLoading(true);
            const { newPassword } = await formData;
            const result = await userResetPassword({ new_password: newPassword });
            message.success('Success!');
          } catch (error) {
            message.success('Error, please try again!');
          } finally {
            setLoading(false);
          }
        }}
      >
        <Row>
          <Col md={24}>
            <Item
              label={<FormattedMessage id="common.form.new_password" />}
              name="newPassword"
              labelCol={{ span: 24 }}
              rules={[
                { required: true },
                { min: 8, message: 'Password at least 8 characters' },
                { max: 40, message: 'Password maximum 40 characters' },
              ]}
            >
              <Input type="password"></Input>
            </Item>
          </Col>
          <Col md={24}>
            <Item
              label={<FormattedMessage id="action.retype-password" />}
              name="repassword"
              labelCol={{ span: 24 }}
              rules={[{ required: true, validator: validatePassword }]}
            >
              <Input type="password"></Input>
            </Item>
          </Col>
          <Col md={24}>
            <Button block loading={loading} type="primary" htmlType="submit">
              <FormattedMessage id="action.save" />
            </Button>
          </Col>
        </Row>
      </Form>
    </>
  );
};
