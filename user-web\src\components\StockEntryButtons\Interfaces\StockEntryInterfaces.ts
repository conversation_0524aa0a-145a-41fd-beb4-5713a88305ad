export interface IMaterialVoucherItem {
  conversion_factor: number;
  item_code: string;
  item_name: string;
  qty: number;
  actual_qty: number;
  rate?: number;
  basic_rate?: number;
  valuation_rate?: number;
  warehouse: string;
  total_price: number;
  item_label: string;
  key: string;
  uom_label: string;
  convert_uom_id?: string;
  convert_uom_label?: string;
}

export interface IMaterialTransferVoucherItem {
  conversion_factor: number;
  item_code: string;
  item_name: string;
  qty: number;
  expected_qty?: number;
  actual_qty: number;
  finished_qty?: number;
  rate?: number;
  valuation_rate?: number;
  warehouse: string;
  total_price: number;
  item_label: string;
  key: string;
  uom_label: string;
  convert_uom_id?: string; //cần truyền lên khi sử dụng chuyển đổi đơn vị, không cần truyền khi sử dụng đơn vị cơ bản
  convert_uom_label?: string; //cần truyền lên khi sử dụng chuyển đổi đơn vị, không cần truyền khi sử dụng đơn vị cơ bản
  total_qty_in_crop?: number; // dùng với quick material issue
}

export interface IStockEntry {
  name: string;
  purpose: string;
  formatted_posting_date: string;
  posting_date: string;
  posting_time: string;
  total_outgoing_value: number;
  total_incoming_value: number;
  total_difference: number;
  iot_customer: string;
  iot_customer_user: string;
  docstatus: number;
}

export interface IStockEntryDetailItem {
  name: string;
  owner: string;
  creation: string;
  modified: string;
  modified_by: string;
  docstatus: number;
  idx: number;
  has_item_scanned: number;
  s_warehouse: string;
  s_warehouse_label: string;
  t_warehouse: string;
  t_warehouse_label: string;
  item_code: string;
  item_name: string;
  is_finished_item: number;
  is_scrap_item: number;
  description: string;
  qty: number;
  transfer_qty: number;
  retain_sample: number;
  uom: string;
  stock_uom: string;
  conversion_factor: number;
  sample_quantity: number;
  basic_rate: number;
  additional_cost: number;
  valuation_rate: number;
  allow_zero_valuation_rate: number;
  set_basic_rate_manually: number;
  basic_amount: number;
  amount: number;
  expense_account: string;
  cost_center: string;
  actual_qty: number;
  transferred_qty: number;
  allow_alternative_item: number;
  parent: string;
  parentfield: string;
  parenttype: string;
  doctype: string;
}

export interface IStockEntryDetail {
  user_address: string;
  user_ward: string;
  user_district: string;
  user_province: string;
  user_phone_number: string;
  description: string;
  name: string;
  owner: string;
  creation: string;
  modified: string;
  modified_by: string;
  docstatus: number;
  idx: number;
  naming_series: string;
  stock_entry_type: string;
  iot_customer?: string; // Optional property
  iot_customer_user?: string; // Optional property
  purpose: string;
  add_to_transit: number;
  company: string;
  posting_date: string;
  posting_time: string;
  set_posting_time: number;
  inspection_required: number;
  apply_putaway_rule: number;
  from_bom: number;
  use_multi_level_bom: number;
  fg_completed_qty: number;
  process_loss_percentage: number;
  process_loss_qty: number;
  total_outgoing_value: number;
  total_incoming_value: number;
  value_difference: number;
  total_additional_costs: number;
  per_transferred: number;
  total_amount: number;
  is_return: number;
  doctype: string;
  additional_costs: any[]; // Array of any type
  user_first_name?: string;
  user_last_name?: string;
  file_path: string;
  items: IStockEntryDetailItem[];
}
