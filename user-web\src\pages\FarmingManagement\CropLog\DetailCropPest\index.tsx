import AttachmentsOfDoctype from '@/components/AttachmentsOfDoctype/AttachmentsOfDoctype';
import { myLazy } from '@/utils/lazy';
import { CameraFilled, LoadingOutlined } from '@ant-design/icons';
import { ListPageSkeleton, PageContainer, ProFormSelect, ProFormUploadButton } from '@ant-design/pro-components';
import { Link, useSearchParams, history } from '@umijs/max';
import { Button, Card, Col, Form, Input, Radio, Row, Select, Tabs, UploadFile, message } from 'antd';
import { FC, Suspense, useEffect, useState } from 'react';
import RemoveCropNote from './components/RemoveCropNote';
import { IIotCropNote } from '@/types/IIotCropNote';
import { getCrop, getCropNote } from '@/services/crop';
import ImageCropNote from './components/Image';
import { generalUpdate } from '@/services/sscript';
import UploadImageCropNote from './components/UploadImageCropNote';
import { updateCropNote } from '@/services/cropManager';
import { DOCTYPE_ERP } from '@/common/contanst/constanst';
import { uploadFile } from '@/services/fileUpload';
import DetailCropPestTab from './components/DetailCropPestTab';
const { Item } = Form;

const DetailCropPest: FC = () => {
  const [tabActive, setTabActive] = useState("1");

  const [searchParams, setSearchParams] = useSearchParams();
  const [loading, setLoading] = useState(false);
  const [cropNote, setCropNote] = useState<Partial<IIotCropNote>>({})
  const [cropList, setCropList] = useState<any[]>([])
  const [imageLinks, setImageLinks] = useState<any[]>([])
  const [form] = Form.useForm();

  const cropPestName = searchParams.get("crop_pest_id") ?? '';

  return (
    <Suspense fallback={<ListPageSkeleton />}>
      <>
        <PageContainer>
          <Tabs activeKey={tabActive}
            onChange={(e) => {
              setTabActive(e)
            }}>
            <Tabs.TabPane tab="Thông tin chi tiết ghi chú" key="1">
              <DetailCropPestTab cropPestID={cropPestName} />
            </Tabs.TabPane>
            {/* <Tabs.TabPane tab="Thông tin khác" key="2">
              Oh no
            </Tabs.TabPane> */}
          </Tabs>

        </PageContainer>
      </>

    </Suspense>
  );
};

export default DetailCropPest;
