import { DEFAULT_DATE_FORMAT_WITHOUT_TIME } from '@/common/contanst/constanst';
import {
  createFarmingPlanState,
  getFarmingPlanList,
  ICreateFarmingPlanStateRes,
} from '@/services/farming-plan';
import { PlusOutlined } from '@ant-design/icons';
import {
  ModalForm,
  ProForm,
  ProFormDateRangePicker,
  ProFormSelect,
  ProFormText,
} from '@ant-design/pro-components';
import { useIntl } from '@umijs/max';
import { App, Button } from 'antd';
import { createStyles } from 'antd-use-styles';
import { FC, ReactNode, useEffect } from 'react';

interface CreateStateProps {
  children?: ReactNode;
  planId?: string;
  onSuccess?: (res: ICreateFarmingPlanStateRes) => void;
}
type IFormData = {
  label: string;
  farming_plan: string;
  date_range: [string, string];
};

const useStyles = createStyles(() => ({
  checkCard: {
    width: 'auto',
    '& .ant-pro-checkcard-content': {
      paddingInline: '12px!important',
      paddingBlock: '8px!important',
      justifyContent: 'center',
      alignItems: 'center',

      '& .ant-pro-checkcard-detail ': {
        width: 'auto',
      },
    },
  },
  checkCardTemperature: {
    backgroundColor: '#edfaf6',
  },
  checkCardHumidity: {
    backgroundColor: '#f6ffed',
  },
}));
const CreateState: FC<CreateStateProps> = ({ children, planId, onSuccess }) => {
  const styles = useStyles();
  const intl = useIntl();
  const { message } = App.useApp();
  const onFinish = async (value: IFormData) => {
    try {
      console.log('dkm', value);
      const res = await createFarmingPlanState({
        label: value.label,
        farming_plan: value.farming_plan,
        start_date: value.date_range?.[0],
        end_date: value.date_range?.[1],
      });
      message.success({
        content: 'Success',
      });
      onSuccess?.(res.data);
      return true;
    } catch (error) {
      message.error({
        content: 'Error, please try again',
      });
      return false;
    }
  };
  const [form] = ProForm.useForm();
  useEffect(() => {
    if (planId) {
      form?.setFieldsValue({
        farming_plan: planId,
      });
    }
  }, [planId]);
  return (
    <ModalForm<IFormData>
      modalProps={{
        destroyOnClose: true,
      }}
      name="crop_plan:create-state"
      onFinish={onFinish}
      title={intl.formatMessage({ id: 'common.add_new_state' })}
      trigger={
        <Button icon={<PlusOutlined />} type="primary" size="small">
          {intl.formatMessage({ id: 'common.add_new_state' })}
        </Button>
      }
      form={form}
      initialValues={{
        farming_plan: planId,
      }}
    >
      <ProFormText
        name="label"
        label={intl.formatMessage({ id: 'common.state' })}
        rules={[
          {
            required: true,
          },
        ]}
      />
      <ProFormSelect
        label={intl.formatMessage({ id: 'common.plan' })}
        name="farming_plan"
        disabled
        rules={[
          {
            required: true,
          },
        ]}
        request={async () => {
          const res = await getFarmingPlanList({
            page: 1,
            size: 10000,
          });
          return res.data.map((item) => ({
            label: item.label,
            value: item.name,
          }));
        }}
        showSearch
      />
      {/* <ProFormItem label="Loại giai đoạn theo mẫu">
        <CheckCard.Group>
          <CheckCard
            className={styles.checkCard}
            size="small"
            avatar={<img src={seedSrc} />}
            title="Gieo hạt"
            value="C1"
          />
          <CheckCard
            className={styles.checkCard}
            size="small"
            avatar={<img src={sprinklersSrc} />}
            value="A2"
            title="Tưới nước"
          />
          <CheckCard
            className={styles.checkCard}
            size="small"
            avatar={<img src={sprayingSrc} />}
            value="Aa"
            title="Bơm thuốc"
          />
          <CheckCard
            className={styles.checkCard}
            size="small"
            avatar={<img src={harvestSrc} />}
            title="Thu hoạch"
            value="C"
          />
        </CheckCard.Group>
      </ProFormItem> */}
      <ProFormDateRangePicker
        label={intl.formatMessage({ id: 'seasonalTab.time_completed' })}
        name="date_range"
        fieldProps={{
          format: DEFAULT_DATE_FORMAT_WITHOUT_TIME,
        }}
      />
    </ModalForm>
  );
};

export default CreateState;
