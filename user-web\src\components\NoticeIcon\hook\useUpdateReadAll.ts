import { updateWebNotificationReadAll } from '@/services/web-notification';
import { useIntl, useModel, useRequest } from '@umijs/max';
import { App } from 'antd';

export const useUpdateReadAll = (params?: { onSuccess?: (data: number) => void }) => {
  const intl = useIntl();
  const mqttModel = useModel('MQTTNotification');

  const { message } = App.useApp();
  return useRequest(updateWebNotificationReadAll, {
    debounceInterval: 400,
    manual: true,
    onSuccess: params?.onSuccess,
    onError: () => {
      message.success(intl.formatMessage({ id: 'common.error' }));
      mqttModel.handleMessage.noticeHandleReadAll.emit.readAll({
        isReadAll: true,
        time: Date.now(),
      });
    },
  });
};
