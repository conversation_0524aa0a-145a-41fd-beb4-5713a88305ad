import ActionModalConfirm from '@/components/ActionModalConfirm';
import { FC } from 'react';
import useDelete from '../../hooks/useDelete';

interface DeleteProcessProps {
  id: string;
  onSuccess?: () => void;
}

const DeleteProcess: FC<DeleteProcessProps> = ({ id, onSuccess }) => {
  const { run } = useDelete({
    onSuccess,
  });
  return (
    <ActionModalConfirm
      modalProps={{
        async onOk(...args) {
          await run(id);
          return true;
        },
      }}
    />
  );
};

export default DeleteProcess;
