import { getPlantUserOwnerAllResources } from '@/services/plantRefAndUserOwner';
import { myLazy } from '@/utils/lazy';
import { nonAccentVietnamese } from '@/utils/string';
import { ListSkeleton } from '@ant-design/pro-components';
import { useRequest } from '@umijs/max';
import { Spin, Tabs, TabsProps } from 'antd';
import { FC, ReactNode, Suspense, useEffect, useMemo, useState } from 'react';
import useParamsUrl from '../hooks/useParamsUrl';

const GeneralInfo = myLazy(() => import('./GeneralInfo'));
const CareInstructions = myLazy(() => import('./CareInstructions'));

interface InstructProps {
  children?: ReactNode;
  plantId?: string;
}

const Instruct: FC<InstructProps> = ({ children, plantId }) => {

  const { tabSecondActive, setTabSecondActive } = useParamsUrl();
  const [isLoading, setIsLoading] = useState<boolean>(false);
  const [plantAllResource, setPlantAllResource] = useState<any>();
  useEffect(() => {
    const fetchData = async () => {
      setIsLoading(true);
      try {
        const res = await getPlantUserOwnerAllResources({
          plant_id: plantId,
        });
        setPlantAllResource(res.data?.[0]);
      } catch (error) {
        console.log(error);
      } finally {
        setIsLoading(false);
      }
    };
    fetchData();
  }, [plantId]);
  console.log("plantAllResource", plantAllResource?.infor_tab_list)
  const mappingTab = useMemo<
    {
      label: string;
      children?: () => React.JSX.Element;
      key: string;
      fallback?: ReactNode;
    }[]
  >(
    () =>
      [
        {
          label: 'Thông tin chung',
          children: () => (
            <GeneralInfo
              data={plantAllResource?.infor_tab_list?.map((item: any) => ({
                id: item.name,
                title: item.label,
                icon: item.icon,
                content: item.description,
                image: item.image,
              }))}
            />
          ),
          fallback: <ListSkeleton size={10} />,
        },
        {
          label: 'Hướng dẫn chăm sóc',
          children: () => (
            <CareInstructions
              data={plantAllResource?.guide_list?.map((item: any) => ({
                id: item.name,
                title: item.label,
                icon: item.icon,
                content: item.description,
                image: item.image,
              }))}
            />
          ),
          fallback: <ListSkeleton size={10} />,
        },
      ].map((item) => ({
        ...item,
        key: nonAccentVietnamese(item.label),
      })),
    [plantAllResource],
  );
  const tabItems: TabsProps['items'] = mappingTab.map((item) => ({
    label: item.label,
    key: item.key,
  }));
  const tabActive = mappingTab.find((item) => item.key === tabSecondActive) || mappingTab[0];
  const Component = tabActive.children;
  return (
    <Spin spinning={isLoading}>
      <Tabs
        style={{
          marginBlockStart: -20,
        }}
        activeKey={tabActive.key}
        items={tabItems}
        onChange={setTabSecondActive}
      />
      <Suspense fallback={tabActive.fallback || null}>{Component && <Component />}</Suspense>
    </Spin>
  );
};

export default Instruct;
