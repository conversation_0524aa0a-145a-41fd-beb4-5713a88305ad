import { DEFAULT_DATE_FORMAT_WITHOUT_TIME, DOCTYPE_ERP } from '@/common/contanst/constanst';
import ActionHover from '@/components/ActionHover';
import { getDocumentList } from '@/services/diary-2/document';
import { getParamsReqTable } from '@/services/utils';
import { ActionType, ProTable } from '@ant-design/pro-components';
import { useIntl } from '@umijs/max';
import { Button, Space } from 'antd';
import moment from 'moment';
import { FC, ReactNode, useEffect, useRef, useState } from 'react';
import DeleteDocument from './DeleteDocument';

interface StageCropListProps {
  children?: ReactNode;
  onSelect?: (noteId: string) => void;
  reloadKey?: string | null;
}

const CertificationList: FC<StageCropListProps> = ({ children, onSelect, reloadKey }) => {
  const { formatMessage } = useIntl();
  const actionRef = useRef<ActionType>();
  const [data, setData] = useState<any[]>([]);
  const [selectedRowKey, setSelectedRowKey] = useState<string | null>(null);

  const handleReload = () => {
    actionRef.current?.reload?.();
  };

  useEffect(() => {
    if (reloadKey) {
      handleReload();
    }
  }, [reloadKey]);

  useEffect(() => {
    if (data.length > 0 && !selectedRowKey) {
      const firstRowKey = data[0].name;
      setSelectedRowKey(firstRowKey);
      onSelect?.(firstRowKey);
    }
  }, [data]);

  return (
    <ProTable
      actionRef={actionRef}
      search={false}
      toolBarRender={() => []}
      rowKey="name"
      form={{
        labelWidth: 'auto',
      }}
      scroll={{ x: 'max-content' }}
      pagination={{
        pageSize: 10,
        showSizeChanger: true,
        defaultPageSize: 10,
      }}
      request={async (params, sort, filter) => {
        const paramsReq = getParamsReqTable({
          doc_name: DOCTYPE_ERP.iot_diary_v2_document,
          tableReqParams: {
            params,
            sort,
            filter,
          },
          defaultSort: 'name asc',
        });
        const res = await getDocumentList(paramsReq);
        setData(res.data);
        return {
          data: res.data,
          total: res.pagination.totalElements,
        };
      }}
      rowClassName={(record) =>
        record.name === selectedRowKey ? 'bg-emerald-100 group/action' : 'group/action'
      }
      options={false}
      columns={[
        {
          title: formatMessage({
            id: 'common.name',
          }),
          dataIndex: 'label',
          render(dom, entity, index, action, schema) {
            return (
              <Button
                type="link"
                onClick={() => {
                  onSelect?.(entity.name);
                  setSelectedRowKey(entity.name);
                }}
              >
                {dom}
              </Button>
            );
          },
        },
        {
          title: formatMessage({
            id: 'common.certification_date',
          }),
          dataIndex: 'issue_date',
          valueType: 'date',
          render(dom, entity, index, action, schema) {
            return moment(entity.issue_date).format(DEFAULT_DATE_FORMAT_WITHOUT_TIME);
          },
        },
        {
          title: formatMessage({
            id: 'common.expiration_date',
          }),
          dataIndex: 'expiry_date',
          valueType: 'date',
          render: (dom, entity) => {
            return (
              <ActionHover
                actions={() => (
                  <Space>
                    <DeleteDocument onSuccess={handleReload} id={entity.name} />
                  </Space>
                )}
              >
                {moment(entity.expiry_date).format(DEFAULT_DATE_FORMAT_WITHOUT_TIME)}
              </ActionHover>
            );
          },
        },
      ]}
    />
  );
};

export default CertificationList;
