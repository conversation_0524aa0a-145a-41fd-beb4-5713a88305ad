import { useIntl } from '@umijs/max';
import { App } from 'antd';
import { useCallback } from 'react';

const useUnsavedChangesModal = (isFormDirty: boolean) => {
  const { formatMessage } = useIntl();
  const { modal } = App.useApp();
  const confirmNavigation = useCallback(
    (callback: () => void) => {
      if (isFormDirty) {
        modal.confirm({
          title: formatMessage({ id: 'common.unsaved_changes' }),
          content: formatMessage({ id: 'common.confirm_leave' }),
          onOk: callback,
          okButtonProps: {
            danger: true,
          },
        });
      } else {
        callback();
      }
    },
    [isFormDirty, formatMessage],
  );

  return confirmNavigation;
};

export default useUnsavedChangesModal;
