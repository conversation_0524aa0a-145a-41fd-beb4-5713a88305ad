import { getCropItemStatisticDetailTask, ICropItemInTask } from '@/services/crop';
import { EyeOutlined } from '@ant-design/icons';
import { ProColumns, ProTable } from '@ant-design/pro-components';
import { App, Button, Modal, Space } from 'antd';
import { useState } from 'react';
export interface Props {
  category_id: string;
  category_label: string;
  crop_id: string;
}
const TaskItemsModal = ({ category_id, category_label, crop_id }: Props) => {
  const [isOpen, setOpen] = useState(false);
  const showModal = () => {
    setOpen(true);
  };

  const { message } = App.useApp();
  const hideModal = () => {
    setOpen(false);
  };

  const handleCancel = () => {
    hideModal();
  };
  const columns: ProColumns<ICropItemInTask>[] = [
    {
      title: 'Công việc',
      dataIndex: 'task_label',
      render(dom, entity, index, action, schema) {
        return (
          <a
            href={`/farming-management/workflow-management/detail/${entity.task_id}`}
            target="_blank"
            rel="noopener noreferrer"
          >
            {entity.task_label}
          </a>
        );
      },
    },
    {
      title: 'Tổng dự kiến',
      dataIndex: 'total_exp_quantity',
    },
    {
      title: 'Tổng thực tế',
      dataIndex: 'total_quantity',
    },
    {
      title: 'Tổng chênh lệch',
      dataIndex: 'total_loss_quantity',
    },
    { title: 'Đơn vị', dataIndex: 'unit_label' },
  ];
  return (
    <>
      <Space size={'small'}>
        <Button icon={<EyeOutlined />} onClick={showModal}></Button>
        <span>{category_label}</span>
      </Space>
      <Modal
        title={`Chi tiết sử dụng vật tư ${category_label} trong vụ mùa`}
        open={isOpen}
        onCancel={handleCancel}
        footer={null}
        width={1200}
      >
        <ProTable<ICropItemInTask>
          columns={columns}
          search={false}
          pagination={{
            pageSizeOptions: [10, 20, 50, 100],
            showSizeChanger: true,
            defaultPageSize: 10,
          }}
          request={async (params, sorter, filter) => {
            try {
              const res = await getCropItemStatisticDetailTask({
                page: params.current,
                size: params.pageSize,
                crop_id,
                category_id,
              });
              return {
                data: res.data,
                success: true,
              };
            } catch (error: any) {
              message.error(`Lỗi khi kéo dữ liệu: ${error.message}`);
              return {
                success: false,
              };
            }
          }}
          rowKey={'task_id'}
        />
      </Modal>
    </>
  );
};

export default TaskItemsModal;
