import { getDetailsProductItemV3 } from '@/services/InventoryManagementV3/product-item';
import { DeleteOutlined } from '@ant-design/icons';
import {
  ActionType,
  EditableProTable,
  ProColumns,
  ProFormSelect,
} from '@ant-design/pro-components';
import { FormattedMessage } from '@umijs/max';
import { Button, InputNumber } from 'antd';
import numeral from 'numeral';
import { useEffect, useRef, useState } from 'react';
import { IMaterialTransferVoucherItem } from '../Interfaces/StockEntryInterfaces';

interface Props {
  data: IMaterialTransferVoucherItem[];
  setData: React.Dispatch<React.SetStateAction<IMaterialTransferVoucherItem[]>>;
}
const QuickMaterialIssueTable = ({ data, setData }: Props) => {
  const actionRef = useRef<ActionType>();
  const handleDeleteItem = (index: any) => {
    const newData = data.filter((item) => item.key !== index);
    setData(newData);
  };
  const [editableKeys, setEditableRowKeys] = useState<React.Key[]>(() =>
    data.map((item) => item.key),
  );
  const handleUpdateQuantity = (record: IMaterialTransferVoucherItem) => {
    // record.total_price = record.qty * record.rate;
    //do chuyển kho không cần nhập đơn giá
    record.total_price = 0;
  };
  // const handleSelectUnit = (index: any, value: number) => {
  //   const newData = structuredClone(data);
  //   const recordIndex = newData.findIndex((item) => item.key === index);
  //   newData[recordIndex].actual_qty =
  //     (newData[recordIndex].actual_qty * newData[recordIndex].conversion_factor) / value;
  //   newData[recordIndex].conversion_factor = value;
  //   setData(newData);
  //   actionRef.current?.reload();
  // };

  const handleSelectUnit = (index: any, value: string) => {
    console.log('value', value);
    const parseValue = JSON.parse(value);
    const newData = structuredClone(data);
    const recordIndex = newData.findIndex((item) => item.key === index);
    newData[recordIndex].actual_qty =
      (newData[recordIndex].actual_qty * newData[recordIndex].conversion_factor) /
      parseValue.conversion_factor;
    newData[recordIndex].conversion_factor = parseValue.conversion_factor;
    newData[recordIndex].convert_uom_id = parseValue.name;
    newData[recordIndex].convert_uom_label = parseValue.label;
    setData(newData);
    actionRef.current?.reload();
  };

  const columns: ProColumns<IMaterialTransferVoucherItem>[] = [
    {
      title: <FormattedMessage id={'common.index'} />,
      dataIndex: 'index',
      editable: false,
      render(dom, entity, index, action, schema) {
        return <div>{index + 1}</div>;
      },
      width: 40,
    },
    {
      title: <FormattedMessage id={'warehouse-management.export-voucher.item_code'} />,
      dataIndex: 'item_name',
      editable: false,
      width: 80,
    },
    {
      title: (
        <FormattedMessage
          id="warehouse-management.export-voucher.item_name"
          defaultMessage="unknown"
        />
      ),
      dataIndex: 'item_label',
      editable: false,
      width: 80,
    },
    // {
    //   title: 'Tồn kho',
    //   dataIndex: 'current_quantity',
    //   search: false,
    //   editable: false,
    //   hideInTable: type === 'import',
    //   renderFormItem(schema, config, form, action) {
    //     return (
    //       <InputNumber
    //         style={{ width: '100%' }}
    //         formatter={(value) => {
    //           const formatNum = numeral(value).format('0,0.00');
    //           return formatNum;
    //         }}
    //       />
    //     );
    //   },
    //   width: 80,
    // },
    // {
    //   title: <FormattedMessage id="warehouse-management.export-voucher.producer" defaultMessage="unknown" />,
    //   dataIndex: 'supplier',
    //   width: 80,
    // },
    {
      title: <FormattedMessage id="warehouse-management.export-voucher.actual_qty" />,
      dataIndex: 'actual_qty',
      search: false,
      editable: false,
      renderFormItem(schema, config, form, action) {
        return (
          <InputNumber
            readOnly
            style={{ width: '100%' }}
            formatter={(value) => {
              const formatNum = numeral(value).format('0,0.00');
              return formatNum;
            }}
          />
        );
      },
      width: 80,
    },
    {
      title: <FormattedMessage id="warehouse-management.export-voucher.actual_quantity" />,
      dataIndex: 'total_qty_in_crop',
      search: false,
      editable: false,
      renderFormItem(schema, config, form, action) {
        return (
          <InputNumber
            readOnly
            style={{ width: '100%' }}
            formatter={(value) => {
              const formatNum = numeral(value).format('0,0.00');
              return formatNum;
            }}
          />
        );
      },
      width: 80,
    },
    {
      title: (
        <FormattedMessage
          id="warehouse-management.export-voucher.quantity"
          defaultMessage="unknown"
        />
      ),
      dataIndex: 'qty',
      search: false,
      renderFormItem(schema, config, form, action) {
        return (
          <InputNumber
            style={{ width: '100%' }}
            formatter={(value) => `${value}`.replace(/\B(?=(\d{3})+(?!\d))/g, ',')}
            parser={(value) => value!.replace(/\$\s?|(,*)/g, '')}
            step="0.01"
          />
        );
      },
      width: 80,
    },

    {
      title: <FormattedMessage id="common.unit" />,
      editable: false,
      dataIndex: 'conversion_factor',
      render: (dom, entity, index, action, schema) => [
        <ProFormSelect
          formItemProps={{
            style: {
              marginBottom: 0,
            },
          }}
          key={'conversion_factor'}
          fieldProps={{
            defaultValue: entity.uom_label,
          }}
          placeholder="Loại đếm"
          request={async () => {
            const res =
              (await getDetailsProductItemV3({
                name: entity.item_code || '',
              })) || [];
            const formatted_res =
              res.data.uoms?.map((item) => ({
                label: item.uom_name,
                value: JSON.stringify({
                  conversion_factor: item.conversion_factor,
                  name: item.uom,
                  label: item.uom_name,
                }),
              })) || [];
            const options = [
              {
                label: entity.uom_label,
                value: JSON.stringify({
                  conversion_factor: 1,
                  name: entity.convert_uom_id,
                  label: entity.convert_uom_label,
                }),
              },
            ];
            return [...options, ...formatted_res];
          }}
          onChange={(e) => handleSelectUnit(entity.key, e)}
        />,
      ],
      width: 80,
    },

    // {
    //   title: <FormattedMessage id="warehouse-management.export-voucher.rate" defaultMessage="unknown" />,
    //   dataIndex: 'rate',
    //   search: false,
    //   width: 80,
    //   renderFormItem(schema, config, form, action) {
    //     return (
    //       <InputNumber
    //         style={{ width: '100%' }}
    //         formatter={(value) => `${value}`.replace(/\B(?=(\d{3})+(?!\d))/g, ',')}
    //         parser={(value) => value!.replace(/\$\s?|(,*)/g, '')}
    //         step="0.01"
    //       />
    //     );
    //   },
    // },
    // {
    //   title: <FormattedMessage id="warehouse-management.export-voucher.price" defaultMessage="unknown" />,
    //   dataIndex: 'total_price',
    //   editable: false,
    //   render: (text, record) => numeral(record.total_price).format('0,0.00'), // Định dạng số ở đây
    //   width: 80,
    // },
    {
      title: 'Action',
      dataIndex: 'key',
      editable: false,
      render: (index, record, _, action) => [
        <Button icon={<DeleteOutlined />} key="delete" onClick={() => handleDeleteItem(index)} />,
      ],
      width: 80,
    },
  ];
  useEffect(() => {
    setEditableRowKeys(data.map((item) => item.key));
  }, [data]);
  useEffect(() => {
    actionRef.current?.reload();
  }, [data, editableKeys]);
  return (
    <EditableProTable<IMaterialTransferVoucherItem>
      style={{ minWidth: '100%' }}
      columns={columns}
      actionRef={actionRef}
      cardBordered
      request={async (params = {}, sort, filter) => {
        return {
          data,
          success: true,
          total: data.length,
        };
      }}
      editable={{
        type: 'multiple',
        editableKeys,
        actionRender: (row, config, defaultDoms) => {
          return [defaultDoms.delete];
        },
        onValuesChange: (record, recordList) => {
          handleUpdateQuantity(record);
          setData(recordList);
        },
        onChange: setEditableRowKeys,
      }}
      recordCreatorProps={false}
      rowKey="key"
      search={false}
      options={{
        setting: {
          listsHeight: 400,
        },
      }}
      pagination={{
        defaultPageSize: 50,
        showSizeChanger: true,
        pageSizeOptions: ['20', '50', '100'],
      }}
      toolbar={{
        multipleLine: false,
      }}
      toolBarRender={false}
      size="small"
      dateFormatter="string"
    />
  );
};

export default QuickMaterialIssueTable;
