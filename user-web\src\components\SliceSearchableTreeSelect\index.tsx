import { useIntl } from '@umijs/max';
import { Col, Input, Row, Tree, Typography } from 'antd';
import type { DataNode } from 'antd/es/tree';
import React, { useEffect, useMemo, useState } from 'react';

const { Search } = Input;
const { Title } = Typography;

interface ITreeNode {
  title: string;
  value: string;
  key: string;
  normalized_title: string;
  fullObject?: any;
  children?: ITreeNode[];
}

interface SearchableTreeSelectProps {
  defaultData: ITreeNode[];
  fieldName?: string;
  onCheck: (selectedWithBOM: React.Key[], selectedWithoutBOM: React.Key[]) => void;
}

const getParentKey = (key: React.Key, tree: DataNode[]): React.Key | undefined => {
  for (const node of tree) {
    if (node.children) {
      if (node.children.some((item) => item.key === key)) {
        return node.key;
      }
      const foundKey = getParentKey(key, node.children);
      if (foundKey) {
        return foundKey;
      }
    }
  }
  return undefined;
};

const SliceSearchableTreeSelect = ({ defaultData, onCheck }: SearchableTreeSelectProps) => {
  const [expandedKeys, setExpandedKeys] = useState<React.Key[]>([]);
  const [searchValue, setSearchValue] = useState('');
  const [autoExpandParent, setAutoExpandParent] = useState(true);
  const [dataList, setDataList] = useState<
    { key: React.Key; title: string; normalized_title: string }[]
  >([]);

  const [checkedKeysWithBOM, setCheckedKeysWithBOM] = useState<React.Key[]>([]);
  const [checkedKeysWithoutBOM, setCheckedKeysWithoutBOM] = useState<React.Key[]>([]);

  const onExpand = (newExpandedKeys: React.Key[]) => {
    setExpandedKeys(newExpandedKeys);
    setAutoExpandParent(false);
  };

  const handleOnCheckWithBOM = (checkedKeys: any) => {
    setCheckedKeysWithBOM(checkedKeys);
    onCheck(checkedKeys, checkedKeysWithoutBOM);
  };

  const handleOnCheckWithoutBOM = (checkedKeys: any) => {
    setCheckedKeysWithoutBOM(checkedKeys);
    onCheck(checkedKeysWithBOM, checkedKeys);
  };

  useEffect(() => {
    const generateList = (data: ITreeNode[]) => {
      const newDataList: { key: React.Key; title: string; normalized_title: string }[] = [];
      for (const node of data) {
        newDataList.push({
          key: node.key,
          title: node.title,
          normalized_title: node.normalized_title,
        });
        if (node.children) {
          newDataList.push(...generateList(node.children));
        }
      }
      return newDataList;
    };
    setDataList(generateList(defaultData));
  }, [defaultData]);

  const onChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { value } = e.target;
    const normalized_value = value
      .toLowerCase()
      .normalize('NFD')
      .replace(/[\u0300-\u036f]/g, '');
    const newExpandedKeys = dataList
      .map((item) => {
        if (item.normalized_title.includes(normalized_value)) {
          return getParentKey(item.key, defaultData);
        }
        return null;
      })
      .filter((item, i, self): item is React.Key => !!item && self.indexOf(item) === i);
    setExpandedKeys(newExpandedKeys);
    setSearchValue(value);
    setAutoExpandParent(true);
  };

  const loop = (data: ITreeNode[]): DataNode[] =>
    data.map((item) => {
      const normalizedSearchValue = searchValue
        .toLowerCase()
        .normalize('NFD')
        .replace(/[\u0300-\u036f]/g, '');
      const index = item.normalized_title.indexOf(normalizedSearchValue);
      const beforeStr = item.title.substring(0, index);
      const str = item.title.substring(index, index + searchValue.length);
      const afterStr = item.title.slice(index + searchValue.length);
      const titleText =
        item.fullObject && item.fullObject.bom && item.fullObject.bom.length > 0
          ? `${item.title}`
          : item.title;

      const title =
        searchValue === '' ? (
          <span>{titleText}</span>
        ) : index > -1 ? (
          <span>
            {beforeStr}
            <span style={{ color: 'white', backgroundColor: 'green' }}>{str}</span>
            {afterStr}
          </span>
        ) : (
          <span>{titleText}</span>
        );

      if (item.children) {
        return { title, key: item.key, children: loop(item.children) };
      }
      return { title, key: item.key };
    });

  const { dataWithBOM, dataWithoutBOM } = useMemo(() => {
    const withBOM: ITreeNode[] = [];
    const withoutBOM: ITreeNode[] = [];

    defaultData.forEach((group) => {
      const childrenWithBOM = group.children?.filter((item) => item.fullObject?.bom?.length > 0);
      const childrenWithoutBOM = group.children?.filter(
        (item) => !item.fullObject?.bom || item.fullObject.bom.length === 0,
      );

      if (childrenWithBOM && childrenWithBOM.length > 0) {
        withBOM.push({
          ...group,
          children: childrenWithBOM,
        });
      }

      if (childrenWithoutBOM && childrenWithoutBOM.length > 0) {
        withoutBOM.push({
          ...group,
          children: childrenWithoutBOM,
        });
      }
    });

    return { dataWithBOM: withBOM, dataWithoutBOM: withoutBOM };
  }, [defaultData]);

  const treeDataWithBOM = useMemo(() => loop(dataWithBOM), [searchValue, dataWithBOM]);
  const treeDataWithoutBOM = useMemo(() => loop(dataWithoutBOM), [searchValue, dataWithoutBOM]);
  const intl = useIntl();
  return (
    <div>
      <Search style={{ marginBottom: 8 }} placeholder="Search" onChange={onChange} />
      <Row gutter={16}>
        <Col span={12}>
          <Title level={5}>{intl.formatMessage({ id: 'common.item_list' })}</Title>
          <Tree
            onExpand={onExpand}
            expandedKeys={expandedKeys}
            autoExpandParent={autoExpandParent}
            treeData={treeDataWithoutBOM}
            checkable
            onCheck={handleOnCheckWithoutBOM}
            checkedKeys={checkedKeysWithoutBOM}
          />
        </Col>
        <Col span={12}>
          <Title level={5}>{intl.formatMessage({ id: 'common.item_list_with_bom' })}</Title>
          <Tree
            onExpand={onExpand}
            expandedKeys={expandedKeys}
            autoExpandParent={autoExpandParent}
            treeData={treeDataWithBOM}
            checkable
            onCheck={handleOnCheckWithBOM}
            checkedKeys={checkedKeysWithBOM}
          />
        </Col>
      </Row>
    </div>
  );
};

export default SliceSearchableTreeSelect;
