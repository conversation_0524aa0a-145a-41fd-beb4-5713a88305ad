import { DEFAULT_DATE_FORMAT_WITHOUT_TIME, DEFAULT_PAGE_SIZE_ALL, DOCTYPE_ERP } from '@/common/contanst/constanst';
import { getFarmingPlanList } from '@/services/farming-plan';
import { getPlantUserOwnerAllResources } from '@/services/plantRefAndUserOwner';
import { zoneList } from '@/services/zones';
import { CameraFilled } from '@ant-design/icons';
import {
  ProForm,
  ProFormDateRangePicker,
  ProFormDigit,
  ProFormSelect,
  ProFormText,
  ProFormTextArea,
  ProFormUploadButton,
} from '@ant-design/pro-components';
import { Card, Divider } from 'antd';
import { FC, ReactNode } from 'react';

interface DetailedInfoProps {
  children?: ReactNode;
}
const DetailedInfo: FC<DetailedInfoProps> = ({ children }) => {
  return (
    <Card title="Thông tin chi tiết">
      <ProForm.Group>
        <ProFormUploadButton
          label="Ảnh đại diện"
          accept="image/*"
          listType="picture-card"
          icon={<CameraFilled />}
          title=""
          name="avatar"
          max={1} // Set maximum files to 1
        />
      </ProForm.Group>
      <ProForm.Group>
        <ProFormText
          label={'Tên vụ mùa'}
          rules={[
            {
              required: true,
            },
          ]}
          width={'md'}
          name="label"
        />
        {/* <ProFormSelect
          request={async () => {
            const res = await projectList({ size: Number.MAX_SAFE_INTEGER, page: 1 });
            return res.data.map((item) => ({
              label: item.title,
              value: item.name,
            }));
          }}
          label="Chọn dự án"
          rules={[
            {
              required: true,
            },
          ]}
          width={'md'}
        /> */}
        <ProFormSelect
          label="Chọn khu vực"
          rules={[
            {
              required: true,
            },
          ]}
          width={'md'}
          request={async () => {
            const res = await zoneList({ size: Number.MAX_SAFE_INTEGER, page: 1 });
            return res.data.map((item) => ({
              label: item.label,
              value: item.name,
            }));
          }}
          name="zone_id"
        />
        <ProFormDateRangePicker
          label="Thời gian hoàn thành"
          width={'md'}
          rules={[
            {
              required: true,
            },
          ]}
          fieldProps={{
            format: DEFAULT_DATE_FORMAT_WITHOUT_TIME,
          }}
          name="date_range"
        />
      </ProForm.Group>
      <ProForm.Group>
        <ProFormDigit label="Diện tích canh tác (m2)" min={0} name="square" width={'md'} />
        <ProFormDigit
          label="Sản lượng dự kiến - Kg"
          min={0}
          name="quantity_estimate"
          width={'md'}
        />
        <ProFormSelect
          label="Chọn loại cây trồng"
          width={'md'}
          request={async () => {
            const res = await getPlantUserOwnerAllResources({
              size: DEFAULT_PAGE_SIZE_ALL,
            });
            return res.data.map((item) => ({
              label: item.label,
              value: item.name,
            }));
          }}
          name="plant_id"
        />
        <ProFormSelect
          width={'md'}
          rules={[
            {
              required: true,
            },
          ]}
          label="Trạng thái"
          name="status"
          options={[
            {
              label: 'Đang diễn ra',
              value: 'In progress',
            },
            {
              label: 'Hoàn tất',
              value: 'Done',
            },
          ]}
          initialValue={'In progress'}
        />
      </ProForm.Group>
      <ProForm.Group>
        {/* <Item>
          <TextArea rows={4} placeholder="ghi chus" maxLength={6} name="description" />
        </Item> */}
        <ProFormTextArea
          width={'xl'}
          name="description"
          label="Ghi chú"
          placeholder="Please enter a name"
        />
      </ProForm.Group>
      <ProForm.Group>
        {/* <ProFormSelect
          label="Kế hoạch canh tác"
          rules={[
            {
              required: true,
            },
          ]}
          width={'md'}
          request={async () => {
            const res = await zoneList({ size: Number.MAX_SAFE_INTEGER, page: 1 });
            return res.data.map((item) => ({
              label: item.label,
              value: item.name,
            }));
          }}
        /> */}
      </ProForm.Group>
      <Divider type="horizontal" orientation="left" orientationMargin="0">
        Tạo kế hoạch
      </Divider>
      <ProForm.Group>
        <ProFormText
          name="plan_label"
          label={'Tên kế hoạch'}
          rules={[
            {
              required: true,
            },
          ]}
        />
        <ProFormDateRangePicker
          colSize={24}
          disabled
          name="date_range"
          label="Thời gian"
          rules={[
            {
              required: true,
            },
          ]}
          fieldProps={{
            format: DEFAULT_DATE_FORMAT_WITHOUT_TIME,
          }}
        />

        <ProFormSelect
          colSize={24}
          allowClear
          showSearch
          label="Sao chép từ kế hoạch khác"
          name="copy_plan_id"
          request={async ({ keyWords }) => {
            const res = await getFarmingPlanList({
              page: 1,
              size: DEFAULT_PAGE_SIZE_ALL,
              ...(keyWords && {
                filters: `[["${DOCTYPE_ERP.iotFarmingPlan}", "label", "like", "%${keyWords}%"]]`,
              }),
            });
            console.log('res', res);
            return res.data.map((item) => ({
              label: item.label,
              value: item.name,
            }));
          }}
        />
        <ProFormUploadButton
          max={1}
          accept="image/*"
          label="Hình ảnh / Video mô tả "
          listType="picture-card"
          icon={<CameraFilled />}
          title=""
          name="img"
        />
      </ProForm.Group>
    </Card>
  );
};

export default DetailedInfo;
