import EventEmitter from 'eventemitter3';
import { OnMessageCallback } from 'mqtt/types/lib/client';
import { HandleRealtimeMqtt } from './type';

// export const keyMqttMessageEventEmitter = 'mqtt-onmessage-device';
export type MqttNoticeDataItem = { ts: number; key: string; value: number | string };
export type MqttNoticeDataSub = {
  // deviceId: string;
  deviceIdThingsBoard: string;
  data: MqttNoticeDataItem[];
};

export class MqttDeviceEventEmitterControl implements HandleRealtimeMqtt<MqttNoticeDataSub> {
  events: EventEmitter;
  keyEvent: string;

  constructor() {
    this.events = new EventEmitter();
    this.keyEvent = 'your_key'; // Replace with appropriate value
  }

  handleReceiveMessage: OnMessageCallback = (topic, msg) => {
    try {

      const device_id_thingsboard = topic.split('/')[topic.split('/').length - 2];
      const data: Array<{ ts: string; key: string; value: string | number }> = JSON.parse(
        msg.toString(),
      );
      if (Array.isArray(data) && device_id_thingsboard) {
        let deviceData = data.filter((d) => {
          return d.key !== 'deviceId';
        });

        let dataFormatted = deviceData.map((d) => ({ ...d, ts: parseInt(d.ts.toString()) }));
        // kiểm tra data có timestamp có lớn hơn data hiện tai của  chart không

        const latestTimestamp = Date.now();
        deviceData = deviceData.filter((item) => (item.ts as any) >= latestTimestamp);

        this.emit({
          data: dataFormatted,
          deviceIdThingsBoard: device_id_thingsboard,
        });
      }
    } catch (error) {
      // console.log('error: ', error);
    }
  };
  emit(message: MqttNoticeDataSub) {
    this.events.emit(this.keyEvent, message);
  }

  on(handle: (message: MqttNoticeDataSub) => void) {
    const removeSubscribe = () => {
      this.events.removeListener(this.keyEvent, handle);
    };

    this.events.on(this.keyEvent, handle);

    return {
      removeSubscribe,
    };
  }

  removeAllListeners() {
    this.events.removeAllListeners('message');
  }
}
