import { sscript } from '@/services/sscript';
import { IIotProductionFunction } from '@/types/IIotProductionFunction';
import { PageContainer } from '@ant-design/pro-components';
import { useModel } from '@umijs/max';
import { <PERSON><PERSON>, Card, Col, ConfigProvider, DatePicker, Input, message, Result, Row, Select, Spin, Tabs } from 'antd';
import React, { useEffect, useState } from 'react';
import RenderDeviceFncGroup from './RenderDeviceFncGroup';

const timeTypeOption = [
    {
        value: "5p-now",
        label: "5 phút trước - hiện tại"
    },
    {
        value: "1h-now",
        label: "1h trước - hiện tại"
    },
    {
        value: "2h-now",
        label: "2h trước - hiện tại"
    },
    {
        value: "1d-now",
        label: "Ngày hôm trước - hiện tại"
    },
    {
        value: "custom",
        label: "Tuỳ chỉnh"
    }
];

const RenderDeviceFncTabs = ({ deviceFunction }: { deviceFunction: any[] }) => {
    if (!deviceFunction.length) return <>Không có dữ liệu</>
    if (deviceFunction.length < 2) {
        return (
            <>
                {deviceFunction.map((d: any) => {
                    return <RenderDeviceFncGroup
                        key={d.name}
                        deviceFunctionGroup={d.groups}>
                    </RenderDeviceFncGroup>
                })
                }
            </>
        );
    }
    else {

        return (
            <Tabs defaultActiveKey={deviceFunction[0].name}>
                {deviceFunction.map((d: any) => {
                    return <Tabs.TabPane tab={d.label} key={"tab-" + d.name}>
                        <RenderDeviceFncGroup
                            deviceFunctionGroup={d.groups}>
                        </RenderDeviceFncGroup>
                    </Tabs.TabPane>
                })}
            </Tabs>
        );
    }

};

export default RenderDeviceFncTabs;
