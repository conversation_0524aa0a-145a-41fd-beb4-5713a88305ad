// @ts-nocheck
// This file is generated by Umi automatically
// DO NOT CHANGE IT MANUALLY!
import model_4 from 'D:/WORK/PYROJECT/VIIS/viis-iot-web-v2/user-web/src/models/MyCrop';
import model_5 from 'D:/WORK/PYROJECT/VIIS/viis-iot-web-v2/user-web/src/models/MyInventory';
import model_10 from 'D:/WORK/PYROJECT/VIIS/viis-iot-web-v2/user-web/src/.umi-production/plugin-initialState/@@initialState';
import model_1 from 'D:/WORK/PYROJECT/VIIS/viis-iot-web-v2/user-web/src/models/Docs';
import model_2 from 'D:/WORK/PYROJECT/VIIS/viis-iot-web-v2/user-web/src/models/MQTTNotification';
import model_3 from 'D:/WORK/PYROJECT/VIIS/viis-iot-web-v2/user-web/src/models/MyCategory';
import model_6 from 'D:/WORK/PYROJECT/VIIS/viis-iot-web-v2/user-web/src/models/MyPlant';
import model_7 from 'D:/WORK/PYROJECT/VIIS/viis-iot-web-v2/user-web/src/models/MyProducts';
import model_8 from 'D:/WORK/PYROJECT/VIIS/viis-iot-web-v2/user-web/src/models/MyResource';
import model_9 from 'D:/WORK/PYROJECT/VIIS/viis-iot-web-v2/user-web/src/models/MyWorkType';

export const models = {
model_4: { namespace: 'MyCrop', model: model_4 },
model_5: { namespace: 'MyInventory', model: model_5 },
model_10: { namespace: '@@initialState', model: model_10 },
model_1: { namespace: 'Docs', model: model_1 },
model_2: { namespace: 'MQTTNotification', model: model_2 },
model_3: { namespace: 'MyCategory', model: model_3 },
model_6: { namespace: 'MyPlant', model: model_6 },
model_7: { namespace: 'MyProducts', model: model_7 },
model_8: { namespace: 'MyResource', model: model_8 },
model_9: { namespace: 'MyWorkType', model: model_9 },
} as const
