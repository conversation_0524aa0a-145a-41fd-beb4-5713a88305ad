import { ITreeNode } from '@/components/SearchableTreeSelect';
import SliceSearchableTreeSelect from '@/components/SliceSearchableTreeSelect';
import { useIntl } from '@umijs/max';
import { Divider, Row } from 'antd';
import { FC } from 'react';
import QuantityInputList from '../BomInTask/InputQtyForItemTaskUsed';

interface CategoryQuantitySelectorProps {
  treeData: ITreeNode[];
  onCheck: (selectedWithBOM: React.Key[], selectedWithoutBOM: React.Key[]) => void;
}

const CategoryQuantitySelector: FC<CategoryQuantitySelectorProps> = ({ treeData, onCheck }) => {
  const intl = useIntl();

  const handleCheck = (selectedWithBOM: React.Key[], selectedWithoutBOM: React.Key[]) => {
    onCheck(selectedWithBOM, selectedWithoutBOM);
  };

  return (
    <div>
      <SliceSearchableTreeSelect
        defaultData={treeData || []}
        fieldName="categories"
        onCheck={handleCheck}
      />
      <Divider plain>{intl.formatMessage({ id: 'common.added_item_qty' })}</Divider>
      <Row gutter={5}>
        <QuantityInputList />
      </Row>
    </div>
  );
};

export default CategoryQuantitySelector;
