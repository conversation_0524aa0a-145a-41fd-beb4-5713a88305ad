import { generalUpdate } from '@/services/sscript';
import { getAllTodo } from '@/services/TaskAndTodo';
import type { ProColumns } from '@ant-design/pro-components';
import { ProTable } from '@ant-design/pro-components';
import { useAccess, useIntl } from '@umijs/max';
import { Checkbox, message } from 'antd';
import moment from 'moment';
import React, { useEffect, useState } from 'react';
import CreateTodoForTask from './CreateTodoForTask';
import DeleteTodoForTask from './DeleteTodoForTask';
import UpdateTodoForTask from './UpdateTodoForTask';

type DataSourceType = {
  id: React.Key;
  title?: string;
  decs?: string;
  state?: string;
  created_at?: string;
  children?: DataSourceType[];
};

export default ({ task_id, showToolbar = true }: { task_id: string; showToolbar?: boolean }) => {
  const [dataSource, setDataSource] = useState([]);
  const [editableKeys, setEditableRowKeys] = useState<React.Key[]>();
  const [loading, setLoading] = useState(false);
  const intl = useIntl();
  const getTaskTodo = async () => {
    try {
      setLoading(true);
      const resData = await getAllTodo(task_id);
      setDataSource(
        resData.data.map((d: any) => {
          d.start_date = moment(d.start_date).isValid() ? moment(d.start_date) : null;
          d.end_date = moment(d.end_date).isValid() ? moment(d.end_date) : null;
          return d;
        }),
      );
    } catch (error: any) {
      message.error(error.toString());
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    getTaskTodo();
  }, []);
  const access = useAccess();
  const canCreateTask = access.canCreateInWorkFlowManagement();
  const canUpdateTask = access.canUpdateInWorkFlowManagement();
  const canDeleteTask = access.canDeleteInWorkFlowManagement();
  const columns: ProColumns<DataSourceType>[] = [
    {
      title: intl.formatMessage({ id: 'workflowTab.complete' }),
      dataIndex: 'is_completed',
      width: 100,
      render: (dom: any, entity: any) => {
        return (
          <Checkbox
            onChange={async (v) => {
              try {
                await generalUpdate('iot_todo', entity.name, {
                  data: {
                    is_completed: v.target.checked ? 1 : 0,
                  },
                });
                await getTaskTodo();
              } catch (error: any) {
                message.error(error.toString());
              }
            }}
            checked={entity.is_completed ? true : false}
          ></Checkbox>
        );
      },
    },
    {
      title: intl.formatMessage({ id: 'workflowTab.work_name' }),
      dataIndex: 'label',
      width: 250,
    },
    {
      title: intl.formatMessage({ id: 'workflowTab.executor' }),
      dataIndex: 'user_full_name',
      width: 250,
      render: (dom: any, entity: any) => {
        return <>{`${entity.user_last_name} ${entity.user_first_name}`}</>;
      },
    },
    {
      title: intl.formatMessage({ id: 'common.description' }),
      dataIndex: 'description',
      formItemProps: {
        rules: [
          {
            required: true,
            whitespace: true,
            message: intl.formatMessage({ id: 'common.description_required' }),
          },
        ],
      },
    },
    {
      title: intl.formatMessage({ id: 'common.action' }),
      dataIndex: 'name',
      width: 100,
      render: (dom: any, entity: any) => {
        return (
          <>
            {canUpdateTask && (
              <UpdateTodoForTask refreshFnc={getTaskTodo} task_id={task_id} data={entity} />
            )}
            {'  '}
            {canDeleteTask && <DeleteTodoForTask refreshFnc={getTaskTodo} value={dom} />}
          </>
        );
      },
    },
  ];
  const toolBarRenderButton: any = [];

  if (canCreateTask || canUpdateTask) {
    toolBarRenderButton.push(<CreateTodoForTask refreshFnc={getTaskTodo} task_id={task_id} />);
  }
  return (
    <>
      <ProTable
        headerTitle={intl.formatMessage({ id: 'common.sub_task' })}
        columns={columns}
        rowKey="name"
        dataSource={[...dataSource]}
        toolBarRender={showToolbar && (() => toolBarRenderButton)}
        search={false}
        tooltip={false}
        pagination={false}
      />
    </>
  );
};
