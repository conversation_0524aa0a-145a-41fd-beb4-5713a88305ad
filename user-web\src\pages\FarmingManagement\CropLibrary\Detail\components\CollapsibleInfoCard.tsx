import treeGreen from '@/assets/img/icons/tree-green.svg';
import Card<PERSON>ollapse from '@/components/CardCollapse';
import ImagePreviewGroupCommon from '@/components/ImagePreviewGroupCommon';
import { getListFileUrlFromStringV2 } from '@/services/utils';
import { Guide } from '@/types/guide.type';
import { InfoTab } from '@/types/infoTab.type';
import { genDownloadUrl } from '@/utils/file';
import { Image, Space, Typography } from 'antd';
import { UploadFileStatus } from 'antd/es/upload/interface';
import { FC } from 'react';

interface CollapsibleInfoCardProps {
  cardInfo: Guide | InfoTab;
}
const CollapsibleInfoCard: FC<CollapsibleInfoCardProps> = ({ cardInfo }) => {
  const genImages = (imageString: string | undefined) => {
    return getListFileUrlFromStringV2({ arrUrlString: imageString })
      .map((url, index) => ({
        name: `Ảnh ${(index + 1).toString()}` || '',
        url: url || '',
        uid: (-index).toString(),
        status: (url ? 'done' : 'error') as UploadFileStatus,
      }))
      .map((item) => ({
        src: item.url,
      }));
  };
  return (
    <CardCollapse
      key={cardInfo.name}
      title={cardInfo.label}
      titleIcon={
        <Image
          src={cardInfo.icon ? genDownloadUrl(cardInfo.icon) : treeGreen}
          style={{ width: '32px', height: 'auto' }}
        />
      }
      extra={''} //Hack to remove Bin icon
    >
      <Space direction="vertical">
        <Typography.Paragraph ellipsis={false} style={{ whiteSpace: 'pre-line' }}>
          {cardInfo.description}
        </Typography.Paragraph>
        <ImagePreviewGroupCommon listImg={genImages(cardInfo.image)} />
      </Space>
    </CardCollapse>
  );
};

export default CollapsibleInfoCard;
