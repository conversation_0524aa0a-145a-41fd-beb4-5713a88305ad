import { DOC_STATUS_COLOR } from '@/common/contanst/constanst';
import { useIntl } from '@umijs/max';
import { Tag } from 'antd';

type IDocStatusTagProps = {
  status: number; // 0, 1, 2
};

const DocStatusTag: React.FC<IDocStatusTagProps> = ({ status }) => {
  const tagColor = DOC_STATUS_COLOR[status];
  const { formatMessage } = useIntl();
  const docStatusLabel = formatMessage({ id: `common.doc_status.${status}` });
  return <Tag color={tagColor}>{docStatusLabel}</Tag>;
};

export default DocStatusTag;
