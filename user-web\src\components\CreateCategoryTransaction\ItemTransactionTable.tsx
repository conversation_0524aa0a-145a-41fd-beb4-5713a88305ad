import { listAllEquivalentUnit, listAllEquivalentUnitOfCategory } from '@/services/equivalent_unit';
import { ICategoryTransactionItem } from '@/types/categoryTransactionItem.type';
import { IEquivalentUnit } from '@/types/equivalentUnit.type';
import { DeleteOutlined } from '@ant-design/icons';
import type { ActionType, ProColumns } from '@ant-design/pro-components';
import { EditableProTable, ProFormSelect } from '@ant-design/pro-components';
import { FormattedMessage } from '@umijs/max';
import { Button, InputNumber, message } from 'antd';
import numeral from 'numeral';
import { useEffect, useRef, useState } from 'react';

export interface ITransactionItem extends ICategoryTransactionItem {
  category_name: string;
  category_id: string;
  unit_name: string;
  key: string;
  current_quantity?: number;
  conversion_factor: number;
  chosen_conversion_factor?: number;
  packing_unit_id?: string;
  packing_unit_label?: string;
  basic_unit_id?: string;
  basic_unit_label?: string;
  quantity_type: 'packing' | 'basic' | 'equivalent';
  input_quantity: number;
}
interface Props {
  data: ITransactionItem[];
  setData: React.Dispatch<React.SetStateAction<ITransactionItem[]>>;
  type?: 'import' | 'export';
}

const ItemTransactionTable = ({ data, setData, type }: Props) => {
  const actionRef = useRef<ActionType>();
  const handleDeleteItem = (index: any) => {
    const newData = data.filter((item) => item.key !== index);
    setData(newData);
  };
  const [equivalentUnits, setEquivalentUnits] = useState<IEquivalentUnit[]>([]);
  const [editableKeys, setEditableRowKeys] = useState<React.Key[]>(() =>
    data.map((item) => item.key),
  );
  useEffect(() => {
    setEditableRowKeys(data.map((item) => item.key));
    // getItemInfo();
    actionRef.current?.reload();
  }, [data]);
  const updateQuantity = (record: ITransactionItem) => {
    const price = record?.unit_price || 0;
    const input_quantity = record.input_quantity;
    const quantity = input_quantity * (record.chosen_conversion_factor || 1);
    const total = price * quantity;

    record.quantity = quantity;
    record.total_price = total;
    return record;
  };
  const handleSelectPackingType = (index: any, value: string) => {
    // May break backward compalities
    const newData = structuredClone(data);
    const recordIndex = newData.findIndex((item) => item.key === index);
    if (value === 'packing') {
      newData[recordIndex].quantity_type = 'packing';
      newData[recordIndex].chosen_conversion_factor = newData[recordIndex].conversion_factor;
      newData[recordIndex].unit = newData[recordIndex].packing_unit_id;
    } else if (value === 'basic') {
      newData[recordIndex].quantity_type = 'basic';
      newData[recordIndex].chosen_conversion_factor = 1;
      newData[recordIndex].unit = newData[recordIndex].basic_unit_id;
    } else {
      const chosen_equivalen_unit = equivalentUnits.find((item) => item.name === value);
      if (!chosen_equivalen_unit) {
        message.error(`lỗi khi chọn đơn vị: không tìm thấy đơn vị`);
        return;
      }
      // console.log({ record: newData[recordIndex], chosen_equivalen_unit });
      newData[recordIndex].quantity_type = 'equivalent';
      newData[recordIndex].unit = chosen_equivalen_unit.name;
      try {
        newData[recordIndex].chosen_conversion_factor =
          chosen_equivalen_unit.reference_unit === newData[recordIndex].packing_unit_id
            ? (1 / chosen_equivalen_unit.equivalent_factor) * newData[recordIndex].conversion_factor
            : 1 / chosen_equivalen_unit.equivalent_factor;
      } catch (error) {
        message.error('Lỗi với đơn vị tương đương: equivalent_factor không tồn tại hoặc bằng 0');
      }
    }
    newData[recordIndex] = updateQuantity(newData[recordIndex]);

    setData(newData);
    actionRef.current?.reload();
  };

  const columns: ProColumns<ITransactionItem>[] = [
    {
      title: (
        <FormattedMessage
          id="category.material-management.category_code"
          defaultMessage="unknown"
        />
      ),
      dataIndex: 'category_id',
      editable: false,
      width: 80,
    },
    {
      title: (
        <FormattedMessage
          id="category.material-management.category_name"
          defaultMessage="unknown"
        />
      ),
      dataIndex: 'category_name',
      editable: false,
      width: 80,
    },
    {
      title: 'Tồn kho',
      dataIndex: 'current_quantity',
      search: false,
      editable: false,
      hideInTable: type === 'import',
      renderFormItem(schema, config, form, action) {
        return (
          <InputNumber
            style={{ width: '100%' }}
            formatter={(value) => {
              const formatNum = numeral(value).format('0,0.00');
              return formatNum;
            }}
          />
        );
      },
      width: 80,
    },
    {
      title: (
        <FormattedMessage id="category.material-management.producer" defaultMessage="unknown" />
      ),
      dataIndex: 'supplier',
      width: 80,
    },
    {
      title: (
        <FormattedMessage
          id="storage-management.category-management.quantity"
          defaultMessage="unknown"
        />
      ),
      dataIndex: 'input_quantity',
      search: false,
      renderFormItem(schema, config, form, action) {
        return (
          <InputNumber
            style={{ width: '100%' }}
            // formatter={(value) => {
            //   const formatNum = numeral(value).format('0,0.00');
            //   return formatNum;
            // }}
            formatter={(value) => `${value}`.replace(/\B(?=(\d{3})+(?!\d))/g, ',')}
            parser={(value) => value!.replace(/\$\s?|(,*)/g, '')}
            step="0.01"
          />
        );
      },
      width: 80,
    },
    {
      title: (
        <FormattedMessage
          id="category.material-management.conversion_factor"
          defaultMessage="unknown"
        />
      ),
      editable: false,
      dataIndex: 'chosen_conversion_factor',
      width: 80,
    },
    {
      title: <FormattedMessage id="category.material-management.unit" defaultMessage="unknown" />,
      dataIndex: 'key',
      editable: false,
      render: (index, record, _, action) => [
        <ProFormSelect
          formItemProps={{
            style: {
              marginBottom: 0,
            },
          }}
          key={'quantity_type'}
          fieldProps={{
            defaultValue: record.quantity_type,
          }}
          placeholder="Loại đếm"
          request={async () => {
            const res =
              (await listAllEquivalentUnitOfCategory({
                category_id: record.category_entity || '',
              })) || [];
            const formatted_res = res.map((item) => ({
              label: item.equivalent_unit_label,
              value: item.equivalent_unit_id,
            }));
            const options = [
              {
                label: record.packing_unit_label,
                value: 'packing',
              },
              {
                label: record.basic_unit_label,
                value: 'basic',
              },
            ];
            return [...formatted_res, ...options];
          }}
          onChange={(e) => {
            handleSelectPackingType(index, e);
          }}
        />,
      ],
      width: 80,
    },

    {
      title: (
        <FormattedMessage id="category.material-management.unit_price" defaultMessage="unknown" />
      ),
      dataIndex: 'unit_price',
      search: false,
      width: 80,
      renderFormItem(schema, config, form, action) {
        return (
          <InputNumber
            style={{ width: '100%' }}
            formatter={(value) => {
              const formatNum = numeral(value).format('0,0.00');
              return formatNum;
            }}
          />
        );
      },
    },
    {
      title: (
        <FormattedMessage
          id="storage-management.category-management.total_price"
          defaultMessage=""
        />
      ),
      dataIndex: 'total_price',
      editable: false,
      render: (text, record) => numeral(record.total_price).format('0,0.00'), // Định dạng số ở đây
      width: 80,
    },
    {
      title: 'Thao tác',
      dataIndex: 'key',
      editable: false,
      render: (index, record, _, action) => [
        // <Button
        //   icon={<EditOutlined />}
        //   key={'edit'}
        //   onClick={() => {
        //     action?.startEditable?.(record.key);
        //   }}
        // ></Button>,
        <Button icon={<DeleteOutlined />} key="delete" onClick={() => handleDeleteItem(index)} />,
      ],
      width: 80,
    },
  ];

  useEffect(() => {
    const fetchData = async () => {
      const res = await listAllEquivalentUnit();
      setEquivalentUnits(res || []);
    };
    fetchData();
  }, []);
  return (
    <EditableProTable<ITransactionItem>
      style={{ minWidth: '100%' }}
      columns={columns}
      actionRef={actionRef}
      cardBordered
      request={async (params = {}, sort, filter) => {
        return {
          data,
          success: true,
          total: data.length,
        };
      }}
      editable={{
        type: 'multiple',
        editableKeys,
        actionRender: (row, config, defaultDoms) => {
          return [defaultDoms.delete];
        },
        onValuesChange: (record, recordList) => {
          updateQuantity(record);

          setData(recordList);
        },
        onChange: setEditableRowKeys,
      }}
      recordCreatorProps={false}
      // columnsState={{
      //   persistenceKey: 'pro-table-singe-demos',
      //   persistenceType: 'localStorage',
      // }}
      rowKey="key"
      search={false}
      options={{
        setting: {
          listsHeight: 400,
        },
      }}
      pagination={{
        pageSizeOptions: [10, 20, 50, 100],
        showSizeChanger: true,
      }}
      toolbar={{
        multipleLine: false,
        // search: {
        //   onSearch: (value: string) => {
        //     alert(value);
        //   },
        // },
      }}
      toolBarRender={false}
      size="small"
      dateFormatter="string"
    />
  );
};

export default ItemTransactionTable;
