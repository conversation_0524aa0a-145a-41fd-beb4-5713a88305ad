import { listAllItem } from '@/services/products';
import { listAllUnit } from '@/services/unit';
import { useModel } from '@umijs/max';
import jwt_decode from 'jwt-decode';
import { useEffect, useState } from 'react';

export default () => {
  const [myCategories, setMyCategories] = useState<any[]>([]);
  const [loadingCategory, setLoadingCategory] = useState<boolean>(false);
  const { initialState } = useModel('@@initialState');

  const fetchAllCategory = async () => {
    try {
      setLoadingCategory(true);
      const data = await listAllItem();
      const unitRes = await listAllUnit();
      const units = unitRes.data || [];

      setMyCategories(
        data.data?.map((d) => {
          d.unit = units?.find((el) => el.name === d.unit_id);
          return d;
        }) || [],
      );
    } catch (error: any) {
      // message.error(error?.toString());
    } finally {
      setLoadingCategory(false);
    }
  };

  useEffect(() => {
    if (initialState?.currentUser) {
      //get token decode from localstorage
      const token = localStorage.getItem('token');
      const decodedToken: any = token ? jwt_decode(token) : null;

      const sections = decodedToken ? decodedToken.sections : 'NULL';
      // console.log('sections', sections);
      if (sections && !(sections.includes('CATEGORY') || sections.includes('SYSTEM_ADMIN'))) return;

      fetchAllCategory();
    }
  }, [initialState]);

  return {
    myCategories,
    fetchAllCategory,
    loadingCategory,
  };
};
