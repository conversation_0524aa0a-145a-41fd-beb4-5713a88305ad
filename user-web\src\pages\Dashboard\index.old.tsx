import { sscript } from '@/services/sscript';
import { PageContainer } from '@ant-design/pro-components';
import { useModel } from '@umijs/max';
import { But<PERSON>, Card, Col, ConfigProvider, DatePicker, Input, message, Result, Row, Select, Spin, Tag } from 'antd';
import React, { useEffect, useState } from 'react';
import RenderDeviceFncDashboard from './components/RenderDeviceFncDashboard';
import SelectDevice from './components/SelectDevice';

const timeTypeOption = [
    {
        value: "5p-now",
        label: "5 phút trước - hiện tại"
    },
    {
        value: "1h-now",
        label: "1h trước - hiện tại"
    },
    {
        value: "2h-now",
        label: "2h trước - hiện tại"
    },
    {
        value: "1d-now",
        label: "Ngày hôm trước - hiện tại"
    },
    {
        value: "custom",
        label: "Tuỳ chỉnh"
    }
];

const Dashboard: React.FC = () => {
    const [loading, setLoading] = useState<boolean>(false);
    const [deviceOptions, setdeviceOptions] = useState<any>([]);
    const [projectOptions, setProjectOptions] = useState<any>([]);
    const [zoneOptions, setZoneOptions] = useState<any>([]);
    const [projectSelected, setProjectSelected] = useState<any>("all");
    const [zoneSelected, setZoneSelected] = useState<any>("all");

    const {
        myProject,
        myZone,
        myDevice,
        selectedProject,
        setSelectedProject,
        selectedZone,
        selectedDevice,
        setSelectedZone,
        setSelectedDevice,
        timeRange,
        setTimeRange,
        selectedTimeType,
        setSelectedTimeType
    } = useModel('MyResource');

    console.log({ myProject, myZone, myDevice });

    const { initialState, setInitialState } = useModel('@@initialState');

    useEffect(() => {

        if (myProject?.length) {
            let projectOptions = [{
                value: "all",
                label: "Tất cả"
            }];
            myProject?.map((d: any) => {
                projectOptions.push({
                    value: d.name,
                    label: d.label
                });
            });
            setProjectOptions(projectOptions);
        }

        if (myZone?.length) {
            let myZoneOptions = [{
                value: "all",
                label: "Tất cả"
            }];
            myZone?.map((d: any) => {
                myZoneOptions.push({
                    value: d.name,
                    label: d.label
                });
            });
            setZoneOptions(myZoneOptions);
        }

        if (myDevice?.length) {
            setdeviceOptions(myDevice?.map((device: any) => {
                return {
                    value: device.name,
                    label: device.label
                }
            }));
        }
    }, [myDevice, myProject, myZone]);

    useEffect(() => {
        setInitialState({ ...initialState, collapsed: true });
    }, []);


    return (
        <ConfigProvider>
            <SelectDevice/>
        </ConfigProvider >
    );
};

export default Dashboard;
