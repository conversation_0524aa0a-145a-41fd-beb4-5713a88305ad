# Thay đổi: Thêm active_uom và active_conversion_factor vào request update

## Tóm tắt
Đã thêm khả năng gửi `active_uom` và `active_conversion_factor` trong request update để theo dõi đơn vị đo lường hiện tại và hệ số chuyển đổi hiện tại của item.

## Các file đã thay đổi

### 1. `src/types/IIotWarehouseItemTaskUsed.ts`
**Thay đổi:** Thêm 2 trường mới vào interface
```typescript
export class IIotWarehouseItemTaskUsed {
  // ... existing fields ...
  active_uom?: string; // Active UOM ID
  active_conversion_factor?: number; // Active conversion factor
  // ... existing fields ...
}
```

### 2. `src/components/Task/TaskItemUsed/ItemUsedTableUpdateView.tsx`

#### a. Hàm `fetchTaskItemUsed` (dòng 58-76)
**Thay đổi:** Khởi tạo active fields từ dữ liệu hiện có
```typescript
return {
  ...d,
  // ... existing fields ...
  active_uom: d.uom_id || d.active_uom,
  active_conversion_factor: d.conversion_factor || d.active_conversion_factor,
};
```

#### b. Hàm `handleUOMChange` (dòng 182-194)
**Thay đổi:** Cập nhật active fields khi người dùng thay đổi UOM
```typescript
const updatedRecord = {
  ...record,
  // ... existing fields ...
  active_uom: selectedUOM.uom_id,
  active_conversion_factor: newConversionFactor,
  // ... existing fields ...
};
```

#### c. Hàm `handleSaveItems` (dòng 94-118)
**Thay đổi:** Gửi active fields trong request update
```typescript
const updateTaskItemList: IIotWarehouseItemTaskUsed[] = dataSource.map((data: any) => ({
  // ... existing fields ...
  active_uom: data.uom_id,
  active_conversion_factor: data.conversion_factor,
}));
```

## Luồng hoạt động

1. **Khởi tạo dữ liệu:** Khi load dữ liệu từ server, `fetchTaskItemUsed` sẽ khởi tạo `active_uom` và `active_conversion_factor` từ `uom_id` và `conversion_factor` hiện có.

2. **Thay đổi UOM:** Khi người dùng thay đổi đơn vị đo lường, `handleUOMChange` sẽ cập nhật cả `uom_id`, `conversion_factor` và các active fields tương ứng.

3. **Lưu dữ liệu:** Khi người dùng nhấn Save, `handleSaveItems` sẽ gửi request bao gồm `active_uom` và `active_conversion_factor`.

## Lợi ích

- **Theo dõi trạng thái:** Server có thể biết được đơn vị đo lường và hệ số chuyển đổi mà người dùng đang sử dụng.
- **Tính nhất quán:** Đảm bảo dữ liệu được lưu với đúng context về đơn vị đo lường.
- **Backward compatibility:** Các trường mới là optional, không ảnh hưởng đến code hiện có.

## Kiểm tra

Để kiểm tra tính năng:
1. Mở component `RelatedSuppliesTable`
2. Thay đổi đơn vị đo lường của một item
3. Nhấn Save
4. Kiểm tra request được gửi đi có chứa `active_uom` và `active_conversion_factor`

## Demo

Xem file `src/components/Task/TaskItemUsed/demo/ActiveUOMDemo.tsx` để hiểu rõ hơn về cách thức hoạt động.
