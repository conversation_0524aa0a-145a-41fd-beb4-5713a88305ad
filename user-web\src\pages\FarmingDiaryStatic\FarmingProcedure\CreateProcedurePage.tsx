import { PageContainer } from '@ant-design/pro-components';
import { FC, ReactNode } from 'react';
import CreateProcedure from './components/Create';

interface CreateProcedurePageProps {
  children?: ReactNode;
}

const CreateProcedurePage: FC<CreateProcedurePageProps> = ({ children }) => {
  return (
    <PageContainer>
      <CreateProcedure />
    </PageContainer>
  );
};

export default CreateProcedurePage;
