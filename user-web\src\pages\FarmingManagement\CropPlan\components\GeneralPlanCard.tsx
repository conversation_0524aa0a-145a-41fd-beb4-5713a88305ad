import { DEFAULT_FALLBACK_IMG } from '@/common/contanst/img';
import { formatDateDefault } from '@/utils/date';
import { genDownloadUrl } from '@/utils/file';
import { EditOutlined } from '@ant-design/icons';
import { history, useAccess } from '@umijs/max';
import { Avatar, Card, Space, Typography } from 'antd';
import Meta from 'antd/es/card/Meta';
const { Text } = Typography;

interface Props {
  label?: string;
  image?: string;
  name: string;
  start_date?: string;
  end_date?: string;
  crop_name?: string;
}
const GeneralPlanCard = ({ label, image, name, start_date, end_date, crop_name }: Props) => {
  function handleDelete(): void {
    throw new Error('Function not implemented.');
  }
  const access = useAccess();
  return (
    <Card
      onClick={() => {
        history.push(`/farming-management/crop-management-plan/detail/${name}`);
      }}
      hoverable
      actions={[
        <EditOutlined key="edit" />,
        // <Fragment key="delete">
        //   {access.canDeleteAllInPageAccess() && (
        //     <Popconfirm
        //       title="Xoá kế hoạch"
        //       description={`Bạn có muốn xoá kế hoạch ${label}?`}
        //       onConfirm={() => handleDelete()}
        //       onPopupClick={(e) => {
        //         e.stopPropagation();
        //       }}
        //     >
        //       <DeleteOutlined
        //         key="delete"
        //         onClick={(e) => {
        //           e.stopPropagation();
        //         }}
        //       />
        //     </Popconfirm>
        //   )}
        // </Fragment>,
      ]}
    >
      <Meta
        avatar={
          <Avatar
            shape="square"
            size={64}
            src={image ? genDownloadUrl(image) : DEFAULT_FALLBACK_IMG}
          />
        }
        title={<Text style={{ whiteSpace: 'normal' }}>{label}</Text>}
        description={
          <Space direction="vertical">
            <span>{`${formatDateDefault(start_date)} - ${formatDateDefault(end_date)}`}</span>
            <span>{`${crop_name}`}</span>
          </Space>
        }
      ></Meta>
    </Card>
  );
};

export default GeneralPlanCard;
