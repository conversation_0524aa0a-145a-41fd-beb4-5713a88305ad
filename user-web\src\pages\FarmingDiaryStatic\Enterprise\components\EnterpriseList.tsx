import { DOCTYPE_ERP } from '@/common/contanst/constanst';
import { Business, getBusinessList } from '@/services/diary-2/business';
import { getParamsReqTable } from '@/services/utils';
import { PlusOutlined } from '@ant-design/icons';
import { ActionType, ProColumns, ProTable } from '@ant-design/pro-components';
import { Link, useIntl } from '@umijs/max';
import { Button, Tooltip } from 'antd';
import { FC, ReactNode, useRef } from 'react';
import DeleteBusiness from './DeleteBusiness';

interface EnterpriseListProps {
  children?: ReactNode;
}

const EnterpriseList: FC<EnterpriseListProps> = ({ children }) => {
  const actionRef = useRef<ActionType>();
  const { formatMessage } = useIntl();

  const handleReload = () => {
    actionRef.current?.reload();
  };

  const columns: ProColumns<Business>[] = [
    {
      title: 'STT',
      valueType: 'index',
      width: 50,
      hideInSearch: true,
    },
    {
      title: formatMessage({ id: 'common.business_code' }),
      dataIndex: 'business_code',
      render: (dom, entity) => (
        <Link to={`/farming-diary-static/enterprise/edit/${entity.name}`}>
          <Button type="link">{dom}</Button>
        </Link>
      ),
      width: 150,
      fixed: 'left',
    },
    {
      title: formatMessage({ id: 'common.business_name' }),
      dataIndex: 'label',
      width: 200,
    },
    {
      title: formatMessage({ id: 'common.number_phone' }),
      dataIndex: 'phone',
      width: 150,
      hideInSearch: true,
    },
    {
      title: 'Email',
      dataIndex: 'email',
      width: 200,
    },
    {
      title: formatMessage({ id: 'common.detailed_address' }),
      dataIndex: 'address',
      width: 250,
      hideInSearch: true,
    },
    {
      title: formatMessage({ id: 'common.web_link' }),
      dataIndex: 'link',
      render: (dom, entity) => {
        const link = entity.link || '';
        return (
          <Tooltip title={link}>
            <span>{link.length > 30 ? `${link.substring(0, 30)}...` : link}</span>
          </Tooltip>
        );
      },
      width: 200,
      hideInSearch: true,
    },
    {
      render: (_, entity) => <DeleteBusiness onSuccess={handleReload} id={entity.name} />,
      width: 100,
      hideInSearch: true,
    },
  ];

  const request = async (params: any, sort: any, filter: any) => {
    const paramsReq = getParamsReqTable({
      doc_name: DOCTYPE_ERP.iotDiaryV2Business,
      tableReqParams: { params, sort, filter },
    });
    const response = await getBusinessList(paramsReq);
    return {
      data: response.data,
      total: response.pagination.totalElements,
    };
  };

  return (
    <ProTable<Business>
      actionRef={actionRef}
      form={{ labelWidth: 'auto' }}
      headerTitle={formatMessage({ id: 'common.business_list' })}
      columns={columns}
      scroll={{ x: 'max-content' }}
      toolBarRender={() => [
        <Link to="/farming-diary-static/enterprise/create" key="create">
          <Button icon={<PlusOutlined />} type="primary">
            {formatMessage({ id: 'common.add_new_business' })}
          </Button>
        </Link>,
      ]}
      request={request}
    />
  );
};

export default EnterpriseList;
