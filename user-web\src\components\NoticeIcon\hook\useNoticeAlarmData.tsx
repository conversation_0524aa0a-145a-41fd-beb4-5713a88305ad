import { getWebNotificationList, NoticeDataRes } from '@/services/web-notification';
import { useModel, useRequest } from '@umijs/max';
import { Avatar } from 'antd';
import { startTransition, useEffect, useState } from 'react';
import { useCountAlarmPending } from './useCountAlarmPending';

{
  /* <Avatar
          src={isWarning ? '/images/new-dashboard/bell.png' : '/images/new-dashboard/calendar.png'}
          shape="square"
        /> */
}
export const formatAlarmToNotice = (alarm: NoticeDataRes): API.NoticeIconItem<NoticeDataRes> => ({
  id: alarm.name,
  key: alarm.name,
  title: alarm.message,
  read: !!alarm.is_read,
  type: alarm.type,
  datetime: alarm.creation,
  // avatar: (<WarningTwoTone twoToneColor="#faad14" />) as any,
  avatar: (<Avatar src={'/images/new-dashboard/bell.png'} shape="square" />) as any,
  status: alarm.is_read ? 'success' : 'warning',
  data: alarm,
});

export const useNoticeAlarmData = () => {
  const [alarmPendingCount, setAlarmPendingCount] = useState(0);
  const { run: getCountAlarmPending } = useCountAlarmPending({
    onSuccess: (data) => {
      setAlarmPendingCount(data);
    },
  });

  const [alarmNotices, setAlarmNotices] = useState<API.NoticeIconItem<NoticeDataRes>[]>([]);
  const {
    data: alarmRes,
    loading: isLoadingAlarmNotice,
    run: loadAlarm,
  } = useRequest(
    ({ page, size } = { page: 1, size: 20 }): Promise<any> =>
      getWebNotificationList({
        page,
        size: size || 20,
      }),
    {
      formatResult(res) {
        return res;
      },
      onSuccess({ data }: { data: NoticeDataRes[] }, params) {
        const newData: API.NoticeIconItem[] = data.map(formatAlarmToNotice);
        setAlarmNotices((prev) => [...prev, ...newData]);
      },
    },
  );

  const mqttModel = useModel('MQTTNotification');

  useEffect(() => {
    const sub = mqttModel?.handleMessage.noticeHandle.receive.on((message) => {
      startTransition(() => {
        if (message.is_read) {
          const newAlarmNotice = formatAlarmToNotice(message);
          setAlarmNotices((prev) =>
            prev.map((item) => (item.id === newAlarmNotice.id ? newAlarmNotice : item)),
          );
          setAlarmPendingCount((prev) => prev - 1);
        } else {
          const newAlarmNotice = formatAlarmToNotice(message);
          setAlarmNotices((prev) => [newAlarmNotice, ...prev]);
          setAlarmPendingCount((prev) => prev + 1);
        }
      });
    });
    return () => {
      sub?.removeSubscribe();
    };
  }, [mqttModel?.mqttClient?.connected]);

  useEffect(() => {
    const sub = mqttModel?.handleMessage.noticeHandleReadAll.receive.on((message) => {
      startTransition(() => {
        setAlarmNotices((prev) =>
          prev.map((item) => ({
            ...item,
            read: true,
          })),
        );
        setAlarmPendingCount(0);
      });
    });
    return () => {
      sub?.removeSubscribe();
    };
  }, [mqttModel?.mqttClient?.connected]);

  return {
    alarmNotices,
    setAlarmNotices,
    isLoadingAlarmNotice,
    loadAlarm,
    alarmRes,
    alarmPendingCount,
  };
};
