import { useParams, useSearchParams } from '@umijs/max';

const tabActiveKey = 'tab';
const tabSecondActiveKey = 'tab2';
export default function useParamsUrl() {
  const [searchParams, setSearchParams] = useSearchParams();
  const { id: detailId } = useParams();
  const tabActive = searchParams.get(tabActiveKey);
  const setTabActive = (tabActive: string | number) => {
    searchParams.set(tabActiveKey, tabActive?.toString());
    setSearchParams(searchParams);
  };
  const tabSecondActive = searchParams.get(tabSecondActiveKey);
  const setTabSecondActive = (tabActive: string | number) => {
    searchParams.set(tabSecondActiveKey, tabActive?.toString());
    setSearchParams(searchParams);
  };
  return {
    tabActive,
    setTabActive,
    tabSecondActive,
    setTabSecondActive,
    detailId,
  };
}
