import { DEFAULT_DATE_FORMAT_WITHOUT_TIME } from '@/common/contanst/constanst';
import { useProFormList } from '@/components/Form/Config/pro-form-list';
import FormUploadFiles from '@/components/UploadFIles';
import { getDocumentList } from '@/services/diary-2/document';
import { PlusOutlined } from '@ant-design/icons';
import {
  ProForm,
  ProFormDatePicker,
  ProFormGroup,
  ProFormList,
  ProFormSelect,
} from '@ant-design/pro-components';
import { history, useIntl, useRequest } from '@umijs/max';
import { But<PERSON>, Card } from 'antd';
import moment from 'moment';
import { FC, ReactNode, useCallback, useState } from 'react';

interface CertificationFormProps {
  children?: ReactNode;
}

const CertificationContent = ({ index }: { index: number }) => {
  const { formatMessage } = useIntl();
  const { data: documents, loading: documentsLoading } = useRequest(getDocumentList);
  const form = ProForm.useFormInstance();
  const [fileValues, setFileValues] = useState<string | undefined>();

  const handleDocumentChange = useCallback(
    (selectedDocumentName: string) => {
      const selectedDocument = documents?.find((doc) => doc.name === selectedDocumentName);
      if (!selectedDocument) return;

      setFileValues(selectedDocument.document_path);

      const currentDocuments = form.getFieldValue('documents') || [];
      const updatedDocuments = currentDocuments.map((doc: any, docIndex: number) =>
        doc.name === selectedDocumentName
          ? {
              ...doc,
              idx: docIndex + 1, // Bổ sung index vào mỗi document
              issue_date: selectedDocument.issue_date,
              expiry_date: selectedDocument.expiry_date,
            }
          : doc,
      );
      form.setFieldsValue({ documents: updatedDocuments });
    },
    [documents, form],
  );

  return (
    <ProFormGroup>
      <ProFormSelect
        width="md"
        rules={[{ required: true }]}
        onChange={handleDocumentChange}
        fieldProps={{ loading: documentsLoading }}
        options={documents?.map((doc) => ({
          label: doc.label,
          value: doc.name,
        }))}
        name="name"
        label={`${index + 1}. ${formatMessage({
          id: 'common.certification',
        })}`}
        showSearch
      />
      <ProFormDatePicker
        disabled
        label={formatMessage({
          id: 'common.certification_date',
        })}
        name="issue_date"
        width="sm"
        fieldProps={{
          format: (value: any) => moment(value).format(DEFAULT_DATE_FORMAT_WITHOUT_TIME),
        }}
      />
      <ProFormDatePicker
        disabled
        label={formatMessage({
          id: 'common.expiration_date',
        })}
        name="expiry_date"
        width="sm"
        fieldProps={{
          format: (value: any) => moment(value).format(DEFAULT_DATE_FORMAT_WITHOUT_TIME),
        }}
      />
      <FormUploadFiles
        label={formatMessage({
          id: 'common.docs',
        })}
        fileLimit={10}
        formItemName={[]}
        isReadonly
        initialImages={fileValues}
        showUploadButton={false}
      />
    </ProFormGroup>
  );
};

const CertificationForm: FC<CertificationFormProps> = () => {
  const { formatMessage } = useIntl();
  const formListProps = useProFormList();

  const renderCreateCertificationButton = () => (
    <Button
      onClick={() =>
        history.replace('/farming-diary-static/certification/create', {
          fromProcedureCreate: true,
        })
      }
      icon={<PlusOutlined />}
      type="default"
    >
      {formatMessage({ id: 'common.create-ceritification' })}
    </Button>
  );

  return (
    <Card
      title={formatMessage({ id: 'common.certification' })}
      extra={renderCreateCertificationButton()}
    >
      <ProFormList name="documents" {...formListProps}>
        {({ key }) => <CertificationContent index={key} />}
      </ProFormList>
    </Card>
  );
};

export default CertificationForm;
