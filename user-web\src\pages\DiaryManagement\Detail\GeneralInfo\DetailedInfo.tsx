import { DEFAULT_DATE_FORMAT_WITHOUT_TIME } from '@/common/contanst/constanst';
import ImagePreviewGroupCommon from '@/components/ImagePreviewGroupCommon';
import { getFullImgUrlArrString } from '@/utils/image';
import { CameraFilled } from '@ant-design/icons';
import {
  ProFormDateRangePicker,
  ProFormDigit,
  ProFormGroup,
  ProFormInstance,
  ProFormSelect,
  ProFormText,
  ProFormTextArea,
  ProFormUploadButton,
} from '@ant-design/pro-components';
import { useAccess, useIntl } from '@umijs/max';
import { Button, Card, Col, Row, Space, Typography } from 'antd';
import { FC, ReactNode, useRef } from 'react';
import CreateTracingFromCrop from './components/CreateTracingFromCrop';
const { Text } = Typography;

interface DetailedInfoProps {
  children?: ReactNode;
  cropId: string;
  form: ProFormInstance;
  callback: () => void;
}

const DetailedInfo: FC<DetailedInfoProps> = ({ children, cropId, form, callback }) => {
  const componentRef = useRef(null);
  const intl = useIntl();
  const access = useAccess();

  return (
    <>
      <Space direction="vertical" size={'small'}>
        <Card
          title={intl.formatMessage({ id: 'seasonalTab.detailInformation' })}
          extra={
            <>
              <Space direction="horizontal">
                <CreateTracingFromCrop
                  crop_id={cropId}
                  refreshFnc={() => {}}
                ></CreateTracingFromCrop>

                {/* <Button
                  size="middle"
                  type="primary"
                  key={'save'}
                  onClick={(e) => {
                    callback();
                  }}
                >
                  {intl.formatMessage({ id: 'common.save' })}
                </Button> */}
              </Space>
            </>
          }
        >
          <Row gutter={10}>
            <Col md={3} sm={24}>
              <Space align="center" direction="vertical" size={'small'}>
                <ProFormUploadButton
                  label={intl.formatMessage({ id: 'common.form.avatar' })}
                  accept="image/*"
                  listType="picture-card"
                  icon={<CameraFilled />}
                  title=""
                  name="avatar"
                  max={1} // Set maximum files to 1
                  style={{
                    width: 80,
                  }}
                  disabled
                  fieldProps={{
                    beforeUpload: () => false, // Add this line
                  }}
                />
                {/* <Button type="link"> */}
                {/* <QRCodeModal form={form} cropId={cropId} /> */}
                {/* <QRCode
                size={50}
                value={`crop,${cropId}`}
                bgColor="#fff"
                style={{ marginBottom: 16 }}
              /> */}
                {/* </Button> */}
              </Space>
            </Col>
            <Col md={14} sm={24}>
              <ProFormGroup>
                <ProFormText
                  label={intl.formatMessage({ id: 'seasonalTab.seasonName' })}
                  rules={[
                    {
                      required: true,
                    },
                  ]}
                  name="label"
                  disabled
                  colProps={{ span: 12 }}
                />
                <ProFormSelect
                  label={intl.formatMessage({ id: 'common.area' })}
                  rules={[
                    {
                      required: true,
                    },
                  ]}
                  name="zone_name"
                  disabled
                  colProps={{ span: 12 }}
                />
              </ProFormGroup>
              <ProFormGroup>
                <ProFormDateRangePicker
                  label={intl.formatMessage({ id: 'seasonalTab.time_completed' })}
                  rules={[
                    {
                      required: true,
                    },
                  ]}
                  name="date_range"
                  fieldProps={{
                    format: DEFAULT_DATE_FORMAT_WITHOUT_TIME,
                  }}
                  disabled
                  colProps={{ span: 12 }}
                />
                <ProFormDigit
                  label={intl.formatMessage({ id: 'seasonalTab.cultivation_area' })}
                  min={0}
                  name="square"
                  disabled
                  colProps={{ span: 12 }}
                />
              </ProFormGroup>
              <ProFormGroup>
                <ProFormSelect
                  label={intl.formatMessage({ id: 'seasonalTab.selectTypeOfPlant' })}
                  disabled
                  name="plant_name"
                  colProps={{ span: 12 }}
                />
                <ProFormDigit
                  label={intl.formatMessage({ id: 'seasonalTab.expectedOutputInKg' })}
                  min={0}
                  name="quantity_estimate"
                  disabled
                  colProps={{ span: 12 }}
                />
              </ProFormGroup>
            </Col>
            <Col md={7} sm={24}>
              <ProFormSelect
                rules={[
                  {
                    required: true,
                  },
                ]}
                label={intl.formatMessage({ id: 'common.status' })}
                name="status"
                options={[
                  {
                    label: 'Đang diễn ra',
                    value: 'In progress',
                  },
                  {
                    label: 'Hoàn tất',
                    value: 'Done',
                  },
                ]}
                initialValue={'Plan'}
                disabled
                colProps={{ span: 24 }}
              />
              <ProFormTextArea
                label={intl.formatMessage({ id: 'common.note' })}
                name="description"
                disabled
                autoSize={{ minRows: 1, maxRows: 1 }}
                colProps={{ span: 24 }}
              />
            </Col>
          </Row>
          <Row gutter={10}>
            <Col md={3} sm={24}>
              <Space align="center" direction="vertical" size={'small'}>
                <ProFormUploadButton
                  label={intl.formatMessage({ id: 'common.business_image' })}
                  accept="image/*"
                  listType="picture-card"
                  icon={<CameraFilled />}
                  title=""
                  name="business_avatar"
                  max={1} // Set maximum files to 1
                  style={{
                    width: 80,
                  }}
                  fieldProps={{
                    beforeUpload: () => false, // Add this line
                  }}
                  disabled
                />
                {/* <Button type="link"> */}
                {/* <QRCodeModal form={form} cropId={cropId} /> */}
                {/* <QRCode
                size={50}
                value={`crop,${cropId}`}
                bgColor="#fff"
                style={{ marginBottom: 16 }}
              /> */}
                {/* </Button> */}
              </Space>
            </Col>
            <Col md={14} sm={24}>
              <ProFormGroup>
                <ProFormText
                  disabled
                  label={intl.formatMessage({ id: 'diary.crop_owner' })}
                  // rules={[
                  //   {
                  //     required: true,
                  //   },
                  // ]}
                  name="crop_owner"
                  colProps={{ span: 12 }}
                />
                <ProFormText
                  disabled
                  label={intl.formatMessage({ id: 'diary.business_info' })}
                  // rules={[
                  //   {
                  //     required: true,
                  //   },
                  // ]}
                  name="business_info"
                  colProps={{ span: 12 }}
                />
                <ProFormText
                  disabled
                  label={intl.formatMessage({ id: 'diary.location' })}
                  // rules={[
                  //   {
                  //     required: true,
                  //   },
                  // ]}
                  name="location"
                  colProps={{ span: 12 }}
                />
                <ProFormText
                  disabled
                  label={intl.formatMessage({ id: 'diary.latitude' })}
                  // rules={[
                  //   {
                  //     required: true,
                  //   },
                  // ]}
                  name="latitude"
                  colProps={{ span: 12 }}
                />
                <ProFormText
                  disabled
                  label={intl.formatMessage({ id: 'diary.phone' })}
                  // rules={[
                  //   {
                  //     required: true,
                  //   },
                  // ]}
                  name="phone"
                  colProps={{ span: 12 }}
                />
                <ProFormText
                  disabled
                  label={intl.formatMessage({ id: 'diary.email' })}
                  // rules={[
                  //   {
                  //     required: true,
                  //   },
                  // ]}
                  name="email"
                  colProps={{ span: 12 }}
                />
                <ProFormText
                  disabled
                  label={intl.formatMessage({ id: 'diary.other_link' })}
                  // rules={[
                  //   {
                  //     required: true,
                  //   },
                  // ]}
                  name="other_link"
                  colProps={{ span: 12 }}
                />
              </ProFormGroup>
            </Col>

            <Col md={7} sm={24}>
              <ProFormText
                disabled
                label={intl.formatMessage({ id: 'diary.website' })}
                // rules={[
                //   {
                //     required: true,
                //   },
                // ]}
                name="website"
                colProps={{ span: 24 }}
              />
              <ProFormText
                disabled
                label={intl.formatMessage({ id: 'diary.longitude' })}
                // rules={[
                //   {
                //     required: true,
                //   },
                // ]}
                name="longitude"
                colProps={{ span: 24 }}
              />
              <ProFormTextArea
                disabled
                label={intl.formatMessage({ id: 'diary.short_description' })}
                name="short_description"
                colProps={{ span: 24 }}
              />
            </Col>
          </Row>
        </Card>
        <Card title={intl.formatMessage({ id: 'common.form.image-limit' })}>
          <Row gutter={10}>
            <Col md={24} sm={24}>
              <ImagePreviewGroupCommon
                wrapperStyle={{
                  flexWrap: 'nowrap',
                }}
                listImg={getFullImgUrlArrString(form.getFieldValue('images')).map((item) => ({
                  src: item,
                  previewSrc: item,
                }))}
              />
              <ProFormUploadButton
                accept="image/*"
                listType="picture-card"
                icon={<CameraFilled />}
                title=""
                name="images"
                style={{
                  width: '100%',
                }}
                fieldProps={{
                  beforeUpload: () => false, // Add this line
                }}
              />
              {/* <Button type="link"> */}
              {/* <QRCodeModal form={form} cropId={cropId} /> */}
              {/* <QRCode
                size={50}
                value={`crop,${cropId}`}
                bgColor="#fff"
                style={{ marginBottom: 16 }}
              /> */}
              {/* </Button> */}
            </Col>
          </Row>
        </Card>
      </Space>
    </>
  );
};

export default DetailedInfo;
