import {
  DEFAULT_DATE_FORMAT_WITHOUT_TIME,
  DEFAULT_PAGE_SIZE_ALL,
} from '@/common/contanst/constanst';
import ProFormTagSelect from '@/pages/FarmingManagement/WorkflowManagement/TagManager/ProFormTagSelect';
import { getCustomerUserList } from '@/services/customerUser';
import { removeFile, uploadFile } from '@/services/uploadFile';
import { generateAPIPath } from '@/services/utils';
import { DeleteFilled, LoadingOutlined, PlusOutlined } from '@ant-design/icons';
import {
  ProForm,
  ProFormCheckbox,
  ProFormDateRangePicker,
  ProFormSelect,
  ProFormText,
  ProFormTextArea,
} from '@ant-design/pro-components';
import {
  Button,
  Card,
  Checkbox,
  Col,
  DatePicker,
  Form,
  InputNumber,
  message,
  Row,
  Upload,
  UploadProps,
} from 'antd';
import { FC, ReactNode, useEffect, useState } from 'react';

interface DetailedInfoProps {
  children?: ReactNode;
  onEditTagSuccess?: () => void;
  onFileListChange?: (fileList: any) => void;
  currentPlanParam?: any;
  planStateOptions: any;
  customerUserOptions: any;
}

const DetailedInfo: FC<DetailedInfoProps> = ({
  children,
  onEditTagSuccess,
  currentPlanParam,
  planStateOptions,
  customerUserOptions,
  onFileListChange = () => {},
}) => {
  const [isInterval, setIsInterval] = useState(false);
  const [currentPlan, setCurrentPlan] = useState<any>({});
  const [selectedPlan, setSelectedPlan] = useState('');
  // const [planStateOptions, setPlanStateOptions] = useState<any>([]);
  const [loading, setLoading] = useState(false);
  const [isOpen, setOpen] = useState(false);
  const [imageUrl, setImageUrl] = useState<string>();
  const [uploading, setUploading] = useState(false);
  const [fileList, setFileList] = useState<any[]>([]);
  const form = ProForm.useFormInstance();
  //debug currentPlan
  useEffect(() => {
    // console.log('currentPlan in detail', currentPlan);
  }, [currentPlan]);

  useEffect(() => {
    // Call the callback function whenever fileList changes
    onFileListChange(fileList);
  }, [fileList]);

  const uploadButton = (
    <div>
      {uploading ? <LoadingOutlined /> : <PlusOutlined />}
      <div style={{ marginTop: 8 }}>Upload</div>
    </div>
  );

  const handleRemove = async (file: any) => {
    if (file.status === 'done') {
      try {
        await removeFile({
          fid: file.uid,
          dt: '',
          dn: '',
        });
      } catch (error) {
        message.error('Delete Error,try again!');
      } finally {
      }
    }
  };

  const handleChangeUpload: UploadProps['onChange'] = (info) => {
    let newFileList = [...info.fileList];
    // Only to show two recent uploaded files, and old ones will be replaced by the new
    // 1. Limit the number of uploaded files
    // 2. Read from response and show file link
    newFileList = newFileList.map((file: any) => {
      if (file.response) {
        // Component will show file.url as link
        file.uid = file.response.name;
        const userdata = JSON.parse(localStorage.getItem('token') || '{}');
        file.url = generateAPIPath(
          'api/v2/file/download?file_url=' + file.response.file_url + '&token=' + userdata?.token,
        );
        file.raw_url = file.response.file_url;
      }
      return file;
    });
    setFileList(newFileList);
    if (newFileList.length) {
      setImageUrl(newFileList[0]?.url || '');
    }
  };

  const handleUpload = async (options: any) => {
    const { onSuccess, onError, file } = options;
    try {
      setUploading(true);
      const res = await uploadFile({
        file,
        doctype: '',
        docname: '',
        is_private: 1,
        folder: 'Home/Attachments',
        optimize: false,
      });
      // console.log('file_url', res);
      onSuccess(res.message);
    } catch (err) {
      // console.log('Eroor: ', err);
      onError({ err });
    } finally {
      setUploading(false);
    }
  };

  //set currentPlan from currentPlanParam
  useEffect(() => {
    form.setFieldValue('farming_plan', currentPlanParam);
    setSelectedPlan(currentPlanParam.name);
    // setCurrentPlan({ label: currentPlanParam.label, value: currentPlanParam.name });
  }, [currentPlanParam]);

  // useEffect(() => {
  //   const fetchData = async () => {
  //     try {
  //       //only get plan state if selectedPlan is not undefined
  //       if (!selectedPlan) return;
  //       const res = await getFarmingPlanState({
  //         page: 1,
  //         size: DEFAULT_PAGE_SIZE_ALL,
  //         filters: [['iot_farming_plan_state', 'farming_plan', 'like', selectedPlan]],
  //       });
  //       setPlanStateOptions(
  //         res.data.map((item) => {
  //           return {
  //             label: item.label,
  //             value: item.name,
  //           };
  //         }),
  //       );
  //     } catch (error: any) {
  //       message.error(error.toString());
  //     }
  //   };
  //   fetchData();
  //   if (currentPlanParam) {
  //     setCurrentPlan(currentPlanParam);
  //   }
  // }, [selectedPlan]);

  return (
    <Row gutter={[5, 5]}>
      <Col md={24}>
        <Card title="Thông tin chi tiết">
          <Row gutter={[5, 5]}>
            <Col className="gutter-row" md={24}>
              <Upload
                name="upload-image"
                listType="picture-card"
                className="avatar-uploader"
                showUploadList={false}
                customRequest={handleUpload}
                onRemove={handleRemove}
                onChange={handleChangeUpload}
                accept="image/png, image/jpeg, image/svg+xml"
              >
                {imageUrl ? (
                  <img src={imageUrl} alt="avatar" style={{ width: '100%' }} />
                ) : (
                  uploadButton
                )}
              </Upload>
              {imageUrl ? (
                <>
                  <Button
                    onClick={() => {
                      setFileList([]);
                      setImageUrl('');
                    }}
                  >
                    <DeleteFilled></DeleteFilled>
                  </Button>
                </>
              ) : (
                <></>
              )}
            </Col>

            <Col md={24}>
              <ProFormText
                label={'Tên công việc'}
                rules={[
                  {
                    required: true,
                  },
                ]}
                name="label"
              />
            </Col>

            {/* <Col md={8}>
              <ProFormSelect
                name="farming_plan"
                label="Chọn kế hoạch"
                onChange={(v: any) => {
                  setSelectedPlan(v);
                }}
                showSearch
                request={async () => {
                  const res: any = await getFarmingPlanList({
                    page: 1,
                    size: DEFAULT_PAGE_SIZE_ALL,
                  });
                  return res.data.map((item: any) => ({
                    label: item.label,
                    value: item.name,
                  }));
                }}
              />
            </Col> */}

            <Col md={12}>
              <ProFormSelect
                label="Chọn giai đoạn"
                name="farming_plan_state"
                mode="tags"
                options={planStateOptions}
                disabled={planStateOptions.length === 0}
                onChange={(value: any) => {
                  if (value?.length > 1) {
                    value.shift();
                  }
                }}
              />
            </Col>
            <Col md={12}>
              <ProFormTagSelect onEditTagSuccess={onEditTagSuccess} />
            </Col>

            <Col md={6}>
              <Form.Item
                label="Thời gian thực hiện"
                rules={[
                  {
                    required: true,
                  },
                ]}
                name="start_date"
              >
                <DatePicker showTime format={'HH:mm DD/MM/YYYY'}></DatePicker>
              </Form.Item>
            </Col>
            <Col md={6}>
              <Form.Item
                label="Thời gian kết thúc"
                rules={[
                  {
                    required: true,
                  },
                ]}
                name="end_date"
              >
                <DatePicker showTime format={'HH:mm DD/MM/YYYY'}></DatePicker>
              </Form.Item>
            </Col>
            <Col md={12}>
              <ProFormSelect
                label="Người thực hiện"
                name="assigned_to"
                mode="tags"
                options={customerUserOptions}
                // request={async () => {
                //   const res = await getCustomerUserList({
                //     page: 1,
                //     size: DEFAULT_PAGE_SIZE_ALL,
                //     fields: ['name', 'first_name', 'last_name', 'email'],
                //   });
                //   return res.data.map((item) => ({
                //     label:
                //       item.first_name || item.last_name
                //         ? `${item.first_name || ''} ${item.last_name || ''}`
                //         : `${item.email}`,
                //     value: item.name,
                //   }));
                // }}
                onChange={(value: any) => {
                  if (value?.length > 1) {
                    value.shift();
                  }
                }}
              />
            </Col>

            <Col md={12}>
              <ProFormSelect
                label="Thành viên liên quan (nếu có)"
                name="involved_in_users"
                request={async () => {
                  const res = await getCustomerUserList({
                    page: 1,
                    size: DEFAULT_PAGE_SIZE_ALL,
                    fields: ['name', 'first_name', 'last_name', 'email'],
                  });
                  return res.data.map((item) => ({
                    label:
                      item.first_name || item.last_name
                        ? `${item.first_name || ''} ${item.last_name || ''}`
                        : `${item.email}`,
                    value: item.name,
                  }));
                }}
                mode="multiple"
              />
            </Col>
            <Col md={12}>
              <ProFormSelect
                rules={[
                  {
                    required: true,
                  },
                ]}
                label="Trạng thái"
                name="status"
                options={[
                  {
                    label: 'Lên kế hoạch',
                    value: 'Plan',
                  },
                  {
                    label: 'Đang xử lý',
                    value: 'In progress',
                  },
                  {
                    label: 'Hoàn tất',
                    value: 'Done',
                  },
                  {
                    label: 'Trì hoãn',
                    value: 'Pending',
                  },
                ]}
                initialValue={'Plan'}
              />
            </Col>

            <Col md={3}>
              <Form.Item label="Lặp lại công việc" name="is_interval" valuePropName="checked">
                <Checkbox
                  value={isInterval}
                  onChange={(v) => {
                    setIsInterval(v.target.checked);
                  }}
                ></Checkbox>
              </Form.Item>
            </Col>
            {isInterval && (
              <>
                <Col md={3}>
                  <Form.Item
                    label="Mỗi"
                    name="interval_value"
                    initialValue={1}
                    rules={[
                      {
                        required: true,
                      },
                    ]}
                  >
                    <InputNumber min={1}></InputNumber>
                  </Form.Item>
                </Col>
                <Col md={4}>
                  <ProFormSelect
                    name="interval_type"
                    label="Loại thời gian"
                    options={[
                      {
                        value: 'd',
                        label: 'Ngày',
                      },
                      {
                        value: 'w',
                        label: 'Tuần',
                      },
                      {
                        value: 'M',
                        label: 'Tháng',
                      },
                    ]}
                    rules={[
                      {
                        required: true,
                      },
                    ]}
                  ></ProFormSelect>
                </Col>
                <Col md={12}>
                  <ProFormDateRangePicker
                    label="Thời gian lặp lại"
                    width={'lg'}
                    rules={[
                      {
                        required: true,
                      },
                    ]}
                    fieldProps={{
                      format: DEFAULT_DATE_FORMAT_WITHOUT_TIME,
                    }}
                    name="intervalRange"
                  />
                </Col>
              </>
            )}
            <Col md={24}>
              <ProFormTextArea label="Ghi chú" name="description" />
            </Col>
            <Col md={12}>
              <ProFormCheckbox label="Cho phép truy xuất" name="enable_origin_tracing" />
            </Col>
          </Row>
          {/* <ProFormSelect
          label="Chọn dự án"
          rules={[
            {
              required: true,
            },
          ]}
          width={'lg'}
        />
        <ProFormSelect
          label="Chọn khu vực"
          rules={[
            {
              required: true,
            },
          ]}
          width={'lg'}
        /> */}

          {/* <ProFormSelect
          label="Chọn vụ mùa"
          rules={[
            {
              required: true,
            },
          ]}
          width={'lg'}
        /> */}
        </Card>
      </Col>
    </Row>
  );
};

export default DetailedInfo;
