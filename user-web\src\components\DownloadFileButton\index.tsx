import { getFileUrl } from '@/services/utils';
import { DownloadOutlined } from '@ant-design/icons'; // Import icon download
import { useIntl } from '@umijs/max';
import { Button } from 'antd';
import React from 'react';

interface DownloadButtonProps {
  filePath: string; // Định nghĩa prop cho đường dẫn file
  buttonName: string; // Định nghĩa prop cho tên nút
  disabled?: boolean; // Định nghĩa prop cho trạng thái disabled của nút
  test?: string; // Thêm prop test
}

const DownloadButton: React.FC<DownloadButtonProps> = ({
  filePath,
  buttonName,
  disabled,
  test,
}) => {
  const handleDownload = async () => {
    const fileUrl = getFileUrl({ src: filePath });

    if (fileUrl) {
      const response = await fetch(fileUrl);
      const blob = await response.blob();
      const url = window.URL.createObjectURL(blob);
      const link = document.createElement('a');
      link.href = url;
      link.download = 'Mẫu phiếu kho nhập bằng Excel.xlsx';
      link.click();
      window.URL.revokeObjectURL(url); // Giải phóng bộ nhớ
    }
  };

  const { formatMessage } = useIntl();
  return (
    <Button
      type="default"
      icon={<DownloadOutlined />} // Thêm icon download vào nút
      onClick={handleDownload}
      disabled={disabled}
    >
      {formatMessage({ id: buttonName })}
    </Button>
  );
};

export default DownloadButton;
