import { DEFAULT_FALLBACK_IMG } from '@/common/contanst/img';
import { getFarmingPlan, updateFarmingPlan } from '@/services/farming-plan';
import { formatDateDefault } from '@/utils/date';
import { genDownloadUrl } from '@/utils/file';
import { DeleteOutlined } from '@ant-design/icons';
import { history, useAccess, useIntl } from '@umijs/max';
import { App, Avatar, Button, Card, Descriptions, Skeleton, Space } from 'antd';
import Meta from 'antd/es/card/Meta';
import { useEffect, useState } from 'react';
import { useRequest } from 'umi';
import EditCropPlan from '../../Edit';

interface Props {
  planId: string;
}
const DetailCard = ({ planId }: Props) => {
  const [reRender, setReRender] = useState(false);
  const { run, data, loading, refresh } = useRequest(async () => {
    const response = await getFarmingPlan(planId);
    return response;
  });
  useEffect(() => {
    const fetchData = () => {
      if (planId) run();
    };
    fetchData();
  }, [planId]);
  const intl = useIntl();
  const { modal, message } = App.useApp();
  const onDeletePlan = () => {
    modal.confirm({
      content: 'Are you sure you want to delete this plan?',
      onOk: async () => {
        try {
          // await deletePlan({
          //   name: planId,
          // });
          await updateFarmingPlan({
            name: planId,
            is_deleted: 1,
          });
          message.success({
            content: 'Delete  successfully',
          });
          history.push(`/farming-management/crop-management-plan`);

          // await refresh();
          return true;
        } catch (error) {
          message.error({
            content: 'Delete error, please try again',
          });
          return false;
        }
      },
      okButtonProps: {
        danger: true,
      },
    });
  };
  const access = useAccess();
  const canDeletePlan = access.canDeleteInPlanManagement();
  const canUpdatePlan = access.canUpdateInPlanManagement();
  return (
    <Skeleton loading={loading}>
      <Card
        title={data?.label}
        // extra={
        //   <Space>
        //     {canUpdatePlan && <EditCropPlan key={'edit'} planId={planId} onSuccess={refresh} />}

        //     {canDeletePlan && (
        //       <Button
        //         size="middle"
        //         icon={<DeleteOutlined />}
        //         key={'delete'}
        //         danger
        //         onClick={onDeletePlan}
        //       >
        //         {intl.formatMessage({ id: 'common.delete' })}
        //       </Button>
        //     )}
        //   </Space>
        // }
      >
        <Meta
          avatar={
            <Avatar
              shape="square"
              size={64}
              src={data?.image ? genDownloadUrl(data?.image) : DEFAULT_FALLBACK_IMG}
            />
          }
          description={
            <Descriptions column={1}>
              <Descriptions.Item
                label={intl.formatMessage({ id: 'common.crop' })}
              >{`${data?.crop_name}`}</Descriptions.Item>
              <Descriptions.Item
                label={intl.formatMessage({ id: 'common.time' })}
              >{`${formatDateDefault(data?.start_date)} - ${formatDateDefault(
                data?.end_date,
              )}`}</Descriptions.Item>
            </Descriptions>
          }
        ></Meta>
      </Card>
    </Skeleton>
  );
};

export default DetailCard;
