import { PageContainer } from '@ant-design/pro-components';
import { FC, ReactNode } from 'react';
import CreateTem from './components/Create';

interface CreateProdProcedurePageProps {
  children?: ReactNode;
}

const CreateProdProcedurePage: FC<CreateProdProcedurePageProps> = ({ children }) => {
  return (
    <PageContainer>
      <CreateTem />
    </PageContainer>
  );
};

export default CreateProdProcedurePage;
