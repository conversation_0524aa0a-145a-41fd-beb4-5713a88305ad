export default {
  'tooltips.description': 'Description',
  'tooltips.item_task_exp_quantity_description':
    'Expected quantity of materials used in current task',
  'tooltips.item_task_draft_quantity_description':
    'Quantity of materials in draft state, input on mobile',
  'tooltips.item_task_used_quantity_description':
    'Actual quantity of materials used in current task',
  'tooltips.item_task_crop_total_quantity_description':
    'Total quantity of materials used in the crop season (in all tasks)',
  'tooltips.item_task_issued_quantity_description':
    'Quantity of materials consumed in the current task',
  //add similar lines like above, but change item_task to production_task and material to product
  'tooltips.production_task_exp_quantity_description':
    'Expected quantity of products produced in current task',
  'tooltips.production_task_used_quantity_description':
    'Actual quantity of products produced in current task',
  'tooltips.production_task_finished_quantity_description':
    'Actual quantity of products produced in current task',
  'tooltips.production_task_crop_total_quantity_description':
    'Total quantity of products produced in the crop season (in all tasks)',
  'tooltips.production_task_issued_quantity_description':
    'Quantity of products disposed in the current task',
  'tooltips.production_task_harvested_quantity_description':
    'Quantity of products harvested in the current task',
  'tooltips.add_expected_item_quantity_button': 'Add expected quantity of materials to use',
  'tooltips.add_material_to_crop_button': 'Add actual materials from warehouse to crop to use',
  'tooltips.add_material_from_crop_button': 'Return materials from crop to warehouse',
  'tooltips.issue_material_button':
    'After using, materials disappear from the crop is called consumption',
  'tooltips.add_haverst_quantity_button': 'Add harvested quantity of products',
  //Xuất huỷ sản phẩm
  'tooltips.issue_product_button': 'Drop product',
  //Thêm số lượng thực tế của sản phẩm vào trong vụ mùa
  'tooltips.add_product_to_crop_button': 'Add actual quantity of products to crop',
  //Thêm số lượng sản phẩm dự kiến
  'tooltips.add_expected_product_quantity_button': 'Add expected quantity of products',
  'tooltips.task_item_used_table': `
  <p><strong>Instructions:</strong></p>
  <p>Step 1: Add expected supplies</p>
  <p>Step 2: Enter usage data (directly into the box below or on the phone)</p>
  <p>Step 3: Add usage quantity to bring supplies into the season or reduce usage quantity to remove from the season</p>
  <p>Step 4: Add consumption quantity if the supply is a consumable material (fertilizers, medicines, seeds,...)</p>`,
  'tooltips.task_production_quantity_table': `
  <p><strong>Instructions:</strong></p>
  <p>Step 1: Add expected production quantity</p>
  <p>Step 2: Enter usage data (directly into the box below or on the phone)</p>
  <p>Step 3: Add actual quantity to generate production in the season</p>
  <p>Step 4: Add damaged quantity if needed to cancel production</p>
  <p>Step 5: Add harvested quantity if the product has been harvested</p>`,
  'tooltips.total_exp_quantity': 'Total expected quantity in the crop season',
  'tooltips.total_quantity':
    'Total used quantity in the crop season, equal to total input to crop minus total return to warehouse',
  'tooltips.total_real_quantity':
    'Total actual quantity, equal to total input to crop minus total return to warehouse minus total issue qty',
  'tooltips.total_real_quantity_for_production':
    'Total actual quantity, equal to total qty created in crop minus total harvested minus total issue qty',
  'tooltips.total_issued_quantity': 'Total quantity issued in the crop season',
  'tooltips.total_finished_quantity': 'Total quantity harvested in the crop season',
  'tooltips.total_draft_quantity': 'Total quantity input from app on mobile',
  'tooltips.quick_issue_material_button':
    'Add actual materials from warehouse to crop and get consumed immediately',
  "tooltips.adjust_voucher_amount": "Adjust item value, enter amount",
  "tooltips.adjust_discount": "Adjust discount, enter amount or percentage (of item value)",
  "tooltips.adjust_add_taxes": "Adjust taxes, enter amount or percentage (of subtotal)",
  "tooltips.adjust_other_charges": "Adjust other charges, enter amount",
  "tooltips.adjust_paid_amount": "Adjust paid amount, enter amount"

};
