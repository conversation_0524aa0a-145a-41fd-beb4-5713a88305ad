import { Space } from 'antd';
import { FC } from 'react';
import SeasonalNoteEmpty from './SeasonalNoteEmpty';
import SeasonalNoteInfo, { SeasonalNoteInfoProps } from './SeasonalNoteInfo';

interface SeasonalNoteInfoListProps {
  data?: SeasonalNoteInfoProps['data'][];
  onDeleteSuccess?: (id: string) => void;
}

const SeasonalNoteInfoList: FC<SeasonalNoteInfoListProps> = ({ data, onDeleteSuccess }) => {
  if (!data || data.length === 0) return <SeasonalNoteEmpty />;
  return (
    <Space
      direction="vertical"
      size="large"
      style={{
        width: '100%',
      }}
    >
      {data.map((item, index) => (
        <SeasonalNoteInfo data={item} key={index} onDeleteSuccess={onDeleteSuccess} />
      ))}
    </Space>
  );
};

export default SeasonalNoteInfoList;
