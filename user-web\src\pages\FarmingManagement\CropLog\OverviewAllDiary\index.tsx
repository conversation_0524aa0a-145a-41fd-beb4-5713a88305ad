import { myLazy } from '@/utils/lazy';
import { TableSkeleton } from '@ant-design/pro-components';
import { FC, ReactNode, Suspense } from 'react';

const OverviewAllDiaryTable = myLazy(() => import('@/pages/FarmingManagement/CropLog/OverviewAllDiary/components/OverviewAllDiaryTable'));

interface LogProps {
  children?: ReactNode;
}

const LogOverview: FC<LogProps> = () => {
  return (
    <Suspense fallback={<TableSkeleton active />}>
      <OverviewAllDiaryTable
        genLinkDetail={(itemId) => `/farming-management/crop-log/log/detail/${itemId}`}
        keepSearchParams
      />
    </Suspense>
  );
};

export default LogOverview;
