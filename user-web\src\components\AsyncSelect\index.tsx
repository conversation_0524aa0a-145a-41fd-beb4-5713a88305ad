import { Select, Spin } from 'antd';
import { SelectProps } from 'antd/es/select';
import { LabeledValue } from 'antd/lib/select';
import debounce from 'lodash/debounce';
import { FC, useMemo, useRef, useState } from 'react';

export interface DebounceSelectProps<ValueType = any>
  extends Omit<SelectProps<ValueType>, 'options' | 'children'> {
  fetchOptions: (search: string) => Promise<ValueType[]>;
  debounceTimeout?: number;
  resetOptions?: boolean;
}

function DebounceSelect<
  ValueType extends { key?: string; label: React.ReactNode; value: string | number } = any,
>({ fetchOptions, debounceTimeout = 800, resetOptions = false, ...props }: DebounceSelectProps) {
  const [fetching, setFetching] = useState(false);
  const [options, setOptions] = useState<ValueType[]>([]);
  const fetchRef = useRef(0);

  const debounceFetcher = useMemo(() => {
    const loadOptions = (value: string) => {
      fetchRef.current += 1;
      const fetchId = fetchRef.current;
      setOptions([]);
      setFetching(true);

      fetchOptions(value).then((newOptions) => {
        if (fetchId !== fetchRef.current) {
          // for fetch callback order
          return;
        }

        setOptions(newOptions);
        setFetching(false);
      });
    };

    return debounce(loadOptions, debounceTimeout);
  }, [fetchOptions, debounceTimeout]);

  return (
    <Select<ValueType>
      filterOption={false}
      onSearch={debounceFetcher}
      notFoundContent={fetching ? <Spin size="small" /> : null}
      {...props}
      options={options}
      onFocus={() => {
        if (resetOptions) {
          setOptions([]);
        }
      }}
    />
  );
}

export const convertValueToOption = (item: any) => {
  if (Array.isArray(item)) {
    return item.map((i) => ({
      label: `${i.code} - ${i.name}`,
      value: i.id,
    }));
  }
  return {
    label: `${item.code} - ${item.name}`,
    value: item.id,
  };
};

export const convertOptionToValue = (option: any) => {
  if (Array.isArray(option)) {
    return option.map((i) => ({ id: i.value }));
  }
  return { id: option.value };
};

// Usage of DebounceSelect
interface UserValue {
  label: string;
  value: string;
}

interface Props extends SelectProps {
  value?: LabeledValue[];
  onChange?: any;
  onSearch?: (keyword?: string) => Promise<[]>;
  convertToOption?: (item: any) => void;
  placeholder?: string;
  disabled?: boolean;
  resetOptions?: boolean;
  allowClear?: boolean;
  autoFocus?: boolean;
}

const AsyncSelect: FC<Props> = (props: Props) => {
  const {
    value,
    onChange,
    onSearch,
    placeholder,
    disabled = false,
    resetOptions = false,
    allowClear = false,
    convertToOption,
    ...selectProps
  } = props;

  const fetchList = async (keyword: string): Promise<UserValue[]> => {
    if (onSearch) {
      return onSearch(keyword).then((res: any) =>
        (res.data || []).map((item: any) => {
          if (convertToOption) {
            return convertToOption(item);
          }
          return convertValueToOption(item);
        }),
      );
    }
    return [];
  };

  return (
    <DebounceSelect
      showSearch
      disabled={disabled}
      value={value}
      placeholder={placeholder}
      fetchOptions={fetchList}
      resetOptions={resetOptions}
      allowClear={allowClear}
      onChange={onChange}
      style={{ width: '100%' }}
      {...selectProps}
    />
  );
};

export default AsyncSelect;
