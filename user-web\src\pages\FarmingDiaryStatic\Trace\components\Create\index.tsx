import {
  DEFAULT_DATE_FORMAT_WITHOUT_TIME,
  DEFAULT_PAGE_SIZE_ALL,
  STANDARD_DATE_FORMAT_WITHOUT_TIME,
} from '@/common/contanst/constanst';
import { getBusinessList } from '@/services/diary-2/business';
import { getProcessList } from '@/services/diary-2/process';
import { getProductList } from '@/services/diary-2/product';
import { getZoneList } from '@/services/zoneManager';
import { dayjsUtil } from '@/utils/date';
import { convertDateFormat } from '@/utils/moment';
import {
  ProForm,
  ProFormDatePicker,
  ProFormDigit,
  ProFormGroup,
  ProFormSelect,
  ProFormText,
} from '@ant-design/pro-components';
import { history, useIntl } from '@umijs/max';
import { Card, Col, message, Row } from 'antd';
import { toString } from 'lodash';
import { FC, ReactNode, useState } from 'react';
import useCreate from '../../hooks/useCreate';

interface CreateEnterpriseProps {
  children?: ReactNode;
}
const width = 'lg';
const CreateProdProcedure: FC<CreateEnterpriseProps> = ({ children }) => {
  const { run } = useCreate();
  const [packingUnitName, setPackingUnitName] = useState<string>('');

  const onSelectProduct = async (value: string) => {
    const filters = [['iot_diary_v2_agri_product', 'name', '=', value]];
    const res = await getProductList({
      page: 1,
      size: DEFAULT_PAGE_SIZE_ALL,
      order_by: 'name asc',
      filters: JSON.stringify(filters),
    });
    const data = res.data[0];
    setPackingUnitName(data.packing_unit_name);
  };
  const onFinish = async (values: any) => {
    const { start_number, end_number, quantity } = values;

    // Chuyển đổi các giá trị từ string sang number
    const startNum = Number(start_number);
    const endNum = Number(end_number);
    const quantityNum = Number(quantity);

    if (endNum - startNum !== quantityNum) {
      console.log({ startNum, endNum, quantityNum });
      message.error('Số kết thúc - số bắt đầu phải bằng số lượng sản phẩm.');
      return;
    }
    if (values.production_date > values.expiration_date) {
      message.error('Ngày hết hạn phải lớn hơn ngày sản xuất');
      return;
    }
    await run({
      // name: string;
      batch_code: values.batch_code,
      start_number: values.start_number,
      end_number: values.end_number,
      zone: values.zone,
      agri_process_id: values.agri_process_id,
      business_info_id: values.business_info_id,
      product_id: values.product_id,
      quantity: toString(values.quantity),
      production_date: convertDateFormat({
        date: values.production_date,
        fromFormat: DEFAULT_DATE_FORMAT_WITHOUT_TIME,
        toFormat: STANDARD_DATE_FORMAT_WITHOUT_TIME,
      }),
      expiration_date: convertDateFormat({
        date: values.expiration_date,
        fromFormat: DEFAULT_DATE_FORMAT_WITHOUT_TIME,
        toFormat: STANDARD_DATE_FORMAT_WITHOUT_TIME,
      }),
      shelf_life: dayjsUtil(values.shelf_life).toISOString(),
    });
    history.push('/farming-diary-static/trace');
    return true;
  };
  const [form] = ProForm.useForm();
  const { formatMessage } = useIntl();
  return (
    <Card>
      <ProForm onFinish={onFinish}>
        <ProFormGroup>
          <Row gutter={24}>
            <Col span={12}>
              <ProFormText
                label={formatMessage({
                  id: 'common.production_batch_code',
                })}
                name="batch_code"
                rules={[
                  {
                    required: true,
                  },
                ]}
              />
            </Col>
            <Col span={6}>
              {' '}
              <ProFormDigit
                label={formatMessage({
                  id: 'common.starting_number',
                })}
                width={'md'}
                rules={[
                  {
                    required: true,
                  },
                ]}
                name="start_number"
              />
            </Col>
            <Col span={6}>
              {' '}
              <ProFormDigit
                label={formatMessage({
                  id: 'common.ending_number',
                })}
                width={'md'}
                rules={[
                  {
                    required: true,
                  },
                ]}
                name="end_number"
              />
            </Col>
            <Col span={12}>
              {' '}
              <ProFormSelect
                label={formatMessage({ id: 'common.select_region' })}
                name="zone"
                showSearch
                request={async () => {
                  const res = await getZoneList();
                  return res.data.map((item) => ({
                    label: item.label,
                    value: item.name,
                  }));
                }}
                rules={[
                  {
                    required: true,
                  },
                ]}
              />
            </Col>
            <Col span={12}>
              {' '}
              <ProFormSelect
                label={formatMessage({ id: 'common.select_farming_process' })}
                name="agri_process_id"
                rules={[
                  {
                    required: true,
                  },
                ]}
                showSearch
                request={async () => {
                  const res = await getProcessList({
                    page: 1,
                    size: DEFAULT_PAGE_SIZE_ALL,
                  });
                  return res.data.map((item) => ({
                    label: item.label,
                    value: item.name,
                  }));
                }}
              />
            </Col>
            <Col span={12}>
              {' '}
              <ProFormSelect
                label={formatMessage({ id: 'common.select_business' })}
                rules={[
                  {
                    required: true,
                  },
                ]}
                showSearch
                name="business_info_id"
                request={async () => {
                  const res = await getBusinessList({
                    page: 1,
                    size: DEFAULT_PAGE_SIZE_ALL,
                  });

                  return res.data.map((item) => ({
                    label: item.label,
                    value: item.name,
                  }));
                }}
              />
            </Col>
          </Row>

          {/* <ProFormText label="Doanh nghiệp"  name="email" /> */}
        </ProFormGroup>

        <Row gutter={24}>
          <Col span={12}>
            <ProFormSelect
              label={formatMessage({
                id: 'common.select_product',
              })}
              rules={[
                {
                  required: true,
                },
              ]}
              name="product_id"
              showSearch
              request={async () => {
                const res = await getProductList({
                  page: 1,
                  size: DEFAULT_PAGE_SIZE_ALL,
                  order_by: 'name asc',
                });
                return res.data.map((item) => ({
                  label: item.label,
                  value: item.name,
                }));
              }}
              onChange={onSelectProduct}
            />
          </Col>
          <Col span={12}>
            <ProFormDigit
              rules={[
                {
                  required: true,
                },
              ]}
              label={formatMessage({
                id: 'common.quantity',
              })}
              name="quantity"
              fieldProps={{
                suffix: packingUnitName,
              }}
            />
          </Col>
          <Col span={12}>
            <ProFormDatePicker
              label={formatMessage({
                id: 'common.date_of_manufacture',
              })}
              name="production_date"
              rules={[
                {
                  required: true,
                },
              ]}
              fieldProps={{
                format: DEFAULT_DATE_FORMAT_WITHOUT_TIME,
              }}
              width={'md'}
            />
          </Col>
          <Col span={12}>
            <ProFormDatePicker
              label={formatMessage({
                id: 'common.expiration_date',
              })}
              width={'md'}
              rules={[
                {
                  required: true,
                },
              ]}
              name="expiration_date"
              fieldProps={{
                format: DEFAULT_DATE_FORMAT_WITHOUT_TIME,
              }}
            />
          </Col>
          {/* <Col span={6}>
              <ProFormDatePicker
                label={formatMessage({
                  id: 'common.expiry',
                })}
                width={'sm'}
                name="shelf_life"
                // fieldProps={{
                //   suffix: formatMessage({
                //     id: 'common.month',
                //   }),
                // }}
              />
            </Col> */}
        </Row>
      </ProForm>
    </Card>
  );
};

export default CreateProdProcedure;
