import { useEffect, useState } from 'react';
type Greeting = {
  time: number[];
  greetings: {
    "vi-VN": string;
    "en-US": string;
  };
}
export function useGetTimeName() {
  const [timeName, setTimeNames] = useState<Greeting>();

  const getTimeNames = () => {
    const times = [
      { time: [5, 10], greetings: { ["vi-VN"]: "Chào buổi sáng", ["en-US"]: "Good morning" } },
      { time: [11, 13], greetings: { ["vi-VN"]: "Chào buổi trưa", ["en-US"]: "Good afternoon" } },
      { time: [14, 18], greetings: { ["vi-VN"]: "Chào buổi chiều", ["en-US"]: "Good evening" } },
      { time: [19, 4], greetings: { ["vi-VN"]: "Chào buổi tối", ["en-US"]: "Good night" } }
    ];

    const currentHour = new Date().getHours();

    const currentTimeName = times.find(time => {
      const [start, end] = time.time;
      if (start <= end) {
        return currentHour >= start && currentHour <= end;
      } else {
        return currentHour >= start || currentHour <= end;
      }
    });

    if (currentTimeName) {
      setTimeNames(currentTimeName);
    } else {
      console.error("No time name found for the current hour.");
    }
  };

  useEffect(() => {
    getTimeNames(); // Set initial time names

    const interval = setInterval(getTimeNames, 10 * 60 * 1000); // Update every 10 minutes

    return () => clearInterval(interval);
  }, []);

  return {
    timeName
  };
}
