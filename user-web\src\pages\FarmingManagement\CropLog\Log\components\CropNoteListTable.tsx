import { getCropNote } from '@/services/crop';
import { ProColumns, ProTable } from '@ant-design/pro-components';
import { FormattedMessage, Link, useModel } from '@umijs/max';
import { Col, Image, Row, Tooltip } from 'antd';
import React, { FC, Fragment, ReactNode, useEffect, useState } from 'react';

interface CropNoteListTableProps {
  children?: ReactNode;
  genLinkDetail?: (itemId: string) => string;
  keepSearchParams?: boolean;
}

type IDataTable = {
  id: string;
  name: string;
  executor: string;
  project: string;
  area: string;
  content: string;
  image: string;
  startDate: string;
  endDate: string;
};

const CropNoteListTable: FC<CropNoteListTableProps> = ({ genLinkDetail, keepSearchParams }) => {
  const { selectedCrop, setSelectedCrop } = useModel('MyCrop');

  const [dataReal, setDataReal] = useState<IDataTable[]>([]);
  const [expandedContentIds, setExpandedContentIds] = useState<string[]>([]);
  const [imageLinks, setImageLinks] = useState<string[]>([]);

  const formatDateTime = (dateTimeString: string) => {
    const dateTime = new Date(dateTimeString);
    if (isNaN(dateTime.getTime())) {
      return '';
    }

    const time = dateTime.toLocaleTimeString('en-GB', { hour12: false });
    const date = dateTime.toLocaleDateString('en-GB', {
      day: '2-digit',
      month: '2-digit',
      year: 'numeric',
    });

    return `${time}, ${date}`;
  };

  const renderImageCropLayout = (record: any) => {
    console.log('run this render');
    try {
      const imageCropComponents: JSX.Element[] = [];
      let rowImages: string[] = [];
      let imageLinksArr: any = record.image.split(',');
      imageLinksArr.forEach((imageLink: string, index: number) => {
        rowImages.push(imageLink);
        if ((index + 1) % 4 === 0 || index === imageLinksArr.length - 1) {
          // When we have 4 images in the row or we have reached the last image
          const imageCropRow = (
            <Row className="gutter-row" gutter={4} key={`row_${index}`}>
              {rowImages.map((image, idx) => {
                console.log('Image link', image);
                return (
                  <Col className="gutter-row" key={`col_${index}_${idx}`}>
                    <Image
                      src={'https://iot.viis.tech/api/v2/file/download?file_url=' + image}
                      width={30}
                      height={30}
                      preview={{
                        mask: 'View',
                      }}
                    />
                  </Col>
                );
              })}
            </Row>
          );
          imageCropComponents.push(imageCropRow);
          rowImages = [];
        }
      });

      return imageCropComponents;
    } catch (error) {
      console.log('render error', error);
      return <></>;
    }
  };

  useEffect(() => {
    const fetchData: any = async () => {
      let [params, sort, filters]: any[] = [
        '',
        '',
        [['iot_Crop_note', 'crop', 'like', selectedCrop.name]],
      ];
      const res: any = await getCropNote({
        page: 1,
        size: 1000,
        fields: ['*'],
        filters: JSON.stringify(filters),
        or_filters: [],
        order_by: '',
        group_by: '',
      });
      let data: any = res.data.data;
      // data.forEach((obj: any) => {
      //   obj.key = obj.name
      // });
      setDataReal(data);
    };
    fetchData();
  }, [selectedCrop]);

  const handleExpandContent = (id: string) => {
    if (expandedContentIds.includes(id)) {
      setExpandedContentIds((prevIds: any) => prevIds.filter((prevId: any) => prevId !== id));
    } else {
      setExpandedContentIds((prevIds: any) => [...prevIds, id]);
    }
  };

  const columns: ProColumns<IDataTable>[] = [
    {
      title: 'STT',
      dataIndex: 'index',
      renderText(_text, record, index, _action) {
        return index + 1;
      },
    },
    {
      title: 'ID',
      dataIndex: 'name',
      render: (dom: any, entity: any) => {
        return (
          <Link to={`/farming-management/crop-log/log-detail?crop_note_id=${dom}`}>
            <span>{dom}</span>
          </Link>
        );
      },
    },
    {
      title: 'Tên ghi chú',
      dataIndex: 'label',
    },
    {
      title: 'Tên mùa vụ',
      dataIndex: 'crop_name',
    },
    {
      title: 'Nội dung ghi chú',
      dataIndex: 'note',
      render: (text: any, record, index, action) => {
        const isExpanded = expandedContentIds.includes(record.id);
        const truncatedContent = text.length > 50 ? text.substring(0, 50) + '...' : text;

        return (
          <div style={{ maxHeight: isExpanded ? 'none' : '60px', overflow: 'hidden' }}>
            <Tooltip title={text}>{isExpanded ? text : truncatedContent}</Tooltip>
            {text.length > 50 && (
              <span
                style={{ color: '#1890ff', cursor: 'pointer', marginLeft: '8px' }}
                onClick={() => handleExpandContent(record.id)}
              >
                {isExpanded ? 'Rút gọn' : 'Xem thêm'}
              </span>
            )}
          </div>
        );
      },
    },
    {
      title: <FormattedMessage id="common.form.image" defaultMessage="Image" />,
      dataIndex: 'image',
      render: (text: any, record, index, action) => {
        return (
          <>{renderImageCropLayout(record)}</>

          // <Tooltip title={text} overlayStyle={{ display: 'flex', alignItems: 'center' }}>
          // </Tooltip>
        );
      },
    },
    // {
    //   title: 'Ngày tạo ghi chú',
    //   dataIndex: 'creation',
    //   render: (text: any, record, index, action) => formatDateTime(text),
    // },
    // {
    //   title: 'Lần cuối chỉnh sửa',
    //   dataIndex: 'modified',
    //   render: (text: any, record, index, action) => formatDateTime(text),
    // },
  ];

  const [selectedRows, setSelectedRows] = useState<string[]>([]);
  const handleSelectRows = (selectedRowKeys: React.Key[], selectedRows: IDataTable[]) => {
    // Update the selected rows state when the checkboxes are clicked
    setSelectedRows(selectedRowKeys as string[]);
  };
  // const rowSelection = {
  //   selectedRowKeys: selectedRows,
  //   onChange: handleSelectRows,
  //   selections: [Table.SELECTION_ALL, Table.SELECTION_INVERT, Table.SELECTION_NONE],
  // };
  const rowSelection = {
    onChange: (selectedRowKeys: React.Key[], selectedRows: IDataTable[]) => {
      console.log(`selectedRowKeys: ${selectedRowKeys}`, 'selectedRows: ', selectedRows);
    },
    getCheckboxProps: (record: IDataTable) => ({
      disabled: record.name === 'Disabled User', // Column configuration not to be checked
      name: record.name,
    }),
  };
  return (
    <Fragment>
      <ProTable
        // rowSelection={{ type: 'checkbox', ...rowSelection }}
        dataSource={dataReal}
        headerTitle="Danh sách ghi chú mùa vụ"
        columns={columns}
        rowKey="name"
        bordered
        // onRow={(record, rowIndex) => {
        //   return {
        //     onClick: (event) => { }, // click row
        //     // onDoubleClick: (event) => { }, // double click row
        //     // onContextMenu: (event) => { }, // right button click row
        //     // onMouseEnter: (event) => { }, // mouse enter row
        //     // onMouseLeave: (event) => { }, // mouse leave row
        //   };
        // }}
      />
    </Fragment>
  );
};

export default CropNoteListTable;
