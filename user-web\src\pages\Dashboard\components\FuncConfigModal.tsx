import { SettingOutlined } from '@ant-design/icons';
import { Button, Col, Form, Input, InputNumber, message, Modal, Row, Select } from 'antd';
import { useState } from 'react';
const { Item } = Form;

import { IIotProductionFunction } from '@/types/IIotProductionFunction';
const { TextArea } = Input;
const { Option } = Select;

const FuncConfigModal = (params: { refreshFnc: any; fnc: IIotProductionFunction | undefined }) => {
  const [loading, setLoading] = useState(false);
  const [isOpen, setOpen] = useState(false);
  const [form] = Form.useForm();

  const showModal = () => {
    if (params.fnc) form.setFieldsValue(params.fnc);
    setOpen(true);
  };

  const hideModal = () => {
    setOpen(false);
  };

  const handleOk = () => {
    form.submit();
  };

  const handleCancel = () => {
    hideModal();
  };
  console.log('sensor', params.fnc);
  return (
    <>
      <Button type="ghost" onClick={showModal}>
        <SettingOutlined />
      </Button>
      <Modal
        title={`Cấu hình cảm biến`}
        open={isOpen}
        onOk={handleOk}
        onCancel={handleCancel}
        width={800}
        confirmLoading={loading}
      >
        <Form
          // size='small'
          layout="horizontal"
          labelCol={{ span: 24 }}
          labelAlign="left"
          form={form}
          onFinish={async (value: any) => {
            try {
              console.log(value);
              // const result = await generalUpdate('Sensor', params?.sensor?.name.toString() || "", {
              //     data: value
              // });
              form.resetFields();
              hideModal();
              message.success('Thành công!');
              if (params.refreshFnc) {
                await params.refreshFnc();
              }
            } catch (error: any) {
              message.error(error.toString());
            } finally {
              setLoading(false);
            }
          }}
        >
          <Row gutter={5}>
            <Col className="gutter-row" md={8}>
              <Item
                label="Kích thước khối"
                help="Là số chẵn,có giá trị từ 1 - 24, chia màn hình thành tỉ lệ 24 phần"
                labelCol={{ span: 24 }}
                rules={[
                  {
                    required: true,
                    message: 'Bắt buộc điền',
                  },
                ]}
                name="md_size"
              >
                <InputNumber min={0} max={24} style={{ width: '100%' }} />
              </Item>
            </Col>
          </Row>
        </Form>
      </Modal>
    </>
  );
};

export default FuncConfigModal;
