export default {
  'warehouse-management.import-history.id': 'Id',
  'warehouse-management.import-history.date': '<PERSON><PERSON><PERSON> nhập',
  'warehouse-management.import-history.supplier': '<PERSON><PERSON><PERSON> cung cấp',
  'warehouse-management.import-history.total-qty': 'Tổng số lượng',
  'warehouse-management.import-history.item': '<PERSON><PERSON> sản phẩm', // Item Code
  'warehouse-management.import-history.accepted-quantity': 'Số lượng nhận', // Accepted Quantity
  'warehouse-management.import-history.rejected-quantity': 'Số lượng loại', // Rejected Quantity
  'warehouse-management.import-history.rate': 'Đơn giá', // Rate
  'warehouse-management.import-history.amount': 'Thành tiền',
  'warehouse-management.import-history.detail': 'Chi tiết',
  'warehouse-management.import-history.total_quantity': 'Tổng số lượng',
  'warehouse-management.import-history.total_price': 'Tổng cộng',
  'warehouse-management.import-history.warehouse_label': 'Tên kho',
  'warehouse-management.import-history.item_label': 'Tên hàng',
  'warehouse-management.import-history.item_id': 'Mã hàng',
  'warehouse-management.import-history.add_taxes': 'Thuế',
  'warehouse-management.import-history.discount': 'Chiết khấu',
  'warehouse-management.import-history.voucher_amount': 'Tiền hàng',
  'warehouse-management.import-history.other_charges': 'Chi phí khác',
  'warehouse-management.import-history.total_amount': 'Tổng tiền',
  'warehouse-management.import-history.paid_amount': 'Đã thanh toán',
};
