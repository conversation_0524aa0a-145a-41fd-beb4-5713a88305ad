import { convertToLunarCalendar, dayjsUtil } from '@/utils/date';
import { toFirstUpperCase } from '@/utils/string';
import { useIntl } from '@umijs/max';
import { Avatar } from 'antd';
import { FC, ReactNode, useMemo } from 'react';
import { useWeather } from '../../hooks/useWeather';
interface WeatherProps {
  children?: ReactNode;
}

const Weather: FC<WeatherProps> = ({ children }) => {
  const date = useMemo(() => {
    const currentDate = dayjsUtil();
    const day = currentDate.format('dddd, DD/MM/YYYY');
    const dayLunar = convertToLunarCalendar(currentDate.toISOString());
    return `${day} (${dayLunar?.day}/${dayLunar?.month} AL)`;
  }, []);
  const { weather } = useWeather('10.7720803%2C106.6553215');
  console.log('weather', weather);
  const { formatMessage } = useIntl();
  return (
    <div className="flex gap-2 text-right">
      <div className="">
        <div className="text-xl font-semibold">{weather?.temperature}°C</div>
        <div>{toFirstUpperCase(date)}</div>
        <div className="flex flex-wrap gap-4 items-center justify-between">
          {/* <div>{weather?.weather}</div> */}
          <div>
            {formatMessage({ id: 'common.windSpeed' })}: {weather?.windSpeed} km/h
          </div>
          <div>
            {formatMessage({ id: 'common.precipitation' })}: {weather?.precipitation} mm
          </div>
          <div>
            {formatMessage({ id: 'common.humidity' })}: {weather?.humidity}%
          </div>
          <div>
            {formatMessage({ id: 'common.uvIndex' })}: {weather?.uvIndex}
          </div>
        </div>
      </div>
      <div>
        <Avatar src={weather?.icon} size={40} shape="square" />
      </div>
    </div>
  );
};

export default Weather;
