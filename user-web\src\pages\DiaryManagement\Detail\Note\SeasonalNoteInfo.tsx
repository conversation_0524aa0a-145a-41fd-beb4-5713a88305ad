import ImagePreviewGroupCommon, { IImagePreview } from '@/components/ImagePreviewGroupCommon';
import { useAccess } from '@umijs/max';
import { App, Card, Space, Typography } from 'antd';
import dayjs from 'dayjs';
import { FC, ReactNode } from 'react';

export interface SeasonalNoteInfoProps {
  data: {
    id: string;
    title?: ReactNode;
    time?: string;
    description?: ReactNode;
    listImg?: IImagePreview[];
  };
  onDeleteSuccess?: (id: string) => void;
}

const SeasonalNoteInfo: FC<SeasonalNoteInfoProps> = ({ data, onDeleteSuccess }) => {
  const { title, time, description, listImg, id } = data;
  const { message, modal } = App.useApp();

  const access = useAccess();
  return (
    <Card
      // headStyle={{
      //   border: 'none',
      // }}
      // bordered={false}
      // style={{
      //   backgroundColor: '#fff8e1',
      //   boxShadow: 'none',
      // }}
      title={title}
      extra={
        <Space>
          <span>{time ? dayjs(time).format('hh:mm:ss ,DD/MM/YYYY') : null}</span>
        </Space>
      }
    >
      <Typography.Paragraph>{description}</Typography.Paragraph>
      <ImagePreviewGroupCommon listImg={listImg} />
    </Card>
  );
};

export default SeasonalNoteInfo;
