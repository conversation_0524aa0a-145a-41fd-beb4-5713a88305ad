import ActionModalConfirm from '@/components/ActionModalConfirm';
import { useProFormList } from '@/components/Form/Config/pro-form-list';
import { createItem, deleteItem, updateItem } from '@/services/diary-2/task';
import { getProductItemV3 } from '@/services/InventoryManagementV3/product-item';
import { getUOM_v3 } from '@/services/InventoryManagementV3/uom';
import { EditOutlined } from '@ant-design/icons';
import {
  EditableProTable,
  ProFormDigit,
  ProFormGroup,
  ProFormList,
  ProFormSelect,
} from '@ant-design/pro-components';
import { useIntl } from '@umijs/max';
import { App, But<PERSON>, Card } from 'antd';
import { nanoid } from 'nanoid';
import { FC, ReactNode } from 'react';

interface MaterialUseProps {
  children?: ReactNode;
  onSuccess?: () => void;
  taskId: string;
}

const MaterialUsed1: FC<MaterialUseProps> = ({ children }) => {
  const { formatMessage } = useIntl();
  const proFormListProps = useProFormList();
  return (
    <Card
      title={formatMessage({
        id: 'common.materials_used',
      })}
    >
      <ProFormList name="related_items" {...proFormListProps}>
        {() => {
          return (
            <ProFormGroup>
              <ProFormSelect
                label={formatMessage({
                  id: 'common.select_supplies',
                })}
                name="item_id"
                required
                width="md"
                request={async () => {
                  const res = await getProductItemV3();
                  return res.data.map((item) => ({
                    label: item.label,
                    value: item.name,
                  }));
                }}
              />
              <ProFormSelect
                label={formatMessage({
                  id: 'common.unit',
                })}
                name="uom_id"
                required
                width="md"
                request={async () => {
                  const res = await getUOM_v3();
                  return res.data.map((item) => ({
                    label: `${item.uom_name}`,
                    value: item.name,
                  }));
                }}
              />
              <ProFormDigit
                label={formatMessage({
                  id: 'common.quantity',
                })}
                name="quantity"
              />
            </ProFormGroup>
          );
        }}
      </ProFormList>
    </Card>
  );
};
const DeleteItem: FC<{
  id: string;
  onSuccess?: () => void;
}> = ({ id, onSuccess }) => {
  const { message } = App.useApp();
  const { formatMessage } = useIntl();

  return (
    <ActionModalConfirm
      modalProps={{
        async onOk(...args) {
          message.success(
            formatMessage({
              id: 'common.delete_success',
            }),
          );
          await deleteItem(id);
          onSuccess?.();
          return true;
        },
      }}
    />
  );
};
const MaterialUsed: FC<MaterialUseProps> = ({ children, onSuccess, taskId }) => {
  const { formatMessage } = useIntl();
  const proFormListProps = useProFormList();
  return (
    <Card
      title={formatMessage({
        id: 'common.materials_used',
      })}
    >
      <EditableProTable
        editable={{
          async onSave(key, record: any, originRow, newLineConfig) {
            if (record.isNew) {
              /**
               * @todo create new item
               */
              await createItem({
                item_id: record.item_id,
                task_id: taskId,
                quantity: record.quantity,
                uom_id: record.uom_id,
              });
              // message.success(formatMessage({ id: 'common.success' }));
              onSuccess?.();
              return true;
            } else {
              /**
               * @todo update item
               */
              await updateItem({
                item_id: record.item_id,
                task_id: taskId,
                quantity: record.quantity,
                uom_id: record.uom_id,
                name: record.name,
              });
              // message.success(formatMessage({ id: 'common.success' }));
              onSuccess?.();
              return true;
            }
          },
          saveText: formatMessage({
            id: 'common.save',
          }),
        }}
        name="related_items"
        rowKey={'name'}
        recordCreatorProps={{
          record: {
            isNew: true,
            name: nanoid(),
          },
        }}
        columns={[
          {
            request: async () => {
              const res = await getProductItemV3();
              return res.data.map((item) => ({
                label: item.label,
                value: item.name,
              }));
            },
            valueType: 'select',
            dataIndex: 'item_id',
            title: formatMessage({
              id: 'common.select_supplies',
            }),
            formItemProps: {
              rules: [{ required: true }],
            },
            fieldProps: {
              showSearch: true,
            },
            width: 20,
          },
          {
            request: async () => {
              const res = await getUOM_v3();
              return res.data.map((item) => ({
                label: `${item.uom_name}`,
                value: item.name,
              }));
            },
            valueType: 'select',
            dataIndex: 'uom_id',
            title: formatMessage({
              id: 'common.unit',
            }),
            fieldProps: {
              showSearch: true,
            },
            formItemProps: {
              rules: [{ required: true }],
            },
            width: 20,
          },
          {
            valueType: 'digit',
            dataIndex: 'quantity',
            title: formatMessage({
              id: 'common.used_qty',
            }),
            fieldProps: {
              style: {
                width: '100%',
              },
            },
            width: 20,
          },
          {
            valueType: 'option',
            render: (text, record, _, action) => [
              <Button
                icon={<EditOutlined />}
                key="editable"
                size="small"
                onClick={() => {
                  action?.startEditable?.(record.name);
                }}
              />,
              <DeleteItem id={record.name} key="delete" onSuccess={onSuccess} />,
            ],
            width: 20,
          },
        ]}
      />
    </Card>
  );
};
export default MaterialUsed;
