import { generalUpdate } from '@/services/sscript';
import { EditOutlined } from '@ant-design/icons';
import { FormattedMessage, useIntl } from '@umijs/max';
import { Button, Col, Form, Input, InputNumber, message, Modal, Row, Select } from 'antd';
import { useState } from 'react';
const { Item } = Form;

const UpdateItemForTask = (params: { refreshFnc: any; task_id: string; data: any; items: any }) => {
  const [loading, setLoading] = useState(false);
  const [isOpen, setOpen] = useState(false);
  const [form] = Form.useForm();

  const showModal = () => {
    form.setFieldsValue(params.data);
    setOpen(true);
  };

  const hideModal = () => {
    setOpen(false);
  };
  const handleOk = () => {
    form.submit();
  };
  const handleCancel = () => {
    hideModal();
    form.resetFields();
  };
  const intl = useIntl();
  return (
    <>
      <Button
        size="small"
        //type="dashed"
        style={{ display: 'flex', alignItems: 'center' }}
        onClick={showModal}
      >
        <EditOutlined></EditOutlined>
      </Button>
      <Modal
        title={`Chỉnh sửa vật tư liên quan`}
        open={isOpen}
        onOk={handleOk}
        onCancel={handleCancel}
        confirmLoading={loading}
      >
        <Form
          layout="horizontal"
          labelCol={{ span: 24 }}
          labelAlign="left"
          form={form}
          onFinish={async (value: any) => {
            try {
              value.task_id = params.task_id;
              value.name = params.data.name;
              const result = await generalUpdate('iot_warehouse_item_task_used', params.data.name, {
                data: value,
              });
              form.resetFields();
              hideModal();
              message.success('Success!');
              if (params.refreshFnc) {
                await params.refreshFnc();
              }
            } catch (error: any) {
              message.error(error.toString());
            } finally {
              setLoading(false);
            }
          }}
        >
          <Row gutter={5}>
            <Col className="gutter-row" md={24}>
              <Item
                label={intl.formatMessage({ id: 'common.select-category' })}
                labelCol={{ span: 24 }}
                rules={[
                  {
                    required: true,
                    message: 'Bắt buộc điền',
                  },
                ]}
                name="iot_category_id"
              >
                <Select
                  options={params.items?.map((item: any) => {
                    return {
                      label:
                        item.label +
                        ` (đơn vị: ${item.unit?.short_label}, đóng gói: ${item.packing_unit?.label})`,
                      value: item.name,
                    };
                  })}
                />
              </Item>
            </Col>

            <Col className="gutter-row" md={24}>
              <Item
                label="Số lượng/định lượng dự kiến sử dụng"
                labelCol={{ span: 24 }}
                rules={[
                  {
                    required: true,
                    message: 'Bắt buộc điền',
                  },
                ]}
                name="exp_quantity"
              >
                <InputNumber min={0} />
              </Item>
            </Col>

            <Col className="gutter-row" md={24}>
              <Item
                label="Số lượng/định lượng thực tế sử dụng"
                labelCol={{ span: 24 }}
                name="quantity"
              >
                <InputNumber min={0} />
              </Item>
            </Col>
            <Col className="gutter-row" md={24}>
              <Item
                label="Số lượng/định lượng hao hụt"
                labelCol={{ span: 24 }}
                name="loss_quantity"
              >
                <InputNumber min={0} />
              </Item>
            </Col>
            <Col className="gutter-row" md={24}>
              <Item
                label={<FormattedMessage id={'common.form.description'} />}
                labelCol={{ span: 24 }}
                name="description"
              >
                <Input.TextArea />
              </Item>
            </Col>
          </Row>
        </Form>
      </Modal>
    </>
  );
};

export default UpdateItemForTask;
