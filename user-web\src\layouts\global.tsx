import { getLocale, setLocale } from '@umijs/max';
import { FC, ReactNode, useEffect } from 'react';

interface GlobalLayoutProps {
  children?: ReactNode;
}

const GlobalLayout: FC<GlobalLayoutProps> = ({ children }) => {
  useEffect(() => {
    const locale = getLocale();
    const isAccessLanguage = ['vi-VN', 'en-US'].includes(locale);
    if (!isAccessLanguage) {
      setLocale('vi-VN');
    }
  }, []);
  return <>{children}</>;
};

export default GlobalLayout;
