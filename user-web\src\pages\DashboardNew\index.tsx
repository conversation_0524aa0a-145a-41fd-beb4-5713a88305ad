import { Col, ConfigProvider, Row } from 'antd';
import classNames from 'classnames';
import { FC, ReactNode } from 'react';
import Alarm from './components/Alarm';
import Crop from './components/Crop';
import Devices from './components/Devices';
import Header from './components/Header';
import Warehouse from './components/Warehouse';

interface IndexProps {
  children?: ReactNode;
}

const Index: FC<IndexProps> = ({ children }) => {
  const fixClass = 'px-[20px] py-[10px]';

  return (
    <ConfigProvider>
      <div className="-mx-[40px] -my-[10px]">
        <div className={classNames(fixClass)}>
          <Header />
        </div>
        <div className={classNames(fixClass, 'bg-[#f3f5f9]')}>
          <Row gutter={[8, 8]}>
            <Col span={24} lg={9}>
              <Alarm />
              <div className="mt-2">
                <Warehouse />
              </div>
            </Col>
            <Col span={24} lg={15}>
              <Devices />
              <div className="mt-2">
                <Crop />
              </div>
            </Col>
          </Row>
        </div>
      </div>
    </ConfigProvider>
  );
};

export default Index;
