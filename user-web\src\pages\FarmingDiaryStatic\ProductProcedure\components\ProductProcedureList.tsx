import { DOCTYPE_ERP } from '@/common/contanst/constanst';
import { deleteProduct, getProductList, Product } from '@/services/diary-2/product'; // Add deleteProduct service
import { getUOM_v3 } from '@/services/InventoryManagementV3/uom';
import { getParamsReqTable } from '@/services/utils';
import { PlusOutlined } from '@ant-design/icons';
import { ActionType, ProColumns, ProTable } from '@ant-design/pro-components';
import { Link, useIntl } from '@umijs/max';
import { Button, message } from 'antd'; // Add Popconfirm and message components
import { FC, ReactNode, useRef } from 'react';
import DeleteProdProcedure from '../DeleteProdProcedurePage';

interface ProductProcedureListProps {
  children?: ReactNode;
}

const ProductProcedureList: FC<ProductProcedureListProps> = ({ children }) => {
  const { formatMessage } = useIntl();
  const actionRef = useRef<ActionType>();

  const handleReload = () => {
    actionRef.current?.reload?.();
  };

  const handleDelete = async (name: string) => {
    try {
      await deleteProduct(name);
      message.success(formatMessage({ id: 'common.delete_success' }));
      handleReload();
    } catch (error) {
      message.error(formatMessage({ id: 'common.delete_failed' }));
    }
  };

  const columns: ProColumns<Product>[] = [
    {
      title: 'STT',
      index: 1,
      render: (dom, entity, index) => index + 1,
      hideInSearch: true,
    },
    {
      title: formatMessage({ id: 'common.product_name' }),
      dataIndex: 'label',
      render: (dom, entity) => (
        <Link
          to={`/farming-diary-static/product-procedure/edit/${entity.name}`}
          className="text-blue-500 cursor-pointer"
        >
          {entity.label}
        </Link>
      ),
      fixed: 'left',
    },
    {
      title: formatMessage({ id: 'common.expiry_date' }),
      dataIndex: 'expire_time',
      // hideInSearch: true,
      render: (dom, entity) => `${entity.expire_time} ${entity.expire_time_unit}`,
    },
    {
      title: formatMessage({ id: 'common.net_weight' }),
      dataIndex: 'net_weight',
      hideInSearch: true,
    },
    {
      title: formatMessage({ id: 'common.unit' }),
      dataIndex: 'unit',
      valueType: 'select',
      request: async () => {
        const res = await getUOM_v3();
        return res.data.map((item: any) => {
          return {
            label: item.uom_name,
            value: item.name,
          };
        });
      },
      fieldProps: {
        showSearch: true,
      },
      render(dom, entity, index, action, schema) {
        return entity.unit_name;
      },
      // hideInSearch: true,
    },
    {
      title: formatMessage({ id: 'common.packaging_unit' }),
      dataIndex: 'packing_unit',
      valueType: 'select',
      request: async () => {
        const res = await getUOM_v3();
        return res.data.map((item: any) => {
          return {
            label: item.uom_name,
            value: item.name,
          };
        });
      },
      fieldProps: {
        showSearch: true,
      },
      render(dom, entity, index, action, schema) {
        return entity.packing_unit_name;
      },
    },
    // {
    //   title: formatMessage({ id: 'common.status' }),
    //   render: () => <Tag color="success">Đang hoạt động</Tag>,
    // },
    {
      title: formatMessage({ id: 'common.web_link' }),
      dataIndex: 'link',
      hideInSearch: true,
      render: (dom, entity) => {
        if (!entity.link) {
          return '-';
        }
        const truncatedLink =
          entity.link.length > 20 ? `${entity.link.substring(0, 20)}...` : entity.link;
        return (
          <Button type="link" href={entity.link} target="_blank">
            {truncatedLink}
          </Button>
        );
      },
    },
    {
      title: formatMessage({ id: 'common.action' }),
      key: 'action',
      hideInSearch: true,
      render: (_, entity) => <DeleteProdProcedure onSuccess={handleReload} id={entity.name} />,
    },
  ];

  return (
    <ProTable<Product>
      actionRef={actionRef}
      form={{ labelWidth: 'auto' }}
      columns={columns}
      toolBarRender={() => [
        <Link to="/farming-diary-static/product-procedure/create" key="create">
          <Button icon={<PlusOutlined />} type="primary">
            {formatMessage({ id: 'common.add_product' })}
          </Button>
        </Link>,
      ]}
      scroll={{ x: 'max-content' }}
      request={async (params, sort, filter) => {
        const paramsReq = getParamsReqTable({
          doc_name: DOCTYPE_ERP.iotDiaryV2Product,
          tableReqParams: { params, sort, filter },
          defaultSort: 'name asc',
          fieldOperators: {
            expire_time: '=', // Specify '=' operator for expire_time field
          },
        });
        const res = await getProductList(paramsReq);
        return {
          data: res.data,
          total: res.pagination.totalElements,
        };
      }}
    />
  );
};

export default ProductProcedureList;
