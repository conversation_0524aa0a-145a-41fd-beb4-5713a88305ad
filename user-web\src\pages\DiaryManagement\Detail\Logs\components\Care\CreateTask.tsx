import { DEFAULT_PAGE_SIZE_ALL, DOCTYPE_ERP } from '@/common/contanst/constanst';
import ItemUsedTableCreateView from '@/components/Task/TaskItemUsed/ItemUsedTableCreateView';
import ProductionTableCreateView from '@/components/Task/TaskProductionNew/ProductionTableCreateView';
import CreateTodoTableEditer from '@/components/Task/TaskTodo/CreateTodoTableEditer';
import CreateWorkTimeTableEditer from '@/components/Task/TaskWorkTime/CreateWorkTimeTableEditer';
import { customerUserListAll, getCustomerUserList } from '@/services/customerUser';
import {
  createFarmingPlanDiaryTask,
  getFarmingPlan,
  getFarmingPlanState,
} from '@/services/farming-plan';
import {
  PageContainer,
  ProForm,
  ProFormInstance,
  useDeepCompareEffect,
} from '@ant-design/pro-components';
import { history, useSearchParams } from '@umijs/max';
import { App, Button, Modal, Space, UploadFile } from 'antd';
import moment from 'moment';
import { FC, ReactNode, useEffect, useRef, useState } from 'react';
import DetailedInfo from './DetailedInfo';

interface CreateWorkflowProps {
  children?: ReactNode;
  mode?: 'normal' | 'modal';
  open?: boolean;
  onOpenChange?: (open: boolean) => void;
  onCreateSuccess?: () => void;
  farmingPlanStateId?: string;
  planId?: string;
  defaultValue?: {
    start_date?: moment.Moment | null;
    end_date?: moment.Moment | null;
  };
}
type IFormData = {
  label: string;
  farming_plan_state: string;
  dateRange: [string, string];
  intervalRange: [string, string];
  assigned_to: string;
  involved_in_users?: string[];
  description?: string;
  img?: UploadFile[];
  is_interval?: boolean;
  interval_value?: number;
  interval_type?: 'w' | 'd' | 'M';
};

const CreateWorkflow: FC<CreateWorkflowProps> = ({
  mode = 'normal',
  onCreateSuccess,
  open,
  onOpenChange,
  farmingPlanStateId,
  planId,
  defaultValue,
}) => {
  const [todoList, setTodoList] = useState([]);
  const [taskItems, setTaskItems] = useState([]);
  const [workTimes, setWorkTimes] = useState([]);
  const [productions, setProductions] = useState([]);

  const [loading, setLoading] = useState(false);
  const [customerUserOptions, setCustomerUserOptions] = useState<any[]>([]);
  const [fileList, setFileList] = useState<any[]>([]);
  const [currentPlan, setCurrentPlan] = useState<any>({});

  const [planStateOptions, setPlanStateOptions] = useState<any[]>([]);
  const onFileListChange = (fileList: any[]) => {
    setFileList(fileList);
  };

  const getCustomerUser = async () => {
    try {
      setLoading(true);
      //call api
      const result = await customerUserListAll();
      // console.log('result', result);
      const res = await getCustomerUserList({
        page: 1,
        size: DEFAULT_PAGE_SIZE_ALL,
        fields: ['name', 'first_name', 'last_name', 'email'],
      });
      setCustomerUserOptions(
        res?.data?.map((item) => {
          return {
            label:
              item.first_name || item.last_name
                ? `${item.first_name || ''} ${item.last_name || ''}`
                : `${item.email}`,
            value: item.name,
          };
        }),
      );
    } catch (error) {
    } finally {
      setLoading(false);
    }
  };

  const getCurrentFarmingPlan = async () => {
    //get default farming plan
    if (planId) {
      const farmingPlan = await getFarmingPlan(planId);
      setCurrentPlan(farmingPlan.data);
      return;
    }

    //get farming plan only if farmingPlanStateId is not undefined
    if (!farmingPlanStateId) return;
    const filters = [[DOCTYPE_ERP.iotFarmingPlanState, 'name', 'like', farmingPlanStateId]];
    const farmingPlanState = await getFarmingPlanState({ filters });
    const farmingPlanId = farmingPlanState.data[0].farming_plan;
    const farmingPlan = await getFarmingPlan(farmingPlanId);
    setCurrentPlan(farmingPlan.data);
  };

  //debug current plan
  // useEffect(() => {
  //   console.log('currentPlan', currentPlan);
  // }, [currentPlan]);

  useEffect(() => {
    getCustomerUser();
    getCurrentFarmingPlan();
  }, [planId]);
  useEffect(() => {
    const fetchData = async () => {
      try {
        //only get plan state if selectedPlan is not undefined
        if (!currentPlan) return;
        const res = await getFarmingPlanState({
          page: 1,
          size: DEFAULT_PAGE_SIZE_ALL,
          filters: [['iot_farming_plan_state', 'farming_plan', 'like', currentPlan.name]],
        });
        setPlanStateOptions(
          res.data.map((item) => {
            return {
              label: item.label,
              value: item.name,
            };
          }),
        );
      } catch (error: any) {
        // message.error(error.toString());
      }
    };
    fetchData();
  }, [currentPlan]);
  const { message } = App.useApp();
  const [submitting, setSubmitting] = useState(false);
  const [form] = ProForm.useForm();
  const formRef = useRef<ProFormInstance<any>>();
  const onFinish = async (values: any) => {
    setSubmitting(true);

    let imagePath = '';
    if (fileList.length) {
      imagePath = fileList[0].raw_url;
    }

    try {
      // create
      const {
        is_interval,
        interval_type,
        interval_value,
        intervalRange,
        start_date,
        end_date,
        enable_origin_tracing,
        involved_in_users,
      } = values;

      let requestArr: any = [];

      let assigned_to = values?.assigned_to?.at(0);
      let farming_plan_state = values?.farming_plan_state?.at(0);
      let text_state = null;
      let text_plan: string;
      let text_assign_user = null;
      if (!farming_plan_state) {
        farming_plan_state = planStateOptions?.at(0)?.value;
      } else if (!planStateOptions.find((item) => item.value === farming_plan_state)) {
        text_state = farming_plan_state;
        farming_plan_state = planStateOptions?.at(0)?.value;
      }
      if (!assigned_to) {
        assigned_to = customerUserOptions?.at(0)?.value;
      } else if (!customerUserOptions.find((item) => item.value === assigned_to)) {
        text_assign_user = assigned_to;
        assigned_to = customerUserOptions?.at(0)?.value;
      }
      // close modal
      // onOpenChange?.(false);
      // return;
      if (
        is_interval &&
        interval_type &&
        interval_value &&
        moment(values.intervalRange[0]).isValid() &&
        moment(values.intervalRange[1]).isValid()
      ) {
        let start_check = moment(values.intervalRange[0]);
        let counter = 1;
        while (start_check.isBefore(values.intervalRange[1])) {
          const task = {
            label: values.label,
            farming_plan_state: farming_plan_state,
            start_date: moment(start_date)
              .add(interval_value * counter, interval_type)
              .format('YYYY-MM-DD HH:mm:ss'),
            end_date: moment(end_date)
              .add(interval_value * counter, interval_type)
              .format('YYYY-MM-DD HH:mm:ss'),
            description: values.description,
            assigned_to: assigned_to,
            image: null,
            status: values.status,
            enable_origin_tracing: enable_origin_tracing ? 1 : 0,
            involve_in_users:
              involved_in_users?.map((d: string) => {
                return {
                  customer_user: d,
                };
              }) || [],
            worksheet_list: workTimes.map((d) => {
              const {
                work_type_id = null,
                exp_quantity = 0,
                quantity = 0,
                type = null,
                description = null,
                cost = 0,
              } = d;
              return {
                cost,
                work_type_id,
                exp_quantity,
                quantity,
                type,
                description,
              };
            }),
            item_list: taskItems.map((d: any) => {
              const {
                quantity = 0,
                description = null,
                iot_category_id = null,
                exp_quantity = 0,
                loss_quantity = 0,
              } = d;
              return { quantity, description, iot_category_id, exp_quantity, loss_quantity };
            }),
            todo_list: todoList.map((d: any) => {
              // d.start_date = moment(d.start_date).isValid() ? moment(d.start_date).add(interval_value * counter, interval_type).format('YYYY-MM-DD HH:mm:ss') : null;
              // d.end_date = moment(d.end_date).isValid() ? moment(d.end_date).add(interval_value * counter, interval_type).format('YYYY-MM-DD HH:mm:ss') : null;
              delete d['name'];
              const { label, description = null, customer_user_id } = d;
              return { label, description, customer_user_id };
            }),
            prod_quantity_list: productions.map((d) => {
              const { exp_quantity = 0, quantity = 0, product_id = null, description = null } = d;
              return {
                product_id,
                exp_quantity,
                quantity,
                description,
              };
            }),
            text_assign_user,
            text_state,
          };
          requestArr.push(task);
          start_check = start_check.add(interval_value, interval_type);
          counter++;
        }

        requestArr = requestArr.filter((d: any) => {
          return d.start_date !== moment(values.start_date[0]).format('YYYY-MM-DD HH:mm:ss');
        });

        requestArr.push({
          label: values.label,
          farming_plan_state: farming_plan_state,
          start_date: moment(start_date).format('YYYY-MM-DD HH:mm:ss'),
          end_date: moment(end_date).format('YYYY-MM-DD HH:mm:ss'),
          description: values.description,
          assigned_to: assigned_to,
          status: values.status,
          image: imagePath,
          enable_origin_tracing: enable_origin_tracing ? 1 : 0,
          involve_in_users:
            involved_in_users?.map((d: string) => {
              return {
                customer_user: d,
              };
            }) || [],
          worksheet_list: workTimes.map((d) => {
            const {
              cost = 0,
              work_type_id = null,
              exp_quantity = 0,
              quantity = 0,
              type = null,
              description = null,
            } = d;
            return {
              cost,
              work_type_id,
              exp_quantity,
              quantity,
              type,
              description,
            };
          }),
          item_list: taskItems.map((d: any) => {
            const {
              quantity = 0,
              description = null,
              iot_category_id = null,
              exp_quantity = 0,
              loss_quantity = 0,
            } = d;
            return { quantity, description, iot_category_id, exp_quantity, loss_quantity };
          }),

          todo_list: todoList.map((d: any) => {
            // d.start_date = moment(d.start_date).isValid() ? moment(d.start_date).add(interval_value * counter, interval_type).format('YYYY-MM-DD HH:mm:ss') : null;
            // d.end_date = moment(d.end_date).isValid() ? moment(d.end_date).add(interval_value * counter, interval_type).format('YYYY-MM-DD HH:mm:ss') : null;
            delete d['name'];
            const { label, description = null, customer_user_id } = d;
            return { label, description, customer_user_id };
          }),
          prod_quantity_list: productions.map((d) => {
            const { exp_quantity = 0, quantity = 0, product_id = null, description = null } = d;
            return {
              product_id,
              exp_quantity,
              quantity,
              description,
            };
          }),
          text_assign_user,
          text_state,
        });
      } else {
        requestArr.push({
          label: values.label,
          farming_plan_state: farming_plan_state,
          start_date: moment(start_date).format('YYYY-MM-DD HH:mm:ss'),
          end_date: moment(end_date).format('YYYY-MM-DD HH:mm:ss'),
          description: values.description,
          assigned_to: assigned_to,
          status: values.status,
          image: imagePath,
          enable_origin_tracing: enable_origin_tracing ? 1 : 0,
          involve_in_users:
            involved_in_users?.map((d: string) => {
              return {
                customer_user: d,
              };
            }) || [],
          worksheet_list: workTimes.map((d) => {
            const {
              cost = 0,
              work_type_id = null,
              exp_quantity = 0,
              quantity = 0,
              type = null,
              description = null,
            } = d;
            return {
              cost,
              work_type_id,
              exp_quantity,
              quantity,
              type,
              description,
            };
          }),
          item_list: taskItems.map((d: any) => {
            const {
              quantity = 0,
              description = null,
              iot_category_id = null,
              exp_quantity = 0,
              loss_quantity = 0,
            } = d;
            return { quantity, description, iot_category_id, exp_quantity, loss_quantity };
          }),
          todo_list: todoList.map((d: any) => {
            // d.start_date = moment(d.start_date).isValid() ? moment(d.start_date).add(interval_value * counter, interval_type).format('YYYY-MM-DD HH:mm:ss') : null;
            // d.end_date = moment(d.end_date).isValid() ? moment(d.end_date).add(interval_value * counter, interval_type).format('YYYY-MM-DD HH:mm:ss') : null;
            delete d['name'];
            const { label, description = null, customer_user_id } = d;
            return { label, description, customer_user_id };
          }),
          prod_quantity_list: productions.map((d) => {
            const { exp_quantity = 0, quantity = 0, product_id = null, description = null } = d;
            return {
              product_id,
              exp_quantity,
              quantity,
              description,
            };
          }),
          text_assign_user,
          text_state,
        });
      }

      // requestArr = requestArr.map((d) => {
      //   d.task_progress = 0;
      //   return d;
      // });
      requestArr = requestArr.map((d: any) => ({
        ...d,
        task_progress: 0,
        tag: values.tag,
      }));
      await createFarmingPlanDiaryTask(requestArr);

      message.success({
        content: 'Created successfully',
      });
      // close modal
      onOpenChange?.(false);
      if (onCreateSuccess) {
        onCreateSuccess?.();
      }

      return true;
    } catch (error) {
      message.error({
        content: 'Error, please try again',
      });
      return false;
    } finally {
      setSubmitting(false);
    }
  };
  const [searchParams, setSearchParams] = useSearchParams();
  const farmingPlanState = searchParams.get('farming_plan_state');

  /// default value for form
  useDeepCompareEffect(() => {
    if (defaultValue) {
      form.setFieldsValue(defaultValue);
    }
  }, [defaultValue]);

  const content = (
    <ProForm<IFormData>
      onFinish={onFinish}
      submitter={false}
      // initialValues={{
      //   farming_plan_state: farmingPlanStateId || farmingPlanState,
      // }}
      form={form}
      formRef={formRef}
    >
      <Space
        size={'large'}
        direction="vertical"
        style={{
          width: '100%',
        }}
      >
        <DetailedInfo
          currentPlanParam={currentPlan}
          planStateOptions={planStateOptions}
          customerUserOptions={customerUserOptions}
          onEditTagSuccess={onCreateSuccess}
          onFileListChange={onFileListChange}
        />
        <CreateTodoTableEditer
          dataSource={todoList}
          setDataSource={setTodoList}
          customerUserOptions={customerUserOptions}
        />
        <ItemUsedTableCreateView dataSource={taskItems} setDataSource={setTaskItems} />
        <CreateWorkTimeTableEditer dataSource={workTimes} setDataSource={setWorkTimes} />
        <ProductionTableCreateView dataSource={productions} setDataSource={setProductions} />

        {/* <RelatedMaterials /> */}
        {/* <EstimateLaborAndCost /> */}
        {/* <CreateEstimateLaborAndCostTable getFormRef={() => formRef.current} /> */}
      </Space>
    </ProForm>
  );
  const footer = [
    <Space key="footer">
      <Button
        key={'cancel'}
        onClick={() => {
          if (mode === 'modal') {
            onOpenChange?.(false);
            return;
          }
          history.back();
        }}
      >
        Hủy
      </Button>
      <Button
        onClick={() => {
          form.submit();
        }}
        loading={submitting}
        key="save"
        type="primary"
      >
        Lưu
      </Button>
    </Space>,
  ];
  if (mode === 'modal')
    return (
      <Modal
        open={open}
        onCancel={() => {
          onOpenChange?.(false);
        }}
        confirmLoading={loading}
        width={800}
        title={'Tạo công việc mới'}
        footer={footer}
      >
        {content}
      </Modal>
    );
  return (
    <PageContainer
      fixedHeader
      // extra={[
      //   <Button
      //     key={'cancel'}
      //     onClick={() => {
      //       history.back();
      //     }}
      //   >
      //     Hủy
      //   </Button>,
      //   <Button key="save" type="primary">
      //     Lưu
      //   </Button>,
      // ]}
      footer={footer}
    >
      {content}
    </PageContainer>
  );
};

export default CreateWorkflow;
