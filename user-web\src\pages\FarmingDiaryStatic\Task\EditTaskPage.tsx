import { PageContainer } from '@ant-design/pro-components';
import { FC, ReactNode } from 'react';
import EditTask from './components/Edit';
import { useParams } from '@umijs/max';

interface EditTaskPageProps {
  children?: ReactNode;
}

const EditTaskPage: FC<EditTaskPageProps> = ({ children }) => {
  const {id} = useParams<{id:string}>();
  if (!id) return null;
  return (
    <PageContainer>
      <EditTask id={id}/>
    </PageContainer>
  );
};

export default EditTaskPage;
