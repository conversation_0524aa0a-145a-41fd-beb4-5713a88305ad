import sunflower from '@/assets/img/icons/sunflower.svg';
import treeGreen from '@/assets/img/icons/tree-green.svg';
import { DOCTYPE_ERP } from '@/common/contanst/constanst';
import CreateWorkflow from '@/pages/FarmingManagement/WorkflowManagement/Create';
import { getTaskManagerList } from '@/services/farming-plan';
import { getFileUrlV2, getParamsReqTable } from '@/services/utils';
import { formatOnlyDate } from '@/utils/date';
import { ActionType, ProColumns, ProTable } from '@ant-design/pro-components';
import { Link, useAccess, useIntl } from '@umijs/max';
import { Avatar, Space, Table, Tag } from 'antd';
import { createStyles } from 'antd-use-styles';
import { useRef, useState } from 'react';

interface Props {
  stateId: string;
}

const useStyles = createStyles(() => ({
  table: {
    '& .ant-pro-table-list-toolbar-left': {
      flex: 'none',
    },
  },
}));
const StateTaskTable = ({ stateId }: Props) => {
  const intl = useIntl();
  const access = useAccess();
  const canCreateTask = access.canCreateInWorkFlowManagement();
  const canReadTask = access.canAccessPageWorkFlowManagement();
  const styles = useStyles();
  const columns: ProColumns<any>[] = [
    {
      title: intl.formatMessage({ id: 'common.index' }),
      renderText(text, record, index, action) {
        return index + 1;
      },
      search: false,
    },
    {
      title: intl.formatMessage({ id: 'workflowTab.workName' }),
      dataIndex: 'label',
      renderText(_text, record, index, action) {
        if (canReadTask) {
          return (
            <Link to={`/farming-management/workflow-management/detail/${record.name}`}>
              <Space>
                <img src={treeGreen} /> {_text}
              </Space>
            </Link>
          );
        } else
          return (
            <Space>
              <img src={treeGreen} /> {_text}
            </Space>
          );
      },
    },
    {
      title: intl.formatMessage({ id: 'common.status' }),
      dataIndex: 'status',
      render(dom, entity, index) {
        switch (dom) {
          case 'Plan':
            return <Tag color="success">Kế hoạch</Tag>;
          case 'Done':
            return <Tag color="success">Đã hoàn thành</Tag>;
          case 'In progress':
            return <Tag color="warning">Đang thực hiện</Tag>;
          case 'Pending':
            <Tag color="danger">Trì hoãn</Tag>;
          default:
            return <Tag></Tag>;
        }
      },
    },
    {
      title: intl.formatMessage({ id: 'workflowTab.executor' }),
      dataIndex: 'assigned_to',
      render(value, record) {
        const info = record.assigned_to_info?.[0];
        if (!info) {
          return null;
        }

        return (
          <Space>
            {info.user_avatar && (
              <Avatar
                size={'small'}
                src={getFileUrlV2({
                  src: info.user_avatar,
                })}
              />
            )}

            <span>{`${info.first_name || ''} ${info.last_name || ''} `}</span>
          </Space>
        );
      },
      search: false,
    },
    {
      title: 'Thành viên liên quan',
      dataIndex: 'involve_in_users',
      hideInTable: true,
      render(value, record) {
        try {
          const involveInArr = record.involve_in_users;
          const userNames = involveInArr.map((data: any) => `${data.first_name} ${data.last_name}`);
          return userNames.join(', ');
        } catch (error) {
          return null;
        }
      },
      search: false,
    },
    {
      title: 'Dự án',
      dataIndex: 'project_name',
      hideInTable: true,
      render(text, record, index, action) {
        return (
          <Space>
            <img src={sunflower} />
            <span>{text}</span>
          </Space>
        );
      },
    },
    {
      title: 'Khu vực ',
      hideInTable: true,
      dataIndex: 'zone_name',
      // renderText() {
      //   return 'Vườn Đà Lạt';
      // },
    },
    {
      title: 'Vụ mùa',
      hideInTable: true,
      dataIndex: 'crop_name',
      // renderText() {
      //   return 'Vụ mùa dâu tây';
      // },
    },
    {
      title: 'Kế hoạch',
      hideInTable: true,
      dataIndex: 'plan_name',
      render(value, record) {
        try {
          return record.plan_name;
        } catch (error) {
          return null;
        }
      },
    },
    {
      title: 'Giai đoạn',
      dataIndex: 'state_name',
      hideInTable: true,
      render(value, record) {
        try {
          return record.state_name;
        } catch (error) {
          return null;
        }
      },
    },
    {
      search: false,
      title: intl.formatMessage({ id: 'common.start_date' }),
      dataIndex: 'start_date',
      render(dom, entity) {
        return formatOnlyDate(entity.start_date);
      },
      sortDirections: ['ascend', 'descend', 'ascend'],
      sorter: true,
      defaultSortOrder: 'descend',
    },
    {
      search: false,
      title: intl.formatMessage({ id: 'common.end_date' }),
      dataIndex: 'end_date',
      render(dom, entity) {
        return formatOnlyDate(entity.end_date);
      },
      sortDirections: ['ascend', 'descend', 'ascend'],
      sorter: true,
    },

    {
      title: 'Công việc',
      hideInTable: true,
      dataIndex: 'worksheet_list',
      render(value, record) {
        try {
          let worksheet_list = record.worksheet_list;
          const dataName = worksheet_list.map((data: any) => `${data.work_type.label}`);
          return dataName.join(', ');
        } catch (error) {
          return null;
        }
      },
      search: false,
    },
    // {
    //         title: <FormattedMessage id="category.material-management.category_name" defaultMessage="unknown" />,
    //   dataIndex: 'item_list',
    //   render(value, record) {
    //     try {
    //       let item_list = record.item_list;
    //       const dataName = item_list.map((data: any) => `${data.category.label}`);
    //       return dataName.join(', ');
    //     } catch (error) {
    //       return null;
    //     }
    //   },
    //   search: false,
    // },
    {
      title: intl.formatMessage({ id: 'common.completion_level' }),
      dataIndex: 'todo_done',
      search: false,
      render(value, record) {
        return `${record.todo_done || 0}/${record.todo_total || 0}`;
      },
    },
  ];
  const actionRef = useRef<ActionType>();
  // create new Task modal
  // modal create new task
  const reloadTable = () => {
    actionRef.current?.reload?.();
  };
  const [openModalCreateNewTask, setOpenModalCreateNewTask] = useState(false);

  const toolbarButtons = [];
  // if (canCreateTask) {
  //   toolbarButtons.push(
  //     <Button
  //       key={'create'}
  //       icon={<PlusOutlined />}
  //       onClick={() => {
  //         setOpenModalCreateNewTask(true);
  //         return;
  //         // vì chỗ này đã memo component
  //         // nên farmingPlanState ko dc cập nhật
  //         const urlParams = new URLSearchParams(window.location.search);
  //         history.push(
  //           `/farming-management/workflow-management/create${
  //             stateId ? `?farming_plan_state=${stateId}` : ''
  //           }`,
  //         );
  //       }}
  //       type="primary"
  //     >
  //       {intl.formatMessage({ id: 'workflowTab.createWork' })}
  //     </Button>,
  //   );
  // }
  return (
    <>
      <ProTable
        style={{ maxWidth: '75vw' }}
        actionRef={actionRef}
        className={styles.table}
        //   form={{
        //     syncToUrl: true,
        //     defaultCollapsed: false,
        //     initialValues: {
        //       pl_state_name: farmingPlanState,
        //     },
        //   }}
        search={false}
        tableAlertOptionRender={() => null}
        toolbar={{
          actions: toolbarButtons,
          // onSearch(keyWords) {
          //   console.log('keyWords: ', keyWords);
          // },
          // filter: (
          //   <ProForm
          //     form={formFilter}
          //     name="crop-detail:table-filter"
          //     onValuesChange={(changeValue) => {
          //       if (changeValue.dateRange) {
          //         searchParams.set(dateRangeFilterKey, JSON.stringify(changeValue.dateRange));
          //       } else {
          //         searchParams.delete(dateRangeFilterKey);
          //       }
          //       setSearchParams(searchParams);
          //     }}
          //     layout="inline"
          //     submitter={false}
          //   >
          //     <ProFormDateRangePicker
          //       name="dateRange"
          //       label="Thời gian thực hiện"
          //       style={{
          //         width: 150,
          //       }}
          //     />
          //     <Space size={'large'}>
          //       <Button
          //         onClick={() => {
          //           formFilter.setFieldsValue({
          //             dateRange: [new Date(), new Date()],
          //           });
          //           //
          //           searchParams.set(
          //             dateRangeFilterKey,
          //             JSON.stringify(
          //               [new Date(), new Date()].map((item) => dayjs(item).format('YYYY-MM-DD')),
          //             ),
          //           );
          //           setSearchParams(searchParams);
          //         }}
          //       >
          //         Hôm nay
          //       </Button>
          //       <Button
          //         icon={<PlusOutlined />}
          //         onClick={() => {
          //           // vì chỗ này  đã memo component
          //           // nên farmingPlanState ko dc cập nhật
          //           const urlParams = new URLSearchParams(window.location.search);
          //           const currentFarmingPlanState = urlParams.get('pl_state_name');
          //           history.push(
          //             `/farming-management/workflow-management/create${
          //               currentFarmingPlanState
          //                 ? `?farming_plan_state=${currentFarmingPlanState}`
          //                 : ''
          //             }`,
          //           );
          //         }}
          //       >
          //         Tạo công việc mới
          //       </Button>
          //     </Space>
          //   </ProForm>
          // ),
          // actions: [
          //   <Button key="primary" type="primary">
          //     添加
          //   </Button>,
          // ],
        }}
        rowSelection={{
          selections: [Table.SELECTION_ALL, Table.SELECTION_INVERT],
        }}
        pagination={{
          showSizeChanger: true,
          pageSizeOptions: [10, 20, 50, 100],
        }}
        request={async (params, sort, filter) => {
          const paramsReq = getParamsReqTable({
            doc_name: DOCTYPE_ERP.iotFarmingPlanTask,
            tableReqParams: {
              params,
              sort,
              filter,
            },
            concatFilter: [['iot_farming_plan_task', 'farming_plan_state', 'like', stateId]],
          });
          const res = await getTaskManagerList(paramsReq);
          return {
            data: res.data,
            success: true,
            total: res?.pagination?.totalElements,
          };
        }}
        headerTitle="Danh sách công việc"
        columns={columns}
        rowKey={'name'}
        scroll={{
          x: 'max-content',
        }}
      />
      {openModalCreateNewTask && (
        <CreateWorkflow
          mode="modal"
          open={openModalCreateNewTask}
          onOpenChange={setOpenModalCreateNewTask}
          onCreateSuccess={reloadTable}
          farmingPlanStateId={stateId}
        />
      )}
    </>
  );
};

export default StateTaskTable;
