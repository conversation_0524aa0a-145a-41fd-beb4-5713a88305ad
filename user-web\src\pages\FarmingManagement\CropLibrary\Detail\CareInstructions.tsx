import { Guide } from '@/types/guide.type';
import { Alert } from 'antd';
import { createStyles } from 'antd-use-styles';
import { FC, ReactNode } from 'react';
import CollapsibleInfoCard from './components/CollapsibleInfoCard';

interface CareInstructionsProps {
  children?: ReactNode;
  guides: Guide[];
}
const useStyles = createStyles(({ token }) => ({
  wrapper: {
    display: 'flex',
    flexDirection: 'column',
    gap: token.margin,
  },
}));
const CareInstructions: FC<CareInstructionsProps> = ({ children, guides }) => {
  const styles = useStyles();
  return (
    <div className={styles.wrapper}>
      {guides && guides.length ? (
        guides.map((guide) => <CollapsibleInfoCard key={guide.name} cardInfo={guide} />)
      ) : (
        <Alert message="Cây trồng chưa được thêm hướng dẫn" type="info" />
      )}
    </div>
  );
};

export default CareInstructions;
