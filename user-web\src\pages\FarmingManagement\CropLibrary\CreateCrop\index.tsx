import { DOCTYPE_ERP } from '@/common/contanst/constanst';
import { uploadFile } from '@/services/fileUpload';
import { createPlant, updatePlant } from '@/services/plants';
import { PlusOutlined } from '@ant-design/icons';
import { ModalForm, ProForm, ProFormText } from '@ant-design/pro-components';
import { FormattedMessage, useModel } from '@umijs/max';
import { Button, Form, message, Modal, Upload, UploadFile, UploadProps } from 'antd';
import ImgCrop from 'antd-img-crop';
import 'antd/es/modal/style';
import 'antd/es/slider/style';
import { RcFile } from 'antd/es/upload';
import { uniqueId } from 'lodash';
import { FC, ReactNode, useState } from 'react';
interface CreateCropProps {
  children?: ReactNode;
  onSuccess?: ()=>void
}
const getBase64 = (file: RcFile): Promise<string> =>
  new Promise((resolve, reject) => {
    const reader = new FileReader();
    reader.readAsDataURL(file);
    reader.onload = () => resolve(reader.result as string);
    reader.onerror = (error) => reject(error);
  });

interface IFormData {
  name: string;
  image: UploadFile<any>[];
}
const CreateCrop: FC<CreateCropProps> = ({
  onSuccess
}) => {
  const [form] = Form.useForm<IFormData>();
  const { myPlant, setMyPlant } = useModel('MyPlant');
  const [submitting, setSubmitting] = useState<boolean>(false);
  const [previewOpen, setPreviewOpen] = useState(false);
  const [previewImage, setPreviewImage] = useState('');
  const [previewTitle, setPreviewTitle] = useState('');

  const handlePreview = async (file: UploadFile) => {
    if (!file.url && !file.preview) {
      file.preview = await getBase64(file.originFileObj as RcFile);
    }

    setPreviewImage(file.url || (file.preview as string));
    setPreviewOpen(true);
    setPreviewTitle(file.name || file.url!.substring(file.url!.lastIndexOf('/') + 1));
  };
  const handleChange: UploadProps['onChange'] = ({ fileList: newFileList }) => {
    form.setFieldValue('image', newFileList);
  };
  const handleCancel = () => setPreviewOpen(false);
  const handleFinish = async (values: IFormData) => {
    setSubmitting(true);
    const { name, image } = values;
    const img = image.at(0);
    await createPlant({ label: name })
      .then(async (newPlant) => {
        if (!newPlant) {
          throw new Error('Lỗi hệ thống khi tạo cây mới');
        }
        const uploadStatus = await uploadFile({
          docType: DOCTYPE_ERP.iotPlant,
          docName: newPlant.name || img?.fileName || uniqueId(),
          file: img?.originFileObj as any,
        });
        if (!uploadStatus.data) {
          throw new Error('Lỗi trong quá trình up ảnh');
        }
        const updatedPlant = await updatePlant({
          name: newPlant.name,
          image: uploadStatus.data.message.file_url,
          type: 'User owner',
        });
        setMyPlant([updatedPlant, ...myPlant]);
        onSuccess?.();
        message.success('Tạo cây thành công');
      })
      .catch((error) => {
        message.error({ content: `Lỗi khi tạo cây: ${error}`, duration: 5 });
      });
    setSubmitting(false);
    return true;
  };
  return (
    <ModalForm<IFormData>
      width="400px"
      title={<FormattedMessage id="plantTab.createPlant" />}
      trigger={
        <Button type="primary" icon={<PlusOutlined />}>
          {' '}
          <FormattedMessage id="plantTab.createPlant" />
        </Button>
      }
      autoFocusFirstInput
      modalProps={{
        destroyOnClose: true,
      }}
      form={form}
      submitter={{
        render: (props, defaultDoms) => {
          return [
            <Button
              key="ok"
              onClick={() => {
                props.submit();
              }}
              type="primary"
              style={{ width: '100%' }}
              loading={submitting}
            >
              <FormattedMessage id="action.save" />
            </Button>,
          ];
        },
      }}
      submitTimeout={2000}
      onFinish={handleFinish}
    >
      <ProFormText
        rules={[{ required: true, message: `Xin vui lòng điền tên cây` }]}
        required
        width="lg"
        name="name"
        label={<FormattedMessage id="plantTab.plant" />}
      />
      <ProForm.Item
        name="image"
        required
        label={<FormattedMessage id="common.form.image" />}
        rules={[{ required: true, message: `Xin vui lòng bổ sung ảnh` }]}
      >
        <ImgCrop rotationSlider>
          <Upload
            listType="picture-card"
            onChange={handleChange}
            onPreview={handlePreview}
            maxCount={1}
            accept="image/x-png,image/jpeg,image/png"
          >
            Upload
          </Upload>
        </ImgCrop>
      </ProForm.Item>
      <Modal open={previewOpen} title={previewTitle} footer={null} onCancel={handleCancel}>
        <img alt="example" style={{ width: '100%' }} src={previewImage} />
      </Modal>
    </ModalForm>
  );
};

export default CreateCrop;
