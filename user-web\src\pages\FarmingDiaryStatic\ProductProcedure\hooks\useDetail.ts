import { DOCTYPE_ERP } from '@/common/contanst/constanst';
import { getProductList, Product } from '@/services/diary-2/product';
import { useRequest } from '@umijs/max';

export default function useDetail(
  { id, onSuccess } = {} as {
    onSuccess?: (data: Product) => void;
    id: string;
  },
) {
  return useRequest(
    async () => {
      if (!id) return null;
      const res = await getProductList({
        filters: [[DOCTYPE_ERP.iotDiaryV2Product, 'name', '=', id]],
        order_by: 'name asc',
        page: 1,
        size: 1,
      });
      const data = res?.data?.[0];
      if (!data) throw new Error('Not found');
      return {
        data,
      };
    },
    {
      onSuccess: (data) => {
        if (data) onSuccess?.(data);
      },
    },
  );
}
