import { IIotCategory } from '@/types/IIotCategory';
import { EditOutlined } from '@ant-design/icons';
import { FormattedMessage } from '@umijs/max';
import { Button, Col, Form, Input, InputNumber, Modal, Row, Select } from 'antd';
import { useState } from 'react';
const { Item } = Form;

const UpdateItem = (params: { data: any; items: any; onFinish: any }) => {
  const [loading, setLoading] = useState(false);
  const [isOpen, setOpen] = useState(false);
  const [form] = Form.useForm();
  const [selectedItem, setSelectedItem] = useState<IIotCategory>();

  const showModal = () => {
    form.setFieldsValue(params.data);
    setSelectedItem(params.data.item);
    setOpen(true);
  };

  const hideModal = () => {
    setOpen(false);
  };
  const handleOk = () => {
    form.submit();
  };
  const handleCancel = () => {
    hideModal();
    form.resetFields();
  };

  return (
    <>
      <Button
        size="small"
        //type="dashed"
        style={{ display: 'flex', alignItems: 'center' }}
        onClick={showModal}
      >
        <EditOutlined></EditOutlined>
      </Button>
      <Modal
        title={`Chỉnh sửa vật tư liên quan`}
        open={isOpen}
        onOk={handleOk}
        onCancel={handleCancel}
        confirmLoading={loading}
      >
        <Form
          layout="horizontal"
          labelCol={{ span: 24 }}
          labelAlign="left"
          form={form}
          onFinish={async (values: any) => {
            values.item = selectedItem;
            values.name = params.data.name;
            await params?.onFinish(values);
            setOpen(false);
            form.resetFields();
          }}
        >
          <Row gutter={5}>
            <Col className="gutter-row" md={24}>
              <Item
                label="Chọn vật tư"
                labelCol={{ span: 24 }}
                rules={[
                  {
                    required: true,
                    message: 'Bắt buộc điền',
                  },
                ]}
                name="iot_category_id"
              >
                <Select
                  onChange={(v) => {
                    setSelectedItem(params?.items?.find((el) => el.name === v));
                  }}
                  options={params.items?.map((item: any) => {
                    return {
                      label:
                        item.label +
                        ` (đơn vị: ${item.unit?.short_label}, đóng gói: ${item.packing_unit?.label})`,
                      value: item.name,
                    };
                  })}
                />
              </Item>
            </Col>
            <Col className="gutter-row" md={24}>
              <Item
                label={
                  'Số lượng/định lượng dự kiến sử dụng ' +
                  (selectedItem ? `(${selectedItem?.unit?.short_label || ''})` : '')
                }
                labelCol={{ span: 24 }}
                name="exp_quantity"
              >
                <InputNumber min={0} />
              </Item>
            </Col>
            <Col className="gutter-row" md={24}>
              <Item
                label={
                  'Số lượng/định lượng thực tế sử dụng ' +
                  (selectedItem ? `(${selectedItem?.unit?.short_label || ''})` : '')
                }
                labelCol={{ span: 24 }}
                name="quantity"
              >
                <InputNumber min={0} />
              </Item>
            </Col>
            <Col className="gutter-row" md={24}>
              <Item
                label={
                  'Số lượng/định lượng hao hụt ' +
                  (selectedItem ? `(${selectedItem?.unit?.short_label || ''})` : '')
                }
                labelCol={{ span: 24 }}
                name="loss_quantity"
              >
                <InputNumber min={0} />
              </Item>
            </Col>
            <Col className="gutter-row" md={24}>
              <Item
                label={<FormattedMessage id={'common.form.description'} />}
                labelCol={{ span: 24 }}
                name="description"
              >
                <Input.TextArea />
              </Item>
            </Col>
          </Row>
        </Form>
      </Modal>
    </>
  );
};

export default UpdateItem;
