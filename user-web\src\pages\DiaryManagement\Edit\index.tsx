import { CameraFilled } from '@ant-design/icons';
import {
PageContainer,
ProForm,
ProFormDateRangePicker,
ProFormSelect,
ProFormTimePicker,
ProFormUploadButton
} from '@ant-design/pro-components';
import { Button,Card,Space } from 'antd';
import { FC,ReactNode } from 'react';
import DetailedInfo from './DetailedInfo';
import SelectDevice from './SelectDevice';
import TaskChild from './TaskChild';
import { useParams } from '@umijs/max';


interface EditWorkflowProps {
  children?: ReactNode;
}
type IFormData = {}
const EditWorkflow: FC<EditWorkflowProps> = () => {
  const onFinish = async (values: IFormData) => {};
  const {id} = useParams();
  return (
    <PageContainer
      fixedHeader
      // extra={[
      //   <Button key="cancel">Hủy</Button>,
      //   <Button key="save" type="primary">
      //     Lưu
      //   </Button>,
      // ]}
      // footer={[
      //   <Button key="cancel">Hủy</Button>,
      //   <Button key="save" type="primary">
      //     Lưu
      //   </Button>,
      // ]}
    >
      <ProForm<IFormData> onFinish={onFinish} submitter={false}>
        <Space
          size={'large'}
          direction="vertical"
          style={{
            width: '100%',
          }}
        >
          <DetailedInfo />
          {/* <TaskChild /> */}
          <Card title="Hình ảnh / Video mô tả ">
            <ProFormUploadButton listType="picture-card" icon={<CameraFilled />} title="" />
          </Card>
          {/* <Card title="Vật tư liên quan">
            <ProFormSelect label="Chọn vật tư liên quan" />
          </Card> */}
          {/* <Card title="Hẹn giờ">
            <ProForm.Group>
              <ProFormTimePicker label="Chọn giờ" width={'lg'} />
              <ProFormDateRangePicker label={'Thời gian'} width={'lg'} />
            </ProForm.Group>
          </Card> */}
          {/* <SelectDevice /> */}
        </Space>
      </ProForm>
    </PageContainer>
  );
};

export default EditWorkflow;
