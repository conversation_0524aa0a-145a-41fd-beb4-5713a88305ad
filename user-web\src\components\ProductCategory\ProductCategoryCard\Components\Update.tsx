import { DeleteFilled, EditOutlined, LoadingOutlined, PlusOutlined } from '@ant-design/icons';
import {
  Button,
  Col,
  Form,
  Input,
  InputNumber,
  message,
  Modal,
  Row,
  Upload,
  UploadProps,
} from 'antd';
import { useEffect, useState } from 'react';
const { Item } = Form;

import { listAllPackingUnit } from '@/services/packing_unit';
import { updateItem } from '@/services/products';
import { IIotAgricultureProduct } from '@/services/products/type';
import { listAllUnit } from '@/services/unit';
import { removeFile, uploadFile } from '@/services/uploadFile';
import { generateAPIPath } from '@/services/utils';
import { IIotCategoryGroup } from '@/types/IIotCategoryGroup';
import { ProFormSelect } from '@ant-design/pro-components';
import { FormattedMessage } from '@umijs/max';
import TextArea from 'antd/es/input/TextArea';
import numeral from 'numeral';

const Update = (params: { refreshFnc: any; value: IIotAgricultureProduct }) => {
  const [loading, setLoading] = useState(false);
  const [isOpen, setOpen] = useState(false);
  const [form] = Form.useForm();

  const [imageUrl, setImageUrl] = useState<string>();
  const [uploading, setUploading] = useState(false);
  const [fileList, setFileList] = useState<any[]>([]);

  const showModal = () => {
    form.setFieldsValue(params.value);
    setOpen(true);
  };
  const hideModal = () => {
    setOpen(false);
  };
  const handleOk = () => {
    form.submit();
  };
  const handleCancel = () => {
    hideModal();
    form.resetFields();
  };

  useEffect(() => {
    setImageUrl(params.value.image);
    return () => {
      // Cleanup code (if needed)
    };
  }, []); // Empty dependency array means this effect runs once after the initial render
  const uploadButton = (
    <div>
      {uploading ? <LoadingOutlined /> : <PlusOutlined />}
      <div style={{ marginTop: 8 }}>Upload</div>
    </div>
  );

  const handleRemove = async (file: any) => {
    if (file.status === 'done') {
      try {
        await removeFile({
          fid: file.uid,
          dt: '',
          dn: '',
        });
      } catch (error) {
        message.error('Delete Error,try again!');
      } finally {
      }
    }
  };

  const handleChangeUpload: UploadProps['onChange'] = (info) => {
    let newFileList = [...info.fileList];
    // 1. Limit the number of uploaded files
    // Only to show two recent uploaded files, and old ones will be replaced by the new
    // 2. Read from response and show file link
    newFileList = newFileList.map((file: any) => {
      if (file.response) {
        // Component will show file.url as link
        file.uid = file.response.name;
        const userdata = JSON.parse(localStorage.getItem('token') || '{}');
        file.url = generateAPIPath(
          'api/v2/file/download?file_url=' + file.response.file_url + '&token=' + userdata?.token,
        );
        file.raw_url = file.response.file_url;
      }
      return file;
    });
    setFileList(newFileList);
    if (newFileList.length) {
      setImageUrl(newFileList[0]?.url || '');
    }
  };

  const handleUpload = async (options: any) => {
    const { onSuccess, onError, file } = options;
    try {
      setUploading(true);
      const res = await uploadFile({
        file,
        doctype: '',
        docname: '',
        is_private: 1,
        folder: 'Home/Attachments',
        optimize: false,
      });
      console.log('file_url', res);
      onSuccess(res.message);
    } catch (err) {
      console.log('Eroor: ', err);
      onError({ err });
    } finally {
      setUploading(false);
    }
  };
  return (
    <>
      <Button
        size="small"
        //type="dashed"
        style={{ display: 'flex', alignItems: 'center' }}
        onClick={showModal}
      >
        <EditOutlined />
      </Button>

      <Modal
        title={<FormattedMessage id="action.update" />}
        open={isOpen}
        onOk={handleOk}
        onCancel={handleCancel}
        confirmLoading={loading}
      >
        <Form
          layout="horizontal"
          labelCol={{ span: 24 }}
          labelAlign="left"
          form={form}
          onFinish={async (value: IIotAgricultureProduct) => {
            try {
              value.image = '';
              value.name = params.value.name;
              if (fileList.length) {
                value.image = fileList[0].raw_url;
              }
              //   console.log('value product', value);
              const result = await updateItem(value);
              form.resetFields();
              hideModal();
              message.success('Success!');
              if (params.refreshFnc) {
                await params.refreshFnc();
              }
            } catch (error: any) {
              message.error(JSON.stringify(value));
            } finally {
              setLoading(false);
            }
          }}
        >
          <Row gutter={5}>
            <Row gutter={5}>
              <Col className="gutter-row" md={12}>
                <Item
                  label={<FormattedMessage id="category.product.product_name" />}
                  labelCol={{ span: 24 }}
                  rules={[
                    {
                      required: true,
                      message: 'Bắt buộc điền',
                    },
                  ]}
                  name="label"
                >
                  <Input placeholder="VD: Cà chua bi" />
                </Item>
              </Col>
              <Col className="gutter-row" md={12}>
                <Item
                  label={<FormattedMessage id="category.product.product_code" />}
                  name="iot_product_id"
                >
                  <Input placeholder="VD: CCB2023" />
                </Item>
              </Col>
              <Col className="gutter-row" md={12}>
                <ProFormSelect
                  rules={[
                    {
                      required: true,
                      message: 'Bắt buộc điền',
                    },
                  ]}
                  allowClear
                  label={<FormattedMessage id="category.material-management.packing_unit" />}
                  name="packing_unit_id"
                  request={async () => {
                    const res = await listAllPackingUnit();
                    return res.data.map((item: IIotCategoryGroup) => ({
                      label: item.label,
                      value: item.name,
                    }));
                  }}
                />
              </Col>
              <Col className="gutter-row" md={12}>
                <ProFormSelect
                  rules={[
                    {
                      required: true,
                      message: 'Bắt buộc điền',
                    },
                  ]}
                  allowClear
                  label={<FormattedMessage id="category.material-management.unit" />}
                  name="unit_id"
                  request={async () => {
                    const res = await listAllUnit();
                    return res.data.map((item: IIotCategoryGroup) => ({
                      label: item.label,
                      value: item.name,
                    }));
                  }}
                />
              </Col>
              <Col className="gutter-row" md={12}>
                <Item
                  rules={[
                    {
                      required: true,
                      message: 'Bắt buộc điền',
                    },
                  ]}
                  label={<FormattedMessage id="category.material-management.unit_price" />}
                  name="product_price"
                  // getValueFromEvent={(event) => parseFloat(event.target.value)}
                >
                  <InputNumber
                    style={{ width: '100%' }}
                    formatter={(value, info) => {
                      const formatNum = numeral(value).format('0,0.00');
                      return formatNum;
                    }}
                  />
                </Item>
              </Col>
              <Col className="gutter-row" md={12}>
                <Item
                  rules={[
                    {
                      required: true,
                      message: 'Bắt buộc điền',
                    },
                  ]}
                  label={<FormattedMessage id="category.material-management.conversion_factor" />}
                  name="conversion_factor"
                  // getValueFromEvent={(event) => parseFloat(event.target.value)}
                >
                  <InputNumber
                    style={{ width: '100%' }}
                    formatter={(value, info) => {
                      const formatNum = numeral(value).format('0,0.00');
                      return formatNum;
                    }}
                  />
                </Item>
              </Col>
              <Col className="gutter-row" md={24}>
                <Item
                  label={<FormattedMessage id={'common.form.description'} />}
                  name="description"
                >
                  <TextArea rows={4} />
                </Item>
              </Col>
              <Col className="gutter-row" md={24}>
                <Item
                  label={<FormattedMessage id={'common.form.image'} />}
                  labelCol={{ span: 24 }}
                  name="image"
                >
                  <Upload
                    name="upload-image"
                    listType="picture-card"
                    className="avatar-uploader"
                    showUploadList={false}
                    customRequest={handleUpload}
                    onRemove={handleRemove}
                    onChange={handleChangeUpload}
                    accept="image/png, image/jpeg, image/svg+xml"
                  >
                    {imageUrl ? (
                      <img
                        src={generateAPIPath('api/v2/file/download?file_url=' + imageUrl)}
                        alt="avatar"
                        style={{ width: '100%' }}
                      />
                    ) : (
                      uploadButton
                    )}
                  </Upload>
                </Item>
                {imageUrl ? (
                  <>
                    <Button
                      onClick={() => {
                        setFileList([]);
                        setImageUrl('');
                      }}
                    >
                      <DeleteFilled></DeleteFilled>
                    </Button>
                  </>
                ) : (
                  <></>
                )}
              </Col>

              {/* <Col className="gutter-row" md={24}>
                                <ProFormSelect
                                    rules={[
                                        {
                                            required: true,
                                            message: 'Bắt buộc điền',
                                        },
                                    ]}
                                    allowClear
                                    label="Đơn vị đóng gói"
                                    name="packing_unit_id"
                                    request={async () => {
                                        const res = await listAllPackingUnit();
                                        return res.data.map((item: IIotCategoryGroup) => ({
                                            label: item.label,
                                            value: item.name
                                        }));
                                    }}
                                />
                            </Col> */}
            </Row>
          </Row>
        </Form>
      </Modal>
    </>
  );
};

export default Update;
