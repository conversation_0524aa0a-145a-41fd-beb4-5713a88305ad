import { listAllItem } from '@/services/workType';
import { IIotWorkType } from '@/services/workType/type';
import { EditOutlined } from '@ant-design/icons';
import { ProFormSelect } from '@ant-design/pro-components';
import { FormattedMessage } from '@umijs/max';
import { Button, Col, Form, Input, InputNumber, Modal, Row } from 'antd';
import { useState } from 'react';
const { Item } = Form;

const UpdateWorkTime = (params: { data: any; onFinish: any }) => {
  const [loading, setLoading] = useState(false);
  const [isOpen, setOpen] = useState(false);
  const [form] = Form.useForm();

  const showModal = () => {
    form.setFieldsValue(params.data);
    setOpen(true);
  };

  const hideModal = () => {
    setOpen(false);
  };
  const handleOk = () => {
    form.submit();
  };
  const handleCancel = () => {
    hideModal();
    form.resetFields();
  };

  return (
    <>
      <Button
        size="small"
        //type="dashed"
        style={{ display: 'flex', alignItems: 'center' }}
        onClick={showModal}
      >
        <EditOutlined></EditOutlined>
      </Button>
      <Modal
        title={`Chỉnh sửa vật tư liên quan`}
        open={isOpen}
        onOk={handleOk}
        onCancel={handleCancel}
        confirmLoading={loading}
      >
        <Form
          layout="horizontal"
          labelCol={{ span: 24 }}
          labelAlign="left"
          form={form}
          onFinish={async (values: any) => {
            values.name = params.data?.name;
            await params?.onFinish(values);
            setOpen(false);
            form.resetFields();
          }}
        >
          <Row gutter={5}>
            <Col className="gutter-row" md={24}>
              <ProFormSelect
                rules={[
                  {
                    required: true,
                    message: 'Bắt buộc điền',
                  },
                ]}
                allowClear
                label="Loại công việc"
                name="work_type_id"
                request={async () => {
                  const res = await listAllItem();
                  return res.data.map((item: IIotWorkType) => ({
                    label: item.label,
                    value: item.name,
                  }));
                }}
              />
            </Col>
            <Col className="gutter-row" md={24}>
              <Item label="Số công/giờ làm dự kiến" labelCol={{ span: 24 }} name="exp_quantity">
                <InputNumber style={{ width: '100%' }} min={0} />
              </Item>
            </Col>
            <Col className="gutter-row" md={24}>
              <Item label="Số công/giờ làm thực tế" labelCol={{ span: 24 }} name="quantity">
                <InputNumber style={{ width: '100%' }} min={0} />
              </Item>
            </Col>
            <Col className="gutter-row" md={24}>
              <Item label="Chi phí" labelCol={{ span: 24 }} name="cost">
                <InputNumber style={{ width: '100%' }} min={0} />
              </Item>
            </Col>
            <Col className="gutter-row" md={24}>
              <ProFormSelect
                rules={[
                  {
                    required: true,
                    message: 'Bắt buộc điền',
                  },
                ]}
                allowClear
                label="Đơn vị"
                name="type"
                options={[
                  {
                    label: 'Giờ',
                    value: 'Hour',
                  },
                  {
                    label: 'Công',
                    value: 'Workday',
                  },
                ]}
              />
            </Col>
            <Col className="gutter-row" md={24}>
              <Item
                label={<FormattedMessage id={'common.form.description'} />}
                labelCol={{ span: 24 }}
                name="description"
              >
                <Input.TextArea />
              </Item>
            </Col>
          </Row>
        </Form>
      </Modal>
    </>
  );
};

export default UpdateWorkTime;
