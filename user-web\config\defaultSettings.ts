import { ProLayoutProps } from '@ant-design/pro-components';
const colorList = [
  { key: 'daybreak', color: '#1890ff' },
  { key: 'dust', color: '#F5222D' },
  { key: 'volcano', color: '#FA541C' },
  { key: 'sunset', color: '#FAAD14' },
  { key: 'cyan', color: '#13C2C2' },
  { key: 'green', color: '#52C41A' },
  { key: 'geekblue', color: '#2F54EB' },
  { key: 'purple', color: '#722ED1' },
];
/**
 * @name
 */
const Settings: ProLayoutProps & {
  pwa?: boolean;
  logo?: string;
} = {
  navTheme: 'light',
  // 拂晓蓝
  colorPrimary: '#44c4a1',
  color: '#44c4a1',
  layout: 'mix',
  contentWidth: 'Fluid',
  fixedHeader: false,
  fixSiderbar: true,
  title: 'Trung tâm dữ liệu',
  pwa: false,
  logo: '/viis_logo.svg',
  iconfontUrl: '',
  colorWeak: false,
  token: {
    // bgLayout: '#44c4a1',
    sider: {
      colorMenuBackground: '#FFFFFF',
      colorTextMenuSelected: '#444444',
      colorTextMenuSecondary: '#44c4a1',
      colorBgMenuItemHover: '#44c4a1',
      colorBgMenuItemSelected: '#44c4a1',
      colorBgCollapsedButton: '#ffffff',
      colorBgMenuItemCollapsedElevated: '#44c4a1',
    },
    pageContainer: {
      paddingBlockPageContainerContent: 10,
      colorBgPageContainer: '#fcfcfc',
    },
    header: {
      colorBgHeader: '#503C3C',
      colorTextMenu: '#FFFFFF',
      colorTextMenuSecondary: '#FFFFFF',
      colorTextMenuSelected: '#FFFFFF',
      colorBgMenuItemHover: '#FFFFFF',
      colorBgMenuItemSelected: '#FFFFFF',
      colorHeaderTitle: '#FFFFFF',
      colorTextRightActionsItem: '#FFFFFF',
      colorBgRightActionsItemHover: '#FFFFFF',
    },
    // colorBgContainer: '#f6ffed',
    // 参见ts声明，demo 见文档，通过token 修改样式
    //https://procomponents.ant.design/components/layout#%E9%80%9A%E8%BF%87-token-%E4%BF%AE%E6%94%B9%E6%A0%B7%E5%BC%8F
  },
};

export default Settings;
