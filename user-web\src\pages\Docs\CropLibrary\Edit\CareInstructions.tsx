import { Guide } from '@/types/guide.type';
import { Plant } from '@/types/plant.type';
import { Alert, Form, Space } from 'antd';
import { createStyles } from 'antd-use-styles';
import { FC, useEffect, useState } from 'react';
import EditableFormCard from './components/EditableFormCard';

interface CareInstructionsProps {
  curPlant: Plant;
}

interface UpdatingGuide extends Guide {
  is_new?: boolean;
  is_deleted?: boolean;
}

const useStyles = createStyles(({ token }) => ({
  collapseHeader: {
    backgroundColor: token.colorBgContainer,
    boxShadow: 'none',
    '& .ant-collapse-header': {
      borderBlockEnd: `1px solid ${token.colorBorderSecondary}`,
    },
  },
}));
const CareInstructions: FC<CareInstructionsProps> = ({ curPlant }) => {
  const styles = useStyles();
  const [guideList, setGuideList] = useState<UpdatingGuide[] | undefined>([]);
  const form = Form.useFormInstance();
  useEffect(() => {
    const fetchData = () => {
      setGuideList(curPlant.guide_list);
    };

    fetchData();
  }, [curPlant]);
  const handleMoveUp = (id: number) => {
    if (id === 0) return;
    const updatedItems = guideList ? [...guideList] : [];
    [updatedItems[id], updatedItems[id - 1]] = [updatedItems[id - 1], updatedItems[id]];
    form.setFieldValue(['guide_list', updatedItems[id].name, 'sort_index'], id);
    form.setFieldValue(['guide_list', updatedItems[id - 1].name, 'sort_index'], id - 1);
    setGuideList(updatedItems);
  };

  const handleMoveDown = (id: number) => {
    if (!guideList || id >= guideList.length - 1) return;
    const updatedItems = [...guideList];
    [updatedItems[id], updatedItems[id + 1]] = [updatedItems[id + 1], updatedItems[id]];
    form.setFieldValue(['guide_list', updatedItems[id].name, 'sort_index'], id);
    form.setFieldValue(['guide_list', updatedItems[id + 1].name, 'sort_index'], id + 1);
    setGuideList(updatedItems);
  };
  return (
    <Space direction="vertical" size={16} style={{ width: '100%' }}>
      {guideList ? (
        guideList.map((guide, index) => {
          return (
            <EditableFormCard
              handleMoveDown={() => handleMoveDown(index)}
              handleMoveUp={() => handleMoveUp(index)}
              index={index}
              cardInfo={guide}
              key={guide.name}
              infoType="guide_list"
            />
          );
        })
      ) : (
        <Alert message="Cây này chưa có hướng dẫn nào" type="info" />
      )}
    </Space>
  );
};

export default CareInstructions;
