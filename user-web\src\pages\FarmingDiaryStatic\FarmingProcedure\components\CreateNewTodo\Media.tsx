import FormUploadsPreviewable from '@/components/FormUploadsPreviewable';
import { Card } from 'antd';
import { FC, ReactNode } from 'react';

interface MediaProps {
  children?: ReactNode;
}

const Media: FC<MediaProps> = ({ children }) => {
  return (
    <Card title="Hình ảnh / Videos mô tả">
      <FormUploadsPreviewable fileLimit={10} label={''} formItemName={'imgs'} />
    </Card>
  );
};

export default Media;
