import ActionPopConfirm from '@/components/ActionPopConfirm';
import { generalDelete } from '@/services/sscript';
import { DeleteFilled } from '@ant-design/icons';
import { useAccess } from '@umijs/max';
import { Form, message } from 'antd';
const { Item } = Form;

const DeleteProductionTask = (params: { refreshFnc: any; value: string }) => {
  const removeData = async () => {
    try {
      await generalDelete('iot_production_quantity', params.value);
    } catch (error: any) {
      message.error(error.toString());
    }
  };
  const access = useAccess();
  if (!access.canDeleteAllInPageAccess()) {
    return null;
  }

  return (
    <ActionPopConfirm
      actionCall={removeData}
      refreshData={params.refreshFnc}
      text={<DeleteFilled />}
      //buttonType={'dashed'}
      danger={true}
      size="small"
    ></ActionPopConfirm>
  );
};

export default DeleteProductionTask;
