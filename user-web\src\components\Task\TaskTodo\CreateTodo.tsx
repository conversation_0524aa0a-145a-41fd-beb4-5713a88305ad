import { getCustomerUserList } from '@/services/customerUser';
import { PlusOutlined } from '@ant-design/icons';
import { ProFormSelect } from '@ant-design/pro-components';
import { FormattedMessage, useIntl } from '@umijs/max';
import { Button, Col, Form, Input, Modal, Row } from 'antd';
import { useState } from 'react';
const { Item } = Form;

const CreateTodo = (params: { onFinish: any }) => {
  const [loading, setLoading] = useState(false);
  const [isOpen, setOpen] = useState(false);
  const intl = useIntl();

  const [form] = Form.useForm();

  const showModal = () => {
    setOpen(true);
  };
  const hideModal = () => {
    setOpen(false);
  };
  const handleOk = () => {
    form.submit();
  };
  const handleCancel = () => {
    hideModal();
    form.resetFields();
  };

  return (
    <>
      <Button type="primary" style={{ display: 'flex', alignItems: 'center' }} onClick={showModal}>
        <PlusOutlined /> <FormattedMessage id="common.add_sub_task" />
      </Button>
      <Modal
        title={intl.formatMessage({ id: 'common.add_sub_task' })}
        open={isOpen}
        onOk={handleOk}
        onCancel={handleCancel}
        confirmLoading={loading}
      >
        <Form
          layout="horizontal"
          labelCol={{ span: 24 }}
          labelAlign="left"
          form={form}
          onFinish={async (values: any) => {
            await params?.onFinish(values);
            setOpen(false);
            form.resetFields();
          }}
        >
          <Row gutter={5}>
            <Col className="gutter-row" md={24}>
              <Item
                label={<FormattedMessage id="common.task_name" />}
                labelCol={{ span: 24 }}
                rules={[
                  {
                    required: true,
                    message: intl.formatMessage({ id: 'common.required' }),
                  },
                ]}
                name="label"
              >
                <Input />
              </Item>
            </Col>
            <Col className="gutter-row" md={24}>
              <ProFormSelect
                label={<FormattedMessage id="common.executor" />}
                showSearch
                name="customer_user_id"
                request={async () => {
                  const result = await getCustomerUserList();
                  return result.data.map((item: any) => {
                    return {
                      label: item.last_name + ' ' + item.first_name,
                      value: item.name,
                    };
                  });
                }}
              />
            </Col>
            <Col className="gutter-row" md={24}>
              <Item
                label={<FormattedMessage id="common.description" />}
                labelCol={{ span: 24 }}
                name="description"
              >
                <Input.TextArea />
              </Item>
            </Col>
          </Row>
        </Form>
      </Modal>
    </>
  );
};

export default CreateTodo;
