import { AimOutlined } from '@ant-design/icons';
import { Space } from 'antd';
import GoogleMapReact, { ClickEventValue } from 'google-map-react';
import React, { FC, useCallback, useEffect, useMemo, useState } from 'react';
import SearchBox from './SearchBox';
// import { geocodeByLatLng } from 'react-google-places-autocomplete';

// const getPositionName = async (lat: number, lng: number): Promise<string | null> => {
//   try {
//     const response = await geocodeByLatLng({ lat, lng });
//     if (response && response.length > 0) {
//       return response[0].formatted_address;
//     }
//     return null;
//   } catch (error) {
//     console.error('Error getting position name:', error);
//     return null;
//   }
// };
const containerStyle = {
  width: '100%',
  height: '500px',
  marginTop: '10px',
  position: 'relative',
} satisfies React.CSSProperties;
// https://www.latlong.net/place/da-nang-vietnam-17961.html
const defaultValueCenter = {
  lat: 16.047079,
  lng: 108.20623,
};
type Value = {
  lat: number;
  lng: number;
};
type ProFormMapProps = {
  value?: Value;
  onChange?: (value: Value) => void;
};

const Marker: FC<{
  lat?: number;
  lng?: number;
}> = ({ lat, lng }) => {
  return (
    <div>
      <AimOutlined style={{ color: 'red', fontSize: '20px' }} />
    </div>
  );
};

const ProFormMap: React.FC<ProFormMapProps> = ({ value, onChange }) => {
  const handleMapClick = useCallback(
    (event: ClickEventValue) => {
      // console.log('event: ', event);
      const latLng = {
        lat: event.lat,
        lng: event.lng,
      };
      onChange?.(latLng);

      // const geocoder = new google.maps.Geocoder();
      // geocoder.geocode({ location: latLng }, (results, status) => {
      //   if (status === 'OK' && results?.length) {
      //     const positionName = results[0].formatted_address;
      //     console.log('positionName: ', positionName);
      //     // Do something with the position name
      //   }
      // });
    },
    [onChange],
  );

  const defaultValueCenterFirstLoad = useMemo(() => value || defaultValueCenter, []);
  // set default value for the first load
  useEffect(() => {
    onChange?.(defaultValueCenter);
  }, []);

  const currentValue = useMemo(() => value || defaultValueCenter, [value]);
  // const mapAPIRef = useRef<any>(null);
  const [mapObj, setMapObj] = useState<{
    mapsAPI: any;
    mapAPI: any;
  }>({
    mapAPI: null,
    mapsAPI: null,
  });

  return (
    <div>
      <Space>
        <span>lat: {currentValue.lat}</span>
        <span>lng: {currentValue.lng}</span>
      </Space>

      <div style={containerStyle}>
        {!!mapObj.mapAPI && mapObj.mapsAPI && (
          <SearchBox
            onLatLngChange={(lat, lng) => {
              onChange?.({ lat, lng });
            }}
            mapsAPI={mapObj.mapsAPI}
            mapAPI={mapObj.mapAPI}
          />
        )}
        <GoogleMapReact
          onClick={handleMapClick}
          bootstrapURLKeys={{
            key: 'AIzaSyDYKnSjpv5LYayk2_sSdwpAIgdxyoZDc0k',
            libraries: ['places'],
            region: 'VN',
            language: 'vi',
          }}
          // defaultCenter={defaultValueCenterFirstLoad}
          defaultZoom={15}
          center={currentValue}
          yesIWantToUseGoogleMapApiInternals
          onGoogleApiLoaded={({ maps, map }) => {
            setMapObj({
              mapAPI: map,
              mapsAPI: maps,
            });
          }}
        >
          <Marker lat={currentValue.lat} lng={currentValue.lng} />
        </GoogleMapReact>
      </div>
    </div>
  );
};

export default ProFormMap;
