import { <PERSON><PERSON>, Drawer, Select, Space, TimePicker, Upload } from 'antd';
import React, { useState, useEffect } from 'react';
import { InputNumber, Card, Row, Col, Form, message } from "antd";
import { DownloadOutlined, UploadOutlined } from '@ant-design/icons';



import type { RcFile, UploadFile, UploadProps } from 'antd/es/upload/interface';
import { sscript } from '@/services/sscript';
import { downloadFile, getListFileByDocname, removeFile, uploadFile } from '@/services/uploadFile';


const AttachmentsOfDoctype = ({
    doctype,
    docname,
    is_private = 1,
    folder = "Home/Attachments",
    optimize = false
}: {
    doctype: string,
    docname: string,
    is_private?: number,
    folder?: string,
    optimize?: Boolean
}) => {
    const [loading, setLoading] = useState(false);
    const [uploading, setUploading] = useState(false);
    const [fileList, setFileList] = useState<UploadFile[]>([]);


    const getFileList = async () => {
        try {
            setLoading(true);
            const result = await getListFileByDocname({ doctype, name: docname });
            const _fileList = result.map((f: any) => {
                return {
                    uid: f.name,
                    name: f.file_name,
                    status: 'done',
                    url: "https://iot-cloud.viis.tech/api/file/download?file_url=" + f.file_url + "&dt=" + doctype + "&dn=" + docname
                }
            });
            setFileList(_fileList);
        } catch (error) {

        } finally {
            setLoading(false);
        }
    }

    useEffect(() => {
        getFileList();
    }, []);

    const handleChange: UploadProps['onChange'] = (info) => {
        let newFileList = [...info.fileList];

        // 1. Limit the number of uploaded files
        // Only to show two recent uploaded files, and old ones will be replaced by the new
        // 2. Read from response and show file link
        newFileList = newFileList.map((file: any) => {
            console.log(file);
            if (file.response) {
                // Component will show file.url as link
                file.uid = file.response.name;
                file.url = "https://iot-cloud.viis.tech/api/file/download?file_url=" + file.response.file_url + "&dt=" + doctype + "&dn=" + docname;
            }
            return file;
        });

        setFileList(newFileList);
    };

    const handleUpload = async (options: any) => {
        const { onSuccess, onError, file, onProgress } = options;
        try {
            setUploading(true);
            const res = await uploadFile(
                {
                    file,
                    doctype,
                    docname,
                    is_private,
                    folder,
                    optimize
                }
            );

            onSuccess(res.message);
        } catch (err) {
            console.log("Eroor: ", err);
            const error = new Error("Some error");
            onError({ err });
        } finally {
            setUploading(true);
        }
    };

    const props: UploadProps = {
        onRemove: async (file) => {
            if (file.status === "done") {
                try {
                    await removeFile({
                        fid: file.uid,
                        dt: doctype,
                        dn: docname
                    });
                    const newFileList = fileList.filter((f) => f.uid !== file.uid);
                    console.log("newFileList", newFileList)
                    setFileList([...newFileList]);
                } catch (error) {
                    message.error("Delete Error,try again!");
                } finally {

                }
            }
        },
        fileList,
        multiple: true,
        customRequest: handleUpload,
        onChange: handleChange,
        // showUploadList: {
        //     showDownloadIcon: true,
        //     downloadIcon: <DownloadOutlined></DownloadOutlined>,
        // },
        onDownload: async (file: any) => {
            console.log("download", file);
            try {
                const res = await downloadFile(
                    {
                        file_url: file.url,
                        fid: file.fid,
                        dt: doctype,
                        dn: docname
                    }
                );
                console.log(res);
            } catch (err) {
                console.log("Eroor: ", err);
            } finally {
            }
        }
    };


    return (
        <>
            <Card
                loading={loading}
                title="Attachments"
            >
                <Upload {...props} >
                    <Button icon={<UploadOutlined />}>Upload</Button>
                </Upload>
            </Card>
        </>
    );
};

export default AttachmentsOfDoctype