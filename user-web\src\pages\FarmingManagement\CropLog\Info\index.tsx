import { createEmptyArray } from '@/utils/array';
import { myLazy } from '@/utils/lazy';
import { DescriptionsSkeleton } from '@ant-design/pro-components';
import { FC,ReactNode,Suspense } from 'react';

const ImagePreviewGroupCommon = myLazy(() => import('@/components/ImagePreviewGroupCommon'));

interface CropLogInfoProps {
  children?: ReactNode;
}

const CropLogInfo: FC<CropLogInfoProps> = ({ children }) => {
  return (
    <Suspense fallback={<DescriptionsSkeleton active />}>
      <ImagePreviewGroupCommon
        width={151}
        imgHeight={222}
        listImg={createEmptyArray(3).map(() => ({
          caption: 'Chứng nhận giống cây trồng đạt chuẩn mới',
          src: '',
        }))}
      />
    </Suspense>
  );
};

export default CropLogInfo;
