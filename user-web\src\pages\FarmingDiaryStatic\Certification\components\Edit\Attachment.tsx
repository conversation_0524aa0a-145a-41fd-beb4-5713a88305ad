import FormUploadFiles from '@/components/UploadFIles';
import { useIntl } from '@umijs/max';
import { Card } from 'antd';
import { FC, ReactNode } from 'react';

interface AttachmentProps {
  children?: ReactNode;
  initialFile?: string;
}

const Attachment: FC<AttachmentProps> = ({ children, initialFile }) => {
  const { formatMessage } = useIntl();
  return (
    <Card
      title={formatMessage({ id: 'common.attachments' })}
      style={{ boxShadow: 'none' }}
      bordered={false}
    >
      <FormUploadFiles fileLimit={10} formItemName="document_path" initialImages={initialFile} />
    </Card>
  );
};

export default Attachment;
