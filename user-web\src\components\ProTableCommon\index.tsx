import { useSearchArrayObj<PERSON>y<PERSON><PERSON> } from '@/hook/useSearchArrayObjByKey';
import { ActionType,ParamsType,ProTable,ProTableProps } from '@ant-design/pro-components';
import { Input } from 'antd';
import { cloneDeep,merge } from 'lodash';
import React,{ ReactNode,startTransition,useMemo,useState } from 'react';


interface ProTableCommonProps<
  DataType extends object = object,
  Params extends ParamsType = ParamsType,
  ValueType = 'text',
> extends ProTableProps<DataType, Params, ValueType> {
  toolBarRender?:
    | ((
        action: ActionType | undefined,
        rows: {
          selectedRowKeys?: (string | number)[] | undefined;
          selectedRows?: DataType[] | undefined;
        },
        searchComponent?: ReactNode,
      ) => React.ReactNode[])
    | false;
  myCustomProps?: {
    allData?: DataType[];
    omitSearchKeys?: (keyof DataType)[];
    pickSearchKeys?: (keyof DataType)[];
    onSearchKey?: (val: string) => void;
  };
}

// const ProTableCommonComponent = <DataSource extends object = object, U extends object = object, ValueType = 'text'>(
//   props: ProTableCommonProps<DataSource, U, ValueType>,
//   ref:React.ForwardedRef<HTMLUListElement>
// ) => {
//   return (
//     <ProTable<DataSource, U, ValueType> ref={ref} {...props}/>
//   );
// };

// export const ProTableCommon = forwardRef(ProTableCommonComponent);

const ProTableCommon = <
  DataType extends Record<string, any>,
  Params extends ParamsType = ParamsType,
  ValueType = 'text',
>({
  myCustomProps,
  ...props
}: ProTableCommonProps<DataType, Params, ValueType>) => {
  const [searchKey, setSearchKey] = useState<string>('');

  const defaultTableProps = useMemo<ProTableProps<DataType, Params, ValueType>>(
    () => ({
      pagination: {
        pageSize: 20,
      },
      className: 'table-striped',
      scroll: { x: 'max-content' },
      bordered: true,
      search: false,
    }),
    [],
  );

  const { searching, dataFilter } = useSearchArrayObjByKey(
    myCustomProps?.allData || [],
    searchKey,
    {
      omitKeys: myCustomProps?.omitSearchKeys,
      pickKeys: myCustomProps?.pickSearchKeys,
    },
  );

  const propsMerge = useMemo(() => merge(cloneDeep(defaultTableProps), props), [props]);

  const defaultToolbarDom = (
    <Input.Search
      key={'search2'}
      onSearch={(v) => {
        startTransition(() => {
          setSearchKey(v);
          myCustomProps?.onSearchKey?.(v);
        });
      }}
      placeholder="Search"
    />
  );
  let toolBarRender = undefined;
  if (props.toolBarRender === false) {
    toolBarRender = false;
  } else if (props.toolBarRender === undefined) {
    toolBarRender = () => [<React.Fragment key="search2">{defaultToolbarDom}</React.Fragment>];
  } else if (typeof props.toolBarRender === 'function') {
    toolBarRender = (...arg: any) =>
      (props.toolBarRender as any)(
        ...arg,
        <React.Fragment key="search2">{defaultToolbarDom}</React.Fragment>,
      );
  }

  return (
    <ProTable<DataType, Params, ValueType>
      dataSource={dataFilter}
      {...propsMerge}
      loading={propsMerge.loading || searching}
      toolBarRender={toolBarRender as any}
    />
  );
};

export default ProTableCommon;
