import classNames from 'classnames';
import { FC, ReactNode } from 'react';

interface ActionHoverProps {
  children?: ReactNode;
  actions?: () => ReactNode;
}
const ActionHover: FC<ActionHoverProps> = ({ children, actions }) => {
  return (
    <div className="relative ">
      <div>{children}</div>
      <div
        className={classNames(
          'absolute bg-white bg-opacity-80 backdrop:blur-sm opacity-0 inset-y-0 -right-1/2 invisible  group-hover/action:visible group-hover/action:right-0 group-hover/action:opacity-100 duration-100 ease-out',
        )}
      >
        {actions?.()}
      </div>
    </div>
  );
};

export default ActionHover;
