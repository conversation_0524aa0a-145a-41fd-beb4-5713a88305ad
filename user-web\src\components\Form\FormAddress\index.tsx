import vietnam_location from '@/helpers/tree.json';
import {
  FormInstance,
  ProFormItemProps,
  ProFormSelect,
  ProFormSelectProps,
  ProFormText,
  useDeepCompareEffect,
} from '@ant-design/pro-components';
import { useIntl } from '@umijs/max';
import { DefaultOptionType } from 'antd/es/select';
import { startTransition, useMemo, useState } from 'react';
import {
  getCityCodeByName,
  getDistrictCodeByName,
  getDistrictOptions,
  getWardOptions,
} from './_utils';

interface Props {
  form: FormInstance<any>;
  formProps?: {
    city?: ProFormSelectProps;
    district?: ProFormSelectProps;
    ward?: ProFormSelectProps;
    address?: ProFormItemProps;
  };
  initialValue?: {
    city?: string;
    district?: string;
    ward?: string;
    address?: string;
  };
}
interface OptionsItem {
  label: string;
  value: string;
  code?: string;
}

export default function useFormAddress(props: Props) {
  const intl = useIntl()
  const { formProps, form, initialValue } = props;
  const initialValueKey = JSON.stringify(initialValue || '{}');
  const namePathCity = formProps?.city?.name || 'province';
  const namePathDistrict = formProps?.district?.name || 'district';
  const namePathWard = formProps?.ward?.name || 'ward';
  const namePathDetails = formProps?.address?.name || 'address';

  const cityOptions: OptionsItem[] = useMemo(
    () =>
      Object.keys(vietnam_location).map((key) => {
        return {
          label: vietnam_location[key].name,
          value: vietnam_location[key].name,
          code: key,
        };
      }),
    [initialValueKey],
  );

  const defaultCityCode = initialValue?.city
    ? getCityCodeByName(initialValue.city) || undefined
    : undefined;
  const defaultDistrictOptions = getDistrictOptions(defaultCityCode);

  const defaultWardOptions = getWardOptions({
    cityCode: defaultCityCode,
    districtCode: getDistrictCodeByName({
      districtName: initialValue?.district,
      cityCode: defaultCityCode,
    }),
  });

  const [cityCodeSelected, setCityCodeSelected] = useState<string | undefined>(defaultCityCode);

  const [districtOptions, setDistrictOptions] =
    useState<ProFormSelectProps['options']>(defaultDistrictOptions);
  const [wardOptions, setWardOptions] = useState<ProFormSelectProps['options']>(defaultWardOptions);

  const onCityChange = (
    province: string,
    optionCitySelected: DefaultOptionType | DefaultOptionType[],
  ) => {
    startTransition(() => {
      if (province) {
        const newCityCodeSelected = optionCitySelected['data-item'].code;
        setCityCodeSelected(newCityCodeSelected);
        const newDistrictOptions: OptionsItem[] = getDistrictOptions(newCityCodeSelected);
        setDistrictOptions(newDistrictOptions);
      } else {
        setDistrictOptions([]);
      }
      form.setFieldValue(namePathDistrict, undefined);
      form.setFieldValue(namePathWard, undefined);
    });
  };

  const onDistrictChange = (
    district: string,
    optionDistrictSelected: DefaultOptionType | DefaultOptionType[],
  ) => {
    startTransition(() => {
      if (district) {
        const newDistrictCodeSelected = optionDistrictSelected['data-item']?.code;
        const newWardOptions = getWardOptions({
          cityCode: cityCodeSelected,
          districtCode: newDistrictCodeSelected,
        });

        setWardOptions(newWardOptions);
      } else {
        setWardOptions([]);
      }

      form.setFieldValue(namePathWard, undefined);
    });
  };

  // change initial
  useDeepCompareEffect(() => {
    setDistrictOptions(defaultDistrictOptions);
    setWardOptions(defaultWardOptions);
  }, [defaultWardOptions, defaultDistrictOptions]);
  return useMemo(
    () => ({
      cityElement: (
        <ProFormSelect
          showSearch
          name={namePathCity}
          label={intl.formatMessage({
            id: 'common.province/city',
          })}
          // label="Tỉnh/Thành phố"
          options={cityOptions}
          fieldProps={{
            onChange: onCityChange,
          }}
          {...formProps?.city}
        />
      ),
      districtElement: (
        <ProFormSelect
          showSearch
          name={namePathDistrict}
          label={intl.formatMessage({
            id: 'common.district',
          })}
          // label="Quận/Huyện"
          options={districtOptions}
          fieldProps={{
            onChange: onDistrictChange,
          }}
          {...formProps?.district}
        />
      ),
      wardElement: (
        <ProFormSelect
          showSearch
          label={intl.formatMessage({
            id: 'common.ward',
          })}
          // label="Phường/Xã"
          name={namePathWard}
          options={wardOptions}
          {...formProps?.ward}
        />
      ),
      detailsElement: (
        <ProFormText
          name={namePathDetails}
          label={intl.formatMessage({
            id: 'common.address',
          })}
          // label={'Địa chỉ cụ thể'}
          {...formProps?.address}
        />
      ),
    }),
    [
      namePathCity,
      cityOptions,
      namePathDistrict,
      districtOptions,
      formProps,
      namePathWard,
      wardOptions,
      namePathDetails,
      initialValueKey,
    ],
  );
}
