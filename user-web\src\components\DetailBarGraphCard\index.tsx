import { history } from '@umijs/max';
import { Card } from 'antd';
import { ReactNode } from 'react';

interface Props {
  label: string;
  data?: any;
  comparativePercentage?: number;
  backgroundColor: string;
  link: string;
  customInfo?: ReactNode;
}
const DetailBarGraphCard = ({
  comparativePercentage,
  data,
  label,
  backgroundColor,
  link,
  customInfo: children,
}: Props) => {
  return (
    <Card
      style={{
        height: '100%',
        width: '90%',
        backgroundColor,
        padding: '0px',
        lineHeight: '0.5',
      }}
      hoverable
      onClick={() => {
        history.push(link);
      }}
    >
      <p className="font-medium	">{label}</p>
      {children}
      {/* <Row>
        <Col span={18}>{data}</Col>
        <Col span={6}>{comparativePercentage}</Col>
      </Row> */}
    </Card>
  );
};

export default DetailBarGraphCard;
