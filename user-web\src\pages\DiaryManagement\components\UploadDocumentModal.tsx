import { DOCTYPE_ERP } from '@/common/contanst/constanst';
import { createCropDocument as addCropDocument } from '@/services/cropDocument';
import { uploadFile } from '@/services/fileUpload';
import { IDocument } from '@/types/document.type';
import { UploadOutlined } from '@ant-design/icons';
import { ModalForm, ProForm, ProFormText } from '@ant-design/pro-components';
import { FormattedMessage, useIntl } from '@umijs/max';
import { App, Button, Form, Upload, UploadFile, UploadProps } from 'antd';
import { uniqueId } from 'lodash';
import { useState } from 'react';
interface IFormData {
  name: string;
  documents: UploadFile<any>[];
}
interface UploadDocumentModalProps {
  cropId: string;
  setDocuments: any;
}
const UploadDocumentModal = ({ cropId, setDocuments }: UploadDocumentModalProps) => {
  const intl = useIntl();
  const [loading, setLoading] = useState(false);

  const { message } = App.useApp();
  const [form] = Form.useForm();
  const handleChange: UploadProps['onChange'] = ({ fileList: newFileList }) => {
    form.setFieldValue('documents', newFileList);
  };
  const handleFinish = async (value: IFormData) => {
    setLoading(true);
    console.log({ value });
    const { name, documents } = value;
    const document = documents.at(0);
    const uploadStatus = await uploadFile({
      docType: DOCTYPE_ERP.iotCropDocument,
      docName: uniqueId(document?.fileName),
      file: document?.originFileObj as any,
    });
    console.log(uploadStatus);
    if (!uploadStatus.data) {
      message.error(intl.formatMessage({ id: 'common.error' }));
    }
    let newDoc: IDocument | undefined;

    const res = await addCropDocument({
      crop_id: cropId,
      document_path: uploadStatus.data.message.file_url,
      file_type: uploadStatus.data.message.file_name.split('.').at(1) || '',
      label: name,
    })
      .then(async (res) => {
        newDoc = res;
        message.success(intl.formatMessage({ id: 'common.success' }));
      })
      .catch((error) => {
        console.log(`addCropDocument error: ${JSON.stringify(error)}`);
        message.error({ content: intl.formatMessage({ id: 'common.error' }) });
      });

    setDocuments((prev: any) => [...prev, newDoc as IDocument]);

    setLoading(false);
    return true;
  };
  return (
    <ModalForm<IFormData>
      title={intl.formatMessage({ id: 'diary.add_document' })}
      trigger={
        <Button type="primary" icon={<UploadOutlined />}>
          {intl.formatMessage({ id: 'diary.add_document' })}
        </Button>
      }
      autoFocusFirstInput
      modalProps={{
        destroyOnClose: true,
      }}
      submitter={{
        render: (props, defaultDoms) => {
          return [
            <Button
              key="ok"
              onClick={() => {
                props.submit();
              }}
              type="primary"
              style={{ width: '100%' }}
              loading={loading}
            >
              <FormattedMessage id="action.save" />
            </Button>,
          ];
        },
      }}
      form={form}
      onFinish={handleFinish}
    >
      <ProFormText
        rules={[{ required: true, message: intl.formatMessage({ id: 'common.required_error' }) }]}
        required
        width="lg"
        name="name"
        label={<FormattedMessage id="common.name" />}
      />
      <ProForm.Item
        name="documents"
        required
        label={<FormattedMessage id="common.form.document" />}
        rules={[{ required: true, message: intl.formatMessage({ id: 'common.required_error' }) }]}
      >
        <Upload maxCount={1} accept=".pdf,.png" onChange={handleChange}>
          <Button icon={<UploadOutlined />}>Click to Upload</Button>
        </Upload>
      </ProForm.Item>
    </ModalForm>
  );
};

export default UploadDocumentModal;
