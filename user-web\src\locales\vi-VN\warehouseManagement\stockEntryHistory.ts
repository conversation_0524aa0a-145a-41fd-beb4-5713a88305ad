export default {
  'warehouse-management.stock-entry-history.id': 'Mã', // Id -> Code
  'warehouse-management.stock-entry-history.date': '<PERSON><PERSON><PERSON> ghi nhận', // Posting date -> Recording date
  'warehouse-management.stock-entry-history.customer': '<PERSON><PERSON><PERSON><PERSON> hàng', // Customer -> Customer
  'warehouse-management.stock-entry-history.total-qty': 'Tổng số lượng', // Total quantity -> Total quantity
  'warehouse-management.stock-entry-history.total-price': 'Tổng giá tiền', // Total Price -> Total price
  'warehouse-management.stock-entry-history.item': 'Mã hàng', // Item -> Item Code
  'warehouse-management.stock-entry-history.accepted-quantity': 'Số lượng đã nhận', // Accepted Quantity -> Accepted Quantity
  'warehouse-management.stock-entry-history.rejected-quantity': 'Số lượng đã hủy', // Rejected Quantity -> Rejected Quantity
  'warehouse-management.stock-entry-history.rate': 'Đơn giá', // Rate -> Unit price
  'warehouse-management.stock-entry-history.amount': 'Th<PERSON>nh tiền', // Amount -> Amount
  'warehouse-management.stock-entry-history.detail': '<PERSON> tiết', // Detail -> Detail
  'warehouse-management.stock-entry-history.total_quantity': 'Tổng số lượng', // Total quantity (duplicate) -> Total quantity (duplicate)
  'warehouse-management.stock-entry-history.total_price': 'Tổng giá trị hàng hóa', // Total price (duplicate) -> Total price (duplicate)
  'warehouse-management.stock-entry-history.item_id': 'ID sản phẩm', // Item ID -> Product ID
  'warehouse-management.stock-entry-history.item_label': 'Tên sản phẩm', // Item label -> Product name
  'warehouse-management.stock-entry-history.warehouse_label': 'Kho hàng', // Warehouse label -> Warehouse
  'warehouse-management.stock-entry-history.purpose': 'Loại', // Purpose -> Purpose
  'warehouse-management.stock-entry': 'Nhập/ Huỷ hàng',
};
