// src/models/count.ts
import { getPlantList } from '@/services/plants';
import { Plant } from '@/types/plant.type';
import { useModel } from '@umijs/max';
import jwt_decode from 'jwt-decode';
import { useEffect, useState } from 'react';

export default () => {
  const [myPlant, setMyPlant] = useState<Plant[]>([]);
  const [curPlant, setCurPlant] = useState<Plant>();
  const [loadingResource, setLoadingResource] = useState<any>(false);
  const { initialState } = useModel('@@initialState');

  const getMyPlant = async () => {
    try {
      //get token decode from localstorage
      const token = localStorage.getItem('token');
      const decodedToken: any = token ? jwt_decode(token) : null;

      const sections = decodedToken ? decodedToken.sections : 'NULL';
      // console.log('sections', sections);
      if (sections && !(sections.includes('SYSTEM_ADMIN') || sections.includes('PLANT'))) return;

      setLoadingResource(true);
      const data = await getPlantList({
        filters: [],
        page: 1,
        size: 1000,
        fields: ['label', 'name', 'image'],
      });
      setMyPlant(data?.data || []);
    } catch (error: any) {
      // message.error(error?.toString());
    } finally {
      setLoadingResource(false);
    }
  };
  useEffect(() => {
    if (initialState?.currentUser) {
      getMyPlant();
    }
  }, [initialState]);
  return {
    myPlant,
    setMyPlant,
    loadingResource,
  };
};
