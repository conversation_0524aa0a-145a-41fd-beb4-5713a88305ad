{"compilerOptions": {"target": "esnext", "module": "esnext", "lib": ["dom", "dom.iterable", "esnext"], "allowJs": true, "skipLibCheck": true, "moduleResolution": "node", "importHelpers": true, "noEmit": true, "jsx": "react-jsx", "esModuleInterop": true, "sourceMap": true, "baseUrl": "../../", "strict": true, "resolveJsonModule": true, "allowSyntheticDefaultImports": true, "paths": {"@/*": ["src/*"], "@@/*": ["src/.umi/*"], "@umijs/max": ["D:\\WORK\\PYROJECT\\VIIS\\viis-iot-web-v2\\user-web\\node_modules\\umi"], "@umijs/max/typings": ["src/.umi/typings"]}}, "include": ["../../.umirc.ts", "../../**/*.d.ts", "../../**/*.ts", "../../**/*.tsx"]}