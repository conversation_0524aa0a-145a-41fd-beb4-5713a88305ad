import React from 'react';
import { message } from "antd";
import { generalDelete } from '@/services/sscript';
import ActionPopConfirm from '@/components/ActionPopConfirm';
import usePermission from '@/hook/usePermission';
import { ACTION_PERMISSION } from '@/common/contanst/enum';

const RemoveCropNote = (params: { refreshFnc: any, name: string }) => {
    // const { hasActionPermission } = usePermission("Class");
    // const actionPermission = hasActionPermission([ACTION_PERMISSION.DELETE]);

    const removeEnity = async () => {
        try {
            await generalDelete("iot_Crop_note", params.name);
        } catch (error: any) {
            message.error(error.toString())
        }
    };
    return (
        <>
            {<ActionPopConfirm
                actionCall={removeEnity}
                refreshData={params.refreshFnc}
                text={"Remove"}
                buttonType={"primary"}
                danger={true}
            ></ActionPopConfirm>}
        </>

    );
};

export default RemoveCropNote;