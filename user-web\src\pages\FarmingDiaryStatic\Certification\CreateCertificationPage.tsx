import { PageContainer } from '@ant-design/pro-components';
import { FC, ReactNode } from 'react';
import DocumentCreate from './components/Create';

interface CreateCertificationPageProps {
  children?: ReactNode;
}

const CreateCertificationPage: FC<CreateCertificationPageProps> = ({ children }) => {
  return (
    <PageContainer>
      <DocumentCreate />
    </PageContainer>
  );
};

export default CreateCertificationPage;
