import { getCropIndividualItemStatistic, getCropStatisticItems } from '@/services/cropStatistic';
import { ICropStatisticItem } from '@/types/cropStatisticItem.type';
import { EyeOutlined } from '@ant-design/icons';
import { ActionType, ProColumns, ProTable } from '@ant-design/pro-components';
import { FormattedMessage } from '@umijs/max';
import { App, Button } from 'antd';
import moment from 'moment';
import numeral from 'numeral';
import { useEffect, useRef, useState } from 'react';
import { IFilter } from '../CustomizeInfoCard';
import PopupStatisticChart from '../PopupStatisticChart';
import RealityColumnRender from '../RealityColumnRender';
export interface IData {
  labels: string[];
  exp_quantities: number[];
  quantities: number[];
}
interface Props {
  filter: IFilter;
}
const CropItemTable = ({ filter }: Props) => {
  const { message } = App.useApp();
  const actionRef = useRef<ActionType>();
  const [currentItem, setCurrentItem] = useState<ICropStatisticItem>();
  const [isModalOpen, setIsModalOpen] = useState(false);
  const queryingData = async (record: ICropStatisticItem, filter: IFilter) => {
    const res = await getCropIndividualItemStatistic({
      cropList: filter.cropList,
      category_id: record?.iot_category_id || '',
      end_date: filter.end_date,
      start_date: filter.start_date,
      type: filter.type,
      group_by: 'iot_category_id',
    });
    const data = res?.data.reduce(
      (acc, ele) => {
        acc.labels.push(moment(ele.interval_start).format('DD/MM/YYYY'));
        acc.exp_quantities.push(Number(ele.total_exp_quantity));
        acc.quantities.push(Number(ele.total_quantity));

        return acc;
      },
      { labels: [], exp_quantities: [], quantities: [] } as IData,
    );
    if (data) {
      const formattedData = {
        labels: data.labels,
        datasets: [
          {
            type: 'bar' as const,
            label: 'Giá trị dự kiến',
            data: data.exp_quantities,
            backgroundColor: '#FFD859',
          },
          {
            type: 'bar' as const,
            label: 'Giá trị thực tế',
            data: data.quantities,
            backgroundColor: '#44C4A1',
          },
        ],
      };
      return {
        generalInfos: res.sum,
        selectedData: formattedData,
      };
    }
    return {
      generalInfos: undefined,
      selectedData: undefined,
    };
  };
  const handlePopup = async (e: ICropStatisticItem) => {
    setIsModalOpen(true);
    setCurrentItem(e);
  };
  const columns: ProColumns<ICropStatisticItem>[] = [
    {
      title: 'Xem chi tiết',
      dataIndex: 'quantity',
      render(dom, entity, index, action, schema) {
        return <Button icon={<EyeOutlined />} onClick={() => handlePopup(entity)}></Button>;
      },
      search: false,
      width: 80,
    },
    {
      title: 'Danh mục vật tư',
      dataIndex: 'label',
      width: 200,
    },
    {
      title: <FormattedMessage id="category.material-management.unit" defaultMessage="unknown" />,
      dataIndex: 'item_unit_label',
      width: 130,
    },

    {
      title: 'Tổng dự kiến',
      dataIndex: 'total_exp_quantity',
      width: 130,
    },

    {
      title: 'Tổng chênh lệch',
      dataIndex: 'total_loss_quantity',
      render(dom, entity: any, index, action, schema) {
        const diff = entity.total_exp_quantity - entity.total_quantity;
        return numeral(diff).format('0,0.00');
      },
      width: 130,
    },

    {
      title: 'Tổng thực tế',
      dataIndex: 'total_quantity',
      width: 130,
      render(dom, entity, index, action, schema) {
        return <RealityColumnRender>{dom}</RealityColumnRender>;
      },
    },
  ];
  useEffect(() => {
    actionRef.current?.reload();
  }, [filter]);

  return (
    <>
      <PopupStatisticChart
        currentItem={currentItem}
        inputFilter={filter}
        isModalOpen={isModalOpen}
        queryingData={queryingData}
        setIsModalOpen={setIsModalOpen}
        title={`Thống kê vật tư - ${currentItem?.label}`}
      />
      <ProTable<ICropStatisticItem>
        headerTitle="Thống kê vật tư"
        columns={columns}
        search={false}
        actionRef={actionRef}
        pagination={false}
        size="small"
        request={async (params, sorter) => {
          try {
            const res = await getCropStatisticItems({
              page: 1,
              size: 1000,
              cropList: filter.cropList,
              end_date: filter.end_date,
              start_date: filter.start_date,
              type: filter.type,
            });
            return {
              data: res.data.map((item, key) => ({
                ...item,
                key,
              })),
              success: true,
              total: res.pagination.totalElements,
            };
          } catch (error: any) {
            message.error(`Error when getting Crop Statistic Items: ${error.message}`);
            return {
              success: false,
            };
          }
        }}
      />
    </>
  );
};

export default CropItemTable;
