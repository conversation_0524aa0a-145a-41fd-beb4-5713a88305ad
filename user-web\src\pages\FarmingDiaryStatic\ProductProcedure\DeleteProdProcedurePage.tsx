import ActionModalConfirm from '@/components/ActionModalConfirm';
import { FC } from 'react';
import useDelete from './hooks/useDelete';

interface DeleteProdProcedureProps {
  id: string;
  onSuccess?: () => void;
}

const DeleteProdProcedure: FC<DeleteProdProcedureProps> = ({ id, onSuccess }) => {
  const { run } = useDelete({
    onSuccess,
  });
  return (
    <ActionModalConfirm
      isDelete
      modalProps={{
        async onOk(...args) {
          await run(id);
          return true;
        },
      }}
    />
  );
};

export default DeleteProdProcedure;
