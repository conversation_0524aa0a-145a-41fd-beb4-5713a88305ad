import { DOCTYPE_ERP } from '@/common/contanst/constanst';
import FormUploadsPreviewable from '@/components/FormUploadsPreviewable';
import { generalUpdate, sscriptGeneralList } from '@/services/sscript';
import { getFullImgUrlArrString } from '@/utils/image';
import { ModalForm, ProFormText } from '@ant-design/pro-components';
import { FormattedMessage, useIntl, useModel, useRequest } from '@umijs/max';
import { App, Button, Form, UploadFile } from 'antd';
import 'antd/es/modal/style';
import 'antd/es/slider/style';
import { RcFile } from 'antd/es/upload';
import { FC, ReactNode, useState } from 'react';
import { documentType } from '../_util';
interface CreateCropProps {
  id: string;
  children?: ReactNode;
  onSuccess?: () => void;
  open?: boolean;
  openChange?: (open: boolean) => void;
}
const getBase64 = (file: RcFile): Promise<string> =>
  new Promise((resolve, reject) => {
    const reader = new FileReader();
    reader.readAsDataURL(file);
    reader.onload = () => resolve(reader.result as string);
    reader.onerror = (error) => reject(error);
  });

interface IFormData {
  label: string;
  image: UploadFile<any>[];
}
type DocDetailRes = {
  name: string;
  creation: string;
  modified: string;
  modified_by: string;
  owner: string;
  docstatus: number;
  idx: number;
  label: string;
  image: string;
  type: string;
  _user_tags: any;
  _comments: any;
  _assign: any;
  _liked_by: any;
  customer: any;
  customer_id: string;
  hhhh: any;
  sort_index: number;
};
const EditDocCard: FC<CreateCropProps> = ({ onSuccess, open, openChange, id }) => {
  const { formatMessage } = useIntl();
  const { message } = App.useApp();

  const [form] = Form.useForm<IFormData>();
  const { reload } = useModel('Docs');
  const [submitting, setSubmitting] = useState<boolean>(false);

  const handleFinish = async (values: IFormData) => {
    try {
      setSubmitting(true);
      await generalUpdate(DOCTYPE_ERP.iotPlant, id, {
        data: {
          label: values.label,
          image: values.image,
          type: documentType.type,
        },
      });

      reload();
      onSuccess?.();
      message.success(
        formatMessage({
          id: 'common.success',
        }),
      );

      return true;
    } catch (error) {
      message.error({ content: `Lỗi khi cập nhật tài liệu: ${error}`, duration: 5 });
    } finally {
      setSubmitting(false);
    }
  };
  const { data, loading } = useRequest(
    async () => {
      const res = await sscriptGeneralList({
        doc_name: 'iot_plant',
        filters: [
          ['iot_plant', 'name', '=', id],
          ['iot_plant', 'type', '=', documentType.type],
        ],
        page: 1,
        size: 1,
        // fields: ['label', 'name', 'image'],
        // order_by:"creation desc"
      });
      const data = res.data?.[0] as DocDetailRes;
      if (!data) throw new Error('Không tìm thấy tài liệu');
      return {
        data: data,
      };

      // setMyPlant(data?.data || []);
    },
    {
      onSuccess: (data) => {
        form.setFieldsValue({
          label: data.label,
          image: data.image as any
        });
      },
      onError: (err) => {
        message.error(err.message);
      },
    },
  );

  return (
    <ModalForm<IFormData>
      width="400px"
      title={<FormattedMessage id="common.create_doc" />}
      open={open}
      onOpenChange={openChange}
      autoFocusFirstInput
      modalProps={{
        destroyOnClose: true,
      }}
      form={form}
      submitter={{
        render: (props, defaultDoms) => {
          return [
            <Button
              key="ok"
              onClick={() => {
                props.submit();
              }}
              type="primary"
              style={{ width: '100%' }}
              loading={submitting}
            >
              <FormattedMessage id="action.save" />
            </Button>,
          ];
        },
      }}
      submitTimeout={2000}
      onFinish={handleFinish}
      loading={loading || undefined}
    >
      <ProFormText
        rules={[{ required: true }]}
        required
        width="lg"
        name="label"
        label={formatMessage({
          id: 'common.name',
        })}
      />
      <FormUploadsPreviewable
        formItemName={'image'}
        label={formatMessage({
          id: 'common.form.image',
        })}
        fileLimit={1}
        initialImages={data?.image}
      />
      {/* <ProForm.Item
        name="image"
        required
        label={formatMessage({
          id: 'common.form.image',
        })}
        rules={[{ required: true }]}
      >
        <ImgCrop rotationSlider>
          <Upload
            defaultFileList={
              data?.image
                ? [
                    {
                      uid: data.image,
                      url: getFullImgUrlArrString(data.image)?.[0],
                      name: data.image,
                      status: 'done',
                    },
                  ]
                : []
            }
            listType="picture-card"
            onChange={handleChange}
            onPreview={handlePreview}
            maxCount={1}
            accept="image/x-png,image/jpeg,image/png"
          >
            Upload
          </Upload>
        </ImgCrop>
      </ProForm.Item>
      <Modal open={previewOpen} title={previewTitle} footer={null} onCancel={handleCancel}>
        <img alt="example" style={{ width: '100%' }} src={previewImage} />
      </Modal> */}
    </ModalForm>
  );
};

export default EditDocCard;
