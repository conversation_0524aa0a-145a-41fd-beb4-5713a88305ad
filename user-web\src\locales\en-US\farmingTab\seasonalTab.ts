export default {
  // Season name
  'seasonalTab.season': 'Season Name',
  // Create a new season
  'seasonalTab.createSeason': 'Create New Crop',
  // Ongoing
  'seasonalTab.ongoing': 'Ongoing',
  // Completed
  'seasonalTab.completed': 'Completed',
  // All
  'seasonalTab.all': 'All',
  // Overview
  'seasonalTab.overview': 'Overview',
  // Care
  'seasonalTab.care': 'Care',
  // Pest
  'seasonalTab.pest': 'Pest',
  'seasonalTab.health': 'Health',

  // Note
  'seasonalTab.note': 'Note',
  // Planting information
  'seasonalTab.plantingInformation': 'Planting Information',
  // Cultivation diary
  'seasonalTab.cultivationDiary': 'Cultivation Diary',
  // Participants
  'seasonalTab.participants': 'Participants',
  // Detail information
  'seasonalTab.detailInformation': 'Detail Information',
  // Season name
  'seasonalTab.seasonName': 'Season Name',
  // Select area
  'seasonalTab.selectArea': 'Select Area',
  // Delete season
  'seasonalTab.deleteSeason': 'Delete Season',
  // Cultivation area
  'seasonalTab.cultivationArea': 'Cultivation Area',
  // Expected output - kg
  'seasonalTab.expectedOutputInKg': 'Expected Output - Kg',
  // Select type of plant
  'seasonalTab.selectTypeOfPlant': 'Select Type of Plant',
  // Basic statistics
  'seasonalTab.basicStatistics': 'Basic Statistics',
  // Supplies statistics
  'seasonalTab.suppliesStatistics': 'Supplies Statistics',
  // Supplies list
  'seasonalTab.suppliesList': 'Material List',
  // Total expected
  'seasonalTab.totalExpected': 'Total Expected',
  'seasonalTab.expected': 'Expected',
  //Total used
  'seasonalTab.totalUsed': 'Total Used',
  'seasonalTab.used': 'Used',
  // Total reality
  'seasonalTab.totalReality': 'Total Reality',
  'seasonalTab.reality': 'Reality',
  // Total loss
  'seasonalTab.totalLoss': 'Total Difference',
  //Total Issue
  'seasonalTab.totalIssue': 'Total Issued',
  'seasonalTab.issue': 'Issued',
  // Unit
  'seasonalTab.unit': 'Unit',
  // Remaining
  'seasonalTab.remaining': 'Remaining',
  // Output statistics
  'seasonalTab.outputStatistics': 'Product Statistics',
  // Product list
  'seasonalTab.productList': 'Product List',
  // Labor and cost statistics
  'seasonalTab.laborAndCostStatistics': 'Labor and Cost Statistics',
  // Labor list
  'seasonalTab.laborList': 'Labor List',
  // Total expected labor
  'seasonalTab.totalExpectedLabor': 'Total Expected Labor',
  // Total cost
  'seasonalTab.totalCost': 'Total Cost',
  // Related supplies
  'seasonalTab.relatedSupplies': 'Related Materials',
  // Related stage
  'seasonalTab.relatedStage': 'Related States',
  // Related labor
  'seasonalTab.relatedLabor': 'Related Labor',
  // Plant information
  'seasonalTab.plantInformation': 'Plant Information',
  // Care guide
  'seasonalTab.careGuide': 'Care Guide',
  // General information
  'seasonalTab.generalInformation': 'General Information',
  'seasonalTab.time_completed': 'Time completed',
  'seasonalTab.cultivation_area': 'Cultivation area (m2)',
};
