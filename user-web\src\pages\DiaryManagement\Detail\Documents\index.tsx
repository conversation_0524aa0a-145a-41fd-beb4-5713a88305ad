import { IDocument } from '@/types/document.type';
import { TableSkeleton } from '@ant-design/pro-components';
import { List } from 'antd';
import { Suspense } from 'react';
import DocumentCard from '../../components/DocumentCard';

interface DocumentsProps {
  cropId: string;
  documents: IDocument[];
  setDocuments: any;
}
const Documents = ({ cropId, documents, setDocuments }: DocumentsProps) => {
  return (
    <Suspense fallback={<TableSkeleton active />}>
      <List
        grid={{
          column: 6,
          gutter: 10,
          lg: 3,
          md: 2,
          sm: 2,
          xs: 1,
        }}
        dataSource={documents}
        renderItem={(item) => (
          <List.Item>
            <DocumentCard
              title={item.label}
              id={item.name}
              url={item.document_path}
              documents={documents}
              setDocuments={setDocuments}
            />
          </List.Item>
        )}
      />
    </Suspense>
  );
};

export default Documents;
