import { DEFAULT_PAGE_SIZE_ALL } from '@/common/contanst/constanst';
import { getMemberTypeList } from '@/services/diary-2/business';
import { ProForm, ProFormText } from '@ant-design/pro-components';
import { useIntl, useRequest } from '@umijs/max';
import { Button, Divider, Select, SelectProps, Tooltip } from 'antd';
import { nanoid } from 'nanoid';
import { FC, useState } from 'react';
import useCreate from '../hooks/useCreate';
import DeleteBusinessMemberType from './DeleteBusinessMemberType';

type SelectOrCreateMemberTypeProps<T = any> = SelectProps<T>;

const FormCreate: FC<{ onSuccess: () => void }> = ({ onSuccess }) => {
  const [form] = ProForm.useForm();
  const { formatMessage } = useIntl();
  const { run, loading } = useCreate();

  const onSubmit = async (values: any) => {
    await run({
      label: values.label,
    });
    form.resetFields();
    onSuccess?.();
    return true;
  };

  return (
    <div style={{ padding: '8px 12px', cursor: 'pointer' }}>
      <ProForm loading={loading} form={form} submitter={false} onFinish={onSubmit}>
        <div className="flex gap-2 items-start">
          <ProFormText width="sm" name="label" />
          <Button loading={loading} type="primary" htmlType="submit">
            {formatMessage({ id: 'common.add' })}
          </Button>
        </div>
      </ProForm>
    </div>
  );
};

const SelectOrCreateMemberType: FC<SelectOrCreateMemberTypeProps> = ({ ...props }) => {
  const [key, setKey] = useState(nanoid());
  const [open, setOpen] = useState(false);

  const { data = [], refresh } = useRequest(async () => {
    const res = await getMemberTypeList({
      page: 1,
      size: DEFAULT_PAGE_SIZE_ALL,
    });
    const data = res.data.map((item) => ({
      label: item.label,
      value: item.name,
    }));
    return {
      data: data,
    };
  }, {});

  const onSuccess = async () => {
    setKey(nanoid());
    refresh();
  };

  return (
    <Select
      key={key}
      open={open}
      onDropdownVisibleChange={setOpen}
      dropdownRender={(menu) => (
        <>
          {menu}
          <Divider style={{ margin: '8px 0' }} />
          <FormCreate onSuccess={onSuccess} />
        </>
      )}
      {...props}
    >
      {data.map((item) => (
        <Select.Option key={item.value} value={item.value}>
          <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
            {item.label}
            {open && (
              <Tooltip title="Remove">
                <div
                  onClick={(e) => e.stopPropagation()}
                  style={{ cursor: 'pointer', display: 'inline-block' }}
                >
                  <DeleteBusinessMemberType id={item.value} key={item.value} onSuccess={refresh} />
                </div>
              </Tooltip>
            )}
          </div>
        </Select.Option>
      ))}
    </Select>
  );
};

export default SelectOrCreateMemberType;
