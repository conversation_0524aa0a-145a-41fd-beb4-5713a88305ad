import { useMQTT } from '@/contexts/MQTTcontext';
import { useWelcome } from '@/pages/Welcome/welcomecontext';
import { resetAlarm } from '@/services/awsthings';
import type { Gateway, Sensor } from '@/types/Resource';
import { ReloadOutlined } from '@ant-design/icons';
import { Button, Card, Col, Empty, Row, Switch, Typography } from 'antd';
import { useEffect, useState } from 'react';
import styles from './ListDevices.less';
import { getLatestDataBySensor } from '@/services/awsthings';

export const ListDevices = () => {
  const { currentLocation } = useWelcome();
  const { senserData, senserAlarm } = useMQTT();
  const [listSensor, setListSensor] = useState([]);

  const allSensorsInStore = currentLocation?.gateways?.reduce(
    (res: Sensor[], cur: Gateway) => [...res, ...(cur.sensors || [])],
    [],
  );

  const getSensorValue = (id: any) => {
    if (listSensor.length < 1) return "";
    const sensor = listSensor.filter((s: any) => s.sensor_id == id);
    if (sensor.length > 0) {
      let data = sensor[0]?.value?.value;
      if (senserData) {
        if (typeof senserData[id.toString()] !== "undefined") {
          data = senserData[id.toString()];
        }
        else {
          data = sensor[0]?.value?.value;
        }
      }
      else {
        data = sensor[0].value.value;
      }
      if (sensor[0].reg_type < 3) {
        return data ? sensor[0].bool_on_text : sensor[0].bool_off_text;
      }
      else {
        return `${data.toFixed(2)} ${sensor[0].unit}`;
      }
    }
    else {
      return ""
    }
  };

  const getData = async () => {
    try {
      const sensor = allSensorsInStore?.map(s => s.id) || [];
      if (sensor.length > 0) {
        const resullt = await getLatestDataBySensor({ sensor: sensor });
        setListSensor(resullt.data);
      }
    } catch (error) {
      console.log(error);
    }
  }

  useEffect(() => {
    getData();
  }, [currentLocation]);

  return (
    <Card className={styles.card} size="small" title="Danh sách cảm biến" extra={<a href='/monitor/sensor'>Chi tiết</a>}>
      {!allSensorsInStore?.length && (
        <Empty
          imageStyle={{
            height: 60,
          }}
          description="Không có cảm biến nào"
        />
      )}
      {allSensorsInStore?.map((d: Sensor) => (
        <Card className="device" key={d.name}>
          <Row align="middle" justify="center">
            <Col lg={7}>
              {senserAlarm?.[d.id || ''] !== undefined ? (
                <Button
                  danger
                  size="small"
                  type="primary"
                  onClick={() => resetAlarm({ sensor_id: d.id })}
                  icon={<ReloadOutlined />}
                >
                  Reset
                </Button>
              ) : (
                <></>
              )}
            </Col>
            <Col lg={17}>
              <Row align="middle" justify="center">
                <Col lg={24}>
                  <Typography.Text strong className="deviceName">
                    #{d.id}-{d.name}
                  </Typography.Text>
                </Col>
                <Col lg={24}>
                  {getSensorValue(d.id)}
                  {/* {(senserData?.[d.id || ''] as any)?.toFixed(2) || '-'}{' '}
                  {senserUnit?.[d.id || ''] || '-'} */}
                </Col>
              </Row>
            </Col>
          </Row>
        </Card>
      ))}
    </Card>
  );
};
