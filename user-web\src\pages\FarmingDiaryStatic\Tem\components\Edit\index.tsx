import {
  ProForm,
  ProFormDatePicker,
  ProFormDigit,
  ProFormGroup,
  ProFormRadio,
  ProFormText,
  ProFormTextArea,
} from '@ant-design/pro-components';
import { useIntl } from '@umijs/max';
import { FC, ReactNode } from 'react';

interface EditEnterpriseProps {
  children?: ReactNode;
  id: string;
}
const width = 'lg';
const EditTem: FC<EditEnterpriseProps> = ({ children }) => {
  const onFinish = async () => {};
  const [form] = ProForm.useForm();
  const { formatMessage } = useIntl();
  return (
    <ProForm onFinish={onFinish}>
      <ProFormGroup>
        <ProFormRadio.Group
          width={'lg'}
          options={[
            {
              label: formatMessage({
                id: 'common.confirmation_tem',
              }),
              value: '1',
            },
            {
              label: formatMessage({
                id: 'common.production_tem',
              }),
              value: '1',
            },
          ]}
        ></ProFormRadio.Group>
        <ProFormDatePicker label={formatMessage({ id: 'common.time' })} width={'lg'} />

        {/* <ProFormText label="Doanh nghiệp" width={width} name="email" /> */}
      </ProFormGroup>
      <ProFormGroup>
        <ProFormText label={formatMessage({ id: 'common.name' })} />
        <ProFormDigit
          label={formatMessage({ id: 'common.starting_number' })}
          width={width}
          name="url"
        />
        <ProFormDigit
          label={formatMessage({ id: 'common.ending_number' })}
          width={width}
          name="url"
        />
      </ProFormGroup>
      <ProFormTextArea label={formatMessage({ id: 'common.note' })} name="url" />
    </ProForm>
  );
};

export default EditTem;
