import { DEFAULT_FALLBACK_IMG } from '@/common/contanst/img';
import FormUploadFiles from '@/components/UploadFIles';
import { Process } from '@/services/diary-2/process';
import { genDownloadUrl } from '@/utils/file';
import { Card } from 'antd';
import { FC, ReactNode } from 'react';

interface CertificationProps {
  children?: ReactNode;
  data?: Process | null;
}

const Certification: FC<CertificationProps> = ({ children, data }) => {
  return (
    <div className="grid grid-cols-4 gap-4">
      {data?.documents?.map((item) => (
        <Card key={item.name} title={item.label}>
          {/* Hiển thị hình ảnh thumbnail */}
          <div className="mb-2">
            <img
              src={item.document_path ? genDownloadUrl(item.document_path) : DEFAULT_FALLBACK_IMG}
              alt={item.label}
              style={{ width: '100%', height: 'auto', borderRadius: '8px' }}
            />
          </div>
          {/* Hiển thị form upload file */}
          <FormUploadFiles
            isReadonly
            initialImages={item.document_path}
            fileLimit={1000}
            formItemName={[]}
            showUploadButton={false}
          />
        </Card>
      ))}
    </div>
  );
};

export default Certification;
