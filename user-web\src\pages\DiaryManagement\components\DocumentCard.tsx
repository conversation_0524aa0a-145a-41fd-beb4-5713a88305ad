import { deleteCropDocument } from '@/services/cropDocument';
import { IDocument } from '@/types/document.type';
import { genDownloadUrl } from '@/utils/file';

import documentIcon from '@/assets/img/icons/documents_icon.svg';
import { DeleteOutlined } from '@ant-design/icons';
import { useAccess } from '@umijs/max';
import { Card, message, Popconfirm, Typography } from 'antd';
import Meta from 'antd/es/card/Meta';
import { Fragment } from 'react';
const { Text } = Typography;

interface Props {
  title?: string;
  id: string;
  url: string;
  documents: IDocument[];
  setDocuments: any;
}
const DocumentCard = ({ id, title, url, documents, setDocuments }: Props) => {
  async function handleDelete() {
    await deleteCropDocument(id)
      .then((res) => {
        const newDocuments = documents.filter((doc) => doc.name !== id);
        setDocuments(newDocuments);
        message.success(`Xoá thành công`);
      })
      .catch((error) => {
        message.error(`Lỗi khi xoá ${title}: ${error}`);
      });
  }
  const access = useAccess();
  return (
    <Card
      style={{ overflow: 'hidden' }}
      onClick={() => {
        window.open(genDownloadUrl(url), '_blank');
      }}
      cover={
        <img
          alt="document-icon"
          src={documentIcon}
          style={{ width: '50%', display: 'block', marginLeft: 'auto', marginRight: 'auto' }}
        />
      }
      hoverable
      actions={[
        <Fragment key="delete">
          {access.canDeleteAllInPageAccess() && (
            <Popconfirm
              title="Xoá"
              description={`Bạn có muốn xoá tài liệu ${title}?`}
              onConfirm={() => handleDelete()}
              key="delete"
              onPopupClick={(e) => {
                e.stopPropagation();
              }}
            >
              <DeleteOutlined
                key="delete"
                onClick={(e) => {
                  e.stopPropagation();
                }}
              />
            </Popconfirm>
          )}
        </Fragment>,
      ]}
    >
      <Meta title={<Text ellipsis={true}>{title}</Text>} />
    </Card>
  );
};

export default DocumentCard;
