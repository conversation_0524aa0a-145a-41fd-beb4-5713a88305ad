import CardCollapse from '@/components/CardCollapse';
import FormUploadsPreviewable from '@/components/FormUploadsPreviewable';
import { Guide } from '@/types/guide.type';
import { InfoTab } from '@/types/infoTab.type';
import { genDownloadUrl } from '@/utils/file';
import { DeleteFilled } from '@ant-design/icons';
import { ProForm,ProFormText,ProFormTextArea } from '@ant-design/pro-components';
import { useAccess } from '@umijs/max';
import { Badge,Button,Form,Space } from 'antd';
import { FC,useState } from 'react';

interface EditableFormCardProps {
  cardInfo: (Guide | InfoTab) & { is_new?: boolean; is_deleted?: boolean };
  infoType: 'guide_list' | 'infor_tab_list';
  index: number;
  handleMoveDown?: (index: number) => void;
  handleMoveUp?: (index: number) => void;
}

const EditableFormCard: FC<EditableFormCardProps> = ({
  infoType,
  cardInfo,
  index,
  handleMoveDown,
  handleMoveUp,
}) => {
  const [isDeleted, setIsDeleted] = useState(false);
  const form = Form.useFormInstance();
  const handleToggleDelete = () => {
    setIsDeleted(!isDeleted);
    form.setFieldValue([infoType, cardInfo.name, 'is_deleted'], !isDeleted);
  };
  const childHandleMoveDown = () => {
    if (handleMoveDown) handleMoveDown(index);
  };
  const childHandleMoveUp = () => {
    if (handleMoveUp) handleMoveUp(index);
  };
  const access = useAccess()
  return (
    <CardCollapse
      handleMoveDown={handleMoveDown && childHandleMoveDown}
      handleMoveUp={handleMoveUp && childHandleMoveUp}
      key={cardInfo.name}
      title={cardInfo.label}
      titleIcon={
        cardInfo.icon && (
          <img style={{ width: '32px', height: 'auto' }} src={genDownloadUrl(cardInfo.icon)} />
        )
      }
      extra={
        <Space split size="middle">
          {isDeleted ? (
            <Badge status="error" text="Đã xoá" />
          ) : (
            cardInfo?.is_new && <Badge status="processing" text="Mới" />
          )}
          {access.canDeleteAllInPageAccess() && (
            <Button icon={<DeleteFilled />} type="text" onClick={handleToggleDelete} />
          )}
        </Space>
      }
    >
      {/* <ProForm.Item
        name={[infoType, cardInfo.name, 'image']}
        initialValue={cardInfo.image}
        style={{ display: 'none' }}
      /> */}
      <ProForm.Item
        name={[infoType, cardInfo.name, 'is_new']}
        initialValue={cardInfo.is_new}
        style={{ display: 'none' }}
      />
      <ProForm.Item
        name={[infoType, cardInfo.name, 'is_deleted']}
        initialValue={cardInfo.is_deleted}
        style={{ display: 'none' }}
      />
      <ProForm.Item
        name={[infoType, cardInfo.name, 'name']}
        initialValue={cardInfo.name}
        style={{ display: 'none' }}
      />
      {/* <ProForm.Item
        name={[infoType, cardInfo.name, 'icon']}
        initialValue={cardInfo.icon}
        style={{ display: 'none' }}
      /> */}
      <ProForm.Item
        name={[infoType, cardInfo.name, 'sort_index']}
        initialValue={cardInfo.sort_index}
        style={{ display: 'none' }}
      />
      <ProFormText
        rules={[{ required: true, message: 'Tên không được để trống' }]}
        required
        name={[infoType, cardInfo.name, 'label']}
        label="Tên hướng dẫn"
        initialValue={cardInfo.label}
      />
      <ProFormTextArea
        rules={[{ required: true, message: `Xin vui lòng điền thông tin` }]}
        style={{ width: '100%' }}
        fieldProps={{
          autoSize: {
            minRows: 3,
          },
        }}
        required
        name={[infoType, cardInfo.name, 'description']}
        label="Nội dung"
        initialValue={cardInfo.description}
      />
      <FormUploadsPreviewable
        label="Icon"
        fileLimit={1}
        initialImages={cardInfo.icon}
        // fileList={fileList}
        // setFileList={setFileList}
        formItemName={[infoType, cardInfo.name, 'icon']}
      />
      <FormUploadsPreviewable
        label="Hình ảnh mô tả"
        fileLimit={8}
        initialImages={cardInfo.image}
        // fileList={fileList}
        // setFileList={setFileList}
        formItemName={[infoType, cardInfo.name, 'image']}
      />
    </CardCollapse>
  );
};

export default EditableFormCard;
