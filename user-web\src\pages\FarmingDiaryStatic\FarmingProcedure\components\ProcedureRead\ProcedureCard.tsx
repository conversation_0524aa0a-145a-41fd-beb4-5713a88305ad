import { DEFAULT_FALLBACK_IMG } from '@/common/contanst/img';
import { Process } from '@/services/diary-2/process';
import { getFirstFileUrlFromStringV2 } from '@/services/utils';
import { EditOutlined } from '@ant-design/icons';
import { Link, useIntl } from '@umijs/max';
import { Avatar, Button, Card, Divider, Typography } from 'antd';
import React, { FC, ReactNode } from 'react';
import DeleteProcess from './DeleteProcess';
import ProcedureItem from './ProcedureItem';

interface ProcedureCardProps {
  children?: ReactNode;
  data: Process;
  reload?: () => void;
}
const MetaHeader: FC<{
  label: string;
  description: string;
  image?: string;
  id: string;
}> = ({ label, description, image, id }) => (
  <Link to={`/farming-diary-static/procedure/detail/${id}`}>
    <div className="flex gap-4">
      <div className="flex-none">
        <Avatar
          size={64}
          shape="square"
          src={
            !image
              ? DEFAULT_FALLBACK_IMG
              : getFirstFileUrlFromStringV2({ arrUrlString: image }) || DEFAULT_FALLBACK_IMG
          }
        />
      </div>
      <div className="flex-auto">
        <h3 className="text-lg font-semibold mb-0 text-emerald-600 hover:text-emerald-500">
          {label}
        </h3>

        <Typography.Paragraph type="secondary">{description}</Typography.Paragraph>
      </div>
    </div>
  </Link>
);
const ProcedureCard: FC<ProcedureCardProps> = ({ children, data, reload }) => {
  const { formatMessage } = useIntl();
  return (
    <Card
      styles={{
        body: {
          padding: 10,
          boxShadow: '0 4px 8px rgba(0, 0, 0, 0.1)',
        },
      }}
    >
      <div className="flex">
        <div className="flex-auto">
          <MetaHeader
            id={data.name}
            label={data.label}
            description={`${formatMessage({ id: 'common.execute_time' })}: ${
              data.expire_time_in_days
            } ${formatMessage({ id: 'common.days' })}`}
            image={data.image}
          />
        </div>
        <div className="flex none gap-2">
          <Link to={`/farming-diary-static/procedure/edit/${data.name}`}>
            <Button size="small" icon={<EditOutlined />}></Button>
          </Link>
          <DeleteProcess id={data.name} onSuccess={reload} />
        </div>
      </div>
      {/* content */}
      <div className="flex flex-col pt-4 px-2">
        {data?.states?.map((item, index) => (
          <React.Fragment key={item.name}>
            <ProcedureItem
              title={item.label}
              description={item.description}
              index={index}
              task_count={item.task_count}
            />
            <Divider
              style={{
                margin: 0,
                marginBlockEnd: 12,
              }}
            />
          </React.Fragment>
        ))}
      </div>
    </Card>
  );
};

export default ProcedureCard;
