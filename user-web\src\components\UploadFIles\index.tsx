import { DOCTYPE_ERP } from '@/common/contanst/constanst';
import { uploadFile } from '@/services/fileUpload';
import { getListFileUrlFromStringV2 } from '@/services/utils';
import { UploadOutlined } from '@ant-design/icons';
import { ProForm, useDeepCompareEffect } from '@ant-design/pro-components';
import { useIntl } from '@umijs/max';
import { Button, Form, message, Upload, UploadFile, UploadProps } from 'antd';
import { RcFile } from 'antd/es/upload';
import { UploadFileStatus } from 'antd/es/upload/interface';
import { nanoid } from 'nanoid';
import { FC, useState } from 'react';

interface Props {
  // fileList: UploadFile<any>[];
  // setFileList: React.Dispatch<React.SetStateAction<UploadFile<any>[]>>;
  formItemName: string | string[];
  fileLimit: number;
  label?: string | React.ReactElement;
  initialImages?: string | undefined;
  docType?: string;
  isReadonly?: boolean;
  onValueChange?: (value: string) => void;
  maxSize?: number; // mb
  showUploadButton?: boolean;
}
const getBase64 = (file: RcFile): Promise<string> =>
  new Promise((resolve, reject) => {
    const reader = new FileReader();
    reader.readAsDataURL(file);
    reader.onload = () => resolve(reader.result as string);
    reader.onerror = (error) => reject(error);
  });
const getFileNameFromUrl = (url: any): string => {
  // Split the URL by '/' and get the last part
  if (typeof url !== 'string') {
    return url?.toString?.();
  }
  let parts = url.split('/');
  let fileName = parts[parts.length - 1];

  // If there's a query string, remove it
  fileName = fileName.split('?')[0];

  // If there's a fragment, remove it
  fileName = fileName.split('#')[0];

  return fileName.split('.')[0];
};

const FormUploadFiles: FC<Props> = ({
  formItemName,
  fileLimit,
  label,
  initialImages,
  docType,
  isReadonly,
  onValueChange,
  maxSize,
  showUploadButton= true
}) => {
  // const [previewOpen, setPreviewOpen] = useState(false);
  // const [previewImage, setPreviewImage] = useState('');
  // const [previewTitle, setPreviewTitle] = useState('');

  const [imageList, setImageList] = useState<string | undefined>(initialImages);
  const [fileList, setFileList] = useState<UploadFile[]>([]);
  const form = Form.useFormInstance();

  useDeepCompareEffect(() => {
    const listImg = getListFileUrlFromStringV2({ arrUrlString: initialImages }).map(
      (url, index) => {
        return {
          // name: `File ${(index + 1).toString()}`,
          name: getFileNameFromUrl(url),
          url: url || '',
          uid: (-index).toString(),
          status: (url ? 'done' : 'error') as UploadFileStatus,
        };
      },
    );
    setFileList(listImg);
  }, [initialImages]);
  // const handlePreview = async (file: UploadFile) => {
  //   if (!file.url && !file.preview) {
  //     file.preview = await getBase64(file.originFileObj as RcFile);
  //   }

  //   setPreviewImage(file.url || (file.preview as string));
  //   setPreviewOpen(true);
  //   setPreviewTitle(file.name || file.url!.substring(file.url!.lastIndexOf('/') + 1));
  // };

  const handleChange: UploadProps['onChange'] = async ({ fileList: newFileList }) => {
    // const uploadIconRes = await uploadFile({
    //   docType: DOCTYPE_ERP.iotPlant,
    //   docName: iconFile.name + Math.random().toString(4) + iconFile.lastModified?.toString(4),
    //   file: iconFile.originFileObj as any,
    // });
    // const oldFileList = [...fileList];
    // setFileList((prev) => newFileList);

    const uploadListRes = await Promise.allSettled(
      newFileList.map(async (item) => {
        if (item.url) {
          return {
            url: item.url.split('file_url=').at(-1),
            uid: item.uid,
            status: 'done',
            name: item.name,
          };
        }
        try {
          const res = await uploadFile({
            docType: docType || DOCTYPE_ERP.iotPlant,
            docName: item.name + Math.random().toString(4) + item.lastModified?.toString(4),
            file: item.originFileObj as any,
          });
          return {
            url: res.data.message.file_url,
            name: getFileNameFromUrl(res.data.message.file_url),
            uid: nanoid(),
            status: 'done',
          };
        } catch (error) {
          message.error({
            content: `upload file không thành công`,
          });
          return {
            ...item,
            status: 'error',
          };
        }
      }),
    );
    // for display
    const newFileListRes = uploadListRes
      .map((item) => (item.status === 'fulfilled' ? item.value : null))
      .filter((item) => item !== null) as UploadFile[];

    // update img path
    const arrFileUrl = newFileListRes
      .map((item) => (item.status === 'done' ? item.url : null))
      .filter((item) => item !== null);

    const fileUrls = arrFileUrl.join(',');
    console.log("fileUrls: ", fileUrls);

    // for value

    //
    await Promise.all([onValueChange?.(fileUrls)]);

    setFileList(() =>
      newFileListRes.map((item) => ({
        ...item,
        url: getListFileUrlFromStringV2({ arrUrlString: item.url })?.[0] || '',
      })),
    );
    setImageList(fileUrls);
    form?.setFieldValue(formItemName, fileUrls);
  };
  const { formatMessage } = useIntl();
  return (
    <>
      <ProForm.Item name={formItemName} initialValue={imageList} style={{ display: 'none' }} />
      <ProForm.Item label={label}>
        <Upload
          // listType="text"
          fileList={fileList}
          // onPreview={handlePreview}
          maxCount={fileLimit}
          onChange={handleChange}
          multiple
          disabled={isReadonly}
          beforeUpload={(file) => {
            if (maxSize) {
              const isLt5M = file.size / 1024 / 1024 <= maxSize;
              if (!isLt5M) {
                message.error(
                  formatMessage({
                    id: 'common.upload-error-file-big',
                  }) + ` ${maxSize}MB`,
                );
                // alert('Image must smaller than 5MB!');
                return Upload.LIST_IGNORE;
              }
            }
            return new Promise((resolve, reject) => {
              // check the file size - you can specify the file size you'd like here:
              if (maxSize) {
                const isLt5M = file.size / 1024 / 1024 <= maxSize;
                if (!isLt5M) {
                  message.error(
                    formatMessage({
                      id: 'common.upload-error-file-big',
                    }) + ` ${maxSize}MB`,
                  );
                  // alert('Image must smaller than 5MB!');
                  reject(Upload.LIST_IGNORE);
                  return;
                }
              }

              resolve(true);
            });
          }}
        >
          {showUploadButton && (
            <Button disabled={isReadonly} icon={<UploadOutlined />}>
              {formatMessage({
                id: 'common.upload',
              })}
            </Button>
          )}
        </Upload>
      </ProForm.Item>
    </>
  );
};

export default FormUploadFiles;
