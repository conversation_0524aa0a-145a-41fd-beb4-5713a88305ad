import { DEFAULT_DATE_FORMAT_WITHOUT_TIME } from '@/common/contanst/constanst';
import { getSingleTask } from '@/services/TaskAndTodo';
import { getCustomerUserList } from '@/services/customerUser';
import { createExportTransaction, getExportProductList } from '@/services/export';
import { createImportTransaction, getImportProductList } from '@/services/import';
import { getProductList, getProducts } from '@/services/product';
import { getStorageList } from '@/services/storage';
import { toLowerCaseNonAccentVietnamese } from '@/services/utils';
import { IProduct } from '@/types/product.type';
import { IProductTransaction } from '@/types/productTransaction.type';
import { PlusOutlined } from '@ant-design/icons';
import {
  ModalForm,
  ProForm,
  ProFormDatePicker,
  ProFormSelect,
  ProFormText,
  ProFormTextArea,
} from '@ant-design/pro-components';
import { useModel } from '@umijs/max';
import { App, Button, Col, Divider, Form, Row, Typography } from 'antd';
import 'antd/es/modal/style';
import 'antd/es/slider/style';
import moment from 'moment';
import numeral from 'numeral';
import { FC, ReactNode, useEffect, useState } from 'react';
import ItemTransactionTable, { ITransactionItem } from './ItemTransactionTable';
interface CreateTransactionProps {
  children?: ReactNode;
  type: 'import' | 'export';
  onSuccess?: any;
  data: any[];
  task_id: string;
}

interface IFormData {
  naming_series: string;
  storage: string;
  description: string;
  note: string;
  export_type: string;
  transaction_date: string;
  document_date: string;
  manufacturing_date: string;
  expiry_date: string;
  region: string;
  employee: string;
  document_code: string;
  products: any;
}
interface ITreeNode {
  title: string;
  value: string;
  key: string;
  children?: ITreeNode[];
}
interface IFormattedProduct extends IProduct {
  current_quantity?: number;
}
const CreateProductTransaction: FC<CreateTransactionProps> = ({
  type,
  onSuccess,
  data,
  task_id,
}) => {
  console.log('data product is', data);
  const { message } = App.useApp();
  const [form] = Form.useForm<IFormData>();
  const [submitting, setSubmitting] = useState<boolean>(false);
  const [productList, setProductList] = useState<ITransactionItem[]>([]);
  const [products, setProducts] = useState<IFormattedProduct[]>([]);
  const [treeData, setTreeData] = useState<ITreeNode[]>();
  const [taskInfo, setTaskData] = useState<any>();
  //current user
  const { initialState } = useModel(`@@initialState`);
  const curentUser = initialState?.currentUser;
  //for export only
  const [selectedStorage, setSelectedStorage] = useState<string>('');
  const [isDisabled, setIsDisabled] = useState<boolean>(true);
  const [documentCode, setDocumentCode] = useState<string>();

  const loadData = async () => {
    setProductList(
      data.map((product: any) => {
        let record: ITransactionItem = {
          product_id: product?.product_id || '',
          product_name: product?.label,
          key: Date.now() * 1000 + Math.floor(Math.random() * 1000).toString(),
          unit_name: product?.item_unit_label || '',
          product_entity: product?.product_id,
          supplier: product?.producer_label || '',
          quantity: 0,
          unit: product?.packing_unit_id,
          unit_price: product?.product_price || 0,
          current_quantity: product?.current_quantity,
          conversion_factor: product?.conversion_factor || 0,
          chosen_conversion_factor: product?.conversion_factor || 0,
          packing_unit_id: product?.packing_unit_id,
          packing_unit_label: product?.packing_unit_label,
          basic_unit_id: product?.item_unit_id,
          basic_unit_label: product?.item_unit_label,
          quantity_type: 'packing' as 'packing' | 'basic',
          input_quantity: product?.quantity || 0,
        };
        products.map((item: any) => {
          if (item.name === product.product_id) {
            record.basic_unit_id = item.unit_id;
            record.basic_unit_label = item.unit_label;
            record.packing_unit_id = item.packing_unit_id;
            record.packing_unit_label = item.packing_unit_label;
            record.conversion_factor = item.conversion_factor;
            record.chosen_conversion_factor = item.conversion_factor;
          }
        });
        const price = record?.unit_price || 0;
        const input_quantity = record.input_quantity;
        const quantity =
          record.quantity_type === 'basic'
            ? input_quantity
            : input_quantity * record.conversion_factor;
        const total = price * input_quantity;
        record.quantity = quantity;
        record.total_price = total;
        return record;
      }),
    );
  };
  useEffect(() => {
    const fetchTaskInfo = async () => {
      const res = await getSingleTask(task_id);
      setTaskData(res.data[0]);
    };
    fetchTaskInfo();
  }, []);
  useEffect(() => {
    loadData();
  }, [data]);
  const handleSelectStorage = (entity: any) => {
    setSelectedStorage(entity);
    // setProductList([]);
    setIsDisabled(false);
  };

  //get list product form select for import
  const getIProductList = async () => {
    const dataAgriProductList = await getProducts({});

    if (dataAgriProductList.data) {
      setProducts(dataAgriProductList.data);
    }
  };
  //get list product form select for export
  const getEProductList = async () => {
    const productInventoryList = await getProductList({
      storage_id: selectedStorage,
    });
    const productFiltered = productInventoryList.data.map((e) => e.product_id);
    //only apply filter if productFiltered is not empty
    const filters = productFiltered.length
      ? [['iot_agriculture_product', 'name', 'in', productFiltered]]
      : [];
    const dataAgriProductList = await getProducts({ filters });
    console.log('data product dkm', dataAgriProductList);
    if (dataAgriProductList.data) {
      const formatted_products = dataAgriProductList.data.map((item) => ({
        ...item,
        current_quantity:
          productInventoryList.data.find((value: any) => value.product_id === item.name)
            ?.quantity || 0,
      }));
      setProducts(formatted_products);
    }
  };

  useEffect(() => {
    const fetchData = async () => {
      if (type === 'export') {
        getEProductList();
      } else {
        getIProductList();
      }
    };

    fetchData();
  }, [selectedStorage]);

  const fetchDocumentCode = async () => {
    const res =
      type === 'import'
        ? await getImportProductList({
            page: '1',
            size: '100',
            creation: moment().format('YYYY-MM-DD'),
          })
        : await getExportProductList({
            page: '1',
            size: '100',
            creation: moment().format('YYYY-MM-DD'),
          });
    const code = `${moment().format('YYYY-MM-DD')}.${(res.data.length + 1)
      .toString()
      .padStart(2, '0')}`;
    setDocumentCode(code);
  };

  useEffect(() => {
    fetchDocumentCode();
  }, []);

  const handleFinish = async (values: IFormData) => {
    setSubmitting(true);
    try {
      form.validateFields();
      // return;
      const newTransaction: IProductTransaction = {
        description: values.description,
        document_date: values.document_date,
        document_code: values.document_code,
        employee: values.employee,
        expiry_date: values.expiry_date,
        manufacturing_date: values.manufacturing_date,
        storage: values.storage,
        transaction_date: values.transaction_date,
        export_type: values.export_type,
        ...(type === 'import'
          ? {
              import_detail: productList.map((product) => ({
                ...product,
                discount: 0,
                type: 'product',
              })),
            }
          : {
              export_detail: productList.map((product) => ({
                ...product,
                discount: 0,
                type: 'product',
              })),
            }),
      };
      const res =
        type === 'import'
          ? await createImportTransaction(newTransaction)
          : await createExportTransaction(newTransaction);

      message.success(`${type === 'import' ? 'Nhập kho' : 'Xuất kho'} thành công`);
      onSuccess?.();
      await fetchDocumentCode();
      form.resetFields();
      return true;
    } catch (error: any) {
      if (type === 'export') {
        message.error('Không thể xuất kho do trong kho không đủ số lượng vật của tư đã chọn.');
      } else message.error(`Error when adding new transaction: ${error.message}`);
    } finally {
      setSubmitting(false);
    }
  };

  // const handleAddItems = async () => {
  //   const product_ids: string[] | undefined = form.getFieldValue('products');
  //   if (!product_ids) {
  //     message.error(`Vui lòng chọn vật tư`);
  //     return;
  //   }
  //   const filtered_categories = products.filter((product) => product_ids.includes(product.name));
  //   const formated_categories: ITransactionItem[] = filtered_categories.map((product) => ({
  //     product_id: product.iot_product_id || '',
  //     product_name: product.label || '',
  //     key: Date.now() * 100 + Math.floor(Math.random() * 100).toString(),
  //     unit_name: product.item_unit_label || '',
  //     product_entity: product.name,
  //     supplier: product.producer_label,
  //     quantity: 0,
  //     price: 0,
  //     unit_price: product.product_price,
  //     current_quantity: product.current_quantity,
  //     conversion_factor: product.conversion_factor,
  //     packing_unit_id: product.packing_unit_id,
  //     packing_unit_label: product.packing_unit_label,
  //     quantity_type: 'packing',
  //     input_quantity: 0,
  //   }));
  //   setProductList((prev) => [...formated_categories, ...prev]);
  //   form.setFieldValue('products', []);
  //   return;
  // };

  const handleAbort = async () => {
    setProductList([]);
  };

  return (
    <ModalForm<IFormData>
      width="80%"
      title={type === 'import' ? 'Nhập kho' : 'Xuất kho'}
      layout="vertical"
      grid={true}
      rowProps={{
        gutter: [16, 0],
      }}
      trigger={
        <Button icon={<PlusOutlined />} onClick={loadData} type="primary">
          {type === 'import' ? 'Nhập kho' : 'Xuất kho'}
        </Button>
      }
      autoFocusFirstInput
      modalProps={{
        onCancel: handleAbort,
        destroyOnClose: true,
      }}
      form={form}
      submitter={{
        render: (props, defaultDoms) => {
          return [
            <Button
              key="ok"
              onClick={() => {
                props.submit();
              }}
              type="primary"
              style={{ width: '100%' }}
              loading={submitting}
            >
              Đồng ý
            </Button>,
          ];
        },
      }}
      submitTimeout={2000}
      onFinish={handleFinish}
      initialValues={{
        transaction_date: Date(),
        document_date: Date(),
        document_code: documentCode,
      }}
    >
      <ProFormDatePicker
        name="transaction_date"
        required
        rules={[{ required: true, message: 'Vui lòng chọn ngày hoạch toán' }]}
        label="Ngày hạch toán"
        colProps={{ span: 8 }}
        width="md"
        fieldProps={{
          format: DEFAULT_DATE_FORMAT_WITHOUT_TIME,
        }}
      />
      <ProFormDatePicker
        name="document_date"
        required
        rules={[{ required: true, message: 'Vui lòng chọn ngày chứng từ' }]}
        label="Ngày chứng từ"
        colProps={{ span: 8 }}
        width="md"
        fieldProps={{
          format: DEFAULT_DATE_FORMAT_WITHOUT_TIME,
        }}
      />
      <ProFormText name="document_code" label="Mã chứng từ" colProps={{ span: 8 }} width={'md'} />
      <ProFormSelect
        showSearch
        name="storage"
        rules={[{ required: true, message: 'Vui lòng chọn kho' }]}
        required
        label="Kho"
        onChange={(value) => handleSelectStorage(value)}
        colProps={{ span: 8 }}
        request={async () => {
          const storages = await getStorageList();
          return storages.data.map((storage) => ({
            label: storage.storage_name,
            value: storage.name,
          }));
        }}
        width={'md'}
      />
      <ProFormSelect
        showSearch
        required
        initialValue={curentUser?.user_id}
        label="Nhân viên thực hiện"
        request={async (option) => {
          const listUser = await getCustomerUserList();
          return listUser.data.map((product: any) => ({
            label: `${product.first_name} ${product.last_name}`,
            value: product.name,
          }));
        }}
        name="employee"
        // onChange={handleChooseItem}
        colProps={{ span: 8 }}
        width={'md'}
      />
      {type === 'export' && (
        <ProFormSelect
          showSearch
          required
          label="Loại xuất kho"
          initialValue={'Xuất kho'}
          request={async (option) =>
            [
              { label: 'Xuất hủy', name: 'Xuất hủy' },
              { label: 'Xuất kho', name: 'Xuất kho' },
            ]
              .filter((product) =>
                toLowerCaseNonAccentVietnamese(product.label || '').includes(
                  toLowerCaseNonAccentVietnamese(option.keyWords),
                ),
              )
              .map((product) => ({
                label: product.label,
                value: product.name,
              }))
          }
          name="export_type"
          // onChange={handleChooseItem}
          colProps={{ span: 6 }}
        />
      )}

      <ProFormTextArea
        name="description"
        label="Diễn giải"
        initialValue={
          'Nhập kho từ công việc: ' +
          `${taskInfo ? taskInfo.label : ''} (Task Id: ${taskInfo ? taskInfo.name : ''})`
        }
      />
      <ProForm.Group title="Danh sách sản phẩm">
        {/* <ProForm.Group>
          <ProFormTreeSelect
            name={'products'}
            disabled={type === 'export' && isDisabled}
            fieldProps={{
              treeNodeFilterProp: 'label',
              showSearch: true,
              multiple: true,
              filterTreeNode: (input: string, treeNode: any) => {
                return true;
              },
              treeCheckable: true,
              showCheckedStrategy: 'SHOW_CHILD',
            }}
            label="Tên vật tư"
            request={async (option) => {
              return treeData || [];
            }}
            colProps={{ span: 24 }}
          />
        </ProForm.Group> */}

        <ProForm.Group>
          <Row style={{ width: '100%' }}>
            <Col span={18}>
              {/* <Space>
                <Button type="primary" icon={<PlusOutlined />} onClick={handleAddItems}>
                  Thêm vật tư
                </Button>
              </Space> */}
            </Col>
            <Col
              span={6}
              style={{ display: 'flex', justifyContent: 'center', alignItems: 'center' }}
            >
              <Typography.Text
                style={{
                  lineHeight: '100%',
                  fontSize: '16px',
                  display: 'inline-block',
                  verticalAlign: 'middle',
                }}
              >
                {productList &&
                  numeral(productList.reduce((acc, ele) => acc + (ele.total_price || 0), 0)).format(
                    '0,0.00',
                  )}{' '}
                VNĐ
              </Typography.Text>
            </Col>
          </Row>
        </ProForm.Group>
      </ProForm.Group>
      <Divider />
      <ItemTransactionTable type="import" data={productList} setData={setProductList} />
    </ModalForm>
  );
};

export default CreateProductTransaction;
