import { nonAccentVietnamese } from '@/utils/string';
import { ProFormTreeSelect, useDebounceValue } from '@ant-design/pro-components';
import { Divider } from 'antd';
import { FC, ReactNode, useMemo, useState } from 'react';

type FormTreeSelectSearchProps = React.ComponentProps<typeof ProFormTreeSelect> & {
  dropdownBottom?: ReactNode;
};
const toLowerCase = (input = '') =>
  nonAccentVietnamese(
    input
      .toLowerCase()
      .normalize('NFD')
      .replace(/[\u0300-\u036f]/g, ''),
  );
const FormTreeSelectSearch: FC<FormTreeSelectSearchProps> = ({ dropdownBottom, ...props }) => {
  // eslint-disable-next-line @typescript-eslint/no-non-null-asserted-optional-chain
  const treeData = props.fieldProps?.treeData!;
  const [searchValue, setSearchValue] = useState('');
  const searchValueDebounce = useDebounceValue(searchValue || '', 100);
  const _treeData = useMemo(() => {
    const loop = (data = [] as typeof treeData): any[] =>
      (data || []).map((item) => {
        const normalizedSearchValue = toLowerCase(searchValueDebounce);

        const itemTitle = (item.title || '').toString();
        const strTitle = nonAccentVietnamese(itemTitle) as string;
        const index = strTitle.indexOf(normalizedSearchValue);
        const beforeStr = itemTitle.substring(0, index);
        const str = itemTitle.substring(index, index + searchValueDebounce.length);
        const afterStr = itemTitle.slice(index + searchValueDebounce.length);

        const title =
          searchValueDebounce === '' ? (
            <span>{itemTitle}</span>
          ) : index > -1 ? (
            <span>
              {beforeStr}
              <span style={{ color: 'white', backgroundColor: 'green' }}>{str}</span>
              {afterStr}
            </span>
          ) : (
            <span>{itemTitle}</span>
          );
        if (item.children) {
          return {
            title,
            key: item.value,
            children: loop(item.children),
            value: item.value,
            _title: itemTitle,
          };
        }

        return {
          title,
          key: item.value,
          value: item.value,
          _title: itemTitle,
        };
      });

    return loop(treeData);
  }, [treeData, searchValueDebounce]);
  return (
    <ProFormTreeSelect
      {...props}
      fieldProps={{
        ...(props.fieldProps || {}),
        treeData: _treeData as any,
        onSearch(value) {
          setSearchValue(value);
        },
        filterTreeNode: (input: string, treeNode: any) => {
          const treeNodeChildrenArr: any[] = treeNode.children || [];
          const normalizedInput = toLowerCase(input);

          const normalizedLabel = toLowerCase(treeNode._title);
          let childrenMatch = false;
          for (let i = 0; i < treeNodeChildrenArr.length; i++) {
            const normalizedLabel = toLowerCase(treeNodeChildrenArr[i]._title);
            if (normalizedLabel.includes(normalizedInput)) {
              childrenMatch = true;
              return true;
            }
          }

          if (normalizedLabel.includes(normalizedInput)) {
            return true;
          }

          return childrenMatch;
        },
        dropdownRender: !dropdownBottom
          ? undefined
          : (menu) => (
              <div>
                {menu}
                <Divider style={{ margin: '4px 0' }} />
                {dropdownBottom}
              </div>
            ),
        showSearch: true,
        multiple: true,
        autoClearSearchValue: true,
        treeCheckable: true,
        treeDefaultExpandAll: true,
        showCheckedStrategy: 'SHOW_CHILD',
      }}
    />
  );
};

export default FormTreeSelectSearch;
