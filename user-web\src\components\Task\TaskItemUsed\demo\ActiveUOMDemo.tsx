import React from 'react';
import { Card, Typography, Space, Tag } from 'antd';
import { IIotWarehouseItemTaskUsed } from '@/types/IIotWarehouseItemTaskUsed';

const { Title, Text, Paragraph } = Typography;

/**
 * Demo component để minh họa cách active_uom và active_conversion_factor đượ<PERSON> sử dụng
 */
const ActiveUOMDemo: React.FC = () => {
  // Ví dụ dữ liệu trước khi cập nhật
  const beforeUpdate: IIotWarehouseItemTaskUsed = {
    name: 'test-item-1',
    description: 'Phân bón NPK',
    task_id: 'task-123',
    iot_category_id: 'category-1',
    exp_quantity: 10,
    draft_quantity: 8,
    conversion_factor: 1,
    uom_id: 'kg',
    uom_label: 'Kilogram',
    // Không có active_uom và active_conversion_factor
  };

  // Ví dụ dữ liệu sau khi cập nhật
  const afterUpdate: IIotWarehouseItemTaskUsed = {
    name: 'test-item-1',
    description: '<PERSON>ân bón NPK',
    task_id: 'task-123',
    iot_category_id: 'category-1',
    exp_quantity: 10,
    draft_quantity: 8,
    conversion_factor: 1,
    uom_id: 'kg',
    uom_label: 'Kilogram',
    active_uom: 'kg', // Thêm active_uom
    active_conversion_factor: 1, // Thêm active_conversion_factor
  };

  // Ví dụ dữ liệu khi người dùng thay đổi UOM từ kg sang g
  const afterUOMChange: IIotWarehouseItemTaskUsed = {
    name: 'test-item-1',
    description: 'Phân bón NPK',
    task_id: 'task-123',
    iot_category_id: 'category-1',
    exp_quantity: 10000, // Chuyển đổi từ 10 kg sang 10000 g
    draft_quantity: 8000, // Chuyển đổi từ 8 kg sang 8000 g
    conversion_factor: 0.001, // Conversion factor của gram
    uom_id: 'g',
    uom_label: 'Gram',
    active_uom: 'g', // Cập nhật active_uom
    active_conversion_factor: 0.001, // Cập nhật active_conversion_factor
  };

  // Ví dụ request update được gửi đi
  const updateRequest = {
    name: 'test-item-1',
    description: 'Phân bón NPK',
    task_id: 'task-123',
    iot_category_id: 'category-1',
    exp_quantity: 10, // exp_quantity * conversion_factor (10000 * 0.001)
    draft_quantity: 8, // draft_quantity * conversion_factor (8000 * 0.001)
    active_uom: 'g', // Gửi active_uom
    active_conversion_factor: 0.001, // Gửi active_conversion_factor
  };

  return (
    <div style={{ padding: '24px' }}>
      <Title level={2}>Demo: Active UOM và Active Conversion Factor</Title>
      
      <Paragraph>
        Tính năng này cho phép gửi thông tin về đơn vị đo lường hiện tại (active_uom) 
        và hệ số chuyển đổi hiện tại (active_conversion_factor) trong request update.
      </Paragraph>

      <Space direction="vertical" size="large" style={{ width: '100%' }}>
        <Card title="1. Dữ liệu ban đầu (trước khi cập nhật)" size="small">
          <pre style={{ background: '#f5f5f5', padding: '12px', borderRadius: '4px' }}>
            {JSON.stringify(beforeUpdate, null, 2)}
          </pre>
          <Tag color="orange">Chưa có active_uom và active_conversion_factor</Tag>
        </Card>

        <Card title="2. Dữ liệu sau khi load (khởi tạo active fields)" size="small">
          <pre style={{ background: '#f5f5f5', padding: '12px', borderRadius: '4px' }}>
            {JSON.stringify(afterUpdate, null, 2)}
          </pre>
          <Tag color="blue">Đã khởi tạo active_uom và active_conversion_factor</Tag>
        </Card>

        <Card title="3. Dữ liệu sau khi người dùng thay đổi UOM (kg → g)" size="small">
          <pre style={{ background: '#f5f5f5', padding: '12px', borderRadius: '4px' }}>
            {JSON.stringify(afterUOMChange, null, 2)}
          </pre>
          <Tag color="purple">Cập nhật active fields khi thay đổi UOM</Tag>
        </Card>

        <Card title="4. Request update được gửi đi" size="small">
          <pre style={{ background: '#f5f5f5', padding: '12px', borderRadius: '4px' }}>
            {JSON.stringify(updateRequest, null, 2)}
          </pre>
          <Tag color="green">Bao gồm active_uom và active_conversion_factor</Tag>
        </Card>

        <Card title="Các thay đổi đã thực hiện" size="small">
          <ul>
            <li>
              <Text strong>Interface IIotWarehouseItemTaskUsed:</Text> Thêm 2 trường mới:
              <ul>
                <li><Text code>active_uom?: string</Text> - ID của đơn vị đo lường hiện tại</li>
                <li><Text code>active_conversion_factor?: number</Text> - Hệ số chuyển đổi hiện tại</li>
              </ul>
            </li>
            <li>
              <Text strong>fetchTaskItemUsed:</Text> Khởi tạo active fields từ dữ liệu hiện có
            </li>
            <li>
              <Text strong>handleUOMChange:</Text> Cập nhật active fields khi thay đổi UOM
            </li>
            <li>
              <Text strong>handleSaveItems:</Text> Gửi active fields trong request update
            </li>
          </ul>
        </Card>
      </Space>
    </div>
  );
};

export default ActiveUOMDemo;
