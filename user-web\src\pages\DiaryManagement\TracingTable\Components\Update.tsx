import { EditOutlined, LoadingOutlined, PlusOutlined } from '@ant-design/icons';
import { Button, Col, DatePicker, Form, Input, message, Modal, Row } from 'antd';
import { useEffect, useState } from 'react';
const { Item } = Form;

import { getCropList } from '@/services/cropManager';
import { IIotAgricultureProduct } from '@/services/products/type';
import { updateCropTracing } from '@/services/tracing';
import { ProFormSelect } from '@ant-design/pro-components';
import { useIntl } from '@umijs/max';

const Update = (params: { refreshFnc: any; value: IIotAgricultureProduct }) => {
  const [loading, setLoading] = useState(false);
  const [isOpen, setOpen] = useState(false);
  const [form] = Form.useForm();

  const [imageUrl, setImageUrl] = useState<string>();
  const [uploading, setUploading] = useState(false);
  const [fileList, setFileList] = useState<any[]>([]);
  const intl = useIntl();
  const showModal = () => {
    form.setFieldsValue(params.value);
    setOpen(true);
  };
  const hideModal = () => {
    setOpen(false);
  };
  const handleOk = () => {
    form.submit();
  };
  const handleCancel = () => {
    hideModal();
    form.resetFields();
  };

  useEffect(() => {
    console.log('params.value', params.value);
    setImageUrl(params.value.image);
    return () => {
      // Cleanup code (if needed)
    };
  }, []); // Empty dependency array means this effect runs once after the initial render
  const uploadButton = (
    <div>
      {uploading ? <LoadingOutlined /> : <PlusOutlined />}
      <div style={{ marginTop: 8 }}>Upload</div>
    </div>
  );
  return (
    <>
      <Button
        size="small"
        //type="dashed"
        style={{ display: 'flex', alignItems: 'center' }}
        onClick={showModal}
      >
        <EditOutlined />
      </Button>

      <Modal
        title={intl.formatMessage({ id: 'action.update' })}
        open={isOpen}
        onOk={handleOk}
        onCancel={handleCancel}
        confirmLoading={loading}
      >
        <Form
          layout="horizontal"
          labelCol={{ span: 24 }}
          labelAlign="left"
          form={form}
          onFinish={async (value: any) => {
            try {
              value.image = '';
              value.name = params.value.name;
              if (fileList.length) {
                value.image = fileList[0].raw_url;
              }
              //   console.log('value product', value);
              const result = await updateCropTracing(value);
              form.resetFields();
              hideModal();
              message.success('Success!');
              if (params.refreshFnc) {
                await params.refreshFnc();
              }
            } catch (error: any) {
              message.error(JSON.stringify(value));
            } finally {
              setLoading(false);
            }
          }}
        >
          <Row gutter={10}>
            <Col className="gutter-row" md={12} span={12}>
              <Item
                label={intl.formatMessage({ id: 'common.origin_name' })}
                labelCol={{ span: 24 }}
                rules={[
                  {
                    required: true,
                    message: 'Bắt buộc điền',
                  },
                ]}
                name="label"
              >
                <Input />
              </Item>
            </Col>

            <Col className="gutter-row" md={12} span={12}>
              <Item label={intl.formatMessage({ id: 'common.expired_time' })} name="expiry_time">
                {/* <DatePicker
                  format="DD/MM/YYYY hh:mm A"
                  showTime={{ use12Hours: true }}
                  placeholder="Default 2 days"
                /> */}
                <DatePicker showTime />
              </Item>
            </Col>
          </Row>
          <Row>
            <Col className="gutter-row" md={12} span={12}>
              <Item
                label={intl.formatMessage({ id: 'common.crop' })}
                required={true}
                name="crop_id"
              >
                <ProFormSelect
                  showSearch
                  request={async () => {
                    const data = await getCropList();
                    return data.data.map((item: any) => ({
                      label: item.label,
                      value: item.name,
                    }));
                  }}
                />
              </Item>
            </Col>
          </Row>
        </Form>
      </Modal>
    </>
  );
};

export default Update;
