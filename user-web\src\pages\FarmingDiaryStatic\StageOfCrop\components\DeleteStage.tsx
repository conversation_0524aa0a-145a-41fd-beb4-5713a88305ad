import ActionModalConfirm from '@/components/ActionModalConfirm';
import { FC } from 'react';
import useDelete from '../hooks/useDelete';

interface DeleteStageProps {
  id: string;
  onSuccess: () => void;
}

const DeleteStage: FC<DeleteStageProps> = ({ id, onSuccess }) => {
  const { run, loading } = useDelete();
  return (
    <ActionModalConfirm
      modalProps={{
        async onOk() {
          await run(id);
          onSuccess?.();
          return true;
        },
      }}
    />
  );
};

export default DeleteStage;
