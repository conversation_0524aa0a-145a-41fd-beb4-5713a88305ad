import { listAllItem } from '@/services/products';
import { IIotAgricultureProduct } from '@/services/products/type';
import { listAllUnit } from '@/services/unit';
import { useModel } from '@umijs/max';
import jwt_decode from 'jwt-decode';
import { useEffect, useState } from 'react';

export default () => {
  const [myProducts, setMyProducts] = useState<IIotAgricultureProduct[]>([]);
  const [loadingResource, setLoadingResource] = useState<any>(false);
  const { initialState } = useModel('@@initialState');

  const fetchAllProducts = async () => {
    try {
      setLoadingResource(true);
      //get token decode from localstorage
      const token = localStorage.getItem('token');
      const decodedToken: any = token ? jwt_decode(token) : null;

      const sections = decodedToken ? decodedToken.sections : 'NULL';
      // console.log('sections', sections);
      if (sections && !(sections.includes('PRODUCT') || sections.includes('SYSTEM_ADMIN'))) return;

      const data = await listAllItem();
      const unitRes = await listAllUnit();
      const units = unitRes.data || [];

      setMyProducts(
        data.data?.map((d) => {
          d.unit = units?.find((el) => el.name === d.unit_id);
          return d;
        }) || [],
      );
    } catch (error: any) {
      // message.error(error?.toString());
    } finally {
      setLoadingResource(false);
    }
  };

  useEffect(() => {
    if (initialState?.currentUser) {
      fetchAllProducts();
    }
  }, [initialState]);

  return {
    myProducts,
    loadingResource,
    fetchAllProducts,
  };
};
