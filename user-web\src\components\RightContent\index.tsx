import { SelectLang, useModel } from '@umijs/max';
import { Space } from 'antd';
import { FC, useEffect } from 'react';
import Avatar from './AvatarDropdown';

import NoticeIconView from '../NoticeIcon';
import styles from './index.less';

export type SiderTheme = 'light' | 'dark';
interface GlobalHeaderRightProps {
  onThemeChange: () => void;
}

const GlobalHeaderRight: FC<GlobalHeaderRightProps> = ({ onThemeChange }) => {
  const { initialState } = useModel('@@initialState');
  const currentUser = initialState?.currentUser;
  // const [theme, setTheme] = useState('light');

  // console.log('currentUser', currentUser);
  // const changeTheme = (value: boolean) => {
  //   onThemeChange();
  //   setTheme(value ? 'dark' : 'light');
  //   // if (initialState && initialState.settings) {
  //   //   initialState.settings.navTheme = 'realDark';
  //   // }
  // };

  // const convertNoticeData = (data: TeamReminderType[]) => {
  //   if (!data.length) {
  //     return [];
  //   }
  //   return data.map<API.NoticeIconItem>((d) => {
  //     return {
  //       id: d.name.toString(),
  //       title: d.message,
  //       datetime: moment(d.created_at).format('YYYY-MM-DD HH:mm:ss'),
  //       description: `From: ${d.from_team} - To: ${d.to_team}`,
  //     };
  //   });
  // };

  useEffect(() => {}, []);

  if (!initialState || !initialState.settings) {
    return null;
  }

  const { navTheme, layout } = initialState.settings;
  let className = styles.right;

  // if ((navTheme === 'dark' && layout === 'top') || layout === 'mix') {
  //   className = `${styles.right}  ${styles.dark}`;
  // }

  return (
    <Space className={className} size={'middle'}>
      {/* <Switch
        checked={theme === 'dark'}
        onChange={changeTheme}
        checkedChildren="Dark"
        unCheckedChildren="Light"
      /> */}
      <NoticeIconView />
      <SelectLang className={styles.action} style={{ color: '#f3f6f4' }} />
      <Avatar menu={true} />
    </Space>
  );
};

export default GlobalHeaderRight;
