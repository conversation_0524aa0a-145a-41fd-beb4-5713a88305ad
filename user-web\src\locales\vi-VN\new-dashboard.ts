const genKeyPrefix = (key: string) => `new-dashboard.${key}`

export default {
  [genKeyPrefix('see-all')]: 'Xem tất cả',
  [genKeyPrefix('iot-device')]:'Thiết bị IoT',
  [genKeyPrefix('notice-task')]: 'Bạn có {count} công việc cần làm hôm nay',
  [genKeyPrefix('crop-ongoing')]: 'vụ mùa dang diễn ra',
  [genKeyPrefix('inventory-value')]: 'Gi<PERSON> trị tồn kho',
  [gen<PERSON>eyPrefix('employee')]: '<PERSON>h<PERSON> sự',
  [genKeyPrefix('employee-working')]: 'nhân sự đang làm việc',
  [genKeyPrefix('project')]: 'Dự án',
  [genKeyPrefix('project-ongoing')]: 'Dự án đang diễn ra',
  [genKeyPrefix('notice-and-alarm')]: 'Thông báo và cảnh báo',
  [genKeyPrefix('import-and-export-warehouse')]: 'Nhập xuất kho',
  [genKeyPrefix('warehouse')]: 'Kho',
  [genKeyPrefix('export-warehouse')]: 'Xuất kho',
  [genKeyPrefix('import-warehouse')]: 'Nhập kho',
  [genKeyPrefix('active-iot-devices')]:'thiết bị IoT đang hoạt động',
  [genKeyPrefix('all-crop')]: 'Tất cả vụ mùa',
  [genKeyPrefix('ongoing')]: 'Đang diễn ra',
  [genKeyPrefix('completed')]: 'Đã kết thúc',
  [genKeyPrefix('stage')]:'Giai đoạn'









  // [genKeyPrefix("crop")]:'Vụ mùa',
}
