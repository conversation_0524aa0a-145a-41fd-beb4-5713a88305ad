// src/models/count.ts
import { documentType } from '@/pages/Docs/CropLibrary/_util';
import { getPlantList } from '@/services/plants';
import { Plant } from '@/types/plant.type';
import { useModel } from '@umijs/max';
import jwt_decode from 'jwt-decode';
import { useEffect, useMemo, useState } from 'react';

export default () => {
  const [myPlant, setMyPlant] = useState<Plant[]>([]);
  const [curPlant, setCurPlant] = useState<Plant>();
  const [loadingResource, setLoadingResource] = useState<any>(false);
  const { initialState } = useModel('@@initialState');
  const isAccessEditDocs = useMemo(
    () =>
      ['stage-smartfarm.viis.tech.s3-website-ap-southeast-1.amazonaws.com', 'localhost'].includes(
        window.location.hostname,
      ),
    [],
  );

  const getMyPlant = async () => {
    try {
      //get token decode from localstorage
      const token = localStorage.getItem('token');
      const decodedToken: any = token ? jwt_decode(token) : null;

      const sections = decodedToken ? decodedToken.sections : 'NULL';
      // console.log('sections', sections);
      if (sections && !(sections.includes('SYSTEM_ADMIN') || sections.includes('PLANT'))) return;

      setLoadingResource(true);
      const data = await getPlantList({
        // filters: [['iot_plant', 'type', '=', 'Common doc']],
        type: documentType.type,
        page: 1,
        size: 1000,
        // fields: ['label', 'name', 'image'],
        // order_by:"creation desc"
      });
      setMyPlant(data?.data || []);
    } catch (error: any) {
      // message.error(error?.toString());
    } finally {
      setLoadingResource(false);
    }
  };
  useEffect(() => {
    if (initialState?.currentUser) {
      getMyPlant();
    }
  }, [initialState]);
  return {
    myPlant,
    setMyPlant,
    loadingResource,
    reload: getMyPlant,
    isAccessEditDocs: isAccessEditDocs,
  };
};
