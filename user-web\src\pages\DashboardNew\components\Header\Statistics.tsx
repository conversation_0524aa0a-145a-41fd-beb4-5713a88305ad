import { DEFAULT_PAGE_SIZE_ALL } from '@/common/contanst/constanst';
import { getCropManagementInfoList } from '@/services/cropManager';
import { getCustomerUserList } from '@/services/customerUser';
import { deviceInProjectList } from '@/services/devices';
import { projectList } from '@/services/projects';
import { deviceSharedInProjectList } from '@/services/sharing-device';
import { getTotalWarehouseQty } from '@/services/stock/dashboard';
import { dayjsUtil } from '@/utils/date';
import { formatMoney } from '@/utils/format';
import { Link, useIntl, useRequest } from '@umijs/max';
import { Avatar, Skeleton } from 'antd';
import { FC, ReactNode } from 'react';

interface StatisticsProps {
  children?: ReactNode;
}

const Item: FC<{
  avatar: string;
  title: string;
  description: string;
  link: string;
  isLoading?: boolean;
}> = ({ avatar, title, description, link, isLoading }) => {
  return (
    <Link to={link} className="flex">
      <div className="border rounded-xl p-2 flex gap-3 min-w-48 items-end hover:shadow cursor-pointer">
        <div className="flex-none">
          <Avatar shape="square" size={55} src={avatar} />
        </div>
        <div>
          <div className="text-base font-semibold mb-1">{title}</div>
          <div className="text-slate-400">
            {isLoading ? <Skeleton.Input size="small" /> : description}
          </div>
        </div>
      </div>
    </Link>
  );
};
type CardItem = {
  key: string;
  avatar: string;
  title: string;
  description: string;
};
const Statistics: FC<StatisticsProps> = ({ children }) => {
  const { data: dataDevice, loading: deviceLoading } = useRequest(async () => {
    const res = await deviceInProjectList({
      filters: [],
      page: 1,
      size: DEFAULT_PAGE_SIZE_ALL,
      fields: ['*'],
      order_by: 'online',
      // project_id: projectId,
    });
    return {
      data: {
        total: res.length,
        deviceOnline: res.reduce((acc, item) => (item.online ? acc + 1 : acc), 0),
      },
    };
  });
  const { data: dataCrop, loading: dataCropLoading } = useRequest(async () => {
    const res = await getCropManagementInfoList({
      page: 1,
      size: 1,
      status: JSON.stringify(['In progress']),
    } as any);

    return {
      data: {
        data: res.data,
        success: true,
        total: res?.pagination?.totalElements,
      },
    };
  });
  const { data: totalWarehouseQty, loading: warehouseLoading } = useRequest(async () => {
    const res = await getTotalWarehouseQty({
      start_date: dayjsUtil().startOf('years').format('YYYY-MM-DD'),
      end_date: dayjsUtil().format('YYYY-MM-DD'),
      warehouse: undefined,
    });
    return {
      data: res.result,
    };
  });
  const { data: myUser, loading: userLoading } = useRequest(async () => {
    const res: any = await getCustomerUserList({
      page: 1,
      size: 1,
    });
    return {
      data: {
        data: res.data,
        total: res.pagination.totalElements,
      },
    };
  });
  const { data: projectData, loading: projectLoading } = useRequest(async () => {
    const res = await projectList({
      page: 1,
      size: 1,
      fields: ['name'],
    });
    return {
      data: {
        data: res.data,
        total: res.pagination.totalElements,
      },
    };
  });
  const { data: dataDeviceShare, loading: deviceShareLoading } = useRequest(async () => {
    const res = await deviceSharedInProjectList({
      filters: [],
      page: 1,
      size: DEFAULT_PAGE_SIZE_ALL,
      fields: ['*'],
      order_by: 'online',
      // project_id: projectId,
    });
    return {
      data: {
        total: res.length,
        deviceOnline: res.reduce((acc, item) => (item.online ? acc + 1 : acc), 0),
      },
    };
  });
  const { formatMessage } = useIntl();
  const cardRender = [
    {
      key: '0',
      avatar: '/images/new-dashboard/iot.png',
      title: formatMessage({
        id: 'new-dashboard.iot-device',
      }),
      description: `${dataDevice?.total} ${formatMessage({
        id: 'new-dashboard.iot-device',
      })}`,
      link: '/iot-device-management',
      isLoading: deviceLoading,
    },
    {
      key: '1',
      avatar: '/images/new-dashboard/iot.png',
      title: formatMessage({
        id: 'common.shared_devices',
      }),
      description: `${dataDeviceShare?.total} ${formatMessage({
        id: 'new-dashboard.iot-device',
      })}`,
      link: '/iot-device-management?tab=thiet+bi+duoc+chia+se',
      isLoading: deviceShareLoading,
    },

    {
      key: '3',
      avatar: '/images/new-dashboard/crop.png',

      title: formatMessage({
        id: 'common.crop',
      }),
      description: `${dataCrop?.total} ${formatMessage({
        id: 'new-dashboard.crop-ongoing',
      })}`,
      link: '/farming-management/seasonal-management',
      isLoading: dataCropLoading,
    },

    {
      key: '4',
      avatar: '/images/new-dashboard/inventoryy.png',

      title: formatMessage({
        id: 'common.inventory',
      }),
      description: `${formatMessage({
        id: 'new-dashboard.inventory-value',
      })} ${formatMoney(totalWarehouseQty?.total_price)} VNĐ`,
      link: '/warehouse-management-v3/inventory',
      isLoading: warehouseLoading,
    },
    {
      key: '5',
      avatar: '/images/new-dashboard/employee.png',

      title: formatMessage({
        id: 'new-dashboard.employee',
      }),
      description: `${myUser?.total} ${formatMessage({
        id: 'new-dashboard.employee-working',
      })}`,
      link: '/employee-management/employee-list',
      isLoading: userLoading,
    },
    {
      key: '6',
      avatar: '/images/new-dashboard/project.png',

      title: formatMessage({
        id: 'new-dashboard.project',
      }),
      description: `${projectData?.total} ${formatMessage({
        id: 'new-dashboard.project-ongoing',
      })}`,
      link: '/project-management',
      isLoading: projectLoading,
    },
  ];

  const ItemCustom: FC<{
    avatar: string;
    title: string;
    description: string;
    link: string;
    isLoading?: boolean;
  }> = ({ avatar, title, description, link, isLoading }) => {
    return (
      <Link to={link} className="flex-none">
        <div className="border hover:border-[#9DDBB1] hover:shadow-sm transition-colors duration-300 p-4 flex gap-4 min-w-[12rem] h-24 items-center cursor-pointer">
          <div className="flex-none">
            <Avatar shape="square" size={44} src={avatar} className="!bg-gray-50" />
          </div>
          <div className="flex flex-col justify-center min-w-0">
            <div className="text-sm text-gray-500 mb-1">{title}</div>
            <div className="text-base font-medium text-gray-900 truncate">
              {isLoading ? <Skeleton.Input size="small" /> : description}
            </div>
          </div>
        </div>
      </Link>
    );
  };
  return (
    <div className="flex items-stretch gap-4 flex-nowrap overflow-auto max-w-full scrollbar-thin scrollbar-thumb-gray-200 scrollbar-track-transparent pb-1">
      {cardRender.map((item) => (
        <ItemCustom {...item} key={item.key} />
      ))}
    </div>
  );
};

export default Statistics;
