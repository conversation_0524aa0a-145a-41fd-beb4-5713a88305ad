import { PageContainer } from '@ant-design/pro-components';
import { FC, ReactNode } from 'react';
import StageOfCropCreate from './components/Create';

interface CreateStageOfCropPageProps {
  children?: ReactNode;
}

const CreateStageOfCropPage: FC<CreateStageOfCropPageProps> = ({ children }) => {
  return (
    <PageContainer>
      <StageOfCropCreate />
    </PageContainer>
  );
};

export default CreateStageOfCropPage;
