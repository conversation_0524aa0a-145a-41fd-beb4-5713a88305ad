import { DOCTYPE_ERP } from '@/common/contanst/constanst';
import { getTraceList, Trace } from '@/services/diary-2/trace';
import { useRequest } from '@umijs/max';

export default function useDetail(
  { id, onSuccess } = {} as {
    onSuccess?: (data: Trace) => void;
    id: string;
  },
) {
  return useRequest(
    async () => {
      if (!id) return null;
      const res = await getTraceList({
        filters: [[DOCTYPE_ERP.iot_diary_v2_traceability_batch, 'name', '=', id]],
        order_by: 'name asc',
        page: 1,
        size: 1,
      });
      const data = res?.data?.[0];
      if (!data) throw new Error('Not found');
      return {
        data,
      };
    },
    {
      onSuccess: (data) => {
        if (data) onSuccess?.(data);
      },
      refreshDeps: [id],
    },
  );
}
