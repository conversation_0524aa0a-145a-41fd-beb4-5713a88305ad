import { FC, ReactNode } from 'react';
import Greetings from './Greetings';
import Statistics from './Statistics';
import Weather from './Weather';

interface HeaderProps {
  children?: ReactNode;
}

const Header: FC<HeaderProps> = ({ children }) => {
  return (
    <div>
      {/* Row container with flex display */}
      <div className="flex items-center justify-between sm:mb-2 lg:mb-6 xl:mb-6 2xl:mb-6">
        <Greetings />
        <Weather />
      </div>
      <div>
        <Statistics />
      </div>
    </div>
  );
};

export default Header;
