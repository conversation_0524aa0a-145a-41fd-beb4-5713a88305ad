import ImagePreviewGroupCommon, { IImagePreview } from '@/components/ImagePreviewGroupCommon';
import { FormattedMessage, useAccess, useIntl } from '@umijs/max';
import { App, Card, Space, Typography } from 'antd';
import dayjs from 'dayjs';
import { FC, ReactNode } from 'react';

export interface PandemicInfoProps {
  data: {
    id: string;
    title?: ReactNode;
    time?: string;
    stage?: ReactNode;
    description?: ReactNode;
    state_list?: ReactNode[];
    category_list?: ReactNode[];
    involved_in_users?: ReactNode[];
    listImg?: IImagePreview[];
  };
  onDeleteSuccess?: (id: string) => void;
}

const PandemicInfo: FC<PandemicInfoProps> = ({ data, onDeleteSuccess }) => {
  const {
    id,
    title,
    time,
    stage,
    description,
    listImg,
    state_list = [],
    category_list = [],
    involved_in_users = [],
  } = data;

  const { message, modal } = App.useApp();

  const access = useAccess();
  const intl = useIntl();

  return (
    <Card
      title={title}
      extra={
        <Space>
          <span>
            {time
              ? `${intl.formatMessage({ id: 'common.date-created' })}: ${dayjs(time).format(
                  'hh:mm:ss ,DD/MM/YYYY',
                )}`
              : null}
          </span>
          {/* <UpdatePandemicModal key="update-pandemic" cropId={'ddđ'} onSuccess={() => {}} /> */}
        </Space>
      }
    >
      {category_list?.length > 0 && (
        <Typography.Paragraph>
          <Typography.Text strong>
            {intl.formatMessage({ id: 'seasonalTab.relatedSupplies' })}:
          </Typography.Text>
          <ul>
            {category_list.map((category: any, index) => (
              <li key={index}>
                <Typography.Text>{`${category.label}`}</Typography.Text>
              </li>
            ))}
          </ul>
        </Typography.Paragraph>
      )}

      {state_list?.length > 0 && (
        <Typography.Paragraph>
          <Typography.Text strong>
            <FormattedMessage id="seasonalTab.relatedSupplies" />:
          </Typography.Text>
          <ul>
            {state_list.map((state: any, index) => (
              <li key={index}>
                <Typography.Text>{`${state.label}`}</Typography.Text>
              </li>
            ))}
          </ul>
        </Typography.Paragraph>
      )}
      {involved_in_users?.length > 0 && (
        <Typography.Paragraph>
          <Typography.Text strong>
            {' '}
            {intl.formatMessage({ id: 'common.people_involved' })}:
          </Typography.Text>
          <ul>
            {involved_in_users.map((user: any, index) => (
              <li key={index}>
                <Typography.Text>{`${user.last_name} ${user.first_name}`}</Typography.Text>
              </li>
            ))}
          </ul>
        </Typography.Paragraph>
      )}

      <Typography.Paragraph>
        <Typography.Text strong>
          {intl.formatMessage({ id: 'common.form.description' })}:
        </Typography.Text>{' '}
        <Typography.Text>{description}</Typography.Text>
      </Typography.Paragraph>

      <ImagePreviewGroupCommon listImg={listImg} />
    </Card>
  );
};

export default PandemicInfo;
