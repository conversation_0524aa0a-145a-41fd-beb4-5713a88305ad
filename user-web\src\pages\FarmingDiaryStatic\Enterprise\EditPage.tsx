import { PageContainer } from '@ant-design/pro-components';
import { useParams } from '@umijs/max';
import { FC, ReactNode } from 'react';
import EditEnterprise from './components/Edit';

interface EditPageProps {
  children?: ReactNode;
}

const EditPage: FC<EditPageProps> = ({ children }) => {
  const { id } = useParams();
  if (!id) return null;
  return (
    <PageContainer>
      <EditEnterprise id={id} />
    </PageContainer>
  );
};

export default EditPage;
