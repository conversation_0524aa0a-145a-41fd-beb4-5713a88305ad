import { DEFAULT_FALLBACK_IMG } from '@/common/contanst/img';
import { Card,Image,Skeleton,Space,Typography } from 'antd';
import { createStyles } from 'antd-use-styles';
import { isArray } from 'lodash';
import { CSSProperties,FC } from 'react';

export interface IImagePreview {
  src?: string | null;
  previewSrc?: string;
  caption?: string;
}
export interface ImagePreviewGroupCommonProps {
  gutter?: number | [number, number];
  width?: number;
  imgHeight?: number;
  listImg?: IImagePreview[];
  wrapperStyle?: CSSProperties;
}
const DEFAULT_WIDTH = 115;
const DEFAULT_HEIGHT = 75;
const useStyles = createStyles(({ token }, params?: { gap: number | [number, number] }) => ({
  wrapper: {
    display: 'flex',
    flexWrap: 'wrap',
    gap: typeof params?.gap === 'number' ? params.gap : undefined,
    rowGap: isArray(params?.gap) ? params?.gap?.[0] : undefined,
    columnGap: isArray(params?.gap) ? params?.gap?.[1] : undefined,
  },
  img: {
    borderRadius: token.borderRadius,
    objectFit: 'cover',
    minWidth: DEFAULT_WIDTH,
  },
}));

const ImagePreviewGroupCommon: FC<ImagePreviewGroupCommonProps> = ({
  gutter,
  imgHeight,
  width,
  listImg,
  wrapperStyle,
}) => {
  const styles = useStyles({
    gap: gutter || 10,
  });
  return (
    <Image.PreviewGroup>
      <div className={styles.wrapper} style={wrapperStyle}>
        {listImg?.map((item, index) => (
          <Card
            style={{
              width: width || DEFAULT_WIDTH,
            }}
            key={index}
            bodyStyle={{
              padding: 3,
            }}
          >
            <Space
              direction="vertical"
              style={{
                width: '100%',
              }}
            >
              <Image
                // onError={(e) => {
                //   e.currentTarget.onerror = null;
                //   e.currentTarget.src = DEFAULT_FALLBACK_IMG;
                // }}
                placeholder={
                  <Skeleton.Image
                    style={{
                      height: imgHeight || DEFAULT_HEIGHT,
                    }}
                    active
                  />
                  // <Image
                  //   preview={false}
                  //   src="https://zos.alipayobjects.com/rmsportal/jkjgkEfvpUPVyRjUImniVslZfWPnJuuZ.png?x-oss-process=image/blur,r_50,s_50/quality,q_1/resize,m_mfit,h_200,w_200"
                  //   width={imgWidth || 110}
                  //   height={imgHeight || 72}
                  // />
                }
                width={'100%'}
                height={imgHeight || DEFAULT_HEIGHT}
                className={styles.img}
                src={item.src || DEFAULT_FALLBACK_IMG}
                fallback={DEFAULT_FALLBACK_IMG}
                preview={
                  item?.previewSrc
                    ? {
                        src: item.previewSrc,
                      }
                    : undefined
                }
              />
              {item.caption && <Typography.Text type="secondary">{item.caption}</Typography.Text>}
            </Space>
          </Card>
        ))}
      </div>
    </Image.PreviewGroup>
  );
};

export default ImagePreviewGroupCommon;
