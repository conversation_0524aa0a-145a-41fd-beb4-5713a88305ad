import { PlusOutlined } from '@ant-design/icons';
import { ModalForm, ProFormText, ProFormTextArea } from '@ant-design/pro-components';
import { Button, Form } from 'antd';
import { uniqueId } from 'lodash';
import { FC, useState } from 'react';

interface CreateGenericInfoProps {
  triggerLabel: string;
  handleNewGenericInfo: any;
}
interface IFormData {
  name: string;
  label: string;
  description: string;
  sort_index?: string;
  is_new?: boolean;
}
const CreateGenericInfo: FC<CreateGenericInfoProps> = ({ triggerLabel, handleNewGenericInfo }) => {
  const [form] = Form.useForm();
  const [submitting, setSubmitting] = useState<boolean>(false);
  const handleFinish = async (values: IFormData) => {
    setSubmitting(true);
    values.name = uniqueId();
    values.is_new = true;
    handleNewGenericInfo(values);
    setSubmitting(false);
    return true;
  };
  return (
    <ModalForm<IFormData>
      width="320px"
      title={triggerLabel}
      trigger={
        <Button type="primary" icon={<PlusOutlined />}>
          {triggerLabel}
        </Button>
      }
      autoFocusFirstInput
      modalProps={{
        destroyOnClose: true,
      }}
      form={form}
      submitter={{
        render: (props) => {
          return [
            <Button
              key="ok"
              onClick={() => {
                props.submit();
              }}
              type="primary"
              style={{ width: '100%' }}
              loading={submitting}
            >
              Đồng ý
            </Button>,
          ];
        },
      }}
      submitTimeout={2000}
      onFinish={handleFinish}
    >
      <ProFormText
        rules={[{ required: true, message: 'Tên không được để trống' }]}
        required
        name="label"
        label="Tên"
      />
      <ProFormTextArea
        rules={[{ required: true, message: `Xin vui lòng điền thông tin` }]}
        style={{ width: '100%' }}
        required
        name="description"
        label="Nội dung"

      />
    </ModalForm>
  );
};

export default CreateGenericInfo;
