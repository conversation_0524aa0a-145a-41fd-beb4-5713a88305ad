import { ProForm } from '@ant-design/pro-components';
import { history, useIntl } from '@umijs/max';
import { App, Spin } from 'antd';
import { FC, ReactNode } from 'react';
import useDetail from '../../hooks/useDetail';
import useUpdate from '../../hooks/useUpdate';
import DetailedInfo from './DetailedInfo';
import ImgVideos from './ImgVideos';
import MaterialUsed from './MaterialUsed';

interface CreateTaskProps {
  children?: ReactNode;
  id: string;
}

const EditTask: FC<CreateTaskProps> = ({ children, id }) => {
  const { formatMessage } = useIntl();
  const [form] = ProForm.useForm();
  const { run } = useUpdate();
  const { data, loading, refresh } = useDetail({
    id: id,
    onSuccess(data) {
      if (data) form.setFieldsValue(data);
    },
  });
  const { message } = App.useApp();
  const onFinish = async (values: any) => {
    const res = await run({
      name: id,
      label: values.label,
      description: values.description,
      assigned_to: values.assigned_to,
      image: values.image,
      level: values.level,
      expire_time_in_days: values.expire_time_in_days,
      execution_day: values.execution_day,
    });

    // await Promise.all(
    //   values.related_items.map(async (item: RelatedItemForm) => {
    //     return createItem({
    //       item_id: item.item_id,
    //       task_id: res.name,
    //       quantity: item.quantity,
    //       uom_id: item.uom_id,
    //     });
    //   }),
    // );
    // message.success(
    //   formatMessage({
    //     id: 'common.success',
    //   }),
    // );
    history.push('/farming-diary-static/task/list');
    return true;
  };

  return (
    <Spin spinning={loading}>
      <ProForm onFinish={onFinish} form={form}>
        <div className="mb-4 space-y-4">
          <DetailedInfo />
          <MaterialUsed onSuccess={refresh} taskId={id} />
          <ImgVideos initialValue={data?.image} />
        </div>
      </ProForm>
    </Spin>
  );
};

export default EditTask;
