import { createItem } from '@/services/workType';
import { PlusOutlined } from '@ant-design/icons';
import { Button, Col, Form, Input, message, Modal, Row } from 'antd';
import { useState } from 'react';
const { Item } = Form;

const Create = (params: { refreshFnc: any }) => {
  const [loading, setLoading] = useState(false);
  const [isOpen, setOpen] = useState(false);
  const [form] = Form.useForm();

  const showModal = () => {
    setOpen(true);
  };
  const hideModal = () => {
    setOpen(false);
  };
  const handleOk = () => {
    form.submit();
  };
  const handleCancel = () => {
    hideModal();
    form.resetFields();
  };

  return (
    <>
      <Button type="primary" onClick={showModal} style={{ display: 'flex', alignItems: 'center' }}>
        <PlusOutlined />
      </Button>
      <Modal
        title={`Thêm loại nhân công`}
        open={isOpen}
        onOk={handleOk}
        onCancel={handleCancel}
        confirmLoading={loading}
      >
        <Form
          layout="horizontal"
          labelCol={{ span: 24 }}
          labelAlign="left"
          form={form}
          onFinish={async (value: any) => {
            try {
              const result = await createItem(value);
              form.resetFields();
              hideModal();
              message.success('Success!');
              if (params.refreshFnc) {
                await params.refreshFnc();
              }
            } catch (error: any) {
              message.error(error.toString());
            } finally {
              setLoading(false);
            }
          }}
        >
          <Row gutter={5}>
            <Row gutter={5}>
              <Col className="gutter-row" md={24}>
                <Item
                  label="Tên"
                  labelCol={{ span: 24 }}
                  rules={[
                    {
                      required: true,
                      message: 'Bắt buộc điền',
                    },
                  ]}
                  name="label"
                >
                  <Input placeholder="VD: Làm cỏ" />
                </Item>
              </Col>
            </Row>
          </Row>
        </Form>
      </Modal>
    </>
  );
};

export default Create;
