import { nonAccentVietnamese } from '@/utils/string';
import { useSearchParams } from '@umijs/max';
import { Tabs } from 'antd';
import React, { FC, ReactNode, Suspense, useMemo } from 'react';

export type PageTabItem = {
  label: string;
  tabKey?: string;
  component?: () => React.JSX.Element;
  fallback?: ReactNode;
};

interface PageContainerTabsProps {
  tabsItems?: PageTabItem[];
  searchParamsUrlKey?: string;
  autoFormatTabKey?: boolean;
  onTabChange?: (tabKey: string | number) => void;
}

const TabsWithSearch: FC<PageContainerTabsProps> = ({
  tabsItems,
  searchParamsUrlKey = 'tab',
  onTabChange,
}) => {
  const tabsItemsFormat = useMemo(
    () =>
      tabsItems?.map((item) => ({
        ...item,
        tabKey: item.tabKey || nonAccentVietnamese(item.label),
      })),
    [tabsItems],
  );
  const [searchParams, setSearchParams] = useSearchParams();
  const tabActive = searchParamsUrlKey ? searchParams.get(searchParamsUrlKey) : undefined;
  const setTabActive = searchParamsUrlKey
    ? (tabActiveVal: string | number) => {
        searchParams.set(searchParamsUrlKey, tabActiveVal.toString());
        setSearchParams(searchParams);
      }
    : undefined;

  const tabList = tabsItemsFormat?.map((item) => ({
    label: item.label,
    key: item.tabKey,
    tabKey: item.tabKey,
  }));

  const tabPageActive = searchParamsUrlKey
    ? tabsItemsFormat?.find((item) => item.tabKey === tabActive) || tabsItemsFormat?.[0]
    : undefined;
  const ComponentActive = tabPageActive?.component;

  return (
    <>
      <Tabs
        activeKey={tabPageActive?.tabKey}
        items={tabList}
        onChange={(tabActiveVal) => {
          onTabChange?.(tabActiveVal);
          if (searchParamsUrlKey) setTabActive?.(tabActiveVal);
        }}
      />
      <Suspense fallback={tabPageActive?.fallback || null}>
        {ComponentActive && <ComponentActive />}
      </Suspense>
    </>
  );
};

export default TabsWithSearch;
