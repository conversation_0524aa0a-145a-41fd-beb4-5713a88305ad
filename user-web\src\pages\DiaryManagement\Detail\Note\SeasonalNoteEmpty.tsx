import wormSrc from '@/assets/img/note-empty.png';
import { PlusOutlined } from '@ant-design/icons';
import { Button, Typography } from 'antd';
import { createStyles } from 'antd-use-styles';
import { FC } from 'react';
import CreateNoteModal from './Create';

interface SeasonalNoteEmptyProps {
  cropId?: string;
  onCreateSuccess?: () => void;
}
const useStyles = createStyles(({ token }) => ({
  container: {
    width: '100%',
    height: '100%',
    minHeight: 700,
    display: 'flex',
    alignItems: 'center',
    justifyContent: 'center',
    padding: token.padding,
    backgroundColor: '#fcfcfd',
  },
  wrapperContent: {
    width: 221,
    textAlign: 'center',
    display: 'flex',
    flexDirection: 'column',
    gap: 24,
  },
  img: {
    objectFit: 'contain',
  },
}));
const SeasonalNoteEmpty: FC<SeasonalNoteEmptyProps> = ({ cropId, onCreateSuccess }) => {
  const styles = useStyles();
  return (
    <div className={styles.container}>
      <div className={styles.wrapperContent}>
        <img className={styles.img} alt="icon" src={wormSrc} />
        <Typography.Paragraph type="secondary">
          Bạn chưa có ghi chú nào cả. Hãy ghi lại những điều cần thiết về cây trồng nhé
        </Typography.Paragraph>
        <CreateNoteModal
          cropId={cropId}
          onSuccess={onCreateSuccess}
          trigger={
            <Button block size="large" type="primary" icon={<PlusOutlined />}>
              Tạo ghi chú
            </Button>
          }
        />
      </div>
    </div>
  );
};

export default SeasonalNoteEmpty;
