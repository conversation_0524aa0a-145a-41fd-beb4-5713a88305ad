import { Form, Input, Tree } from 'antd';
import type { DataNode } from 'antd/es/tree';
import React, { useEffect, useMemo, useState } from 'react';

const { Search } = Input;

const dataList: { key: React.Key; title: string }[] = [];
export interface CustomDataNode extends DataNode {
  normalized_title: React.Key;
}
const getParentKey = (key: React.Key, tree: DataNode[]): React.Key => {
  let parentKey: React.Key;
  for (let i = 0; i < tree.length; i++) {
    const node = tree[i];
    if (node.children) {
      if (node.children.some((item) => item.key === key)) {
        parentKey = node.key;
      } else if (getParentKey(key, node.children)) {
        parentKey = getParentKey(key, node.children);
      }
    }
  }
  return parentKey!;
};
export interface ITreeNode {
  title: string;
  value: string;
  key: string;
  normalized_title: string;
  children?: ITreeNode[];
}
interface SearchableTreeSelectProps {
  defaultData: ITreeNode[];
  fieldName: string;
}
const SearchableTreeSelect = ({ defaultData, fieldName }: SearchableTreeSelectProps) => {
  const [expandedKeys, setExpandedKeys] = useState<React.Key[]>([]);
  const [searchValue, setSearchValue] = useState('');
  const [autoExpandParent, setAutoExpandParent] = useState(true);
  const [dataList, setDataList] = useState<
    { key: React.Key; title: string; normalized_title: string }[]
  >([]);
  const onExpand = (newExpandedKeys: React.Key[]) => {
    setExpandedKeys(newExpandedKeys);
    setAutoExpandParent(false);
  };
  const form = Form.useFormInstance();

  useEffect(() => {
    const newDataList: { key: React.Key; title: string; normalized_title: string }[] = [];
    const generateList = (data: ITreeNode[]) => {
      for (let i = 0; i < data.length; i++) {
        const node = data[i];
        const { key, title, normalized_title } = node;
        newDataList.push({
          key,
          title: title,
          normalized_title,
        });
        if (node.children) {
          generateList(node.children);
        }
      }
    };
    generateList(defaultData);
    setDataList(newDataList);
  }, [defaultData]);

  const onChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { value } = e.target;
    const normalized_value = value
      .toLowerCase()
      .normalize('NFD')
      .replace(/[\u0300-\u036f]/g, '');
    const newExpandedKeys = dataList
      .map((item) => {
        if (item.normalized_title.indexOf(normalized_value) > -1) {
          return getParentKey(item.key, defaultData);
        }
        return null;
      })
      .filter((item, i, self): item is React.Key => !!(item && self.indexOf(item) === i));
    setExpandedKeys(newExpandedKeys);
    setSearchValue(value);
    setAutoExpandParent(true);
  };

  const treeData = useMemo(() => {
    const loop = (data: ITreeNode[]): DataNode[] =>
      data.map((item) => {
        const normalizedSearchValue = searchValue
          .toLowerCase()
          .normalize('NFD')
          .replace(/[\u0300-\u036f]/g, '');
        const strTitle = item.normalized_title as string;
        const index = strTitle.indexOf(normalizedSearchValue);
        const beforeStr = item.title.substring(0, index);
        const str = item.title.substring(index, index + searchValue.length);
        const afterStr = item.title.slice(index + searchValue.length);

        const title =
          searchValue === '' ? (
            <span>{item.title}</span>
          ) : index > -1 ? (
            <span>
              {beforeStr}
              <span style={{ color: 'white', backgroundColor: 'green' }}>{str}</span>
              {afterStr}
            </span>
          ) : (
            <span>{item.title}</span>
          );
        if (item.children) {
          return { title, key: item.key, children: loop(item.children) };
        }

        return {
          title,
          key: item.key,
        };
      });

    return loop(defaultData);
  }, [searchValue]);

  const handleOnCheck = (e: any) => {
    form.setFieldValue(fieldName, e);
  };
  return (
    <div>
      <Search style={{ marginBottom: 8 }} placeholder="Search" onChange={onChange} />
      <Tree
        onExpand={onExpand}
        expandedKeys={expandedKeys}
        autoExpandParent={autoExpandParent}
        treeData={treeData}
        checkable={true}
        onCheck={handleOnCheck}
      />
    </div>
  );
};

export default SearchableTreeSelect;
