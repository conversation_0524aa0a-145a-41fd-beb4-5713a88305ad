import { ProForm } from '@ant-design/pro-components';
import { FC, ReactNode } from 'react';
import Info from './Info';
import MaterialUse from './MaterialUse';
import Media from './Media';

interface CreateNewTodoProps {
  children?: ReactNode;
}

const CreateNewTodo: FC<CreateNewTodoProps> = ({ children }) => {
  const onFinish = async () => {};
  const [form] = ProForm.useForm();
  return (
    <ProForm onFinish={onFinish} form={form}>
      <div className="flex flex-col gap-4 mb-4">
        <Info />
        <MaterialUse />
        <Media />
      </div>
    </ProForm>
  );
};

export default CreateNewTodo;
