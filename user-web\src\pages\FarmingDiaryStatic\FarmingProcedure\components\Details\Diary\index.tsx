import { Process } from '@/services/diary-2/process';
import { getFullImgUrlArrString } from '@/utils/image';
import { Avatar, Button } from 'antd';
import { FC, ReactNode, useEffect, useRef, useState } from 'react';
import Detail from './Detail';

interface DiaryProps {
  children?: ReactNode;
  data?: Process | null;
}

const Diary: FC<DiaryProps> = ({ children, data }) => {
  const [active, setActive] = useState(data?.states?.[0]);
  const containerRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    // Set the width of the scrollable container to match the width of the Detail component
    if (containerRef.current) {
      const detailComponent = containerRef.current.nextElementSibling;
      if (detailComponent) {
        containerRef.current.style.width = `${detailComponent.clientWidth}px`;
      }
    }
  }, []);

  return (
    <div>
      <div
        ref={containerRef}
        className="flex mb-6 overflow-x-auto"
        // style={{
        //   msOverflowStyle: 'none' /* Hide scrollbar in IE and Edge */,
        //   scrollbarWidth: 'none' /* Hide scrollbar in Firefox */,
        // }}
      >
        {data?.states?.map((item, index, arr) => (
          <div key={item.name} className="flex items-center">
            <Button
              disabled={false}
              icon={<Avatar src={getFullImgUrlArrString(item.image)?.[0]} size={'large'} />}
              type="link"
              onClick={() => setActive(item)}
              className="flex items-center"
              style={{
                paddingTop: 20,
                paddingBottom: 20,
                // msOverflowStyle: 'none' /* Hide scrollbar in IE and Edge */,
                // scrollbarWidth: 'none' /* Hide scrollbar in Firefox */,
                WebkitOverflowScrolling: 'touch' /* Enable smooth scrolling */,
                // overflow: 'hidden' /* Hide scrollbar in WebKit browsers */,
              }}
            >
              <span>{item.label}</span>
              {index === arr.length - 1 ? null : (
                <span className="ml-4 border-t-2 border-gray-500 w-20"></span>
              )}
            </Button>
          </div>
        ))}
      </div>
      <div>{active && <Detail dataState={active} />}</div>
    </div>
  );
};

export default Diary;
