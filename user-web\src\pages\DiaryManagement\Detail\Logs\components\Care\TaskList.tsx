import { DOCTYPE_ERP } from '@/common/contanst/constanst';
import { getDiaryTaskList, IDiaryTask } from '@/services/farming-plan';
import { PlusOutlined } from '@ant-design/icons';
import { ActionType, ProList } from '@ant-design/pro-components';
import { useIntl } from '@umijs/max';
import { Button, Divider } from 'antd';
import { useRef, useState } from 'react';
import CreateWorkflow from './CreateTask';
import TaskCard from './TaskCard';

interface Props {
  cropId: string;
  planId: string;
}
const TaskList = ({ cropId, planId }: Props) => {
  const [openModalCreateNewTask, setOpenModalCreateNewTask] = useState(false);
  const intl = useIntl();

  const tableRef = useRef<ActionType>();
  return (
    <>
      <CreateWorkflow
        mode="modal"
        open={openModalCreateNewTask}
        onOpenChange={setOpenModalCreateNewTask}
        onCreateSuccess={() => {
          tableRef.current?.reload();
        }}
        planId={planId}
      />
      <Button
        key={'create'}
        icon={<PlusOutlined />}
        onClick={() => {
          setOpenModalCreateNewTask(true);
          return;
        }}
        type="primary"
      >
        {intl.formatMessage({ id: 'workflowTab.createWork' })}
      </Button>
      <Divider />

      <ProList<IDiaryTask>
        itemCardProps={{ ghost: true }}
        grid={{ column: 1, gutter: 5 }}
        actionRef={tableRef}
        pagination={{ pageSize: 20 }}
        renderItem={(item) => {
          return <TaskCard item={item} />;
        }}
        request={async (params, sort, filter) => {
          try {
            const res: any = await getDiaryTaskList({
              page: params.current,
              size: params.pageSize,
              filters: [[DOCTYPE_ERP.iotCrop, 'name', 'like', cropId]],
              order_by: 'start_date desc',
            });
            return {
              data: res.data,
              total: res.pagination.totalElements,
            };
          } catch (error) {
            return {
              data: [],
              success: false,
            };
          }
        }}
      ></ProList>
    </>
  );
};

export default TaskList;
