import { deleteStage } from '@/services/diary-2/stage';
import { useIntl, useRequest } from '@umijs/max';
import { App } from 'antd';

export default function useDelete(
  { onError, onSuccess } = {} as {
    onSuccess?: () => void;
    onError?: () => void;
  },
) {
  const { formatMessage } = useIntl();
  const { message } = App.useApp();
  return useRequest(deleteStage, {
    manual: true,
    onSuccess(data, params) {
      message.success(
        formatMessage({
          id: 'common.success',
        }),
      );
      onSuccess?.();
    },
    onError: (error) => {
      //message.error(error.message);
      onError?.();
    },
  });
}
