import { DEFAULT_DATE_FORMAT_WITHOUT_TIME } from '@/common/contanst/constanst';
import { cropList } from '@/services/crop';
import { ProForm, ProFormDateRangePicker, ProFormSelect } from '@ant-design/pro-components';
import { useModel } from '@umijs/max';
import { Card, Col, Input, Row } from 'antd';
import { FC, ReactNode, useEffect, useState } from 'react';

interface DetailedInfoProps {
  children?: ReactNode;
}

const DetailedInfo: FC<DetailedInfoProps> = ({ children }) => {
  const [crops, setCrops] = useState([]); // Danh sách vụ mùa
  const { selectedCrop, setSelectedCrop } = useModel('MyCrop');
  // const [selectedCrop, setSelectedCrop] = useState<any>({}); // Initialize as an empty object
  //handle crop
  useEffect(() => {
    const getCrop = async () => {
      const res: any = await cropList({
        page: 1,
        size: 1000,
        fields: ['*'],
        filters: [],
        or_filters: [],
        order_by: '',
        group_by: '',
      });
      setCrops(res.data); // Lưu danh sách dự án vào state
    };
    getCrop();
  }, [selectedCrop]);

  // Update the selectedCrop state when the user selects a crop
  const handleCropChange = async (value: string) => {
    crops.map((crop: any) => {
      if (crop.name === value) {
        setSelectedCrop(crop); // Lưu dự án đã chọn vào state
        console.log('crop nek', crop);
      }
    });
  };

  useEffect(() => {
    console.log('selectedCrop', selectedCrop);
  }, [selectedCrop]);

  return (
    <Card title="Thông tin chi tiết">
      <ProForm.Group>
        <Row gutter={25}>
          <Col className="gutter-row" md={12}>
            <ProFormSelect
              label={'Tên vụ mùa'}
              rules={[
                {
                  required: true,
                },
              ]}
              showSearch
              width={'lg'}
              options={crops.map((crop: any) => ({ value: crop.name, label: crop.label }))} // Hiển thị danh sách vu mua
              onChange={handleCropChange} // Xử lý sự kiện khi người dùng chọn
            />
            <ProForm.Item name="" label="Diện tích canh tác">
              <Input disabled width={'large'} value={selectedCrop.square} />
            </ProForm.Item>
          </Col>
          <Col className="gutter-row" md={12}>
            <ProFormDateRangePicker
              disabled
              label="Thời gian hoàn thành"
              width={'lg'}
              id="completionTimeInput"
              placeholder={[selectedCrop.start_date, selectedCrop.end_date]}
              fieldProps={{
                format: DEFAULT_DATE_FORMAT_WITHOUT_TIME,
              }}
            />
            <ProForm.Item name="" label="Sản phẩm canh tác">
              <Input disabled width={'large'} value={selectedCrop.plant_label} />
            </ProForm.Item>
          </Col>
        </Row>
      </ProForm.Group>
    </Card>
  );
};

export default DetailedInfo;
