import ActionModalConfirm from '@/components/ActionModalConfirm';
import { FC, ReactNode } from 'react';
import useDelete from './hooks/useDelete';

interface DeleteMemberProps {
  children?: ReactNode;
  id: string;
  onSuccess?: () => void;
}

const DeleteMember: FC<DeleteMemberProps> = ({ children, id, onSuccess }) => {
  const { run, loading } = useDelete({
    onSuccess,
  });
  return (
    <ActionModalConfirm
      modalProps={{
        async onOk() {
          await run(id);
          return true;
        },
      }}
    />
  );
};

export default DeleteMember;
