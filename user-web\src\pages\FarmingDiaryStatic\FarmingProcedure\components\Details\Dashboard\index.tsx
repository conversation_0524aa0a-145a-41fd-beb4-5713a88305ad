import FormUploadsPreviewable from '@/components/FormUploadsPreviewable';
import { Process } from '@/services/diary-2/process';
import { ProForm, ProFormText, ProFormTextArea } from '@ant-design/pro-components';
import { useIntl } from '@umijs/max';
import { Card, Col, Row } from 'antd';
import { FC, ReactNode, useEffect } from 'react';

interface DashboardProps {
  children?: ReactNode;
  data?: Process | null;
}
const w = 'xl';
const Dashboard: FC<DashboardProps> = ({ children, data }) => {
  const { formatMessage } = useIntl();
  const [form] = ProForm.useForm();
  useEffect(() => {
    if (data) {
      form.setFieldsValue(data);
    }
  }, [data]);
  return (
    <ProForm disabled form={form} submitter={false}>
      <Card
        title={formatMessage({
          id: 'task.detailed_info',
        })}
      >
        <FormUploadsPreviewable
          initialImages={data?.image}
          fileLimit={10}
          label={formatMessage({ id: 'common.image' })}
          formItemName={'image'}
        />
        <Row gutter={24}>
          <Col span={12}>
            <ProFormText label={formatMessage({ id: 'common.name' })} name="label" />
          </Col>{' '}
          <Col span={12}>
            <ProFormTextArea
              fieldProps={{
                autoSize: { minRows: 1, maxRows: 10 }, // Adjust minRows and maxRows as needed
              }}
              label={formatMessage({ id: 'common.note' })}
              name="description"
            />
          </Col>
        </Row>
      </Card>
    </ProForm>
  );
};

export default Dashboard;
