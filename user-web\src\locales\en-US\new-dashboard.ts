const genKeyPrefix = (key: string) => `new-dashboard.${key}`

export default {
  [genKeyPrefix('see-all')]: 'See all',
  [genKeyPrefix('iot-device')]: 'IoT device ',
  [genKeyPrefix('notice-task')]: 'You have {count} tasks to do today',
  [genKeyPrefix('crop-ongoing')]: 'crops are ongoing',
  [genKeyPrefix('inventory-value')]: 'Inventory value',
  [genKeyPrefix('employee')]: 'Employee',
  [genKeyPrefix('employee-working')]: 'Employees are working',
  [genKeyPrefix('project')]: 'Project',
  [genKeyPrefix('project-ongoing')]: 'projects are ongoing',
  [genKeyPrefix('notice-and-alarm')]: 'Notice and alarm',
  [genKeyPrefix('import-and-export-warehouse')]: 'Import and export warehouse',
  [genKeyPrefix('warehouse')]: 'Warehouse',
  [genKeyPrefix('export-warehouse')]: 'Export warehouse',
  [genKeyPrefix('import-warehouse')]: 'Import warehouse',
  [genKeyPrefix('active-iot-devices')]: 'active IoT devices',
  [genKeyPrefix('all-crop')]: 'All crops',
  [genKeyPrefix('ongoing')]: 'Ongoing',
  [genKeyPrefix('completed')]: 'Completed',
  [genKeyPrefix('stage')]: 'Stage'

  // [genKeyPrefix("crop")]:'Vụ mùa',
}
