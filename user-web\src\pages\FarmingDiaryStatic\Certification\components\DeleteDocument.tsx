import ActionModalConfirm from '@/components/ActionModalConfirm';
import { FC } from 'react';
import useDelete from '../hooks/useDelete';

interface DeletePestProps {
  id: string;
  onSuccess?: () => void;
}

const DeleteDocument: FC<DeletePestProps> = ({ id, onSuccess }) => {
  const { run } = useDelete({
    onSuccess,
  });
  return (
    <ActionModalConfirm
      isDelete={true}
      modalProps={{
        async onOk() {
          await run(id);
          return true;
        },
      }}
    />
  );
};

export default DeleteDocument;
