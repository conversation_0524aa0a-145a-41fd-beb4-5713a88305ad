import { ProFormInstance } from '@ant-design/pro-components';
import { FormattedMessage } from '@umijs/max';
import { Card, Form, Modal, QRCode, Typography } from 'antd';
import html2canvas from 'html2canvas';
import jsPDF from 'jspdf';
import moment from 'moment';
import { useState } from 'react';
const { Item } = Form;
const { Text } = Typography;

const handleDownload = () => {
  const doc = new jsPDF({
    unit: 'px',
    format: [400, 600],
  });

  const contentElement = document.getElementById('myqrcode');
  if (contentElement) {
    html2canvas(contentElement).then((canvas) => {
      const imgData = canvas.toDataURL('image/png');
      doc.addImage(imgData, 'PNG', 0, 0, 400, 600);
      doc.save('crop_qr_code.pdf');
    });
  }
};

const QRCodeModal = ({ form, cropId }: { form: ProFormInstance; cropId: string }) => {
  const [isOpen, setOpen] = useState(false);

  const showModal = () => {
    setOpen(true);
  };

  const hideModal = () => {
    setOpen(false);
  };

  const handleOk = () => {
    handleDownload();
  };

  const handleCancel = () => {
    hideModal();
  };

  return (
    <>
      <a onClick={showModal}>
        <FormattedMessage id="common.download_qr_code" />
      </a>

      <Modal
        title={`Mã QR mùa vụ`}
        open={isOpen}
        onOk={handleOk}
        onCancel={handleCancel}
        okText={'Tải xuống'}
        cancelText={'Đóng'}
      >
        <div
          id="myqrcode"
          style={{
            width: '400px',
            height: '600px',
            position: 'relative',
            margin: 'auto',
          }}
        >
          <Card
            bordered
            style={{ width: 400, height: 600 }}
            cover={
              <QRCode
                size={400}
                value={`crop,${cropId}`}
                bgColor="#fff"
                style={{ paddingBlock: 20 }}
              />
            }
          >
            <Text style={{ fontSize: 20 }}>
              Tên: <strong>{form.getFieldValue('label')}</strong>
            </Text>
            <br></br>
            <Text style={{ fontSize: 20 }}>
              Thời gian
              <strong>
                {' '}
                {moment(form.getFieldValue('date_range')?.[0]).format('DD/MM/YYYY')} -{' '}
                {moment(form.getFieldValue('date_range')?.[1]).format('DD/MM/YYYY')}
              </strong>
            </Text>
            <br></br>
            <Text style={{ fontSize: 20 }}>
              Sản lượng dự kiến: <strong>{form.getFieldValue('quantity_estimate')}</strong>
            </Text>
          </Card>
        </div>
      </Modal>
    </>
  );
};

export default QRCodeModal;
