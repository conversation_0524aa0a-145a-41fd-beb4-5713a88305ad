import { ProForm } from '@ant-design/pro-components';
import { history } from '@umijs/max';
import { Spin } from 'antd';
import { FC, ReactNode } from 'react';
import useDetail from '../../hooks/useDetail';
import useUpdate from '../../hooks/useUpdate';
import Certification from './Certification';
import Info from './Info';
import Note from './Note';
import Stage from './Stage';

interface CreateProcedureProps {
  children?: ReactNode;
  id: string;
  onSuccess?: () => void;
}

const EditProcedure: FC<CreateProcedureProps> = ({ children, id, onSuccess }) => {
  const { run } = useUpdate({
    onSuccess() {
      history.push('/farming-diary-static/procedure/list');
    },
  });
  const onFinish = async (values: any) => {
    await run({
      name: id,
      label: values.label,
      description: values.description,
      image: values.image,
      states: values.states?.map((item: any) => ({
        name: item.name,
        idx: item.idx,
      })),
      notes: values.notes?.map((item: any) => ({
        name: item.name,
        idx: item.idx,
      })),
      documents: values.documents?.map((item: any) => ({
        name: item.name,
        idx: item.idx,
      })),
      expire_time_in_days: 0,
    });
    return true;
  };
  const [form] = ProForm.useForm();
  const { loading, data } = useDetail({
    id: id,
    onSuccess(data) {
      form.setFieldsValue(data);
    },
  });
  return (
    <Spin spinning={loading}>
      <ProForm onFinish={onFinish} form={form}>
        <div className="flex flex-col gap-4 mb-4">
          <Info image={data?.image} />
          <Stage />
          <Certification />
          {/* <Demo /> */}
          <Note />
        </div>
      </ProForm>
    </Spin>
  );
};

export default EditProcedure;
