import ActionModalConfirm from '@/components/ActionModalConfirm';
import { FC } from 'react';
import useDelete from '../hooks/useDelete';

interface DeleteTaskProps {
  id: string;
  onSuccess?: () => void;
}

const DeleteTask: FC<DeleteTaskProps> = ({ onSuccess, id }) => {
  const { run, loading } = useDelete({
    onSuccess,
  });
  return (
    <ActionModalConfirm
      isDelete={true}
      modalProps={{
        async onOk() {
          await run(id);
          return true;
        },
      }}
    />
  );
};

export default DeleteTask;
