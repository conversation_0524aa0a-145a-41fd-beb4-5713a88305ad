import PageContainerTabsWithPath from '@/components/PageContainerTabsWithPath';
import { FC } from 'react';
import CreateNoteModal from '../SeasonalManagement/Detail/Note/Create';
import { PageContainer } from '@ant-design/pro-components';
import LogOverview from './OverviewAllDiary';



const CropLog: FC = () => {
  return (
    <LogOverview />
    // <PageContainerTabsWithPath
    //   generalPath="/farming-management/crop-log"
    //   tabItems={[
    //     {
    //       key: 'overview',
    //     }
    //   ]}
    // />
  );
};

export default CropLog;
