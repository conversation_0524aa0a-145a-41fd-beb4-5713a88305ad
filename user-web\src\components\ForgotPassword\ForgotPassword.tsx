import { forgotPassword, resetPasswordByToken } from '@/services/auth';
import { ModalForm, ProFormText } from '@ant-design/pro-components';
import { Button, Col, ConfigProvider, message, Modal, Row } from 'antd';

import { useState } from 'react';


export const ForgotPassword = () => {
  const [isModalVisible, setIsModalVisible] = useState(false);
  const [sent, setSent] = useState(false);

  const showModal = () => {
    setIsModalVisible(true);
  };

  const validatePassword = (rule: any, value: any, callback: (arg0?: string) => void) => {
    if (value !== document.getElementById('newPassword')?.value) {
      callback('Xác nhận mật khẩu và mật khẩu không giống nhau!');
    } else {
      callback();
    }
  };

  return (
    <ConfigProvider >
      <Button type="link" style={{ padding: 0, margin: 0 }} onClick={showModal}>
        Reset Password
      </Button>
      <ModalForm
        title="Reset Password"
        submitter={{
          searchConfig: {
            submitText: sent ? 'Đặt lại mật khẩu' : 'Send request',
            resetText: 'Cancel',
          },
        }}
        visible={isModalVisible}
        onFinish={async (formData: any) => {
          const { email, token, newPassword } = await formData;
          if (!sent) {
            await forgotPassword({ email });
            setSent(true);
            Modal.success({
              title: 'Gửi mã xác thực (OTP) thành công',
              content: <div>Chúng tôi đã gửi mã xác thực (OTP) qua email {email}.</div>,
              okText: 'OK',
            });
          } else {
            await resetPasswordByToken({ token, password: newPassword, email });
            // setSent(false);
            Modal.success({
              title: 'Thành công',
              content: 'Đặt lại mật khẩu thành công',
              okText: 'OK',
            });
          }
          message.success(sent ? 'Đặt lại mật khẩu thành công' : 'Gửi mail thành công');
          // onFinish?.({});
          if (sent) {
            setIsModalVisible(false);
            setSent(false);
          }
        }}
        onOpenChange={setIsModalVisible}
      >
        <ProFormText
          placeholder="Nhập địa chỉ email"
          name="email"
          label="Email"
          rules={[{ required: true, type: 'email' }]}
          disabled={sent}
        />
        {sent && (
          <Row gutter={10}>
            <Col xs={24}>
              <ProFormText
                placeholder="OTP"
                name="token"
                label="OTP"
                rules={[{ required: true }]}
              />
            </Col>
            <Col xs={12}>
              <ProFormText.Password
                name="newPassword"
                label="Mật khẩu"
                placeholder="Mật khẩu"
                required={sent}
                rules={[
                  { required: true },
                  { min: 6, message: 'Mật khẩu tối thiểu 6 ký tự.' },
                  { max: 40, message: 'Mật khẩu tối đa 40 ký tự.' },
                ]}
              />
            </Col>
            <Col xs={12}>
              <ProFormText.Password
                name="repassword"
                label="Xác nhận mật khẩu"
                placeholder="Mật khẩu mới"
                required={!sent}
                rules={[{ required: true, validator: validatePassword }]}
              />
            </Col>
          </Row>
        )}
      </ModalForm>
    </ConfigProvider>
  );
};
