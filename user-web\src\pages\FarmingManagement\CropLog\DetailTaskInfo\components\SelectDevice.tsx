import { ProDescriptions, ProFormRadio, ProFormSelect } from '@ant-design/pro-components';
import { Card, Col, Row } from 'antd';
import { FC, ReactNode } from 'react';

interface SelectDeviceProps {
  children?: ReactNode;
}

const SelectDevice: FC<SelectDeviceProps> = ({ children }) => {
  return (
    <Card title="Chọn thiết bị">
      <Row gutter={[40, 40]}>
        <Col span={8}>
          <ProFormSelect colProps={{ span: 24 }} label="Chọn thiết bị" />
          <ProDescriptions
            column={1}
            title="Dữ liệu hẹn giờ"
            contentStyle={{
              justifyContent: 'end',
            }}
          >
            <ProDescriptions.Item label={'Nhiệt độ (C)'}>30</ProDescriptions.Item>
            <ProDescriptions.Item label={'Độ ẩm (%)'}>30</ProDescriptions.Item>
            <ProDescriptions.Item label={'Ánh sáng môi trường (lx)'}>100</ProDescriptions.Item>
          </ProDescriptions>
        </Col>

        <Col span={8}>
          <ProFormRadio.Group label="Chọn dữ liệu" fieldProps={{ buttonStyle: 'solid' }}>
            <ProFormRadio.Button>Công suất tiêu thụ</ProFormRadio.Button>
            <ProFormRadio.Button>Nhiệt đọ</ProFormRadio.Button>
            <ProFormRadio.Button>Độ ẩm</ProFormRadio.Button>
          </ProFormRadio.Group>
        </Col>
      </Row>
    </Card>
  );
};

export default SelectDevice;
