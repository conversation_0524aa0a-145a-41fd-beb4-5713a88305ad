import {
  getCropProductionQuantityStatistic,
  ICropProductionQuantityStatistic,
} from '@/services/crop';
import { ProColumns, ProTable } from '@ant-design/pro-components';
import { FormattedMessage, useIntl } from '@umijs/max';
import { App } from 'antd';

interface Props {
  cropId: string;
}
const ProductionQuantityStatistic = ({ cropId }: Props) => {
  const { message } = App.useApp();
  const intl = useIntl();
  const columns: ProColumns<ICropProductionQuantityStatistic>[] = [
    {
      title: intl.formatMessage({ id: 'category.product.product_list' }),
      dataIndex: 'agri_product_label',
    },
    {
      title: intl.formatMessage({ id: 'seasonalTab.totalExpected' }),
      dataIndex: 'total_exp_quantity',
    },

    {
      title: intl.formatMessage({ id: 'seasonalTab.totalReality' }),
      dataIndex: 'total_quantity',
    },
    {
      title: intl.formatMessage({ id: 'seasonalTab.totalLoss' }),
      dataIndex: 'total_loss_quantity',
    },
    {
      title: <FormattedMessage id="category.material-management.unit" defaultMessage="unknown" />,
      dataIndex: 'unit_label',
    },
  ];
  return (
    <ProTable<ICropProductionQuantityStatistic>
      headerTitle={intl.formatMessage({ id: 'seasonalTab.outputStatistics' })}
      columns={columns}
      search={false}
      pagination={{
        pageSizeOptions: [10, 20, 50, 100],
        showSizeChanger: true,
      }}
      request={async (params, sorter, filter) => {
        try {
          const res = await getCropProductionQuantityStatistic({
            page: params.current,
            size: params.pageSize,
            crop_id: cropId,
          });
          return {
            data: res.data,
            success: true,
          };
        } catch (error: any) {
          message.error(`Error when getting Crop Production Quantity: ${error.message}`);
          return {
            success: false,
          };
        }
      }}
      rowKey={'name'}
    />
  );
};

export default ProductionQuantityStatistic;
