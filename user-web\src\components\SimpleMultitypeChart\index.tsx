import {
  BarController,
  BarElement,
  CategoryScale,
  Chart as ChartJS,
  Legend,
  LinearScale,
  LineController,
  LineElement,
  PointElement,
  Title,
  Tooltip,
} from 'chart.js';
import { useEffect, useRef } from 'react';
import { Chart } from 'react-chartjs-2';

ChartJS.register(
  CategoryScale,
  LinearScale,
  BarElement,
  Title,
  Tooltip,
  Legend,
  LinearScale,
  CategoryScale,
  BarElement,
  PointElement,
  LineElement,
  Legend,
  Tooltip,
  LineController,
  BarController,
);

export interface Props {
  data: any;
  chart_label: string;
  height?: string;
}
const SimpleMultitypeChart = ({ data, chart_label, height }: Props) => {
  const options = {
    // responsive: true,
    maintainAspectRatio: false,
    plugins: {
      legend: {
        position: 'top' as const,
      },
      title: {
        display: true,
        text: chart_label,
      },
    },
  };
  const chartRef = useRef<ChartJS<'bar'>>(null);
  useEffect(() => {
    chartRef.current?.update();
  }, [data]);

  return (
    <div style={{ height: height || '500px' }}>
      <Chart type="bar" ref={chartRef} options={options} data={data} height={'500px'} />
    </div>
  );
};

export default SimpleMultitypeChart;
