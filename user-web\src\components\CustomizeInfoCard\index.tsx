import { getCropManagementInfoList, ICropManagerInfo } from '@/services/cropManager';
import { toLowerCaseNonAccentVietnamese } from '@/services/utils';
import { DownloadOutlined, FilterOutlined } from '@ant-design/icons';
import {
  ProCard,
  ProForm,
  ProFormDateRangePicker,
  ProFormSelect,
} from '@ant-design/pro-components';
import { Alert, Button, Col, Divider, Form, Row, Skeleton, Space, Typography } from 'antd';
import { useEffect, useState } from 'react';
import CropItemTable from '../CropItemTable';
import CropProductTable from '../CropProductTable';
import CropWorksheetTable from '../CropWorksheetTable';
import GeneralStatistics from './components/GeneralStatistics';

import {
  getCropStatisticItems,
  getCropStatisticProducts,
  getCropStatisticWorksheet,
} from '@/services/cropStatistic';
import { FormattedMessage } from '@umijs/max';
import * as XLSX from 'xlsx';
import { DEFAULT_DATE_FORMAT_WITHOUT_TIME } from '@/common/contanst/constanst';
export interface IFilter {
  cropList: string[];
  start_date: string;
  end_date: string;
  type: 'YEAR' | 'MONTH' | 'WEEK';
}
interface IFormData {
  cropList: string[];
  type: 'YEAR' | 'MONTH' | 'WEEK';
  date_range: string[];
}
const CustomizeInfoCard = () => {
  const [crops, setCrops] = useState<ICropManagerInfo[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [isDownloading, setIsDownloading] = useState(false);
  const [filter, setFilter] = useState<IFilter>();
  const [form] = Form.useForm<IFormData>();
  const [type, setType] = useState<'year' | 'month' | 'week'>('week');
  useEffect(() => {
    const fetchData = async () => {
      const data = await getCropManagementInfoList();
      if (data.data) {
        setCrops(data.data);
        setIsLoading(false);
      }
    };
    fetchData();
  }, []);
  const handleChooseType = (e: string) => {
    setType(e.toLowerCase() as any);
    form.resetFields(['date_range']);
  };

  useEffect(() => {}, [type]);
  const downloadData = async () => {
    setIsDownloading(true);
    try {
      const query = {
        page: 1,
        size: 1000,
        cropList: filter!.cropList,
        end_date: filter!.end_date,
        start_date: filter!.start_date,
        type: filter!.type,
      };
      const cropStatisticItem = await getCropStatisticItems(query);
      const cropStatisticWorksheet = await getCropStatisticWorksheet(query);
      const cropStatisticProduct = await getCropStatisticProducts(query);
      const itemSheet = XLSX.utils.json_to_sheet(cropStatisticItem.data);
      const worksheetSheet = XLSX.utils.json_to_sheet(cropStatisticWorksheet.data);
      const productSheet = XLSX.utils.json_to_sheet(cropStatisticProduct.data);
      const workbook = XLSX.utils.book_new();
      XLSX.utils.book_append_sheet(workbook, itemSheet, 'Items');
      XLSX.utils.book_append_sheet(workbook, worksheetSheet, 'Worksheets');
      XLSX.utils.book_append_sheet(workbook, productSheet, 'Products');
      const buffer = XLSX.write(workbook, { bookType: 'xlsx', type: 'buffer' });
      const newblob = new Blob([buffer], { type: 'application/octet-stream' });
      const url = window.URL.createObjectURL(newblob);
      const link = document.createElement('a');
      link.href = url;
      link.download = `export_statistic.xlsx`;
      link.click();
    } catch (error) {
      console.log(`downloading error: ${error}`);
    } finally {
      setIsDownloading(false);
    }
  };
  const handleQuickAction = (e: any) => {
    if (e === 'all') {
      form.setFieldValue(
        'cropList',
        crops.map((crop) => crop.name),
      );
    } else if (e === 'done') {
      const crop_ids = crops.filter((crop) => crop.status === 'Done').map((crop) => crop.name);
      form.setFieldValue('cropList', crop_ids);
    } else if (e === 'in_progress') {
      const crop_ids = crops
        .filter((crop) => crop.status === 'In progress')
        .map((crop) => crop.name);
      form.setFieldValue('cropList', crop_ids);
    }
  };
  return (
    <>
      <ProCard
        headStyle={{
          alignItems: 'center',
          backgroundColor: '#44C4A1',
          color: 'white',
        }}
      >
        <Row>
          <Col span={6}>
            <Typography.Title level={4} style={{ paddingBottom: 10 }}>
              Thống kê mùa vụ
            </Typography.Title>
          </Col>
          <Col span={18}>
            <Skeleton loading={isLoading} active>
              <ProForm<IFormData>
                key="filter"
                form={form}
                layout="horizontal"
                submitter={{
                  render: (props, defaultDoms) => {
                    return [];
                  },
                }}
                onFinish={async (e) => {
                  if (
                    e.cropList &&
                    e.cropList.length !== 0 &&
                    e.date_range &&
                    e.date_range.length !== 0 &&
                    e.type !== undefined
                  )
                    setFilter({
                      cropList: e.cropList,
                      start_date: e.date_range?.at(0) || '',
                      end_date: e.date_range?.at(1) || '',
                      type: e.type,
                    });
                }}
              >
                <Row
                  gutter={20}
                  justify={'space-around'}
                  style={{
                    minWidth: '100%',
                  }}
                >
                  <Col xxl={3} lg={6}>
                    <ProFormSelect
                      name={'type'}
                      options={[
                        { label: 'Tuần', value: 'WEEK' },
                        { label: 'Tháng', value: 'MONTH' },
                        { label: 'Năm', value: 'YEAR' },
                      ]}
                      onChange={handleChooseType}
                      placeholder={'Tuần/Tháng/Năm'}
                      // colProps={{
                      //   lg: 3,
                      // }}
                      // fieldProps={{
                      //   style: {
                      //     width: '100%',
                      //   },
                      // }}
                    />
                  </Col>

                  <Col xxl={6} lg={10}>
                    <ProFormDateRangePicker
                      name="date_range"
                      fieldProps={{
                        picker: type,
                        format: DEFAULT_DATE_FORMAT_WITHOUT_TIME,
                      }}
                      initialValue={['2023-01-01', '2023-12-31']}

                      // colProps={{
                      //   lg: 6,
                      // }}
                    />
                  </Col>
                  <Col xxl={5} lg={10}>
                    <ProFormSelect
                      name={'quick-action'}
                      label="Lọc"
                      // colProps={{
                      //   lg: 7,
                      // }}
                      options={[
                        { value: 'all', label: 'Tất cả vụ mùa' },
                        { value: 'done', label: 'Vụ mùa đã hoàn thành' },
                        { value: 'in_progress', label: 'Vụ mùa đang diễn ra' },
                      ]}
                      onChange={handleQuickAction}
                    />
                  </Col>
                  <Col xxl={7} lg={10}>
                    <ProFormSelect
                      mode="tags"
                      name={'cropList'}
                      label="Vụ mùa"
                      fieldProps={{
                        maxTagCount: 0,
                      }}
                      // colProps={{
                      //   lg: 5,
                      // }}
                      showSearch
                      request={async (option) => {
                        return crops
                          .filter((item) =>
                            toLowerCaseNonAccentVietnamese(item.label || '').includes(
                              toLowerCaseNonAccentVietnamese(option.keyWords),
                            ),
                          )
                          .map((item) => ({
                            label: item.label,
                            value: item.name,
                          }));
                      }}
                    />
                  </Col>

                  <Col xxl={3} lg={3}>
                    <Space wrap>
                      <Button
                        style={{
                          marginLeft: 10,
                        }}
                        key="ok"
                        onClick={() => {
                          form.submit();
                        }}
                        type="primary"
                        icon={<FilterOutlined />}
                      >
                        Lọc
                      </Button>

                      <Button
                        icon={<DownloadOutlined />}
                        type="default"
                        onClick={downloadData}
                        disabled={isDownloading || !filter}
                      >
                        <FormattedMessage id="action.download" />
                      </Button>
                    </Space>
                  </Col>
                </Row>
              </ProForm>
            </Skeleton>
          </Col>
        </Row>
        <Divider />
        {filter?.type === undefined ||
        filter?.cropList?.length === 0 ||
        filter.end_date === undefined ||
        filter.start_date === undefined ? (
          <Alert message="Vui lòng chọn filter để xem thống kê" type="info" />
        ) : (
          <>
            <GeneralStatistics filter={filter!} />
            <Divider />
            <CropItemTable filter={filter!} />
            <Divider />
            <CropWorksheetTable filter={filter!} />
            <Divider />
            <CropProductTable filter={filter!} />
          </>
        )}
      </ProCard>
    </>
  );
};

export default CustomizeInfoCard;
