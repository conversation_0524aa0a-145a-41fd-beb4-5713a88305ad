import { isSubscribedStock } from '@/access';
import { COLOR_HEX } from '@/common/contanst/constanst';
import RealityColumnRender from '@/components/RealityColumnRender';
import MaterialIssue from '@/components/StockEntryButtons/ItemTaskButtons/MaterialIssueVoucher';
import MaterialTransferFromWIP from '@/components/StockEntryButtons/ItemTaskButtons/MaterialTransferFromWIPVoucher';
import MaterialTransfer from '@/components/StockEntryButtons/ItemTaskButtons/MaterialTransferVoucher';
import QuickMaterialIssue from '@/components/StockEntryButtons/ItemTaskButtons/QuickMaterialIssueVoucher';
import { getProductItemV3 } from '@/services/InventoryManagementV3/product-item';
import { updateTaskItemListSQL } from '@/services/item';
import { getAllTaskItemUsed } from '@/services/TaskAndTodo';
import { IIotCategory } from '@/types/IIotCategory';
import { IIotWarehouseItemTaskUsed, UOM } from '@/types/IIotWarehouseItemTaskUsed';
import { DownOutlined, InfoCircleOutlined } from '@ant-design/icons';
import type { ActionType, ProColumns } from '@ant-design/pro-components';
import { EditableProTable } from '@ant-design/pro-components';
import { FormattedMessage, useAccess, useIntl } from '@umijs/max';
import {
  Button,
  Dropdown,
  InputNumber,
  MenuProps,
  message,
  Select,
  Skeleton,
  Space,
  Tooltip,
} from 'antd';
import React, { useEffect, useRef, useState } from 'react';
import { useTaskItemUsedUpdateStore } from '../../../stores/TaskItemUsedUpdateStore';
import CreateItemForTask from './CreateItemUpdateView';
import DeleteItemForTask from './DeleteItemForTask';

const RealityColumnNumberRender = ({ value, onChange }: any) => {
  return (
    <RealityColumnRender>
      <InputNumber
        value={value}
        onChange={onChange}
        disabled
        style={{ width: '100%' }}
        formatter={(value) => `${value}`.replace(/\B(?=(\d{3})+(?!\d))/g, ',')}
        parser={(value) => value!.replace(/\$\s?|(,*)/g, '')}
        step="0.01"
      />
    </RealityColumnRender>
  );
};

const fetchTaskItemUsed = async (
  task_id: string,
  items: IIotCategory[],
  setDataSource: any,
  setLoading: any,
) => {
  try {
    setLoading(true);
    const resData = await getAllTaskItemUsed(task_id);
    setDataSource(
      resData.data.map((d: any) => {
        d.item = items.find((el: any) => el.name === d.iot_category_id);
        // Tìm active UOM từ danh sách uoms
        const activeUom = d.uoms?.find((uom: any) => uom.uom_id === d.active_uom_id);
        const baseUom = d.uoms?.find((uom: any) => uom.conversion_factor === 1);

        // Tính toán ratio để convert từ base unit sang active unit
        let displayQuantity = d.quantity;
        let displayExpQuantity = d.exp_quantity;
        let displayLossQuantity = d.loss_quantity;
        let displayIssuedQuantity = d.issued_quantity;
        let displayTotalQtyInCrop = d.total_qty_in_crop;
        let displayDraftQuantity = d.draft_quantity;

        if (activeUom && baseUom && d.active_conversion_factor) {
          const ratio = baseUom.conversion_factor / d.active_conversion_factor;
          displayQuantity = d.quantity * ratio;
          displayExpQuantity = d.exp_quantity * ratio;
          displayLossQuantity = d.loss_quantity * ratio;
          displayIssuedQuantity = d.issued_quantity * ratio;
          displayTotalQtyInCrop = d.total_qty_in_crop * ratio;
          displayDraftQuantity = d.draft_quantity * ratio;
        }

        return {
          ...d,
          quantity: displayQuantity,
          exp_quantity: displayExpQuantity,
          loss_quantity: displayLossQuantity,
          issued_quantity: displayIssuedQuantity,
          total_qty_in_crop: displayTotalQtyInCrop,
          draft_quantity: displayDraftQuantity,
          // Lưu giá trị gốc để có thể convert về sau
          original_quantity: d.quantity,
          original_exp_quantity: d.exp_quantity,
          original_loss_quantity: d.loss_quantity,
          original_issued_quantity: d.issued_quantity,
          original_total_qty_in_crop: d.total_qty_in_crop,
          original_draft_quantity: d.draft_quantity,
          // Set UOM hiện tại theo active_uom từ API
          uom_id: d.active_uom_id || d.uom_id,
          uom_label: d.active_uom_label || d.uom_label,
          conversion_factor: d.active_conversion_factor || d.conversion_factor,
          active_uom: d.active_uom_id || d.uom_id,
          active_conversion_factor: d.active_conversion_factor || d.conversion_factor,
        };
      }),
    );
  } catch (error: any) {
    message.error(error.toString());
  } finally {
    setLoading(false);
  }
};

const fetchProductItems = async (setItems: any, setLoading: any) => {
  try {
    setLoading(true);
    const resData = await getProductItemV3();
    setItems(resData.data);
  } catch (error: any) {
    message.error(error.toString());
  } finally {
    setLoading(false);
  }
};

const handleSaveItems = async (dataSource: any, setIsSaving: any, setRefreshKey: any) => {
  try {
    const updateTaskItemList: IIotWarehouseItemTaskUsed[] = dataSource.map((data: any) => ({
      name: data.name,
      // quantity: data.quantity,
      description: data.description,
      task_id: data.task_id,
      iot_category_id: data.iot_category_id,
      exp_quantity: data.exp_quantity * data.conversion_factor,
      // loss_quantity: data.loss_quantity,
      // issued_quantity: data.issued_quantity,
      draft_quantity: data.draft_quantity * data.conversion_factor,
      // total_qty_in_crop: data.total_qty_in_crop,
      active_uom: data.uom_id,
      active_conversion_factor: data.conversion_factor,
    }));
    await updateTaskItemListSQL(updateTaskItemList);
    setRefreshKey((prev: any) => prev + 1); // Thêm dòng này để cập nhật refreshKey
    message.success('Lưu vật tư liên quan thành công');
  } catch (error: any) {
    message.error('Đã có lỗi xảy ra');
  } finally {
    setIsSaving(false);
  }
};

const RelatedSuppliesTable = ({
  task_id,
  isTemplateTask,
}: {
  task_id: string;
  isTemplateTask?: boolean;
}) => {
  const [refreshKey, setRefreshKey] = useState(0);
  const { dataSource, setDataSource } = useTaskItemUsedUpdateStore();
  const tempDataSourceRef = useRef(dataSource);
  const [isSaving, setIsSaving] = useState(false);
  const [items, setItems] = useState<IIotCategory[]>([]);
  const actionRef = useRef<ActionType>();
  const [editableKeys, setEditableRowKeys] = useState<React.Key[]>(
    dataSource.map((data) => data.name),
  );
  const [loading, setLoading] = useState(false);

  useEffect(() => {
    fetchProductItems(setItems, setLoading);
  }, []);

  useEffect(() => {
    if (items.length) fetchTaskItemUsed(task_id, items, setDataSource, setLoading);
  }, [items, refreshKey]); // Thêm refreshKey vào dependencies

  useEffect(() => {
    if (isSaving) handleSaveItems(dataSource, setIsSaving, setRefreshKey);
    setEditableRowKeys(dataSource.map((data) => data.name));
    tempDataSourceRef.current = [...dataSource];
    actionRef.current?.reload();
  }, [dataSource, isSaving]);

  const handleStoreItem = () => {
    setIsSaving(true);
    setDataSource(tempDataSourceRef.current);
  };

  const handleUOMChange = (value: string, record: IIotWarehouseItemTaskUsed, uoms: UOM[]) => {
    const selectedUOM = uoms.find((uom) => uom.uom_id === value);
    const baseUOM = uoms.find((uom) => uom.conversion_factor === 1);
    if (selectedUOM && baseUOM) {
      const newConversionFactor = selectedUOM.conversion_factor;
      const ratio = baseUOM.conversion_factor / selectedUOM.conversion_factor;

      // Nếu các giá trị gốc chưa được thiết lập, thiết lập chúng bằng các giá trị hiện tại
      if (record.original_quantity === undefined) {
        record.original_quantity = record.quantity;
      }
      if (record.original_exp_quantity === undefined) {
        record.original_exp_quantity = record.exp_quantity;
      }
      if (record.original_draft_quantity === undefined) {
        record.original_draft_quantity = record.draft_quantity;
      }
      if (record.original_issued_quantity === undefined) {
        record.original_issued_quantity = record.issued_quantity;
      }
      if (record.original_total_qty_in_crop === undefined) {
        record.original_total_qty_in_crop = record.total_qty_in_crop;
      }

      const updatedRecord = {
        ...record,
        conversion_factor: newConversionFactor,
        uom_id: selectedUOM.uom_id,
        uom_label: selectedUOM.uom_label,
        active_uom: selectedUOM.uom_id,
        active_conversion_factor: newConversionFactor,
        quantity: record.original_quantity! * ratio,
        exp_quantity: record.original_exp_quantity! * ratio,
        draft_quantity: record.original_draft_quantity! * ratio,
        issued_quantity: record.original_issued_quantity! * ratio,
        total_qty_in_crop: record.original_total_qty_in_crop! * ratio,
      };

      const updatedDataSource = tempDataSourceRef.current.map((item) =>
        item.name === record.name ? updatedRecord : item,
      );

      tempDataSourceRef.current = updatedDataSource;
      setDataSource(updatedDataSource);
    }
  };

  const access = useAccess();
  const canCreateTask = access.canCreateInWorkFlowManagement();
  const canUpdateTask = access.canUpdateInWorkFlowManagement();
  const canDeleteTask = access.canDeleteInWorkFlowManagement();

  const columns: ProColumns<IIotWarehouseItemTaskUsed>[] = [
    {
      title: <FormattedMessage id="category.material-management.category_code" />,
      editable: false,
      render: (_, entity: any) => <>{entity.item?.item_name}</>,
      width: 100,
      fixed: 'left',
    },
    {
      title: <FormattedMessage id="category.material-management.category_name" />,
      editable: false,
      render: (_, entity: any) => <>{entity.item?.label}</>,
      width: 100,
      fixed: 'left',
    },
    {
      title: <FormattedMessage id="common.unit" />,
      dataIndex: 'uom_id',
      renderFormItem: (_, record) => (
        <Select
          value={record.record?.uom_id}
          onChange={(value) => handleUOMChange(value, record.record!, record.record?.uoms || [])}
        >
          {record.record?.uoms?.map((uom) => (
            <Select.Option key={uom.uom_id} value={uom.uom_id}>
              {uom.uom_label}
            </Select.Option>
          ))}
        </Select>
      ),
      fixed: 'left',
      render: (_, entity: any) => <>{entity.uom_label}</>,
      width: 100,
    },
    {
      title: (
        <Tooltip
          color={COLOR_HEX.GREEN_TOOLTIP}
          key={COLOR_HEX.GREEN_TOOLTIP}
          title={<FormattedMessage id="tooltips.item_task_exp_quantity_description" />}
        >
          <FormattedMessage id="storage-management.category-management.expected_quantity" />{' '}
          <InfoCircleOutlined />
        </Tooltip>
      ),
      dataIndex: 'exp_quantity',
      renderFormItem: () => (
        <InputNumber
          style={{ width: '100%' }}
          formatter={(value) => `${value}`.replace(/\B(?=(\d{3})+(?!\d))/g, ',')}
          parser={(value) => value!.replace(/\$\s?|(,*)/g, '')}
          step="0.01"
        />
      ),
      width: 100,
    },
    {
      title: (
        <Tooltip
          color={COLOR_HEX.GREEN_TOOLTIP}
          key={COLOR_HEX.GREEN_TOOLTIP}
          title={<FormattedMessage id="tooltips.item_task_draft_quantity_description" />}
        >
          <FormattedMessage id="storage-management.category-management.draft_quantity" />{' '}
          <InfoCircleOutlined />
        </Tooltip>
      ),
      dataIndex: 'draft_quantity',
      renderFormItem: () => (
        <InputNumber
          style={{ width: '100%' }}
          formatter={(value) => `${value}`.replace(/\B(?=(\d{3})+(?!\d))/g, ',')}
          parser={(value) => value!.replace(/\$\s?|(,*)/g, '')}
          step="0.01"
        />
      ),
      width: 100,
    },
    {
      title: (
        <Tooltip
          color={COLOR_HEX.GREEN_TOOLTIP}
          key={COLOR_HEX.GREEN_TOOLTIP}
          title={<FormattedMessage id="tooltips.item_task_used_quantity_description" />}
        >
          <FormattedMessage id="storage-management.category-management.used_quantity" />{' '}
          <InfoCircleOutlined />
        </Tooltip>
      ),
      renderFormItem: (schema, config) => {
        const quantity = config?.record?.quantity || 0;
        const issued_quantity = config?.record?.issued_quantity || 0;
        const used_quantity = quantity + issued_quantity;
        return (
          <InputNumber
            disabled
            style={{ width: '100%' }}
            formatter={(value) => `${used_quantity}`.replace(/\B(?=(\d{3})+(?!\d))/g, ',')}
            parser={(value) => used_quantity.toString().replace(/\$\s?|(,*)/g, '')}
            step="0.01"
          />
        );
      },
      width: 100,
      hideInTable: !isSubscribedStock() || isTemplateTask,
    },
    {
      title: (
        <Tooltip
          color={COLOR_HEX.GREEN_TOOLTIP}
          key={COLOR_HEX.GREEN_TOOLTIP}
          title={<FormattedMessage id="tooltips.item_task_issued_quantity_description" />}
        >
          <FormattedMessage id="storage-management.category-management.issue_quantity" />{' '}
          <InfoCircleOutlined />
        </Tooltip>
      ),
      dataIndex: 'issued_quantity',
      renderFormItem: () => (
        <InputNumber
          disabled
          style={{ width: '100%' }}
          formatter={(value) => `${value}`.replace(/\B(?=(\d{3})+(?!\d))/g, ',')}
          step="0.01"
        />
      ),
      width: 100,
      hideInTable: !isSubscribedStock() || isTemplateTask,
    },
    {
      title: (
        <Tooltip
          color={COLOR_HEX.GREEN_TOOLTIP}
          key={COLOR_HEX.GREEN_TOOLTIP}
          title={<FormattedMessage id="tooltips.item_task_crop_total_quantity_description" />}
        >
          <FormattedMessage id="storage-management.category-management.real_quantity" />{' '}
          <InfoCircleOutlined />
        </Tooltip>
      ),
      dataIndex: 'total_qty_in_crop',

      renderFormItem(schema, config, form, action) {
        return <RealityColumnNumberRender />;
      },

      width: 100,
      hideInTable: !isSubscribedStock() || isTemplateTask,
    },
    {
      title: <FormattedMessage id="common.description" />,
      dataIndex: 'description',
      formItemProps: {
        rules: [
          {
            required: true,
            whitespace: true,
            message: 'Mô tả không được để trống',
          },
        ],
      },
      width: 100,
    },
    {
      title: 'Hành động',
      dataIndex: 'name',
      renderFormItem: (schema, config: any) => (
        <>
          {canDeleteTask && (
            <DeleteItemForTask
              refreshFnc={() => fetchTaskItemUsed(task_id, items, setDataSource, setLoading)}
              value={config.recordKey}
            />
          )}
        </>
      ),
      width: 80,
    },
  ];

  const itemsDropdownMenu: MenuProps['items'] = [
    canCreateTask || canUpdateTask
      ? {
          key: '1',
          label: (
            <CreateItemForTask
              refreshFnc={() => fetchTaskItemUsed(task_id, items, setDataSource, setLoading)}
              task_id={task_id}
              buttonType="link"
            />
          ),
        }
      : null,
    (canCreateTask || canUpdateTask) && isSubscribedStock() && !isTemplateTask
      ? {
          key: '2',
          label: (
            <MaterialTransfer
              onSuccess={() => fetchTaskItemUsed(task_id, items, setDataSource, setLoading)}
              buttonType="link"
            />
          ),
        }
      : null,
    (canCreateTask || canUpdateTask) && isSubscribedStock() && !isTemplateTask
      ? {
          key: '3',
          label: (
            <MaterialTransferFromWIP
              onSuccess={() => fetchTaskItemUsed(task_id, items, setDataSource, setLoading)}
              buttonType="link"
            />
          ),
        }
      : null,
    (canCreateTask || canUpdateTask) && isSubscribedStock() && !isTemplateTask
      ? {
          key: '4',
          label: (
            <MaterialIssue
              onSuccess={() => fetchTaskItemUsed(task_id, items, setDataSource, setLoading)}
              buttonType="link"
            />
          ),
        }
      : null,
    (canCreateTask || canUpdateTask) && isSubscribedStock() && !isTemplateTask
      ? {
          key: '5',
          label: (
            <QuickMaterialIssue
              onSuccess={() => fetchTaskItemUsed(task_id, items, setDataSource, setLoading)}
              buttonType="link"
            />
          ),
        }
      : null,
  ].filter(Boolean);

  const intl = useIntl();

  return !loading ? (
    <>
      <Space direction="vertical" style={{ width: '100%', marginBottom: 16 }}>
        <EditableProTable<IIotWarehouseItemTaskUsed>
          scroll={{ x: 1000 }}
          actionRef={actionRef}
          headerTitle={
            <Tooltip
              color={COLOR_HEX.GREEN_TOOLTIP}
              key={COLOR_HEX.GREEN_TOOLTIP}
              title={
                <div
                  dangerouslySetInnerHTML={{
                    __html: intl.formatMessage({ id: 'tooltips.task_item_used_table' }),
                  }}
                />
              }
            >
              <FormattedMessage id="seasonalTab.relatedSupplies" /> <InfoCircleOutlined />
            </Tooltip>
          }
          columns={columns}
          rowKey="name"
          key={JSON.stringify(dataSource)}
          request={async (params) => ({
            data: dataSource,
            success: true,
            total: dataSource.length,
          })}
          toolBarRender={() => [
            <Dropdown menu={{ items: itemsDropdownMenu }}>
              <Button>
                <FormattedMessage id="common.action" /> <DownOutlined />
              </Button>
            </Dropdown>,
          ]}
          editable={{
            type: 'multiple',
            editableKeys,
            actionRender: (row, config, defaultDoms) => [defaultDoms.delete],
            onValuesChange: (_, recordList) => {
              tempDataSourceRef.current = recordList.map((item) => ({
                ...item,
                quantity:
                  typeof item.quantity === 'string' ? parseFloat(item.quantity) : item.quantity,
                exp_quantity:
                  typeof item.exp_quantity === 'string'
                    ? parseFloat(item.exp_quantity)
                    : item.exp_quantity,
                loss_quantity:
                  typeof item.loss_quantity === 'string'
                    ? parseFloat(item.loss_quantity)
                    : item.loss_quantity,
                draft_quantity:
                  typeof item.draft_quantity === 'string'
                    ? parseFloat(item.draft_quantity)
                    : item.draft_quantity,
              }));
            },
            onChange: setEditableRowKeys,
          }}
          recordCreatorProps={false}
          search={false}
          pagination={{
            defaultPageSize: 50,
            pageSizeOptions: [10, 20, 50, 100],
            showSizeChanger: true,
          }}
        />
        {canUpdateTask && (
          <Button loading={isSaving} onClick={handleStoreItem}>
            <FormattedMessage id={'common.save_related_material'} />
          </Button>
        )}
      </Space>
    </>
  ) : (
    <Skeleton active />
  );
};

export default RelatedSuppliesTable;
