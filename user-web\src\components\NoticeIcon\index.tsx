import { myLazy } from '@/utils/lazy';
import { useModel, useNavigate } from '@umijs/max';
import { Button, Tag } from 'antd';
import dayjs from 'dayjs';
import relativeTime from 'dayjs/plugin/relativeTime';
import React, { Suspense, useDeferredValue, useState } from 'react';
import { useNoticeAlarmData } from './hook/useNoticeAlarmData';
import { useUpdateIsRead } from './hook/useUpdateIsRead';
import { useUpdateReadAll } from './hook/useUpdateReadAll';
import styles from './index.less';
import NoticeIcon from './NoticeIcon';

const MonitorDevice = myLazy(() => import('@/pages/IoTDeviceMangement/components/MonitorDevice'));

dayjs.extend(relativeTime);

export type GlobalHeaderRightProps = {
  fetchingNotices?: boolean;
  onNoticeVisibleChange?: (visible: boolean) => void;
  onNoticeClear?: (tabName?: string) => void;
};

const formatNoticeData = (notices: API.NoticeIconItem[]): API.NoticeIconItem[] => {
  if (!notices || notices.length === 0 || !Array.isArray(notices)) {
    return [];
  }

  const newNotices = notices.map((notice) => {
    const newNotice = { ...notice };

    if (newNotice.datetime) {
      newNotice.datetime = dayjs(notice.datetime).fromNow();
    }

    if (newNotice.id) {
      newNotice.key = newNotice.id;
    }

    if (newNotice.extra && newNotice.status) {
      const colors = {
        todo: '',
        processing: 'blue',
        urgent: 'red',
        doing: 'gold',
        warning: 'warning',
        success: 'success',
      };
      const color = colors?.[newNotice?.status as keyof typeof colors];

      newNotice.extra = (
        <Tag
          color={color}
          style={{
            marginRight: 0,
          }}
        >
          {newNotice.extra}
        </Tag>
      ) as any;
    }

    return newNotice;
  });
  return newNotices;
};

const NoticeIconView: React.FC = () => {
  const mqttModel = useModel('MQTTNotification');
  const [openNotice, setOpenNotice] = useState(false);

  const {
    alarmRes,
    loadAlarm,
    alarmNotices,
    isLoadingAlarmNotice,
    setAlarmNotices,
    alarmPendingCount,
  } = useNoticeAlarmData();
  const { run: updateIsRead } = useUpdateIsRead();
  const { run: updateReadAll, loading: isUpdatingReadAll } = useUpdateReadAll({
    onSuccess: () => {
      // Cập nhật trạng thái đã đọc cho tất cả thông báo sau khi "Đánh dấu xem tất cả"
      setAlarmNotices((prev) => prev.map((item) => ({ ...item, read: true })));
      mqttModel.handleMessage.noticeHandleReadAll.emit.readAll({
        isReadAll: true,
        time: Date.now(),
      });
    },
  });
  const hashLoadMoreAlarm =
    (alarmRes?.pagination.pageNumber || 1) < (alarmRes?.pagination?.totalPages || 1);

  const loadMoreAlarm = () => {
    if (isLoadingAlarmNotice) return;
    const pageNumber = (alarmRes?.pagination?.pageNumber || 1) + 1;
    if (alarmRes?.pagination?.totalPages && pageNumber > alarmRes.pagination.totalPages) {
      return;
    }
    loadAlarm({
      page: pageNumber,
    });
  };

  const allNoticeData = useDeferredValue(formatNoticeData(alarmNotices));
  const navigate = useNavigate();

  const [openMonitor, setOpenMonitor] = useState(false);
  const [deviceId, setDeviceId] = useState<string | null>(null);

  const openDeviceDetail = (deviceId: string) => {
    setDeviceId(deviceId);
    setOpenMonitor(true);
  };

  return (
    <>
      <NoticeIcon
        popupVisible={openNotice}
        onPopupVisibleChange={setOpenNotice}
        className={styles.action}
        count={alarmPendingCount || 0}
        loading={false}
        viewMoreText="Xem thêm"
        clearText={'Xem tất cả'}
      >
        <NoticeIcon.Tab
          tabKey="task"
          count={alarmPendingCount || 0}
          list={allNoticeData}
          title={'Thông báo'}
          emptyText={'Chưa có data'}
          showViewMore
          showClear={false}
          loadMoreData={loadMoreAlarm}
          hasLoadMore={hashLoadMoreAlarm}
          viewMoreText={
            <Button
              loading={isUpdatingReadAll}
              block
              type="link"
              onClick={async () => {
                await updateReadAll();
              }}
            >
              Đánh dấu xem tất cả
            </Button>
          }
          onClick={async (item) => {
            if (item.read === false) {
              try {
                const dataUpdate = {
                  ...item.data,
                  is_read: true,
                };
                await updateIsRead({
                  name: dataUpdate.name,
                  is_read: dataUpdate.is_read,
                });
                mqttModel?.handleMessage.noticeHandle.emit.updateNoticeTask(dataUpdate);
              } catch {}
            }
            if (['approval response', 'approval request'].includes(item.type!)) {
              const taskId = item.data.entity;
              if (taskId) navigate(`/employee-management/approval/details/${taskId}`);
              return;
            }
            if (item.type === 'device') {
              const deviceId = item.data.entity;
              openDeviceDetail(deviceId);
              return;
            }
            const taskId = item.data.entity;
            if (taskId) navigate(`/farming-management/workflow-management/detail/${taskId}`);
          }}
        />
        <NoticeIcon.Tab
          tabKey="other"
          count={0}
          list={[]}
          title={'Khác'}
          emptyText={'Chưa có data'}
          showViewMore
          showClear
          clearText={'Xem tất cả'}
        />
      </NoticeIcon>
      <Suspense fallback={null}>
        {openMonitor && deviceId && (
          <MonitorDevice open={openMonitor} deviceId={deviceId} onOpenChange={setOpenMonitor} />
        )}
      </Suspense>
    </>
  );
};

export default NoticeIconView;
