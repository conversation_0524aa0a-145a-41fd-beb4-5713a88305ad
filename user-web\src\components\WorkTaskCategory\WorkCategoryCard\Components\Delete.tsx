import { DeleteFilled, PlusOutlined } from '@ant-design/icons';
import { Button, Col, Form, Input, message, Modal, Row, Select } from 'antd';
import { useState } from 'react';
const { Item } = Form;
import { createCategorie, removeCategorie } from '@/services/categires';
import { IIotCategoryGroup } from '@/services/categires/type';
import ActionPopConfirm from '@/components/ActionPopConfirm';
import { removeUnit } from '@/services/unit';
import { IIotItemUnit } from '@/services/unit/type';
import { createItem, listAllItem, updateItem, removeItem } from '@/services/workType';
import { IIotWorkType } from '@/services/workType/type';

const Delete = (params: { refreshFnc: any, value: IIotWorkType }) => {

    const removeData = async () => {
        try {
            await removeItem(params.value)
        } catch (error: any) {
            message.error(error.toString())
        }
    };


    return (
        <ActionPopConfirm
            actionCall={removeData}
            refreshData={params.refreshFnc}
            text={<DeleteFilled />}
            buttonType={"dashed"}
            danger={true}
            size='small'
        ></ActionPopConfirm>
    );
};

export default Delete;
