import { createEmptyArray } from '@/utils/array';
import { myLazy } from '@/utils/lazy';
import { DescriptionsSkeleton } from '@ant-design/pro-components';
import { Card } from 'antd';
import { createStyles } from 'antd-use-styles';
import { FC, ReactNode, Suspense } from 'react';
const ImagePreviewGroupCommon = myLazy(() => import('@/components/ImagePreviewGroupCommon'));

interface indexProps {
  children?: ReactNode;
}
const useStyles = createStyles(({ token }) => ({
  wrapper: {
    display: 'flex',
    flexDirection: 'column',
    gap: token.marginSM,
  },
}));
const index: FC<indexProps> = ({ children }) => {
  const styles = useStyles();
  return (
    <Suspense fallback={<DescriptionsSkeleton active />}>
      <div className={styles.wrapper}>
        <Card title="Phân bón chính">
          <ImagePreviewGroupCommon
            width={151}
            imgHeight={222}
            listImg={createEmptyArray(3).map(() => ({
              caption: 'Chứng nhận giống cây trồng đạt chuẩn mới',
              src: '',
            }))}
          />
        </Card>
        <Card title="Phân bón phụ">
          <ImagePreviewGroupCommon
            width={151}
            imgHeight={222}
            listImg={createEmptyArray(3).map(() => ({
              caption: 'Chứng nhận giống cây trồng đạt chuẩn mới',
              src: '',
            }))}
          />
        </Card>
      </div>
    </Suspense>
  );
};

export default index;
