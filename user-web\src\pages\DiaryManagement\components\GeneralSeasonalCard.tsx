import { DEFAULT_FALLBACK_IMG } from '@/common/contanst/img';
import { genDownloadUrl } from '@/utils/file';
import { EditOutlined } from '@ant-design/icons';
import { history } from '@umijs/max';
import { Avatar, Card } from 'antd';
import Meta from 'antd/es/card/Meta';

interface Props {
  label?: string;
  avatar?: string;
  name: string;
  start_date?: string;
  end_date?: string;
}

const formatDate = (dateString: any) => {
  const date = new Date(dateString);
  const day = String(date.getDate()).padStart(2, '0');
  const month = String(date.getMonth() + 1).padStart(2, '0');
  const year = date.getFullYear();
  return `${day}/${month}/${year}`;
};

const GeneralSeasonalCard = ({ label, avatar, name, start_date, end_date }: Props) => {
  function handleDelete(): void {
    throw new Error('Function not implemented.');
  }

  return (
    <Card
      size="default"
      onClick={() => {
        history.push(`/farming-diary/detail/${name}`);
      }}
      hoverable
      actions={[
        <EditOutlined key="edit" />,
        // <Popconfirm
        //   title="Xoá vụ mùa"
        //   description={`Bạn có muốn xoá vụ mùa ${label}?`}
        //   onConfirm={() => handleDelete()}
        //   key="delete"
        //   onPopupClick={(e) => {
        //     e.stopPropagation();
        //   }}
        // >
        //   <DeleteOutlined
        //     key="delete"
        //     onClick={(e) => {
        //       e.stopPropagation();
        //     }}
        //   />
        // </Popconfirm>,
      ]}
    >
      <Meta
        avatar={
          <Avatar
            shape="square"
            size={54}
            src={avatar ? genDownloadUrl(avatar) : DEFAULT_FALLBACK_IMG}
          />
        }
        title={label}
        description={`${formatDate(start_date)} - ${formatDate(end_date)}`}
      ></Meta>
    </Card>
  );
};

export default GeneralSeasonalCard;
