// gg api : https://developers.google.com/maps/documentation/javascript/examples/places-searchbox#maps_places_searchbox-typescript
import { Input } from 'antd';
import { FC, useEffect, useId, useRef, useState } from 'react';

interface SearchBoxProps {
  mapsAPI: any;
  mapAPI: any;
  onLatLngChange?: (lat: number, lng: number) => void;
}

const SearchBox: FC<SearchBoxProps> = ({ mapsAPI, mapAPI, onLatLngChange }) => {
  const inputContainerId = useId();
  const inputRef = useRef<HTMLInputElement>(null);
  const [value, setValue] = useState('');

  const initialSearchBox = () => {
    try {
      const inputElement = inputRef.current!;

      const SearchComponent = () => {
        const div = document.createElement('div');
        div.appendChild(document.getElementById(inputContainerId)!);
        return div;
      };
      const searchComponent = SearchComponent();
      const searchBox = new mapsAPI.places.SearchBox(inputElement);
      mapAPI.controls[mapsAPI.ControlPosition.TOP_CENTER].push(searchComponent);

      // Bias the SearchBox results towards current map's viewport.
      mapAPI.addListener('bounds_changed', () => {
        searchBox.setBounds(mapAPI.getBounds());
      });

      let markers: any[] = [];

      // Listen for the event fired when the user selects a prediction and retrieve
      // more details for that place.
      searchBox.addListener('places_changed', () => {
        const places = searchBox.getPlaces();

        if (places.length === 0) {
          return;
        }

        // Clear out the old markers.
        markers.forEach((marker) => {
          marker?.setMap?.(null);
        });
        markers = [];

        // For each place, get the icon, name and location.
        const bounds = new mapsAPI.LatLngBounds();

        places.forEach((place: any) => {
          if (!place.geometry || !place.geometry.location) {
            console.log('Returned place contains no geometry');
            return;
          }

          // const icon = {
          //   url: place.icon as string,
          //   size: new mapsAPI.Size(71, 71),
          //   origin: new mapsAPI.Point(0, 0),
          //   anchor: new mapsAPI.Point(17, 34),
          //   scaledSize: new mapsAPI.Size(25, 25),
          // };

          // Create a marker for each place.
          // markers.push(
          //   new mapsAPI.Marker({
          //     map: mapAPI,
          //     icon,
          //     title: place.name,
          //     position: place.geometry.location,
          //   }),
          // );
          // console.log(google.maps.marker);
          //  markers.push(
          //    new google.maps.marker.AdvancedMarkerElement({
          //      map: mapAPI,
          //      content: icon,
          //      title: place.name,
          //      position: place.geometry.location,
          //    }),
          //  );

          const lat = place.geometry.location.lat();
          const lng = place.geometry.location.lng();

          onLatLngChange?.(lat, lng);
          setValue(place.formatted_address);

          if (place.geometry.viewport) {
            // Only geocodes have viewport.
            bounds.union(place.geometry.viewport);
          } else {
            bounds.extend(place.geometry.location);
          }
        });
        mapAPI.fitBounds(bounds);
      });
    } catch (error) {
      console.log('error: ', error);
    }
  };
  useEffect(() => {
    // Create the search box and link it to the UI element.
    initialSearchBox();
  }, []);
  return (
    <div
      style={{
        zIndex: 5,
        // position: 'absolute',
        top: 0,
        // center horizontally
        left: '50%',
        transform: 'translateX(-50%)',
        display: 'none', // hide the search box and show it when the map is loaded
      }}
    >
      <div id={inputContainerId} style={{}}>
        {/* <input type="text" ref={inputRef} /> */}
        <Input.Search
          enterButton
          style={{
            boxShadow: '0 2px 6px rgba(0, 0, 0, 0.3)',
          }}
          value={value}
          onChange={(e) => {
            setValue(e.target.value);
          }}
          placeholder="Search..."
          ref={(ref) => ((inputRef.current as any) = ref?.input)}
          autoComplete="off"
        />
      </div>
    </div>
  );
};

export default SearchBox;
