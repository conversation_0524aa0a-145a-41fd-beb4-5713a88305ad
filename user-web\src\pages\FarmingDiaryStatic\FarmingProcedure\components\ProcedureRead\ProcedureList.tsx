import { getProcessList } from '@/services/diary-2/process';
import { useRequest } from '@umijs/max';
import { Spin } from 'antd';
import { FC, ReactNode } from 'react';
import ProcedureCard from './ProcedureCard';

interface ProcedureListProps {
  children?: ReactNode;
}

const ProcedureList: FC<ProcedureListProps> = ({ children }) => {
  const { data, loading, refresh } = useRequest(getProcessList, {});
  return (
    <div className="overflow-hidden">
      <Spin spinning={loading}>
        <div className="flex flex-nowrap items-start gap-4 overflow-auto max-w-full scrollbar-thin p-3">
          {data?.map((item) => (
            <div key={item.name} className="w-96 flex-none">
              <ProcedureCard reload={refresh} data={item} />
            </div>
          ))}
        </div>
      </Spin>
    </div>
  );
};

export default ProcedureList;
