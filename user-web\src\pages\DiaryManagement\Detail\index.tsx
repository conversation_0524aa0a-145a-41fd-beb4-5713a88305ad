import { myLazy } from '@/utils/lazy';
import { DescriptionsSkeleton } from '@ant-design/pro-components';
import { App, Button } from 'antd';
import { FC, ReactNode, useEffect, useState } from 'react';

import { DEFAULT_PAGE_SIZE_ALL, DOCTYPE_ERP } from '@/common/contanst/constanst';
import PageContainerTabsWithSearch from '@/components/PageContainerTabsWithSearch';
import { getCrop } from '@/services/crop';
import { getCropDocument } from '@/services/cropDocument';
import { getStatisticNoteList } from '@/services/cropManager';
import { getCropDiary } from '@/services/diary';
import { getDiaryTaskList } from '@/services/farming-plan';
import { getPestList } from '@/services/pandemic';
import { formatDate } from '@/services/utils';
import { IDocument } from '@/types/document.type';
import { DownloadOutlined } from '@ant-design/icons';
import { useAccess, useIntl, useModel, useRequest } from '@umijs/max';
import moment from 'moment';
import { nanoid } from 'nanoid';
import * as XLSX from 'xlsx';
import UploadDocumentModal from '../components/UploadDocumentModal';
import Documents from './Documents';
import useParamsUrl from './hooks/useParamsUrl';
import Logs from './Logs';

const Pandemic = myLazy(() => import('./Pandemic'));
const GeneralInfo = myLazy(() => import('./GeneralInfo'));
const SeasonalNote = myLazy(() => import('./Note'));
const Participants = myLazy(() => import('./Participants'));
interface DetailWorkflowProps {
  children?: ReactNode;
}
const DetailWorkflow: FC<DetailWorkflowProps> = () => {
  const { detailId, tabActive, setTabActive } = useParamsUrl();
  const { selectedCrop, setSelectedCrop } = useModel('MyCrop');
  const [isLoading, setIsLoading] = useState(false);
  const intl = useIntl();
  //  hướng dẫn cần thông tin plant_id nên get ở đây luôn
  const { data: detailCrop, loading } = useRequest(
    () =>
      getCrop({
        page: 1,
        size: 1,
        filters: JSON.stringify([['iot_crop', 'name', 'like', detailId]]),
        fields: JSON.stringify(['name', 'plant_id']),
      }),
    {
      refreshDeps: [detailId],
      formatResult(res: any) {
        setSelectedCrop(res.data.data[0]);
        return res.data.data[0];
      },
    },
  );
  const [documents, setDocuments] = useState<IDocument[]>();
  useEffect(() => {
    const fetchData = async () => {
      const res = await getCropDocument(detailId as string);
      setDocuments(res || []);
    };
    fetchData();
  }, [detailId]);

  const { modal } = App.useApp();

  const onDelete = () => {
    modal.confirm({
      title: 'Bạn có chắc chắn muốn xóa công việc này',
      content: 'Nếu đồng ý, tất cả mọi dữ liệu liên quan sẽ bị thay đổi',
      okText: 'Tôi chắc chắn',
      okButtonProps: {
        danger: true,
      },
      onOk: async () => {},
    });
  };
  // refresh the Pandemic
  const [pandemicCacheKey, setPandemicCacheKey] = useState(nanoid());
  const [cropNoteCacheKey, setCropNoteCacheKey] = useState(nanoid());
  const access = useAccess();
  const canReadTask = access.canAccessPageWorkFlowManagement();
  const handleDownload = async () => {
    setIsLoading(true);
    try {
      const cropDiary = (await getCropDiary({
        size: 1,
        page: 1,
        filters: JSON.stringify([[DOCTYPE_ERP.iotCropDiary, 'crop_id', '=', detailId]]),
      })) as any;

      const workbook = XLSX.utils.book_new();
      const generalData = cropDiary.data.data.at(0);
      const generalAoa = [
        ['Thông tin chi tiết', '', '', 'Thông tin doanh nghiệp', ''],
        ['Tên vụ mùa', generalData.label, '', 'Chủ sở hữu', generalData.crop_owner],
        ['Khu vực', generalData.zone_name, '', 'Thông tin doanh nghiệp', generalData.business_info],
        ['Thời gian hoàn thành', generalData.end_date, '', 'Địa chỉ', generalData.location],
        ['Diện tích canh tác', generalData.square, '', 'Vĩ độ', generalData.latitude],
        ['Loại cây', generalData.plant_name, '', 'Kinh độ', generalData.longitude],
        ['Sản lượng dự kiến', generalData.quantity_estimate, '', 'Điện thoại', generalData.phone],
        ['Trạng thái', generalData.status, '', 'Email', generalData.email],
        ['Ghi chú', generalData.description, '', 'Link website', generalData.website],
        ['', '', '', 'Ghi chú', generalData.short_description],
      ];

      const generalSheet = XLSX.utils.aoa_to_sheet(generalAoa);
      const rawTasks = (
        await getDiaryTaskList({
          size: 10000,
          filters: [[DOCTYPE_ERP.iotCrop, 'name', 'like', detailId]],
          order_by: 'start_date desc',
        })
      ).data;
      const filteredItemList = [];
      for (const task of rawTasks) {
        filteredItemList.push(
          ...(task.item_list?.map((item: any) => ({
            task_label: task.label,
            label: item?.category?.label,
            type: 'Vật tư',
            exp_quantity: item.exp_quantity || 0,
            quantity: item.quantity || 0,
            loss_quantity: item.loss_quantity || 0,
            unit_label: item.category?.item_unit?.label || '',
            description: item.description,
          })) || []),
        );
        filteredItemList.push(
          ...(task.prod_quantity_list?.map((item: any) => ({
            task_label: task.label,
            label: item?.agriculture_product?.label,
            type: 'Sản phẩm',
            exp_quantity: item.exp_quantity || 0,
            quantity: item.quantity || 0,
            loss_quantity: item.lost_quantity || 0,
            unit_label: item.agriculture_product?.unit_label,
            description: item.description,
          })) || []),
        );
      }

      const itemSheet = XLSX.utils.json_to_sheet(
        filteredItemList.map((item, index) => ({ index: index + 1, ...item })),
      );
      // Update header name
      XLSX.utils.sheet_add_aoa(
        itemSheet,
        [
          [
            'STT',
            'Tên công việc',
            'Tên hàng hóa',
            'Loại',
            'Số lượng dự kiến',
            'Số lượng thực tế',
            'Số lượng hao hụt',
            'Đơn vị',
            'Ghi chú',
          ],
        ],
        { origin: 'A1' },
      );
      const formattedTaskList = rawTasks.map((task) => ({
        state: task.text_state || task.state_name,
        label: task.label,
        start_date: moment(task.start_date).format('HH:mm DD/MM/YYYY'),
        end_date: moment(task.end_date).format('HH:mm DD/MM/YYYY'),
        assigned_to:
          task.text_assign_user ||
          `${task.assigned_to_info?.at(0)?.first_name} ${task.assigned_to_info?.at(0)?.last_name}`,

        people_involved: task?.involve_in_users?.reduce(
          (acc, ele) => acc + `${ele.first_name} ${ele.last_name}, `,
          '',
        ),
        project: task.project_name,
        zone: task.zone_name,
        status: task.status,
        description: task.description,
      }));
      formattedTaskList.sort((a, b) => {
        if (a.state < b.state) {
          return -1;
        }
        if (a.state > b.state) {
          return 1;
        }
        return -moment(a.start_date).valueOf() + moment(b.start_date).valueOf();
      });

      const taskSheet = XLSX.utils.json_to_sheet(formattedTaskList);
      // Update header name
      XLSX.utils.sheet_add_aoa(
        taskSheet,
        [
          [
            'Giai đoạn',
            'công việc',
            'Thời gian bắt đầu',
            'Thời gian kết thúc',
            'Người thực hiện',
            'Người liên quan',
            'Dự án',
            'Khu vực',
            'Trạng thái',
            'Ghi chú',
          ],
        ],
        { origin: 'A1' },
      );

      let curIndex = 0;
      let currentState = formattedTaskList.at(0)?.state;
      const range = [];
      if (!taskSheet['!merges']) taskSheet['!merges'] = [];
      formattedTaskList.forEach((task, index) => {
        if (task.state !== currentState) {
          range.push([`A${curIndex + 2}`, `A${index + 2}`]);
          taskSheet['!merges']!.push(XLSX.utils.decode_range(`A${curIndex + 2}:A${index + 1}`));
          currentState = task.state;
          curIndex = index;
        }
      });
      if (curIndex !== 0) {
        taskSheet['!merges']!.push(XLSX.utils.decode_range(`A${curIndex + 2}:A`));
      }

      const noteList = await getStatisticNoteList({
        page: 1,
        size: DEFAULT_PAGE_SIZE_ALL,
        order_by: 'creation desc',
        cropId: detailId,
      });
      const noteSheet = XLSX.utils.json_to_sheet(
        noteList.data.map((note, index) => ({
          index: index + 1,
          creation: formatDate(note.creation),
          label: note.note_label,
          des: note.note,
        })),
      ); // Update header name

      XLSX.utils.sheet_add_aoa(noteSheet, [['STT', 'Ngày tạo', 'Tiêu đề', 'Mô tả']], {
        origin: 'A1',
      });

      const pestList = await getPestList({
        page: 1,
        size: DEFAULT_PAGE_SIZE_ALL,
        filters: [[DOCTYPE_ERP.iotPest, 'iot_crop', 'like', `${detailId}`]],
        order_by: 'creation desc',
      });

      const pestSheet = XLSX.utils.json_to_sheet(
        pestList.data.map((pest, index) => ({
          index: index + 1,
          creation: formatDate(pest.creation),
          label: pest.label,
          state: pest.state_list.join(', '),
          item: pest.category_list.join(', '),
          involved: pest.involved_in_users.join(', '),
          des: pest.description,
        })),
      );
      XLSX.utils.book_append_sheet(workbook, generalSheet, 'Tổng quát');
      XLSX.utils.book_append_sheet(workbook, itemSheet, 'Vật tư-sản phẩm');
      XLSX.utils.book_append_sheet(workbook, taskSheet, 'Công việc');
      XLSX.utils.book_append_sheet(workbook, noteSheet, 'Ghi chú');
      XLSX.utils.book_append_sheet(workbook, pestSheet, 'Dịch hại');
      const buffer = XLSX.write(workbook, { bookType: 'xlsx', type: 'buffer' });
      const newblob = new Blob([buffer], { type: 'application/octet-stream' });
      const url = window.URL.createObjectURL(newblob);
      const link = document.createElement('a');
      link.href = url;
      link.download = `export_statistic.xlsx`;
      link.click();
    } catch (error) {
    } finally {
      setIsLoading(false);
    }
  };
  const extraPage = [
    <Button
      icon={<DownloadOutlined />}
      onClick={handleDownload}
      key={'download`'}
      loading={isLoading}
    >
      {intl.formatMessage({ id: 'action.download' })}
    </Button>,
  ];
  return (
    <PageContainerTabsWithSearch
      tabsItems={[
        {
          label: intl.formatMessage({ id: 'seasonalTab.overview' }),
          component: () => <GeneralInfo cropId={detailId as string} />,
          // extraPage: [<Button key={'cancel'}>Hủy</Button>, <Button key={'save'}>Lưu</Button>],
          fallback: <DescriptionsSkeleton active />,
          extraPage,
        },
        {
          label: intl.formatMessage({ id: 'common.logs' }),
          component: () => <Logs cropId={detailId} />,
          // extraPage: [<Button key={'cancel'}>Hủy</Button>, <Button key={'save'}>Lưu</Button>],
          fallback: <DescriptionsSkeleton active />,
          extraPage,
        },
        {
          label: intl.formatMessage({ id: 'seasonalTab.pest' }),
          component: () => <Pandemic cropId={detailId} />,
          fallback: <DescriptionsSkeleton active />,
          extraPage,
        },
        {
          label: intl.formatMessage({ id: 'common.informations' }),
          component: () => (
            <Documents
              cropId={detailId as string}
              documents={documents || []}
              setDocuments={setDocuments}
            />
          ),
          extraPage: [
            <UploadDocumentModal
              key={'new'}
              cropId={detailId as string}
              setDocuments={setDocuments}
            />,
            ...extraPage,
          ],
          fallback: <DescriptionsSkeleton active />,
        },
      ]}
      autoFormatTabKey
    />
  );
};

export default DetailWorkflow;
