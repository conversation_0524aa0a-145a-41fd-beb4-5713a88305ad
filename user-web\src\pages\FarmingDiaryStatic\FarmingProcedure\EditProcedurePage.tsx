import { PageContainer } from '@ant-design/pro-components';
import { useParams } from '@umijs/max';
import { FC, ReactNode } from 'react';
import EditProcedure from './components/Edit';

interface EditProcessPageProps {
  children?: ReactNode;
}

const EditProcessPage: FC<EditProcessPageProps> = ({ children }) => {
  const { id } = useParams();
  if (!id) return null;
  return (
    <PageContainer>
      <EditProcedure id={id} />
    </PageContainer>
  );
};

export default EditProcessPage;
