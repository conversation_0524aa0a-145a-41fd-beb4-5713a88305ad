import { getCropList } from '@/services/cropManager';
import { createCropTracing } from '@/services/tracing';
import { PlusOutlined } from '@ant-design/icons';
import { ProFormSelect } from '@ant-design/pro-components';
import { useIntl } from '@umijs/max';
import { Button, Col, DatePicker, Form, Input, message, Modal, Row } from 'antd';
import { useEffect, useState } from 'react';
import TracingTable from './CropTraceList';

const { Item } = Form;

const CreateTracingFromCrop = (params: { refreshFnc: any; crop_id: string }) => {
  const [loading, setLoading] = useState(false);
  const [isOpen, setOpen] = useState(false);
  const [form] = Form.useForm();

  const [imageUrl, setImageUrl] = useState<string>();
  const [uploading, setUploading] = useState(false);
  const [fileList, setFileList] = useState<any[]>([]);
  useEffect(() => {
    if (params.crop_id) {
      form.setFieldsValue({ crop_id: params.crop_id });
    }
  }, [params.crop_id]);
  const showModal = () => {
    setOpen(true);
  };
  const hideModal = () => {
    setOpen(false);
  };
  const handleOk = () => {
    form.submit();
  };
  const handleCancel = () => {
    hideModal();
    // form.resetFields();
  };
  const intl = useIntl();
  return (
    <>
      <Button type="default" onClick={showModal}>
        <PlusOutlined /> {intl.formatMessage({ id: 'common.add_origin' })}
      </Button>
      <Modal
        title={intl.formatMessage({ id: 'common.add_origin' })}
        open={isOpen}
        onOk={handleOk}
        onCancel={handleCancel}
        style={{ maxWidth: '100%' }}
        width={'80%'}
        confirmLoading={loading}
      >
        <Form
          layout="horizontal"
          labelCol={{ span: 24 }}
          labelAlign="left"
          form={form}
          wrapperCol={{ span: 24 }}
          style={{ padding: '0 25px' }} // Add padding here
          onFinish={async (value: any) => {
            try {
              value.image = '';
              if (fileList.length) {
                value.image = fileList[0].raw_url;
              }
              //   console.log('value product', value);
              const result = await createCropTracing(value);
              form.resetFields();
              hideModal();
              message.success('Success!');
              if (params.refreshFnc) {
                await params.refreshFnc();
              }
            } catch (error: any) {
              message.error(error.toString());
            } finally {
              setLoading(false);
            }
          }}
        >
          <Row gutter={20}>
            <Col className="gutter-row" md={8} span={8}>
              <Item
                label={intl.formatMessage({ id: 'common.origin_name' })}
                labelCol={{ span: 24 }}
                rules={[
                  {
                    required: true,
                    message: 'Bắt buộc điền',
                  },
                ]}
                name="label"
              >
                <Input />
              </Item>
            </Col>

            <Col className="gutter-row" md={8} span={8}>
              <Item label={intl.formatMessage({ id: 'common.expired_time' })} name="expiry_time">
                <DatePicker
                  format="DD/MM/YYYY hh:mm A"
                  onChange={(date, dateString) => console.log(date, dateString)}
                  showTime={{ use12Hours: true }}
                  placeholder="Default 2 days"
                  style={{ width: '100%' }}
                />
              </Item>
            </Col>
            <Col className="gutter-row" md={8} span={8}>
              <Item
                label={intl.formatMessage({ id: 'common.crop' })}
                required={true}
                name="crop_id"
              >
                <ProFormSelect
                  showSearch
                  request={async () => {
                    const data = await getCropList();
                    return data.data.map((item: any) => ({
                      label: item.label,
                      value: item.name,
                    }));
                  }}
                />
              </Item>
            </Col>
          </Row>
          <Row></Row>
        </Form>
        <div style={{ padding: '0 0px' }}>
          <TracingTable crop_id={params.crop_id}></TracingTable>
        </div>{' '}
      </Modal>
    </>
  );
};

export default CreateTracingFromCrop;
