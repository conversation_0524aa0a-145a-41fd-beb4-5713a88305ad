import RealityColumnRender from '@/components/RealityColumnRender';
import WorkTypeCategoryCard from '@/components/WorkTaskCategory';
import { getAllTaskWorkTime } from '@/services/TaskAndTodo';
import { IIotCategory } from '@/types/IIotCategory';
import type { ProColumns } from '@ant-design/pro-components';
import { ProTable } from '@ant-design/pro-components';
import { FormattedMessage, useAccess, useModel } from '@umijs/max';
import { message } from 'antd';
import React, { useEffect, useState } from 'react';
import CreateWorkTimeForTask from './CreateWorkTimeForTask';
import DeleteWorkTimeForTask from './DeleteWorkTimeForTask';
import UpdateWorkTimeForTask from './UpdateWorkTimeForTask';

type DataSourceType = {
  id: React.Key;
  title?: string;
  decs?: string;
  state?: string;
  created_at?: string;
  children?: DataSourceType[];
};

export default ({ task_id }: { task_id: string }) => {
  const [dataSource, setDataSource] = useState([]);
  // const [units, setUnits] = useState<IIotItemUnit[]>([]);
  // const [categoreis, setCategories] = useState<IIotCategoryGroup[]>([]);
  // const [packings, setPackings] = useState<IIotItemPackingUnit[]>([]);
  const [items, setItems] = useState<IIotCategory[]>([]);

  const [editableKeys, setEditableRowKeys] = useState<React.Key[]>();
  const [loading, setLoading] = useState(false);
  const { myWorkType } = useModel('MyWorkType');
  const getTaskTodo = async () => {
    try {
      setLoading(true);
      const resData = await getAllTaskWorkTime(task_id);
      setDataSource(resData.data);
    } catch (error: any) {
      message.error(error.toString());
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    getTaskTodo();
  }, []);

  const columns: ProColumns<DataSourceType>[] = [
    {
      title: 'Loại công việc',
      dataIndex: 'work_type_id',
      render(dom, entity, index, action, schema) {
        return <>{myWorkType.find((el) => el.name === dom)?.label}</>;
      },
    },
    {
      title: <FormattedMessage id="category.material-management.unit" defaultMessage="unknown" />,
      dataIndex: 'type',
      render: (dom: any, entity: any) => {
        if (dom === 'Workday') return <>Công</>;
        if (dom === 'Hour') return <>Giờ</>;
        return '';
      },
    },
    {
      title: 'Số công/giờ làm dự kiến',
      dataIndex: 'exp_quantity',
    },

    {
      title: 'Chi phí',
      dataIndex: 'cost',
    },

    {
      title: 'Mô tả',
      dataIndex: 'description',
      formItemProps: {
        rules: [
          {
            required: true,
            whitespace: true,
            message: 'Mô tả không được để trống',
          },
        ],
      },
    },
    {
      title: 'Số công/giờ thực tế',
      dataIndex: 'quantity',
      render(dom) {
        return <RealityColumnRender>{dom}</RealityColumnRender>;
      },
    },
    {
      title: 'Hành động',
      dataIndex: 'name',
      render: (dom: any, entity: any) => {
        return (
          <>
            <UpdateWorkTimeForTask refreshFnc={getTaskTodo} task_id={task_id} data={entity} />
            {'  '}
            <DeleteWorkTimeForTask refreshFnc={getTaskTodo} value={dom} />
          </>
        );
      },
    },
  ];
  const access = useAccess();
  const canCreateTask = access.canCreateInWorkFlowManagement();
  const canUpdateTask = access.canUpdateInWorkFlowManagement();
  const toolBarRenderButtons: any = [];
  if (canCreateTask || canUpdateTask) {
    toolBarRenderButtons.push(
      <CreateWorkTimeForTask key="create-new" refreshFnc={getTaskTodo} task_id={task_id} />,
      <WorkTypeCategoryCard key="create-cag" />,
    );
  }
  return (
    <>
      <ProTable
        headerTitle="Nhân công"
        columns={columns}
        rowKey="name"
        dataSource={[...dataSource]}
        toolBarRender={() => {
          return toolBarRenderButtons;
        }}
        search={false}
      />
    </>
  );
};
