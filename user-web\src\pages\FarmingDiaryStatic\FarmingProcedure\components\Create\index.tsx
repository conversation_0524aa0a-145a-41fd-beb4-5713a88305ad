import { ProForm } from '@ant-design/pro-components';
import { history } from '@umijs/max';
import { FC, ReactNode } from 'react';
import useCreate from '../../hooks/useCreate';
import Certification from './Certification';
import Info from './Info';
import Note from './Note';
import Stage from './Stage';

interface CreateProcedureProps {
  children?: ReactNode;
}

const CreateProcedure: FC<CreateProcedureProps> = ({ children }) => {
  const { run } = useCreate({
    onSuccess() {
      history.push('/farming-diary-static/procedure/list');
    },
  });
  const onFinish = async (values: any) => {
    await run({
      label: values.label,
      description: values.description,
      image: values.image,
      states: values.states?.map((item: any) => ({
        name: item.name,
        idx: item.idx,
      })),
      notes: values.notes?.map((item: any) => ({
        name: item.name,
        idx: item.idx,
      })),
      documents: values.documents?.map((item: any) => ({
        name: item.name,
        idx: item.idx,
      })),
      expire_time_in_days: 0,
    });
    return true;
  };
  const [form] = ProForm.useForm();
  return (
    <ProForm onFinish={onFinish} form={form}>
      <div className="flex flex-col gap-4 mb-4">
        <Info />
        <Stage />
        <Certification />
        <Note />
      </div>
    </ProForm>
  );
};

export default CreateProcedure;
