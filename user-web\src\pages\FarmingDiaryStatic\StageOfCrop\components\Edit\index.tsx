import { ProForm } from '@ant-design/pro-components';
import { useIntl } from '@umijs/max';
import { Spin } from 'antd';
import { FC, ReactNode, useEffect } from 'react';
import useDetail from '../../hooks/useDetail';
import useUpdate from '../../hooks/useUpdate';
import DetailedInfo from './DetailedInfo';
import Task from './Task';

interface StageOfCountEditProps {
  children?: ReactNode;
  id: string;
  onSuccess?: () => void;
  setIsFormDirty: (dirty: boolean) => void;
}

const StageOfCountEdit: FC<StageOfCountEditProps> = ({
  children,
  id,
  onSuccess,
  setIsFormDirty = () => {},
}) => {
  const { formatMessage } = useIntl();
  const [form] = ProForm.useForm();
  const { data, loading } = useDetail({
    id: id,
    onSuccess(data) {
      form.setFieldsValue({
        ...data,
        tasks: data.tasks?.map((task) => ({
          ...task,
          supplies: task?.related_items
            ?.map((related_item) => ` ${related_item.quantity} ${related_item.label}`)
            .join(','),
        })),
      });
    },
  });
  const { run } = useUpdate({
    onSuccess() {
      if (onSuccess) {
        onSuccess();
      }
    },
  });

  useEffect(() => {
    setIsFormDirty(false);
  }, [data]);

  return (
    <Spin spinning={loading}>
      <ProForm
        form={form}
        onValuesChange={() => setIsFormDirty(true)}
        onFinish={async (values) => {
          await run({
            name: id,
            label: values.label,
            description: values.description,
            expire_time_in_days: values.expire_time_in_days || 0,
            image: values.image,
            task_count: values.tasks.length,
            is_deleted: 0,
            tasks: values.tasks?.map((task: any) => ({
              name: task.name,
              idx: task.idx || 0,
            })),
          });
          setIsFormDirty(false);
          return true;
        }}
        submitter={{
          render: (_, dom) => {
            return (
              <div style={{ textAlign: 'right', margin: 24 }}>
                {dom.map((item, index) => (
                  <span key={index} style={{ marginRight: index === 0 ? 8 : 0 }}>
                    {item}
                  </span>
                ))}
              </div>
            );
          },
        }}
      >
        <div className="space-y-4 mb-4">
          <DetailedInfo initialImage={data?.image} />
          <Task />
        </div>
      </ProForm>
    </Spin>
  );
};

export default StageOfCountEdit;
