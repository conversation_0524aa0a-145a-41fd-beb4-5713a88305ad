import { listAllItem } from '@/services/products';
import { generalUpdate } from '@/services/sscript';
import { listAllUnit } from '@/services/unit';
import { EditOutlined } from '@ant-design/icons';
import { ProFormSelect } from '@ant-design/pro-components';
import { FormattedMessage } from '@umijs/max';
import { Button, Col, Form, Input, InputNumber, message, Modal, Row } from 'antd';
import { useState } from 'react';
const { Item } = Form;

const UpdateProductionForTask = (params: { refreshFnc: any; task_id: string; data: any }) => {
  const [loading, setLoading] = useState(false);
  const [isOpen, setOpen] = useState(false);
  const [form] = Form.useForm();

  const showModal = () => {
    form.setFieldsValue(params.data);
    setOpen(true);
  };
  const hideModal = () => {
    setOpen(false);
  };
  const handleOk = () => {
    form.submit();
  };
  const handleCancel = () => {
    hideModal();
    form.resetFields();
  };

  return (
    <>
      <Button
        size="small"
        //type="dashed"
        style={{ display: 'flex', alignItems: 'center' }}
        onClick={showModal}
      >
        <EditOutlined></EditOutlined>
      </Button>
      <Modal
        width={800}
        title={`Chỉnh sửa dữ liệu`}
        open={isOpen}
        onOk={handleOk}
        onCancel={handleCancel}
        confirmLoading={loading}
      >
        <Form
          layout="horizontal"
          labelCol={{ span: 24 }}
          labelAlign="left"
          form={form}
          onFinish={async (value: any) => {
            try {
              value.task_id = params.task_id;
              value.name = params.data.name;
              const result = await generalUpdate('iot_production_quantity', params.data.name, {
                data: value,
              });
              form.resetFields();
              hideModal();
              message.success('Success!');
              if (params.refreshFnc) {
                await params.refreshFnc();
              }
            } catch (error: any) {
              message.error(error.toString());
            } finally {
              setLoading(false);
            }
          }}
        >
          <Row gutter={5}>
            <Col className="gutter-row" md={24}>
              <ProFormSelect
                rules={[
                  {
                    required: true,
                    message: 'Bắt buộc điền',
                  },
                ]}
                allowClear
                label="Sản phẩm thu hoạch"
                name="product_id"
                request={async () => {
                  const res = await listAllItem();
                  const unitsRes = await listAllUnit();
                  return res.data.map((item: any) => {
                    const unit = unitsRes.data?.find((d) => d.name === item.unit_id);
                    return {
                      label: item.label + `${unit ? ' - ' + unit?.label : ''}`,
                      value: item.name,
                    };
                  });
                }}
              />
            </Col>

            <Col className="gutter-row" md={12}>
              <Item label="Sản lượng/số lượng dự kiến" labelCol={{ span: 24 }} name="exp_quantity">
                <InputNumber style={{ width: '100%' }} min={0} />
              </Item>
            </Col>
            <Col className="gutter-row" md={12}>
              <Item label="Sản lượng/số lượng thực tế" labelCol={{ span: 24 }} name="quantity">
                <InputNumber style={{ width: '100%' }} min={0} />
              </Item>
            </Col>
            <Col className="gutter-row" md={12}>
              <Item label="Sản lượng/số lượng hao hụt" labelCol={{ span: 24 }} name="lost_quantity">
                <InputNumber style={{ width: '100%' }} min={0} />
              </Item>
            </Col>
            <Col className="gutter-row" md={24}>
              <Item
                label={<FormattedMessage id={'common.form.description'} />}
                labelCol={{ span: 24 }}
                name="description"
              >
                <Input.TextArea />
              </Item>
            </Col>
          </Row>
        </Form>
      </Modal>
    </>
  );
};

export default UpdateProductionForTask;
