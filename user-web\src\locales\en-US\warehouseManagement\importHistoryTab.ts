export default {
  'warehouse-management.import-history.id': 'Id',
  'warehouse-management.import-history.date': 'Import date',
  'warehouse-management.import-history.supplier': 'Supplier',
  'warehouse-management.import-history.total-qty': 'Total quantity',
  'warehouse-management.import-history.total-price': 'Total Price',
  'warehouse-management.import-history.item': 'Item', // Item Code
  'warehouse-management.import-history.accepted-quantity': 'Accepted Quantity', // Accepted Quantity
  'warehouse-management.import-history.rejected-quantity': 'Rejected Quantity', // Rejected Quantity
  'warehouse-management.import-history.rate': 'Rate', // Rate
  'warehouse-management.import-history.amount': 'Amount',
  'warehouse-management.import-history.detail': 'Detail',
  'warehouse-management.import-history.total_quantity': 'Total quantity',
  'warehouse-management.import-history.total_price': 'Total price',
  'warehouse-management.import-history.warehouse_label': 'Warehouse',
  'warehouse-management.import-history.item_label': 'Item',
  'warehouse-management.import-history.item_id': 'Item ID',
  'warehouse-management.import-history.add_taxes': 'Add Taxes',
  'warehouse-management.import-history.discount': 'Discount',
  'warehouse-management.import-history.voucher_amount': 'Voucher Amount',
  'warehouse-management.import-history.other_charges': 'Other Charges',
  'warehouse-management.import-history.total_amount': 'Total Amount',
  'warehouse-management.import-history.paid_amount': 'Paid Amount',
};
