import { useIntl } from '@umijs/max';
import { Typography } from 'antd';
import { FC, ReactNode } from 'react';

interface ProcedureItemProps {
  children?: ReactNode;
  title?: string;
  icon?: ReactNode;
  description?: ReactNode;
  index?: number;
  task_count?: number;
}

const ProcedureItem: FC<ProcedureItemProps> = ({ index, title, task_count }) => {
  const { formatMessage } = useIntl();
  return (
    <div className="flex gap-4 items-start">
      <div className="flex-none w-10 h-10 flex items-center justify-center text-emerald-500 text-xl font-bold rounded">
        {index !== undefined && (index + 1).toString().padStart(2, '0')}
      </div>
      <div>
        <div className="font-semibold">{title}</div>
        <Typography.Paragraph>{`${formatMessage({
          id: 'common.you_need_to_complete',
        })} ${task_count} ${formatMessage({
          id: 'common.task_lower_case',
        })}`}</Typography.Paragraph>
      </div>
    </div>
  );
};

export default ProcedureItem;
