/**
 * Standardized UOM Display Renderer Component
 * Provides consistent UOM display logic across all components
 */

import React from 'react';
import { ActiveUOMData, UOMInfo } from '@/types/uom.types';
import { getUOMDisplayName } from '@/utils/uom-validation';

interface UOMDisplayRendererProps {
  /** UOM data to display */
  data: ActiveUOMData;
  /** Available UOMs for enhanced display */
  availableUOMs?: UOMInfo[];
  /** Fallback text when no UOM is available */
  fallback?: string;
  /** Additional CSS classes */
  className?: string;
  /** Custom render function */
  render?: (displayName: string, data: ActiveUOMData) => React.ReactNode;
}

/**
 * Renders UOM display name with consistent fallback logic
 */
export const UOMDisplayRenderer: React.FC<UOMDisplayRendererProps> = ({
  data,
  availableUOMs = [],
  fallback = '-',
  className,
  render
}) => {
  const displayName = getUOMDisplayName(data, availableUOMs) || fallback;
  
  if (render) {
    return <>{render(displayName, data)}</>;
  }
  
  return <span className={className}>{displayName}</span>;
};

/**
 * Hook for getting UOM display name with consistent logic
 */
export function useUOMDisplayName(
  data: ActiveUOMData,
  availableUOMs: UOMInfo[] = [],
  fallback: string = '-'
): string {
  return React.useMemo(() => {
    return getUOMDisplayName(data, availableUOMs) || fallback;
  }, [data, availableUOMs, fallback]);
}

/**
 * Higher-order component for adding UOM display functionality
 */
export function withUOMDisplay<P extends object>(
  Component: React.ComponentType<P>
) {
  return React.forwardRef<any, P & { uomData?: ActiveUOMData; availableUOMs?: UOMInfo[] }>((props, ref) => {
    const { uomData, availableUOMs, ...restProps } = props;
    
    const displayName = useUOMDisplayName(uomData || {}, availableUOMs);
    
    return (
      <Component
        ref={ref}
        {...(restProps as P)}
        uomDisplayName={displayName}
      />
    );
  });
}

/**
 * Utility component for form items that need UOM display
 */
export const UOMFormItemRenderer: React.FC<{
  record?: any;
  config?: any;
  availableUOMs?: UOMInfo[];
  fallback?: string;
}> = ({ record, config, availableUOMs = [], fallback = '-' }) => {
  const data = record || config?.record || {};
  const displayName = getUOMDisplayName(data, availableUOMs) || fallback;
  
  return <span>{displayName}</span>;
};

export default UOMDisplayRenderer;
