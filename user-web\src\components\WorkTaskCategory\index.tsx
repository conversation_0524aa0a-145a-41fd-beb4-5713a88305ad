import { PlusOutlined } from '@ant-design/icons';
import { Button, Col, Form, Modal, Row } from 'antd';
import { useState } from 'react';
import UnitManagement from './WorkCategoryCard';
const { Item } = Form;

const WorkTypeCategoryCard = () => {
  const [loading, setLoading] = useState(false);
  const [isOpen, setOpen] = useState(false);
  const [form] = Form.useForm();

  const showModal = () => {
    setOpen(true);
  };
  const hideModal = () => {
    setOpen(false);
  };
  const handleOk = () => {
    hideModal();
  };
  const handleCancel = () => {
    hideModal();
  };

  return (
    <>
      <Button type="default" style={{ display: 'flex', alignItems: 'center' }} onClick={showModal}>
        <PlusOutlined /> Loại nhân công
      </Button>
      <Modal
        title={``}
        open={isOpen}
        onOk={handleOk}
        onCancel={handleCancel}
        confirmLoading={loading}
        closeIcon={false}
        footer={[
          <Button key="back" onClick={handleCancel}>
            Đóng
          </Button>,
        ]}
      >
        <Row gutter={[15, 15]}>
          <Col md={24}>
            <UnitManagement />
          </Col>
        </Row>
      </Modal>
    </>
  );
};

export default WorkTypeCategoryCard;
