import { DOCTYPE_ERP } from '@/common/contanst/constanst';
import FallbackComponent from '@/components/FallbackContent';
import { getFarmingPlanList } from '@/services/farming-plan';
import { ActionType, ProTable } from '@ant-design/pro-components';
import { Access, FormattedMessage, Link, useAccess } from '@umijs/max';

import { IFarmPlanRes } from '@/types/IFarmPlanRes.type';
import { genDownloadUrl } from '@/utils/file';
import { useDebounceFn } from 'ahooks';
import { Avatar, Input } from 'antd';
import { FC, ReactNode, useRef, useState } from 'react';
import CreateCropPlan from '../Create';
import { formatDateDefault } from '@/utils/date';

interface CropPlanProps {
  children?: ReactNode;
}

const CropPlanList: FC<CropPlanProps> = ({ children }) => {
  const [searchPlan, setSearchPlan] = useState('');
  const actionRef = useRef<ActionType>(null);
  const handleReload = () => {
    actionRef.current?.reload();
  };
  const { run: handleSearch } = useDebounceFn(
    (e: React.ChangeEvent<HTMLInputElement>) => {
      setSearchPlan(e.target.value);
      handleReload();
    },
    {
      wait: 400,
    },
  );

  const access = useAccess();
  const canCreatePlan = access.canCreateInPlanManagement();

  return (
    <Access accessible={access.canAccessPagePlanManagement()} fallback={<FallbackComponent />}>
      <ProTable<IFarmPlanRes>
        actionRef={actionRef}
        headerTitle={
          <Input placeholder="Tìm..." addonBefore="Tên kế hoạch" onChange={handleSearch} />
        }
        toolBarRender={() => [
          canCreatePlan && <CreateCropPlan key="add" onSuccess={handleReload} />,
        ]}
        pagination={{
          pageSize: 20,
        }}
        search={false}
        rowKey={'name'}
        columns={[
          {
            title: 'ID',
            copyable: true,
            dataIndex: 'name',
            width: 200,
          },
          {
            title: <FormattedMessage id="storage-management.category-management.object_name" />,
            dataIndex: 'label',
            render(dom, entity, index, action, schema) {
              return (
                <Link to={`/farming-management/crop-management-plan/detail/${entity.name}`}>
                  {dom}
                </Link>
              );
            },
          },
          {
            title: <FormattedMessage id="common.form.image" defaultMessage="Image" />,
            render(dom, entity, index, action, schema) {
              return <Avatar src={entity.image ? genDownloadUrl(entity.image) : undefined} />;
            },
          },
          {
            title: 'Ngày bắt đầu',
            dataIndex: 'start_date',
            valueType: 'dateTime',
            render: (text: any, record, index, action) => formatDateDefault(record.start_date),
          },
          {
            title: 'Ngày bắt đầu',
            dataIndex: 'end_date',
            valueType: 'dateTime',
            render: (text: any, record, index, action) => formatDateDefault(record.end_date),
          },
          {
            title: 'Ngày tạo',
            dataIndex: 'creation',
            valueType: 'fromNow',
          },
          {
            title: 'Ngày chỉnh sửa',
            dataIndex: 'modified',
            valueType: 'fromNow',
          },
        ]}
        // dataSource={data as any}
        request={async (params) => {
          const filters = searchPlan
            ? `[["${DOCTYPE_ERP.iotFarmingPlan}", "label", "like", "%${searchPlan}%"]]`
            : `[]`;

          const order_by = 'modified DESC';
          const requestData = await getFarmingPlanList({
            page: params.current,
            size: params.pageSize,
            filters,
            order_by,
          });

          return { data: requestData.data, total: requestData.pagination.totalElements };
        }}
      />
    </Access>
  );
};

export default CropPlanList;
