import { listAllItem } from '@/services/products';
import { generalCreate } from '@/services/sscript';
import { listAllUnit } from '@/services/unit';
import { PlusOutlined } from '@ant-design/icons';
import { ProFormSelect } from '@ant-design/pro-components';
import { Button, Col, Form, Input, InputNumber, message, Modal, Row } from 'antd';
import { useState } from 'react';

export const CreateProductionUpdateView = (params: { refreshFnc: any; task_id: string }) => {
  const [loading, setLoading] = useState(false);
  const [isOpen, setOpen] = useState(false);
  const [form] = Form.useForm();
  const [uinits, setAllUnits] = useState([]);

  const showModal = () => {
    setOpen(true);
  };
  const hideModal = () => {
    setOpen(false);
  };
  const handleOk = () => {
    form.submit();
  };
  const handleCancel = () => {
    hideModal();
    form.resetFields();
  };

  return (
    <>
      <Button type="primary" onClick={showModal} style={{ display: 'flex', alignItems: 'center' }}>
        <PlusOutlined></PlusOutlined>Thêm sản lượng
      </Button>
      <Modal
        title={`Thêm sản lượng`}
        open={isOpen}
        onOk={handleOk}
        onCancel={handleCancel}
        confirmLoading={loading}
      >
        <Form
          layout="horizontal"
          labelCol={{ span: 24 }}
          labelAlign="left"
          form={form}
          onFinish={async (value: any) => {
            try {
              value.task_id = params.task_id;
              value._uinit = uinits.find((d: any) => d.name === value.unit);
              const result = await generalCreate('iot_production_quantity', {
                data: value,
              });
              form.resetFields();
              hideModal();
              message.success('Success!');
              if (params.refreshFnc) {
                await params.refreshFnc();
              }
            } catch (error: any) {
              message.error(error.toString());
            } finally {
              setLoading(false);
            }
          }}
        >
          <Row gutter={5}>
            <Col className="gutter-row" md={24}>
              <ProFormSelect
                rules={[
                  {
                    required: true,
                    message: 'Bắt buộc điền',
                  },
                ]}
                allowClear
                label="Sản phẩm thu hoạch"
                name="product_id"
                request={async () => {
                  const res = await listAllItem();
                  const unitsRes = await listAllUnit();
                  setAllUnits(unitsRes.data);
                  return res.data.map((item: any) => {
                    const unit = unitsRes.data?.find((d) => d.name === item.unit_id);
                    return {
                      label: item.label + `${unit ? ' - ' + unit?.label : ''}`,
                      value: item.name,
                    };
                  });
                }}
                showSearch
              />
            </Col>
            <Col className="gutter-row" md={24}>
              <Item label="Sản lượng/số lượng dự kiến" labelCol={{ span: 24 }} name="exp_quantity">
                <InputNumber style={{ width: '100%' }} min={0} />
              </Item>
            </Col>
            <Col className="gutter-row" md={24}>
              <Item label="Sản lượng/số lượng thực tế" labelCol={{ span: 24 }} name="quantity">
                <InputNumber style={{ width: '100%' }} min={0} />
              </Item>
            </Col>
            <Col className="gutter-row" md={24}>
              <Item label="Sản lượng/số lượng hao hụt" labelCol={{ span: 24 }} name="lost_quantity">
                <InputNumber style={{ width: '100%' }} min={0} />
              </Item>
            </Col>
            <Col className="gutter-row" md={24}>
              <Item
                label={<FormattedMessage id={'common.form.description'} />}
                labelCol={{ span: 24 }}
                name="description"
              >
                <Input.TextArea />
              </Item>
            </Col>
          </Row>
        </Form>
      </Modal>
    </>
  );
};
