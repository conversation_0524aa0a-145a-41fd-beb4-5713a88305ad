import { DOCTYPE_ERP } from '@/common/contanst/constanst';
import { uploadFile } from '@/services/fileUpload';
import { getListFileUrlFromStringV2 } from '@/services/utils';
import { PlusOutlined } from '@ant-design/icons';
import { ProForm, useDeepCompareEffect } from '@ant-design/pro-components';
import { Form, message, Modal, Upload, UploadFile, UploadProps } from 'antd';
import { RcFile } from 'antd/es/upload';
import { UploadFileStatus } from 'antd/es/upload/interface';
import { CSSProperties, FC, ReactNode, useState } from 'react';

interface Props {
  formItemName: string | string[];
  fileLimit: number;
  label?: string | React.ReactElement;
  initialImages?: string | undefined;
  docType?: string;
  isRequired?: boolean;
  hideUploadButton?: boolean;
  hideDeleteIcon?: boolean;
  hidePreviewIcon?: boolean;
  // New props for upload button customization
  uploadButtonLayout?: 'vertical' | 'horizontal';
  uploadButtonIcon?: ReactNode;
  uploadButtonText?: string;
  uploadButtonStyle?: CSSProperties;
  readOnly?: boolean;
}

const getBase64 = (file: RcFile): Promise<string> =>
  new Promise((resolve, reject) => {
    const reader = new FileReader();
    reader.readAsDataURL(file);
    reader.onload = () => resolve(reader.result as string);
    reader.onerror = (error) => reject(error);
  });

const FormUploadsPreviewable: FC<Props> = ({
  formItemName,
  fileLimit,
  label,
  initialImages,
  docType,
  isRequired,
  hideUploadButton,
  hideDeleteIcon,
  hidePreviewIcon,
  // Add new props with defaults
  uploadButtonLayout = 'vertical',
  uploadButtonIcon = <PlusOutlined />,
  uploadButtonText = 'Tải lên',
  uploadButtonStyle,
  readOnly,
}) => {
  const [previewOpen, setPreviewOpen] = useState(false);
  const [previewImage, setPreviewImage] = useState('');
  const [previewTitle, setPreviewTitle] = useState('');

  const [imageList, setImageList] = useState<string | undefined>(initialImages);
  const [fileList, setFileList] = useState<UploadFile[]>([]);
  const form = Form.useFormInstance();

  useDeepCompareEffect(() => {
    const listImg = getListFileUrlFromStringV2({ arrUrlString: initialImages }).map(
      (url, index) => ({
        name: `Ảnh ${(index + 1).toString()}`,
        url: url || '',
        uid: (-index).toString(),
        status: (url ? 'done' : 'error') as UploadFileStatus,
      }),
    );
    setFileList(listImg);
  }, [initialImages]);

  const handlePreview = async (file: UploadFile) => {
    if (!file.url && !file.preview) {
      file.preview = await getBase64(file.originFileObj as RcFile);
    }

    setPreviewImage(file.url || (file.preview as string));
    setPreviewOpen(true);
    setPreviewTitle(file.name || file.url!.substring(file.url!.lastIndexOf('/') + 1));
  };

  const handleChange: UploadProps['onChange'] = async ({ fileList: newFileList }) => {
    const oldFileList = [...fileList];
    if (readOnly) return;
    setFileList(newFileList);
    const uploadListRes = await Promise.allSettled(
      newFileList.map(async (item) => {
        if (item.url) {
          return {
            data: {
              message: {
                file_url: item.url.split('file_url=').at(-1),
              },
            },
          };
        }
        return await uploadFile({
          docType: docType || DOCTYPE_ERP.iotPlant,
          docName: item.name + Math.random().toString(4) + item.lastModified?.toString(4),
          file: item.originFileObj as any,
        });
      }),
    );
    const checkUploadFailed = uploadListRes.find((item) => item.status === 'rejected');
    if (checkUploadFailed) {
      message.error({
        content: `upload ảnh không thành công`,
      });
      setFileList(oldFileList);
    }

    const arrFileUrl = uploadListRes
      .reduce<string[]>(
        (prev, item) =>
          item.status === 'fulfilled' && item?.value?.data?.message?.file_url
            ? [...prev, item?.value?.data?.message?.file_url]
            : prev,
        [],
      )
      .filter((item) => typeof item === 'string');
    if (arrFileUrl) {
      setImageList(arrFileUrl.join(','));
      form.setFieldValue(formItemName, arrFileUrl.join(','));
    }
  };

  const uploadButton = (
    <div
      style={{
        display: 'flex',
        flexDirection: uploadButtonLayout === 'vertical' ? 'column' : 'row',
        alignItems: 'center',
        gap: uploadButtonLayout === 'vertical' ? '8px' : '4px',
        ...uploadButtonStyle,
      }}
    >
      {uploadButtonIcon}
      <div>{uploadButtonText}</div>
    </div>
  );

  const handleCancel = () => setPreviewOpen(false);
  const normFile = (e: any) => {
    if (Array.isArray(e)) {
      return e;
    }
    return e && e.fileList;
  };

  return (
    <>
      <ProForm.Item
        name={formItemName}
        initialValue={imageList}
        label={label}
        rules={[
          ...(isRequired
            ? [
                {
                  required: isRequired,
                },
              ]
            : []),
        ]}
      >
        <Upload
          listType="picture-card"
          fileList={fileList}
          onPreview={handlePreview}
          maxCount={fileLimit}
          onChange={handleChange}
          multiple
          showUploadList={{
            showRemoveIcon: !hideDeleteIcon,
            showPreviewIcon: !hidePreviewIcon,
          }}
        >
          {!hideUploadButton && (fileList.length >= fileLimit ? null : uploadButton)}
        </Upload>
      </ProForm.Item>
      <Modal open={previewOpen} title={previewTitle} footer={null} onCancel={handleCancel}>
        <img alt="example" style={{ width: '100%' }} src={previewImage} />
      </Modal>
    </>
  );
};

export default FormUploadsPreviewable;
