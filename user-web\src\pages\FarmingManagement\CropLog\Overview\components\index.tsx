import { CameraFilled } from '@ant-design/icons';
import { ProForm, ProFormUploadButton } from '@ant-design/pro-components';
import { Card, Space } from 'antd';
import { FC, ReactNode } from 'react';
import DetailedInfo from './DetailedInfo';
import Location from './Location';
import TaskChild from './TaskChild';
import { useModel } from '@umijs/max';
import ImageCrop from './Image';

interface DetailInfoProps {
  children?: ReactNode;
}
type IFormData = {};
const DetailInfoComponent: FC<DetailInfoProps> = () => {

  const onFinish = async (values: IFormData) => { };
  return (
    <ProForm<IFormData> onFinish={onFinish} submitter={false}>
      <Space
        size={'large'}
        direction="vertical"
        style={{
          width: '100%',
        }}
      >
        <DetailedInfo />
        <Card title="Hình ảnh / Video mô tả ">
          <ImageCrop />
        </Card>
        <Location />
      </Space>
    </ProForm>
  );
};

export default DetailInfoComponent;
