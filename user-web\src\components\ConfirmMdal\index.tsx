// components/shared/ConfirmModal.tsx
import { ExclamationCircleOutlined, WarningOutlined } from '@ant-design/icons';
import { FormattedMessage } from '@umijs/max';
import { Modal, ModalProps, Typography } from 'antd';
import { FC } from 'react';

const { Text } = Typography;

interface ConfirmModalProps extends Omit<ModalProps, 'onOk'> {
  // Tiêu đề của modal
  title?: string;
  // Nội dung message chính
  message: string;
  // Nội dung message phụ/mô tả thêm (optional)
  description?: string;
  // Callback khi xác nhận
  onConfirm: () => void | Promise<void>;
  // Callback khi hủy
  onCancel?: () => void;
  // Loại confirm: danger sẽ có icon warning và màu đỏ
  type?: 'danger' | 'default';
  // Text cho nút xác nhận
  okText?: string;
  // Text cho nút hủy
  cancelText?: string;
  // <PERSON><PERSON> hiển thị loading khi đang xử lý hay không
  confirmLoading?: boolean;
  // C<PERSON> cho phép đóng modal bằng cách click ra ngoài hoặc nhấn ESC không
  maskClosable?: boolean;
  // Có hiển thị icon danger không
  showIcon?: boolean;
  // Custom thêm content ở footer
  extraContent?: React.ReactNode;
}

const ConfirmModal: FC<ConfirmModalProps> = ({
  title = 'Xác nhận',
  message,
  description,
  onConfirm,
  onCancel,
  type = 'default',
  okText = 'Xác nhận',
  cancelText = 'Hủy',
  confirmLoading = false,
  maskClosable = false,
  showIcon = true,
  extraContent,
  ...modalProps
}) => {
  const isDanger = type === 'danger';

  const handleOk = async () => {
    await onConfirm();
  };

  const handleCancel = () => {
    if (onCancel) {
      onCancel();
    }
  };

  const icon = isDanger ? (
    <WarningOutlined style={{ color: '#ff4d4f', fontSize: 22 }} />
  ) : (
    <ExclamationCircleOutlined style={{ color: '#faad14', fontSize: 22 }} />
  );

  return (
    <Modal
      title={
        <div style={{ display: 'flex', alignItems: 'center', gap: '8px' }}>
          {showIcon && icon}
          <span>{title}</span>
        </div>
      }
      open={modalProps.open}
      onOk={handleOk}
      onCancel={handleCancel}
      okText={<FormattedMessage id={okText} defaultMessage={okText} />}
      cancelText={<FormattedMessage id={cancelText} defaultMessage={cancelText} />}
      confirmLoading={confirmLoading}
      maskClosable={maskClosable}
      centered
      okButtonProps={{
        danger: isDanger,
      }}
      {...modalProps}
    >
      <div style={{ marginLeft: showIcon ? '30px' : '0' }}>
        <Text strong>{message}</Text>
        {description && (
          <div style={{ marginTop: '8px' }}>
            <Text type="secondary">{description}</Text>
          </div>
        )}
        {extraContent && <div style={{ marginTop: '16px' }}>{extraContent}</div>}
      </div>
    </Modal>
  );
};

export default ConfirmModal;
