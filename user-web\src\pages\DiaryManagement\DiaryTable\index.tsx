import { ICropManagerInfo } from '@/services/cropManager';
import { getCropDiary } from '@/services/diary';
import { formatDateDefault } from '@/utils/date';
import { ActionType, ParamsType, ProTable } from '@ant-design/pro-components';
import { history, useIntl } from '@umijs/max';
import { message } from 'antd';
import { SortOrder } from 'antd/es/table/interface';
import { useRef, useState } from 'react';

const DiaryTable = () => {
  const intl = useIntl();
  const tableRef = useRef<ActionType>();

  const reloadTable = async () => {
    tableRef.current?.reload();
  };
  const [firstLoad, setFirstLoad] = useState(true);
  const [cropList, setCropList] = useState<ICropManagerInfo[]>([]);
  const columns: any = [
    {
      title: 'STT',
      dataIndex: 'stt',
      valueType: 'index',
      width: 48,
    },
    {
      title: intl.formatMessage({ id: 'common.crop_name' }),
      dataIndex: 'label',
      sorter: true,
      // hideInSearch: true,
      width: 150,
      render: (text: string, entity: any) => (
        <a
          onClick={(e) => {
            e.preventDefault();
            history.push(`/farming-diary/detail/${entity.name}`);
          }}
        >
          {text}
        </a>
      ),
    },
    {
      title: intl.formatMessage({ id: 'common.crop_start_date' }),
      // dataIndex: 'start_date',
      dataIndex: 'start_date_range',
      sorter: true,
      valueType: 'dateRange',
      // hideInSearch: true,
      render: (text: string, entity: any) => {
        return formatDateDefault(entity.start_date);
      },
      width: 150,
    },
    {
      title: intl.formatMessage({ id: 'common.crop_end_date' }),
      dataIndex: 'end_date_range',
      sorter: true,
      valueType: 'dateRange',
      // hideInSearch: true,
      render: (text: string, entity: any) => {
        return formatDateDefault(entity.end_date);
      },
      width: 150,
    },
    {
      title: intl.formatMessage({ id: 'common.plant_name' }),
      dataIndex: 'plant_name',
      hideInSearch: true,
      // render: (text: string, entity: any) => {
      //   return formatDateDefault(entity.end_date);
      // },
      width: 150,
    },
  ];

  return (
    <ProTable<any>
      columns={columns}
      //   dataSource={props.dataSource}
      toolBarRender={() => []}
      pagination={{
        pageSizeOptions: [10, 20, 50, 100],
        showSizeChanger: true,
        defaultPageSize: 20,
      }}
      actionRef={tableRef}
      request={async (
        params: ParamsType & { pageSize?: number; current?: number; keyword?: string },
        sort: Record<string, SortOrder>,
        filter: Record<string, (string | number)[] | null>,
      ) => {
        try {
          console.log('sort', sort);
          //format params
          params.page = params.current;
          params.size = params.pageSize;
          params.filters = [];

          if (params.label) {
            params.filters.push(['iot_crop', 'label', 'like', params.label]);
          }
          if (params.start_date_range) {
            params.filters.push([
              'iot_crop',
              'start_date',
              '>=',
              `'${params.start_date_range[0]}'`,
            ]);
            params.filters.push([
              'iot_crop',
              'start_date',
              '<=',
              `'${params.start_date_range[1]}'`,
            ]);
          }
          if (params.end_date_range) {
            params.filters.push(['iot_crop', 'end_date', '>=', `'${params.end_date_range[0]}'`]);
            params.filters.push(['iot_crop', 'end_date', '<=', `'${params.end_date_range[1]}'`]);
          }
          //add sort to params
          if (Object.keys(sort).length > 0) {
            params.order_by = `${Object.keys(sort)} ${Object.values(sort)}`;
          }
          console.log('params s', params);
          params.filters = JSON.stringify(params.filters);

          const res: any = await getCropDiary(params);
          return {
            data: res.data.data,
            total: res.data.pagination.totalElements,
            success: true,
          };
        } catch (error: any) {
          message.error(`Error when getting Crop Statistic Products: ${error.message}`);
        }
      }}
      rowKey="name"
      search={{ labelWidth: 100 }}
    />
  );
};

export default DiaryTable;
