import { getCropNote, getCropPest } from '@/services/crop';
import { getTaskManageTracingList } from '@/services/farming-plan';
import { ProColumns, ProTable } from '@ant-design/pro-components';
import { FormattedMessage, Link, useModel } from '@umijs/max';
import { Col, Image, Row, Tooltip } from 'antd';
import React, { FC, Fragment, ReactNode, useEffect, useState } from 'react';

interface OverviewAllDiaryProps {
  children?: ReactNode;
  genLinkDetail?: (itemId: string) => string;
  keepSearchParams?: boolean;
  cropID?: string;
}

type IDataTable = {
  id: string;
  name: string;
  executor: string;
  project: string;
  area: string;
  content: string;
  image: string;
  startDate: string;
  endDate: string;
};

const OverviewAllDiaryTable: FC<OverviewAllDiaryProps> = ({
  genLinkDetail,
  keepSearchParams,
  cropID,
}) => {
  const { selectedCrop, setSelectedCrop } = useModel('MyCrop');
  console.log('cropIDnekkk', selectedCrop.name);

  const [dataReal, setDataReal] = useState<IDataTable[]>([]);
  const [expandedContentIds, setExpandedContentIds] = useState<string[]>([]);
  const [imageLinks, setImageLinks] = useState<string[]>([]);

  const fetchDataCropNote: any = async () => {
    const res: any = await getCropNote({
      page: 1,
      size: 1000,
      fields: ['*'],
      filters: JSON.stringify([['iot_Crop_note', 'crop', 'like', selectedCrop.name]]),
      or_filters: JSON.stringify([]),
      order_by: '',
      group_by: '',
    });
    let data: any = res.data.data;
    return data;
  };

  const fetchDataCropPest: any = async () => {
    const res: any = await getCropPest({
      page: 1,
      size: 1000,
      fields: ['*'],
      filters: JSON.stringify([['iot_pest', 'iot_crop', 'like', selectedCrop.name]]),
      or_filters: JSON.stringify([]),
      order_by: '',
      group_by: '',
    });
    let data: any = res.data.data;
    return data;
  };

  const fetchDataTaskManagementInfo: any = async () => {
    const res: any = await getTaskManageTracingList({
      page: 1,
      size: 1000,
      fields: ['*'],
      filters: [['iot_crop', 'name', 'like', selectedCrop.name]],
      or_filters: [],
      order_by: '',
      group_by: '',
    });
    let data: any = res.data.data;
    return data;
  };

  const formatDateTime = (dateTimeString: string) => {
    const dateTime = new Date(dateTimeString);
    if (isNaN(dateTime.getTime())) {
      return '';
    }

    const time = dateTime.toLocaleTimeString('en-GB', { hour12: false });
    const date = dateTime.toLocaleDateString('en-GB', {
      day: '2-digit',
      month: '2-digit',
      year: 'numeric',
    });

    return `${time}, ${date}`;
  };

  const renderImageCropLayout = (record: any) => {
    console.log('run this render');
    try {
      const imageCropComponents: JSX.Element[] = [];
      let rowImages: string[] = [];
      let imageLinksArr: any = record.image.split(',');
      imageLinksArr.forEach((imageLink: string, index: number) => {
        rowImages.push(imageLink);
        if ((index + 1) % 4 === 0 || index === imageLinksArr.length - 1) {
          // When we have 4 images in the row or we have reached the last image
          const imageCropRow = (
            <Row className="gutter-row" gutter={4} key={`row_${index}`}>
              {rowImages.map((image, idx) => {
                console.log('Image link', image);
                return (
                  <Col className="gutter-row" key={`col_${index}_${idx}`}>
                    <Image
                      src={'https://iot.viis.tech/api/v2/file/download?file_url=' + image}
                      width={30}
                      height={30}
                      preview={{
                        mask: 'View',
                      }}
                    />
                  </Col>
                );
              })}
            </Row>
          );
          imageCropComponents.push(imageCropRow);
          rowImages = [];
        }
      });

      return imageCropComponents;
    } catch (error) {
      console.log('render error', error);
      return <></>;
    }
  };

  useEffect(() => {
    const fetchData = async () => {
      const cropNote = await fetchDataCropNote();
      const cropPest = await fetchDataCropPest();
      const taskInfo = await fetchDataTaskManagementInfo();
      // Duyệt qua mảng cropNote và thêm trường "element_type"
      const cropNoteWithElementType = cropNote
        .filter((item: any) => item.enable_origin_tracing)
        .map((item: any) => ({ ...item, element_type: 'crop_note', description: item.note }));

      const cropPestWithElementType = cropPest
        .filter((item: any) => item.enable_origin_tracing)
        .map((item: any) => ({ ...item, element_type: 'crop_pest' }));

      const taskInfoWithElementType = taskInfo
        .filter((item: any) => item.enable_origin_tracing)
        .map((item: any) => ({ ...item, element_type: 'task_info' }));

      // Kết hợp các mảng đã được cập nhật thành mảng combineData
      let combineData = [
        ...cropNoteWithElementType,
        ...cropPestWithElementType,
        ...taskInfoWithElementType,
      ];
      console.log('cropNote', cropNote);
      console.log('cropPest', cropPest);
      console.log('taskInfo', taskInfo);

      console.log('cropNoteWithElementType', cropNoteWithElementType);
      console.log('cropPestWithElementType', cropPestWithElementType);
      console.log('taskInfoWithElementType', taskInfoWithElementType);

      console.log('combineData', combineData);
      setDataReal(combineData);
    };
    fetchData();
  }, []);

  const handleExpandContent = (id: string) => {
    if (expandedContentIds.includes(id)) {
      setExpandedContentIds((prevIds: any) => prevIds.filter((prevId: any) => prevId !== id));
    } else {
      setExpandedContentIds((prevIds: any) => [...prevIds, id]);
    }
  };

  const columns: ProColumns<any>[] = [
    {
      title: 'STT',
      dataIndex: 'index',
      renderText(_text, record, index, _action) {
        return index + 1;
      },
      search: false,
    },
    {
      title: 'ID',
      dataIndex: 'name',
      render: (dom: any, entity: any) => {
        // console.log("entity", entity)
        // return (
        //   <Link to={`/farming-management/crop-log/log-detail?crop_note_id=${dom}`}>
        //     <span>{dom}</span>
        //   </Link>
        // );
        let linkTo = '';
        if (entity && entity.element_type === 'crop_note') {
          linkTo = `/farming-management/crop-log/crop-note-detail?crop_note_id=${dom}`;
        } else if (entity && entity.element_type === 'crop_pest') {
          linkTo = `/farming-management/crop-log/crop-pest-detail?crop_pest_id=${dom}`;
        } else if (entity && entity.element_type === 'task_info') {
          linkTo = `/farming-management/crop-log/task-info-detail?task_info_id=${dom}`;
        }

        return (
          <Link to={linkTo}>
            <span>{dom}</span>
          </Link>
        );
      },
      search: false,
    },
    {
      title: 'Tên nhật ký',
      dataIndex: 'label',
      search: false,
    },
    {
      title: 'Người thực hiện',
      dataIndex: 'assigned_to',
      render: (text: any, record: any, index, action) => {
        try {
          return (
            <>
              {record.assigned_to_info[0].first_name} {record.assigned_to_info[0].last_name}
            </>
          );
        } catch (error) {
          return <></>;
        }
      },
      search: false,
    },
    {
      title: 'Người liên quan',
      dataIndex: 'assigned_to_info',
      render: (text: any, record: any, index, action) => {
        try {
          let involveInArr = record.involve_in_users;
          const userNames = involveInArr.map((data: any) => `${data.first_name} ${data.last_name}`);
          return userNames.join(', ');
        } catch (error) {
          return null;
        }
      },
      search: false,
    },
    {
      title: 'Thời gian bắt đầu',
      dataIndex: 'start_date',
      render: (text: any, record, index, action) => formatDateTime(text),
      search: false,
    },
    {
      title: 'Thời gian kết thúc',
      dataIndex: 'end_date',
      render: (text: any, record, index, action) => formatDateTime(text),
      search: false,
    },
    {
      title: 'Trạng thái',
      dataIndex: 'status',
      search: false,
    },
    {
      title: 'Tên giai đoạn',
      dataIndex: 'pl_state_name',
      search: false,
    },
    {
      title: 'Tên kế hoạch',
      dataIndex: 'pl_name',
      search: false,
    },
    {
      title: 'Tên mùa vụ',
      dataIndex: 'crop_name',
      search: false,
    },
    {
      title: 'Nội dung ghi chú',
      dataIndex: 'description',
      render: (text: any, record, index, action) => {
        try {
          const isExpanded = expandedContentIds.includes(record.id);
          const truncatedContent = text.length > 50 ? text.substring(0, 50) + '...' : text;

          return (
            <div style={{ maxHeight: isExpanded ? 'none' : '60px', overflow: 'hidden' }}>
              <Tooltip title={text}>{isExpanded ? text : truncatedContent}</Tooltip>
              {text.length > 50 && (
                <span
                  style={{ color: '#1890ff', cursor: 'pointer', marginLeft: '8px' }}
                  onClick={() => handleExpandContent(record.id)}
                >
                  {isExpanded ? 'Rút gọn' : 'Xem thêm'}
                </span>
              )}
            </div>
          );
        } catch (error) {
          return <></>;
        }
      },
      search: false,
    },
    {
      title: <FormattedMessage id="common.form.image" defaultMessage="Image" />,
      dataIndex: 'image',
      render: (text: any, record, index, action) => {
        return (
          <>{renderImageCropLayout(record)}</>

          // <Tooltip title={text} overlayStyle={{ display: 'flex', alignItems: 'center' }}>
          // </Tooltip>
        );
      },
      search: false,
    },
    // {
    //   title: 'Ngày tạo ghi chú',
    //   dataIndex: 'creation',
    //   render: (text: any, record, index, action) => formatDateTime(text),
    // },
    // {
    //   title: 'Lần cuối chỉnh sửa',
    //   dataIndex: 'modified',
    //   render: (text: any, record, index, action) => formatDateTime(text),
    // },
  ];

  const [selectedRows, setSelectedRows] = useState<string[]>([]);
  const handleSelectRows = (selectedRowKeys: React.Key[], selectedRows: IDataTable[]) => {
    // Update the selected rows state when the checkboxes are clicked
    setSelectedRows(selectedRowKeys as string[]);
  };
  // const rowSelection = {
  //   selectedRowKeys: selectedRows,
  //   onChange: handleSelectRows,
  //   selections: [Table.SELECTION_ALL, Table.SELECTION_INVERT, Table.SELECTION_NONE],
  // };
  const rowSelection = {
    onChange: (selectedRowKeys: React.Key[], selectedRows: IDataTable[]) => {
      console.log(`selectedRowKeys: ${selectedRowKeys}`, 'selectedRows: ', selectedRows);
    },
    getCheckboxProps: (record: IDataTable) => ({
      disabled: record.name === 'Disabled User', // Column configuration not to be checked
      name: record.name,
    }),
  };
  return (
    <Fragment>
      <ProTable
        // rowSelection={{ type: 'checkbox', ...rowSelection }}
        dataSource={dataReal}
        // request={async (params, sort, filter) => {
        //   const res: any = '';
        //   console.log('res', res);
        //   return {
        //     data: res.data,
        //     success: true,
        //     total: res.pagination.totalElements,
        //   };
        // }}
        headerTitle="Danh sách dữ liệu"
        columns={columns}
        rowKey="name"
        bordered
        search={false}

        // onRow={(record, rowIndex) => {
        //   return {
        //     onClick: (event) => { }, // click row
        //     // onDoubleClick: (event) => { }, // double click row
        //     // onContextMenu: (event) => { }, // right button click row
        //     // onMouseEnter: (event) => { }, // mouse enter row
        //     // onMouseLeave: (event) => { }, // mouse leave row
        //   };
        // }}
      />
    </Fragment>
  );
};

export default OverviewAllDiaryTable;
