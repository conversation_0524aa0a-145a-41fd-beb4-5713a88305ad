import CardCollapse from '../Components/CardCollapse';
import ImagePreviewGroupCommon from '@/components/ImagePreviewGroupCommon';
import { getFileUrl } from '@/services/utils';
import { Empty } from 'antd';
import { createStyles } from 'antd-use-styles';
import { FC, ReactNode } from 'react';

interface GeneralInfoProps {
  data?: {
    id: string;
    title?: string;
    icon?: ReactNode;
    content?: ReactNode;
    image?: string;
  }[];
}
const useStyles = createStyles(({ token }) => ({
  wrapper: {
    display: 'flex',
    flexDirection: 'column',
    gap: token.margin,
  },
}));
const GeneralInfo: FC<GeneralInfoProps> = ({ data }) => {
  const styles = useStyles();
  if ((data || []).length === 0) {
    return <Empty />;
  }
  return (
    <div className={styles.wrapper}>
      {data?.map((item) => (
        <CardCollapse key={item.id} title={item.title} extra={"undefined"}>
          <p>{item.content}</p>
          {item.image && (
            <ImagePreviewGroupCommon listImg={[{ src: getFileUrl({ src: item.image }) }]} />
          )}
        </CardCollapse>
      ))}
      {/* <CardCollapse title="Chậu cây">
        <p>Cây trồng trong luồng hoặc chậu</p>
        <p> Size: 21x25cm</p>
        <p>Hình ảnh mẫu</p>

      </CardCollapse>
      <CardCollapse title="Phòng trồng">
        <p>Cây trồng trong luồng hoặc chậu</p>
        <p> Size: 21x25cm</p>
        <p>Hình ảnh mẫu</p>
      </CardCollapse> */}
    </div>
  );
};

export default GeneralInfo;
