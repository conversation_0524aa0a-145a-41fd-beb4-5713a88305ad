import PageContainerTabsWithSearch from '@/components/PageContainerTabsWithSearch';
import { myLazy } from '@/utils/lazy';
import { TableSkeleton } from '@ant-design/pro-components';
import { useIntl, useParams } from '@umijs/max';
import { Spin } from 'antd';
import { FC, ReactNode } from 'react';
import useDetail from '../../hooks/useDetail';
import Certification from './Certification';
import Note from './Note';
const Dashboard = myLazy(() => import('./Dashboard'));
const Diary = myLazy(() => import('./Diary'));
const Pest = myLazy(() => import('./Note'));

interface DetailProcedureProps {
  children?: ReactNode;
}

const DetailProcedure: FC<DetailProcedureProps> = ({ children }) => {
  const { formatMessage } = useIntl();
  const { id } = useParams();
  const { loading, data } = useDetail({
    id: id!,
  });
  if (!id) return null;
  return (
    <Spin spinning={loading}>
      <PageContainerTabsWithSearch
        tabsItems={[
          {
            label: formatMessage({
              id: 'common.dashboard',
            }),
            component() {
              return <Dashboard data={data} />;
            },
            fallback: <TableSkeleton active />,
          },
          {
            label: formatMessage({
              id: 'common.diary',
            }),
            component() {
              return <Diary data={data} />;
            },
            fallback: <TableSkeleton active />,
          },
          {
            label: formatMessage({ id: 'common.note' }),
            fallback: <TableSkeleton active />,
            component() {
              return <Note data={data} />;
            },
          },
          {
            label: formatMessage({
              id: 'common.certification',
            }),
            fallback: <TableSkeleton active />,
            component() {
              return <Certification data={data} />;
            },
          },
        ]}
      />
    </Spin>
  );
};

export default DetailProcedure;
