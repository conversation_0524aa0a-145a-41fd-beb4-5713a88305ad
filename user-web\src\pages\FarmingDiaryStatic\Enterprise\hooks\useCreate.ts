import { createBusiness } from '@/services/diary-2/business';
import { useIntl, useRequest } from '@umijs/max';
import { App } from 'antd';

export default function useCreate(
  { onSuccess, onError } = {} as {
    onSuccess?: () => void;
    onError?: () => void;
  },
) {
  const { formatMessage } = useIntl();
  const { message } = App.useApp();
  return useRequest(createBusiness, {
    manual: true,
    onSuccess(data, params) {
      message.success(
        formatMessage({
          id: 'common.success',
        }),
      );
      onSuccess?.();
    },
    onError: (error) => {
      //message.error(error.message);
      onError?.();
    },
  });
}
