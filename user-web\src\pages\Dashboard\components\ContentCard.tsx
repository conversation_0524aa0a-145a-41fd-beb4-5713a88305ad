import { ISensor } from '@/types/ISensor';
import { useModel } from '@umijs/max';

import React, { useEffect, useMemo, useRef, useState } from 'react';

// import style from './style.module.scss';
import {
  ArcElement,
  BarElement,
  CategoryScale,
  Chart as ChartJS,
  Filler,
  Legend,
  LinearScale,
  LineElement,
  PointElement,
  TimeScale,
  Title,
  Tooltip,
} from 'chart.js';
import 'chartjs-adapter-moment';
import annotationPlugin from 'chartjs-plugin-annotation';
import zoomPlugin from 'chartjs-plugin-zoom';
import { Line } from 'react-chartjs-2';

import { controlDevice, getDataTimeSeries, getLatestDataFunction } from '@/services/devices';
import { IIotProductionFunction } from '@/types/IIotProductionFunction';
import { Button, Col, Form, InputNumber, message, Row, Spin } from 'antd';
import moment from 'moment';
const { Item } = Form;

ChartJS.register(
  Filler,
  TimeScale,
  CategoryScale,
  LinearScale,
  PointElement,
  LineElement,
  BarElement,
  ArcElement,
  zoomPlugin,
  Title,
  Tooltip,
  Legend,
  annotationPlugin,
);

type propsType = {
  title?: string;
  displayTable?: {
    display?: boolean;
    displayMin?: boolean;
    displayMax?: boolean;
    displayAvg?: boolean;
  };
  initData: Array<{ label: string; data: number[] }>;
  min?: string;
  max?: string;
  avg?: string;
  openDialog: boolean;
  sensor: ISensor;
};

const getAnnotation = (sensor: ISensor) => {
  let res: any = {};
  if (sensor.data_eligible_min && sensor.data_eligible_max) {
    res.eligibleRange = {
      type: 'box',
      label: {
        content: 'Vùng an toàn',
        display: true,
        position: 'start',
        color: 'rgba(0, 0, 255)',
      },
      yMin: sensor.data_eligible_min,
      yMax: sensor.data_eligible_max,
      backgroundColor: 'rgba(51, 204, 51, 0.25)',
    };
  }
  if (sensor.set_point !== null) {
    res.setpoint = {
      type: 'line',
      yMin: sensor.set_point,
      yMax: sensor.set_point,
      borderColor: 'rgb(0, 0, 0)',
      borderWidth: 2,
      label: {
        content: 'Set point',
        display: true,
        position: 'start',
      },
    };
  }
  if (sensor.alarm_enable) {
    switch (sensor.alarm_type) {
      case 'Lower':
        res.lowerLine = {
          type: 'line',
          yMin: sensor.alarm_lower_value,
          yMax: sensor.alarm_lower_value,
          borderColor: 'rgb(255, 99, 132)',
          borderWidth: 2,
          label: {
            color: 'rgb(255, 99, 132)',
            content: 'Alarm min',
            display: true,
            position: 'start',
          },
        };
        break;
      case 'Upper':
        res.upperLine = {
          type: 'line',
          yMin: sensor.alarm_upper_value,
          yMax: sensor.alarm_upper_value,
          borderColor: 'rgb(255, 99, 132)',
          borderWidth: 2,
          label: {
            color: 'rgb(255, 99, 132)',
            content: 'Alarm max',
            display: true,
            position: 'start',
          },
        };
        break;
      case 'Lower and Upper':
        res.lowerLine = {
          type: 'line',
          yMin: sensor.alarm_lower_value,
          yMax: sensor.alarm_lower_value,
          borderColor: 'rgb(255, 99, 132)',
          borderWidth: 2,
          label: {
            color: 'rgb(255, 99, 132)',
            content: 'Alarm min',
            display: true,
            position: 'start',
          },
        };
        res.upperLine = {
          type: 'line',
          yMin: sensor.alarm_upper_value,
          yMax: sensor.alarm_upper_value,
          borderColor: 'rgb(255, 99, 132)',
          borderWidth: 2,
          label: {
            color: 'rgb(255, 99, 132)',
            content: 'Alarm max',
            display: true,
            position: 'start',
          },
        };
        break;
      default:
        break;
    }
  }
  console.log(res);

  return res;
};

const ContentCard: React.FC<propsType> = ({
  displayTable,
  initData,
  openDialog,
  sensor,
}: {
  displayTable: any;
  initData: any;
  openDialog: any;
  sensor: IIotProductionFunction;
}) => {
  const chartRef = useRef<ChartJS<'line'>>(null);

  // const { aggregateData, setHiddenField, data, paramQuery, setParamQuery } = useModel('chartData');
  const [generalLineChartOptions, setGeneralLineChartOptions] = useState<any>({});
  const [generalLineChartData, setGeneralLineChartData] = useState<any>({});
  const [timeseries, setTimeseries] = useState<any>([]);
  const [form] = Form.useForm();

  const [loading, setLoading] = useState(true);
  const [fncValue, setFncValue] = useState(true);
  const [sending, setSending] = useState(false);

  const { latestDeviceValue, selectedTimeType, selectedDeviceThingsboard, timeRange } =
    useModel('MyResource');

  const getLatestData = async () => {
    try {
      const data = await getLatestDataFunction(sensor);
    } catch (error) {
      console.log(error);
    }
  };

  useEffect(() => {}, [sensor]);

  const getTimeSeriesData = async () => {
    try {
      const result = await getDataTimeSeries({
        agg: 'NONE',
        startTs: timeRange[0].unix() * 1000,
        endTs: timeRange[1].unix() * 1000,
        keys: sensor.identifier || '',
        device_id_thingsboard: sensor.device_id_thingsboard || '',
        limit: 1000000,
      });
      let data = result[sensor.identifier || ''] || [];
      if (data.length) {
        data = data.map((d: any) => {
          d.value = parseFloat(d.value.toString());
          return d;
        });
        data = data.sort((a: any, b: any) => a.ts - b.ts);
        setTimeseries(data);
      }
    } catch (error: any) {
      message.error(error.toString());
      console.log('error', error);
    } finally {
      setLoading(false);
    }
  };

  useMemo(() => {
    getTimeSeriesData();
  }, [timeRange]);

  useMemo(() => {
    if (latestDeviceValue?.length) {
      const match_key = latestDeviceValue.filter((d: any) => {
        return d.key === sensor.identifier;
      });
      if (match_key && match_key.length) {
        let value = match_key[match_key.length - 1];
        let new_timeseries = timeseries || [];
        new_timeseries = new_timeseries.sort((a: any, b: any) => a.ts - b.ts);
        switch (selectedTimeType) {
          case '5p-now':
            if (!new_timeseries.length) {
              new_timeseries.push(value);
            } else {
              if (value.ts > new_timeseries[new_timeseries.length - 1]?.ts) {
                new_timeseries.push(value);
              }
            }
            break;
          case '1h-now':
            if (!new_timeseries.length) {
              new_timeseries.push(value);
            } else {
              if (value.ts > new_timeseries[new_timeseries.length - 1]?.ts) {
                new_timeseries.push(value);
              }
            }
            break;
          case '2h-now':
            if (!new_timeseries.length) {
              new_timeseries.push(value);
            } else {
              if (value.ts > new_timeseries[new_timeseries.length - 1]?.ts) {
                new_timeseries.push(value);
              }
            }
            break;
          case '1d-now':
            if (!new_timeseries.length) {
              new_timeseries.push(value);
            } else {
              if (value.ts > new_timeseries[new_timeseries.length - 1]?.ts) {
                new_timeseries.push(value);
              }
            }
            break;
          case 'custom':
            break;
          default:
            break;
        }
        setTimeseries([...new_timeseries]);
        setFncValue(value.value);
        form.setFieldValue('data', parseInt(value.value?.toString() || ''));
      }
    }
  }, [latestDeviceValue]);

  // useMemo(() => {
  //   const old_timeseries = timeseries;
  //   const new_timeseries = sensorsDataSelected.filter((d) => d.sensor_id === sensor.name);
  //   if (old_timeseries.length !== new_timeseries.length) {
  //     new_timeseries.sort((a, b) => a.ts - b.ts);
  //     setTimeseries(new_timeseries);
  //   }
  // }, [sensorsDataSelected]);

  useEffect(() => {
    const chart = chartRef.current;
    if (chart !== null) {
      chart?.data.datasets.forEach((dataset) => {
        dataset.data = timeseries?.map((d: any) => {
          return {
            x: moment(d.ts),
            y: d.value,
          };
        });
      });
      chart?.update();
    } else {
      console.log('new data', timeseries);
      setGeneralLineChartOptions({
        stacked: false,
        responsive: true,
        maintainAspectRatio: false,
        plugins: {
          zoom: {
            pan: {
              enabled: true,
              mode: 'x',
            },
            // zoom: {
            //     wheel: {
            //         enabled: true,
            //     },
            //     pinch: {
            //         enabled: true,
            //     },
            //     mode: 'x',
            //     speed: 1,
            // },
            // speed: 1,
          },
          title: {
            display: false,
            text: '',
          },

          // annotation: {
          //   annotations: getAnnotation(sensor)
          // }
        },
        scales: {
          x: {
            type: 'time',
            time: {
              displayFormats: {
                day: 'HH:mm DD/MM',
              },
            },
          },
          y: {
            type: 'linear' as const,
            display: true,
            position: 'left' as const,
            // ticks: {
            //     // Include a dollar sign in the ticks
            //     callback: function (value, index, ticks) {
            //         return 'Total Coach Lesson: ' + value;
            //     }
            // }
          },
        },
      });
      console.log(
        `timeseries?.map((d: any) => {
              return {
                x: moment(d.ts),
                y: d.value
              }
            })`,
        timeseries?.map((d: any) => {
          return {
            x: moment(d.ts),
            y: d.value,
          };
        }),
      );
      setGeneralLineChartData({
        // labels: student_by_date?.map((t: any) => moment(t.creation).format('DD/MM/YYYY')),
        datasets: [
          {
            fill: false,
            label: `${sensor.label} ${sensor.unit || ''}`,
            data: timeseries?.map((d: any) => {
              return {
                x: moment(d.ts),
                y: d.value,
              };
            }),
            borderColor: 'rgb(0, 153, 255,0.5)',
            backgroundColor: 'rgba(0, 153, 255, 0.5)',
            yAxisID: 'y',
            radius: 2,
            animation: false,
          },
        ],
      });
    }
  }, [timeseries]);

  if (timeseries.length && !loading)
    return (
      <>
        <Row gutter={[5, 5]}>
          <Col md={24}>
            {sensor.data_permission === 'w' || sensor.data_permission === 'rw' ? (
              <>
                <Form
                  onFinish={async (values: any) => {
                    try {
                      setSending(true);
                      let { sp_value } = values;
                      sp_value = parseFloat(sp_value.toString());
                      let params = {};
                      params[sensor.identifier || ''] = sp_value;
                      await controlDevice({
                        device_id_thingsboard: selectedDeviceThingsboard,
                        method: 'set_state',
                        params: params,
                      });
                    } catch (error) {
                      message.error('Gửi thất bại, hãy chắc rằng thiết bị của bạn đang online!');
                    } finally {
                      setSending(false);
                    }
                  }}
                  layout="horizontal"
                  labelCol={{ span: 24 }}
                  labelAlign="left"
                  form={form}
                >
                  <Row gutter={[5, 5]}>
                    <Col className="gutter-row" md={8}>
                      <Item label="Giá trị đặt hiện tại" labelCol={{ span: 24 }} name={'data'}>
                        <InputNumber disabled style={{ width: '100%' }} />
                      </Item>
                    </Col>
                    <Col className="gutter-row" md={8}>
                      <Item
                        label="Giá trị đặt mới"
                        labelCol={{ span: 24 }}
                        name="sp_value"
                        rules={[
                          {
                            required: true,
                            message: 'Bắt buộc điền',
                          },
                        ]}
                      >
                        <InputNumber style={{ width: '100%' }} />
                      </Item>
                    </Col>
                    <Col className="gutter-row" md={8}>
                      <Item label="Thiết lập giá trị đặt" labelCol={{ span: 24 }}>
                        <Button loading={sending} htmlType="submit" type="primary">
                          Gửi tới thiết bị
                        </Button>
                      </Item>
                    </Col>
                  </Row>
                </Form>
              </>
            ) : (
              <></>
            )}
          </Col>
          <Col md={24}>
            {sensor.show_chart || sensor.data_permission === 'r' ? (
              <>
                <div
                  style={{
                    width: '100%',
                    margin: 'auto',
                    height: '300px',
                  }}
                  className="chart-container"
                >
                  {Object.keys(generalLineChartData).length &&
                  Object.keys(generalLineChartOptions).length ? (
                    <Line
                      ref={chartRef}
                      style={{ width: '100%' }}
                      height={'300px'}
                      options={generalLineChartOptions}
                      data={generalLineChartData}
                    />
                  ) : (
                    ''
                  )}
                </div>
              </>
            ) : (
              <></>
            )}
          </Col>
        </Row>
      </>
    );
  else if (loading) return <Spin />;
  else return <></>;
};
export default ContentCard;
