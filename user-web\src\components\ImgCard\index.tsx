import { DeleteOutlined, EditOutlined } from '@ant-design/icons';
import { Link } from '@umijs/max';
import { Card, message, Popconfirm } from 'antd';
import Meta from 'antd/es/card/Meta';
import { useState } from 'react';
interface Props {
  title: string;
  image: string;
  id: string;
}
// ! Currently tailored specificly for crop plant
// ! to use, refactor for general purpose or do not use for other mean
const ImgCard = (props: Props) => {
  const [confirmLoading, setConfirmLoading] = useState(false);
  const [open, setOpen] = useState(false);

  const showPopconfirm = () => {
    setOpen(true);
  };

  const handleOk = () => {
    setConfirmLoading(true);

    setTimeout(() => {
      setOpen(false);
      setConfirmLoading(false);
      message.success('Success');
    }, 2000);
  };

  const handleCancel = () => {
    console.log('Clicked cancel button');
    setOpen(false);
  };
  return (
    <Card
      hoverable
      cover={
        <div style={{ height: 280, display: 'flex', alignItems: 'center' }}>
          <img
            alt="Plant image here"
            src={`https://iot.viis.tech/api/v2/file/download?file_url=${props.image}`}
            style={{
              maxHeight: 400,
              maxWidth: '100%',
              alignItems: 'center',
              display: 'flex',
              borderRadius: '0',
            }}
          />
        </div>
      }
      // TODO move actions to props
      actions={[
        <Link to={`/farming-management/crop-library/${props.id}/detail`} key="edit">
          <EditOutlined key="edit" />
        </Link>,
        <Popconfirm
          title="Delete the task"
          description="Are you sure to delete this task?"
          open={open}
          onConfirm={handleOk}
          okButtonProps={{ loading: confirmLoading }}
          onCancel={handleCancel}
          key="delete"
        >
          <DeleteOutlined key="delete" onClick={showPopconfirm} />
        </Popconfirm>,
      ]}
    >
      <Meta title={props.title} />
    </Card>
  );
};

export default ImgCard;
