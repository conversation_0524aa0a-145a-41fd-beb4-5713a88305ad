import FormUploadsPreviewable from '@/components/FormUploadsPreviewable';
import { ProFormText } from '@ant-design/pro-components';
import { useIntl } from '@umijs/max';
import { Card, Col, Row } from 'antd';
import { FC, ReactNode } from 'react';

interface DetailedInfoProps {
  children?: ReactNode;
  initialImage?: string;
}
const w = 'md';
const DetailedInfo: FC<DetailedInfoProps> = ({ children, initialImage }) => {
  const { formatMessage } = useIntl();

  return (
    <Card
      title={formatMessage({
        id: 'task.detailed_info',
      })}
      bordered={false}
      style={{ boxShadow: 'none' }}
    >
      <FormUploadsPreviewable
        label={formatMessage({
          id: 'common.image',
        })}
        fileLimit={10}
        formItemName={'image'}
        initialImages={initialImage}
      />
      <Row gutter={24}>
        <Col span={12}>
          <ProFormText
            width={w}
            name={'label'}
            label={formatMessage({
              id: 'common.stage_name',
            })}
            rules={[
              {
                required: true,
              },
            ]}
          />
        </Col>
        <Col span={12}>
          {' '}
          <ProFormText
            width={w}
            name={'description'}
            label={formatMessage({
              id: 'common.note',
            })}
          />
        </Col>
        <Col span={12}>
          {' '}
          <ProFormText
            width={w}
            name={'expire_time_in_days'}
            label={formatMessage({
              id: 'common.expire_time_in_days',
            })}
            disabled
          />
        </Col>
      </Row>
    </Card>
  );
};

export default DetailedInfo;
