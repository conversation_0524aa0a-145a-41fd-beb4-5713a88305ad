import SimpleMultitypeChart from '@/components/SimpleMultitypeChart';
import { getCropStatisticProducts, getFilteredCropStatistics } from '@/services/cropStatistic';
import { Card, Col, List, Row, Space, Typography } from 'antd';
import moment from 'moment';
import numeral from 'numeral';
import React, { useEffect, useState } from 'react';
import { IFilter } from '..';
export interface IData {
  labels: string[];
  exp_quantities: number[];
  quantities: number[];
}
const { Text } = Typography;
const bodyStyle: React.CSSProperties = {
  fontSize: 24,
  color: '#44C4A1',
  fontWeight: 'bold',
  backgroundColor: 'none',
};
export interface Props {
  filter: IFilter;
}
const GeneralStatistics = ({ filter }: Props) => {
  const [generalInfos, setGeneralInfos] = useState<{ title: string; value: string }[]>();

  const [selectedData, setSelectedData] = useState<any>();
  const queryingData = async () => {
    const res = await getCropStatisticProducts({
      cropList: filter.cropList,
      end_date: filter.end_date,
      start_date: filter.start_date,
      type: filter.type,
    });
    const data = res?.data.reduce(
      (acc, ele) => {
        acc.labels.push(ele.interval_start);
        acc.exp_quantities.push(Number(ele.total_exp_quantity));
        acc.quantities.push(Number(ele.total_quantity));

        return acc;
      },
      { labels: [], exp_quantities: [], quantities: [] } as IData,
    );
    if (data) {
      const formattedData = {
        labels: data.labels,
        datasets: [
          {
            type: 'bar' as const,
            label: 'Giá trị dự kiến',
            data: data.exp_quantities,
            backgroundColor: '#FFD859',
          },
          {
            type: 'bar' as const,
            label: 'Giá trị thực tế',
            data: data.quantities,
            backgroundColor: '#44C4A1',
          },
        ],
      };
      setSelectedData(formattedData);
    }
  };
  useEffect(() => {
    const fetchData = async () => {
      // await queryingData();
      const res = await getFilteredCropStatistics({
        ...filter,
      });
      const contentList = [
        { title: 'Tổng số vụ mùa', value: filter.cropList.length.toString() },
        {
          title: 'Sản lượng dự kiến',
          value: numeral(res?.product.sum.totalExpectedQuantity).format('0,0.00') || '0',
        },
        {
          title: 'Sản lượng thực tế',
          value: numeral(res?.product.sum.totalRealQuantity).format('0,0.00') || '0',
        },
      ];
      const data = res?.product?.data.reduce(
        (acc, ele) => {
          acc.labels.push(moment(ele.interval_start).format('YYYY-MM-DD'));
          acc.exp_quantities.push(Number(ele.total_exp_quantity));
          acc.quantities.push(Number(ele.total_quantity));

          return acc;
        },
        { labels: [], exp_quantities: [], quantities: [] } as IData,
      );
      if (data) {
        const formattedData = {
          labels: data.labels,
          datasets: [
            {
              type: 'bar' as const,
              label: 'Giá trị dự kiến',
              data: data.exp_quantities,
              backgroundColor: '#FFD859',
            },
            {
              type: 'bar' as const,
              label: 'Giá trị thực tế',
              data: data.quantities,
              backgroundColor: '#44C4A1',
            },
          ],
        };
        setSelectedData(formattedData);
      }
      setGeneralInfos(contentList);
    };
    fetchData();
  }, [filter]);

  return (
    <Row gutter={12}>
      <Col span={12}>
        {selectedData ? (
          <SimpleMultitypeChart data={selectedData} chart_label={``} height="200px" />
        ) : (
          'Loading...'
        )}
      </Col>
      <Col
        span={12}
        style={{
          backgroundImage: 'linear-gradient(rgba(228,255,244,1), rgba(228,255,244,0))',
        }}
      >
        <List
          grid={{
            column: 3,
            gutter: 10,
            md: 3,
            sm: 2,
            xs: 1,
          }}
          dataSource={generalInfos}
          renderItem={(item) => (
            <List.Item>
              <Card
                bodyStyle={bodyStyle}
                style={{
                  background: 'none',
                  border: 'none',
                }}
              >
                <Space direction="vertical">
                  <Text style={{ whiteSpace: 'normal' }}>{item.title}</Text>
                  <Text style={bodyStyle}>{item.value}</Text>
                </Space>
              </Card>
            </List.Item>
          )}
        />
      </Col>
    </Row>
  );
};

export default GeneralStatistics;
