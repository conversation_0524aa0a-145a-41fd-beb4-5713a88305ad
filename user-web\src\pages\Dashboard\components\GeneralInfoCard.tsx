import { getGeneralCropStatistics } from '@/services/cropStatistic';
import { FormattedMessage, useIntl } from '@umijs/max';
import { Card, Col, List, Row, Typography } from 'antd';
import numeral from 'numeral';
import { useEffect, useState } from 'react';

const { Text } = Typography;
const bodyStyle: React.CSSProperties = {
  fontSize: 20,
  color: '#44C4A1',
  fontWeight: 'bold',
};
const GeneralInfoCard = () => {
  const [generalInfos, setGeneralInfos] =
    useState<{ title: string; value: string; imageUrl: string; color: string }[]>();
  const intl = useIntl();
  useEffect(() => {
    const fetchData = async () => {
      const res = await getGeneralCropStatistics();
      console.log("res: ", res);
      const contentList = [
        {
          imageUrl: 'url(/images/dashboard_bg_card1.png)',
          title: intl.formatMessage({ id: 'homeTab.ongoing' }),
          value: res?.inProgressCrop || '0',
          color: '#FD7E5E',
        },
        {
          imageUrl: 'url(/images/dashboard_bg_card2.png)',
          title: intl.formatMessage({ id: 'homeTab.completed' }),
          value: res?.finishedCrop || '0',
          color: '#44C4A1',
        },
        {
          imageUrl: 'url(/images/dashboard_bg_card3.png)',
          title: intl.formatMessage({ id: 'homeTab.cropsIsOverdue' }),
          value: res?.getOutdatedCrop || '0',
          color: '#2770BA',
        },
        {
          imageUrl: 'url(/images/dashboard_bg_card4.png)',
          title: intl.formatMessage({ id: 'homeTab.staff' }),
          value: res?.totalEmployee || '0',
          color: '#A46ECF',
        },
        // {
        //   imageUrl: 'url(/images/dashboard_bg_card5.png)',
        //   title: intl.formatMessage({ id: 'homeTab.expectedOutput' }),
        //   value: numeral(res?.totalExpectedQuantity).format('0,0.00') || '0',
        //   color: '#FF659C',
        // },
        // {
        //   imageUrl: 'url(/images/dashboard_bg_card6.png)',
        //   title: intl.formatMessage({ id: 'homeTab.completedOutput' }),
        //   value: numeral(res?.totalRealQuantity.toString()).format('0,0.00') || '0',
        //   color: '#94C137',
        // },
      ];
      setGeneralInfos(contentList);
    };
    fetchData();
  }, []);

  return (
    <Card
      headStyle={
        {
          // textAlign: 'center',
          // alignItems: 'center',
          // backgroundColor: '#44C4A1',
          // color: 'white',
        }
      }
      title={
        <Typography.Title level={4} style={{ margin: '0px' }}>
          <FormattedMessage id="homeTab.home" />
        </Typography.Title>
      }
    >
      <List
        grid={{
          // column: 6,
          lg: 3,
          xl: 4,
          xxl: 6,
          gutter: 10,
          md: 2,
          sm: 2,
          xs: 1,
        }}
        dataSource={generalInfos}
        renderItem={(item) => (
          <List.Item>
            <Card
              // bodyStyle={bodyStyle}
              bodyStyle={{
                backgroundColor: item.color,
                color: 'white',
              }}
              title={
                <div></div>
                // <Typography.Text style={{ whiteSpace: 'normal' }}>{item.title}</Typography.Text>
              }
              headStyle={{
                height: 100,
                backgroundImage: item.imageUrl,
                backgroundRepeat: 'no-repeat',
                backgroundSize: 'cover',
                backgroundColor: '#E4FFF4',
              }}
            >
              <Row style={{ height: 54 }} align="middle">
                <Col span={12}>
                  <Typography.Text></Typography.Text>
                  {item.title}
                </Col>
                <Col span={12}>
                  <Typography.Title
                    level={5}
                    style={{
                      color: 'white',
                      float: 'right',
                      display: 'inline-flex',
                      alignItems: 'center',
                    }}
                  >
                    {item.value}
                  </Typography.Title>
                </Col>
              </Row>
            </Card>
          </List.Item>
        )}
      />
    </Card>
  );
};

export default GeneralInfoCard;
