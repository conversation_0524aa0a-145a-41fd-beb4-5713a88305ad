import { DEFAULT_PAGE_SIZE_ALL, DOCTYPE_ERP } from '@/common/contanst/constanst';
import { getCropManagementInfoList } from '@/services/cropManager';
import { getFarmingPlanState } from '@/services/farming-plan';
import { uploadFile } from '@/services/fileUpload';
import {
  createPest,
  createPestMultipleRelevant,
  PestRelevantBody,
  updatePest,
} from '@/services/pandemic';
import { sscriptGeneralList } from '@/services/sscript';
import { CameraFilled, PlusOutlined } from '@ant-design/icons';
import {
  ModalForm,
  ProForm,
  ProFormSelect,
  ProFormText,
  ProFormTextArea,
  ProFormUploadButton,
} from '@ant-design/pro-components';
import { FormattedMessage, useAccess } from '@umijs/max';
import { App, Button } from 'antd';
import { UploadFile } from 'antd/es/upload';
import { FC, ReactElement, useEffect } from 'react';

interface UpdatePandemicModalProps {
  cropId?: string;
  onSuccess?: () => void;
  trigger?: ReactElement;
}
type IFormData = {
  involved_in_users?: string[];
  state_list?: string[];
  category_list?: string[];
  label: string;
  iot_crop: string;
  description?: string;
  img?: UploadFile[];
};
const UpdatePandemicModal: FC<UpdatePandemicModalProps> = ({ cropId, onSuccess, trigger }) => {
  const { message } = App.useApp();
  const userdata = JSON.parse(localStorage.getItem('token') || '{}');
  const customerId = userdata?.user.customer_id;
  console.log('userdata', userdata);
  const onFinish = async (values: IFormData) => {
    try {
      console.log('values', values);
      // create
      const dataUpdate = await createPest({
        label: values.label,
        iot_crop: values.iot_crop,
        description: values.description,
      });
      const pestId = dataUpdate.data.name;
      try {
        if (values.img && values.img.length > 5) {
          message.error('Chỉ được upload tối đa 5 ảnh');
          return;
        }
        // upload file
        if (values.img && (values.img || []).length > 0) {
          // upload bất kể thành công hay ko
          const uploadListRes = await Promise.allSettled(
            values.img.map(async (item) => {
              return await uploadFile({
                docType: DOCTYPE_ERP.iotPest,
                docName: pestId,
                file: item.originFileObj as any,
              });
            }),
          );
          // check if() 1 vài upload failed
          const checkUploadFailed = uploadListRes.find((item) => item.status === 'rejected');
          if (checkUploadFailed) {
            message.error({
              content: 'Some file upload failed',
            });
          }

          // update img path
          const arrFileUrl = uploadListRes
            .reduce<string[]>(
              (prev, item) =>
                item.status === 'fulfilled'
                  ? [...prev, item?.value?.data?.message?.file_url]
                  : prev,
              [],
            )
            .filter((item) => typeof item === 'string');

          if (arrFileUrl.length > 0) {
            await updatePest({
              name: pestId,
              iot_crop: values.iot_crop,
              image: arrFileUrl.join(','),
            });
          }
        }
      } catch (error) {}

      //create multiple relevant
      const dataRelevant: PestRelevantBody = {
        name: pestId,
        category_list: values.category_list as string[],
        state_list: values.state_list as string[],
        involved_in_users: values.involved_in_users as string[],
      };
      await createPestMultipleRelevant(dataRelevant);

      message.success({
        content: 'Updated successfully',
      });
      onSuccess?.();
      return true;
    } catch (error) {
      message.error({
        content: 'Error, please try again',
      });
      return false;
    }
  };
  const [form] = ProForm.useForm();
  useEffect(() => {
    // default select crop
    if (cropId) {
      form.setFieldsValue({
        iot_crop: cropId,
      });
    }
  }, [cropId]);
  const access = useAccess();
  const canUpdateCrop = access.canUpdateInSeasonalManagement();
  if (canUpdateCrop) {
    return (
      <ModalForm<IFormData>
        modalProps={{
          destroyOnClose: true,
        }}
        name="create-pandemic"
        title="Cập nhật thông tin dịch hại"
        trigger={
          trigger || (
            <Button type="primary" icon={<PlusOutlined />}>
              Cập nhật dịch hại
            </Button>
          )
        }
        width={500}
        form={form}
        onFinish={onFinish}
        initialValues={{
          iot_crop: cropId,
        }}
      >
        <ProFormText
          label="Tên dịch hại"
          name="label"
          rules={[
            {
              required: true,
            },
          ]}
        />
        <ProFormSelect
          label={'Chọn vụ mùa'}
          name="iot_crop"
          hidden={true}
          rules={[
            {
              required: true,
            },
          ]}
          request={async () => {
            const res = await getCropManagementInfoList({
              page: 1,
              size: DEFAULT_PAGE_SIZE_ALL,
            });
            return res.data.map((item) => ({
              label: item.label,
              value: item.name,
            }));
          }}
        />
        <ProFormSelect
          label={'Chọn vật tư'}
          name="category_list"
          mode="multiple"
          showSearch={true}
          rules={[
            {
              required: true,
            },
          ]}
          request={async () => {
            const res = await sscriptGeneralList({
              doc_name: 'iot_category',
              page: 1,
              size: DEFAULT_PAGE_SIZE_ALL,
              filters: [['iot_category', 'customer_id', 'like', customerId]],
            });
            return res.data.map((item: any) => ({
              label: item.label,
              value: item.name,
            }));
          }}
        />
        <ProFormSelect
          label={'Chọn giai đoạn'}
          name="state_list"
          mode="multiple"
          showSearch={true}
          rules={[
            {
              required: true,
            },
          ]}
          request={async () => {
            const res = await getFarmingPlanState({
              page: 1,
              size: DEFAULT_PAGE_SIZE_ALL,
              filters: [['iot_farming_plan_state', 'crop', 'like', cropId]],
            });
            return res.data.map((item: any) => ({
              label: item.label,
              value: item.name,
            }));
          }}
        />
        <ProFormSelect
          label={'Người liên quan'}
          name="involved_in_users"
          showSearch={true}
          mode="multiple"
          rules={[
            {
              required: true,
            },
          ]}
          request={async () => {
            const res = await sscriptGeneralList({
              doc_name: 'iot_customer_user',
              page: 1,
              size: DEFAULT_PAGE_SIZE_ALL,
              filters: [['iot_customer_user', 'customer_id', 'like', customerId]],
            });
            return res.data.map((item: any) => ({
              label: `${item.last_name} ${item.first_name}`,
              value: item.name,
            }));
          }}
        />
        {/* <ProFormDatePicker
        label="Chọn ngày"
        rules={[
          {
            required: true,
          },
        ]}
      /> */}
        <ProFormTextArea
          label={<FormattedMessage id={'common.form.description'} />}
          name={'description'}
        />
        <ProFormUploadButton
          label={<FormattedMessage id={'common.form.image'} />}
          listType="picture-card"
          name="img"
          icon={<CameraFilled />}
          title=""
          accept="image/*"
          // rules={[
          //   {
          //     required: true,
          //   },
          // ]}
        />
      </ModalForm>
    );
  } else return <></>;
};

export default UpdatePandemicModal;
