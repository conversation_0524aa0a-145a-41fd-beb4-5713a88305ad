import { DEFAULT_DATE_FORMAT_WITHOUT_TIME } from '@/common/contanst/constanst';
import { useProFormList } from '@/components/Form/Config/pro-form-list';
import FormUploadFiles from '@/components/UploadFIles';
import { getDocumentList } from '@/services/diary-2/document';
import { PlusOutlined } from '@ant-design/icons';
import {
  ProForm,
  ProFormDatePicker,
  ProFormGroup,
  ProFormList,
  ProFormSelect,
} from '@ant-design/pro-components';
import { history, useIntl, useParams, useRequest } from '@umijs/max';
import { But<PERSON>, Card } from 'antd';
import moment from 'moment';
import { FC, ReactNode, useCallback, useEffect, useState } from 'react';

interface CertificationFormProps {
  children?: ReactNode;
}

const CertificationContent = ({ form, index }: { form: any; index: number }) => {
  const { formatMessage } = useIntl();
  const { data: documents, loading: documentsLoading } = useRequest(getDocumentList);
  const [fileValues, setFileValues] = useState<string | undefined>();

  const handleDocumentChange = useCallback(
    (selectedDocumentName: string) => {
      const selectedDocument = documents?.find((doc) => doc.name === selectedDocumentName);
      if (!selectedDocument) return;

      const currentDocuments = form.getFieldValue('documents') || [];
      const updatedDocuments = currentDocuments.map((doc: any, docIndex: number) =>
        doc.name === selectedDocumentName
          ? {
              ...doc,
              idx: docIndex + 1, // Bổ sung idx nếu chưa có
              issue_date: selectedDocument.issue_date,
              expiry_date: selectedDocument.expiry_date,
              document_path: selectedDocument.document_path,
            }
          : doc,
      );

      setFileValues(selectedDocument.document_path);
      form.setFieldsValue({ documents: updatedDocuments });
    },
    [documents, form],
  );

  useEffect(() => {
    const currentDocuments = form.getFieldValue('documents') || [];
    if (currentDocuments[index]) {
      setFileValues(currentDocuments[index].document_path);
    }
  }, [form, index]);

  return (
    <ProFormGroup>
      <ProFormSelect
        width="md"
        required
        onChange={handleDocumentChange}
        fieldProps={{ loading: documentsLoading }}
        options={documents?.map((doc) => ({
          label: doc.label,
          value: doc.name,
        }))}
        name={`name`}
        label={`${index + 1}. ${formatMessage({ id: 'common.certification' })}`}
        showSearch
      />
      <ProFormDatePicker
        disabled
        label={formatMessage({ id: 'common.certification_date' })}
        name={`issue_date`}
        width="sm"
        fieldProps={{
          format: (value: any) => moment(value).format(DEFAULT_DATE_FORMAT_WITHOUT_TIME),
        }}
      />
      <ProFormDatePicker
        disabled
        label={formatMessage({ id: 'common.expiration_date' })}
        name={`expiry_date`}
        width="sm"
        fieldProps={{
          format: (value: any) => moment(value).format(DEFAULT_DATE_FORMAT_WITHOUT_TIME),
        }}
      />
      <FormUploadFiles
        label={formatMessage({ id: 'common.docs' })}
        fileLimit={10}
        formItemName={`document_path`}
        isReadonly
        initialImages={fileValues}
        showUploadButton={false}
      />
    </ProFormGroup>
  );
};

const CertificationForm: FC<CertificationFormProps> = () => {
  const { formatMessage } = useIntl();
  const { id } = useParams();
  const form = ProForm.useFormInstance();
  const formListProps = useProFormList();

  const renderCreateCertificationButton = () => (
    <Button
      onClick={() => {
        history.replace('/farming-diary-static/certification/create', {
          fromProcedureEdit: true,
          id: id,
        });
      }}
      icon={<PlusOutlined />}
      type="default"
    >
      {formatMessage({ id: 'common.create-ceritification' })}
    </Button>
  );

  return (
    <Card
      title={formatMessage({ id: 'common.certification' })}
      extra={renderCreateCertificationButton()}
    >
      <ProFormList name="documents" {...formListProps}>
        {({ key }) => <CertificationContent form={form} index={key} />}
      </ProFormList>
    </Card>
  );
};

export default CertificationForm;
