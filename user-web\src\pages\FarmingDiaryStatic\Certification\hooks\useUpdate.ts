import { updateDocument } from '@/services/diary-2/document';
import { updateProduct } from '@/services/diary-2/product';
import { useIntl, useRequest } from '@umijs/max';
import { App } from 'antd';

export default function useUpdate(
  { onSuccess } = {} as {
    onSuccess?: () => void;
  },
) {
  const { message } = App.useApp();
  const { formatMessage } = useIntl();
  return useRequest(updateDocument, {
    manual: true,
    onSuccess(data, params) {
      message.success(
        formatMessage({
          id: 'common.success',
        }),
      );
      onSuccess?.();
    },
    onError(error) {
      message.error(error.message || formatMessage({ id: 'common.error' }));
    },
  });
}
