import { DEFAULT_PAGE_SIZE_ALL } from '@/common/contanst/constanst';
import { getCustomerUserList } from '@/services/customerUser';
import { createMember, getMemberTypeList, updateMember } from '@/services/diary-2/business';
import { EditOutlined } from '@ant-design/icons';
import { EditableProTable } from '@ant-design/pro-components';
import { useIntl } from '@umijs/max';
import { App, Button, Card } from 'antd';
import { nanoid } from 'nanoid';
import { FC, ReactNode } from 'react';
import SelectOrCreateMemberType from '../Create/SelectOrCreateMemberType';
import DeleteMember from '../DeleteMember';

interface MemberProps {
  children?: ReactNode;
  onSuccess?: () => void;
  businessId: string;
}

const Member: FC<MemberProps> = ({ children, onSuccess, businessId }) => {
  const { formatMessage } = useIntl();
  const { message } = App.useApp();
  return (
    <Card
      title={formatMessage({
        id: 'common.member',
      })}
    >
      <EditableProTable
        name="members"
        scroll={{ x: 'max-content' }}
        editable={{
          async onSave(key, record: any, originRow, newLineConfig) {
            if (record.isNew) {
              /**
               * @todo create new item
               */
              await createMember({
                // name: record.name,
                business_id: businessId,
                member_type: record.member_type,
                user_id: record.user_id,
              });
              message.success(formatMessage({ id: 'common.success' }));
              onSuccess?.();
              return true;
            } else {
              /**
               * @todo update item
               */
              await updateMember({
                name: record.name,
                business_id: businessId,
                member_type: record.member_type,
                user_id: record.user_id,
              });

              message.success(formatMessage({ id: 'common.success' }));
              onSuccess?.();
              return true;
            }
          },
          saveText: formatMessage({
            id: 'common.save',
          }),
          onlyOneLineEditorAlertMessage: formatMessage({
            id: 'common.only_one_line_editor_alert_message',
            defaultMessage: 'Only one line can be edited at a time',
          }),
          onlyAddOneLineAlertMessage: formatMessage({
            id: 'common.only_add_one_line_alert_message',
            defaultMessage: 'Only one new line can be added at a time',
          }),
        }}
        rowKey={'name'}
        recordCreatorProps={{
          record: {
            isNew: true,
            name: nanoid(),
          },
        }}
        columns={[
          {
            title: formatMessage({
              id: 'common.member_group',
            }),
            dataIndex: 'member_type',
            valueType: 'select',
            request: async () => {
              const res = await getMemberTypeList({
                page: 1,
                size: DEFAULT_PAGE_SIZE_ALL,
              });
              const data = res.data.map((item) => ({
                label: item.label,
                value: item.name,
              }));
              return data;
            },
            width: '30',
            renderFormItem() {
              return (
                <SelectOrCreateMemberType
                  style={{
                    width: '100%',
                  }}
                />
              );
            },
          },
          {
            title: formatMessage({
              id: 'common.member',
            }),
            valueType: 'select',
            dataIndex: 'user_id',
            request: async () => {
              const res = await getCustomerUserList({
                page: 1,
                size: DEFAULT_PAGE_SIZE_ALL,
              });
              return res.data.map((item) => ({
                label: `${item.first_name} ${item.last_name}`,
                value: item.name,
              }));
            },
            width: '30',
          },
          {
            title: formatMessage({
              id: 'common.number_phone',
            }),
            dataIndex: 'phone_number',
            editable: false,
            width: '30',
          },
          {
            title: 'Email',
            dataIndex: 'email',
            editable: false,
            width: '30',
          },
          {
            title: formatMessage({
              id: 'common.address',
            }),
            dataIndex: 'address',
            editable: false,
            width: '30',
          },
          {
            valueType: 'option',
            render: (text, record, _, action) => [
              <Button
                icon={<EditOutlined />}
                key="editable"
                onClick={() => {
                  action?.startEditable?.(record.name);
                }}
                size="small"
              />,
              <DeleteMember id={record.name} key="delete" onSuccess={onSuccess} />,
            ],
            width: '30',
          },
        ]}
      />
    </Card>
  );
};

export default Member;
