import { getCrop } from '@/services/crop';
import { getTaskManageTracingList } from '@/services/farming-plan';
import { ProFormDateTimePicker } from '@ant-design/pro-components';
import { FormattedMessage } from '@umijs/max';
import { Card, Col, Form, Input, Row } from 'antd';
import { FC, useEffect, useState } from 'react';
import ImageCropNote from './Image';

const { Item } = Form;
interface DetailTaskInfotabProps {
  taskID: string;
}
const DetailTaskInfotab: FC<DetailTaskInfotabProps> = (props) => {
  const [loading, setLoading] = useState(false);
  const [task, setTask] = useState<Partial<any>>({});
  const [cropList, setCropList] = useState<any[]>([]);
  const [imageLinks, setImageLinks] = useState<any[]>([]);
  const [form] = Form.useForm();

  const taskID = props.taskID;

  const renderImageCropNoteLayout = () => {
    const ImageCropNoteComponents: JSX.Element[] = [];
    let rowImages: string[] = [];

    imageLinks.forEach((imageLink, index) => {
      rowImages.push(imageLink);

      if ((index + 1) % 4 === 0 || index === imageLinks.length - 1) {
        // When we have 4 images in the row or we have reached the last image
        const ImageCropNoteRow = (
          <Row className="gutter-row" gutter={4} key={`row_${index}`}>
            {rowImages.map((image, idx) => (
              <Col className="gutter-row" key={`col_${index}_${idx}`}>
                <ImageCropNote imageLink={image} index={idx} />
              </Col>
            ))}
          </Row>
        );
        ImageCropNoteComponents.push(ImageCropNoteRow);
        rowImages = [];
      }
    });

    return ImageCropNoteComponents;
  };

  const initTaskInfo = async () => {
    try {
      setLoading(true);
      const res: any = await getTaskManageTracingList({
        page: 1,
        size: 1000,
        fields: ['*'],
        filters: [],
        or_filters: [],
        order_by: '',
        group_by: '',
      });
      let data: any = res.data.data;
      console.log('receive task info', data[0]);
      setTask(data[0]);
      if (data[0].image) {
        setImageLinks(data[0].image.split(','));
      }
      form.setFieldsValue(data[0]);
    } catch (error) {
      console.log(error);
    } finally {
      setLoading(false);
    }
  };

  const initCropList = async () => {
    try {
      setLoading(true);
      // let [params, sort, filters]: any[] = ['', '', [["iot_Crop_note", "name", "like", taskID]]];
      const res: any = await getCrop({
        page: 1,
        size: 1000,
        fields: ['*'],
        filters: JSON.stringify([]),
        or_filters: [],
        order_by: '',
        group_by: '',
      });
      let data: any = res.data.data;
      console.log('receive data', data);
      setCropList(data);
    } catch (error) {
      console.log(error);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    initTaskInfo();
    initCropList();
  }, [taskID]);
  const assignedToInfo = form.getFieldValue('assigned_to_info');
  const involveInUsers = form.getFieldValue('involve_in_users');

  console.log('assignedToInfo', assignedToInfo);
  return (
    <Card
      title={task ? task.name : 'Loading...'} // Hoặc "N/A" hoặc giá trị mặc định khác tuỳ ý
      loading={loading}
    >
      <Form size="small" layout="horizontal" labelCol={{ span: 24 }} labelAlign="left" form={form}>
        <Row gutter={5}>
          <Col md={6}>
            <Item
              label="Công việc"
              labelCol={{ span: 24 }}
              name="label"
              style={{ marginBottom: '16px', fontWeight: 'bold' }} // Add the fontWeight property here
            >
              <Input readOnly />
            </Item>
          </Col>
          <Col md={6}>
            <Item
              label="Vụ mùa"
              labelCol={{ span: 24 }}
              name="crop_name"
              style={{ marginBottom: '16px', fontWeight: 'bold' }} // Add the fontWeight property here
            >
              <Input readOnly />
              {/* <ProFormSelect showSearch options={cropList.map((crop: any) => ({ value: crop.name, label: crop.label }))}></ProFormSelect> */}
            </Item>
          </Col>
          <Col md={6}>
            <Item
              label="Khu vực"
              labelCol={{ span: 24 }}
              name="zone_name"
              style={{ marginBottom: '16px', fontWeight: 'bold' }} // Add the fontWeight property here
            >
              <Input readOnly />
              {/* <ProFormSelect showSearch options={cropList.map((crop: any) => ({ value: crop.name, label: crop.label }))}></ProFormSelect> */}
            </Item>
          </Col>
          <Col md={6}>
            <Item
              label="Dự án"
              labelCol={{ span: 24 }}
              name="project_name"
              style={{ marginBottom: '16px', fontWeight: 'bold' }} // Add the fontWeight property here
            >
              <Input readOnly />
              {/* <ProFormSelect showSearch options={cropList.map((crop: any) => ({ value: crop.name, label: crop.label }))}></ProFormSelect> */}
            </Item>
          </Col>
        </Row>
        <Row gutter={5}>
          <Col md={6}>
            <Item
              label="Kế hoạch"
              labelCol={{ span: 24 }}
              name="pl_name"
              style={{ marginBottom: '16px', fontWeight: 'bold' }} // Add the fontWeight property here
            >
              <Input readOnly />
            </Item>
          </Col>
          <Col md={6}>
            <Item
              label="Giai đoạn"
              labelCol={{ span: 24 }}
              name="crop_name"
              style={{ marginBottom: '16px', fontWeight: 'bold' }} // Add the fontWeight property here
            >
              <Input readOnly />
              {/* <ProFormSelect showSearch options={cropList.map((crop: any) => ({ value: crop.name, label: crop.label }))}></ProFormSelect> */}
            </Item>
          </Col>
          <Col md={6}>
            <Item
              label="Trạng thái"
              labelCol={{ span: 24 }}
              name="status"
              style={{ marginBottom: '16px', fontWeight: 'bold' }} // Add the fontWeight property here
            >
              <Input readOnly />
              {/* <ProFormSelect showSearch options={cropList.map((crop: any) => ({ value: crop.name, label: crop.label }))}></ProFormSelect> */}
            </Item>
          </Col>
          <Col md={6}>
            <Item
              label="Tên dự án"
              labelCol={{ span: 24 }}
              name="project_name"
              style={{ marginBottom: '16px', fontWeight: 'bold' }} // Add the fontWeight property here
            >
              <Input readOnly />
              {/* <ProFormSelect showSearch options={cropList.map((crop: any) => ({ value: crop.name, label: crop.label }))}></ProFormSelect> */}
            </Item>
          </Col>
        </Row>
        <Row gutter={5}>
          <Col className="gutter-row" md={12}>
            <Item
              label="Ngày bắt đầu"
              labelCol={{ span: 24 }}
              name="start_date"
              style={{ marginBottom: '16px', fontWeight: 'bold' }} // Add the fontWeight property here
            >
              <ProFormDateTimePicker disabled />
              {/* <Input.TextArea readOnly /> */}
            </Item>
          </Col>
          <Col className="gutter-row" md={12}>
            <Item
              label="Ngày kết thúc"
              labelCol={{ span: 24 }}
              name="end_date"
              style={{ marginBottom: '16px', fontWeight: 'bold' }} // Add the fontWeight property here
            >
              <ProFormDateTimePicker disabled />
            </Item>
          </Col>
        </Row>
        <Row gutter={5}>
          <Col className="gutter-row" md={24}>
            <Item
              label="Nội dung công việc"
              labelCol={{ span: 24 }}
              name="description"
              style={{ marginBottom: '16px', fontWeight: 'bold' }} // Add the fontWeight property here
            >
              <Input.TextArea readOnly />
            </Item>
          </Col>
        </Row>
        <Row gutter={5}>
          <Col className="gutter-row" md={8}>
            <Item
              label="Người thực hiện"
              labelCol={{ span: 24 }}
              // name="assigned_to_info"
              style={{ marginBottom: '16px', fontWeight: 'bold' }} // Add the fontWeight property here
            >
              {assignedToInfo ? (
                <Input
                  readOnly
                  value={assignedToInfo
                    .map((user: any) => `${user.first_name} ${user.last_name}`)
                    .join(', ')}
                />
              ) : (
                <Input readOnly value="" />
              )}
            </Item>
          </Col>
          <Col className="gutter-row" md={16}>
            <Item
              label="Người liên quan"
              labelCol={{ span: 24 }}
              style={{ marginBottom: '16px', fontWeight: 'bold' }} // Add the fontWeight property here
            >
              {assignedToInfo ? (
                <Input
                  readOnly
                  value={involveInUsers
                    .map((user: any) => `${user.first_name} ${user.last_name}`)
                    .join(', ')}
                />
              ) : (
                <Input readOnly value="" />
              )}
            </Item>
          </Col>
        </Row>
        <Col className="gutter-row">
          <Item
            label={<FormattedMessage id={'common.form.image'} />}
            labelCol={{ span: 24 }}
            name="image"
            style={{ marginBottom: '16px', fontWeight: 'bold' }} // Add the fontWeight property here
          >
            {renderImageCropNoteLayout()}
          </Item>
        </Col>
      </Form>
    </Card>
  );
};
export default DetailTaskInfotab;
