import { getCropTracing } from '@/services/tracing';
import { getParamsReqList, getParamsReqTable } from '@/services/utils';
import { formatDateDefault } from '@/utils/date';
import { EyeOutlined } from '@ant-design/icons';
import { ActionType, ParamsType, ProTable } from '@ant-design/pro-components';
import { useIntl } from '@umijs/max';
import { Button, message, Modal, QRCode, Space, Typography } from 'antd';
import { SortOrder } from 'antd/es/table/interface';
import { useRef, useState } from 'react';
import Create from './Components/Create';
import Delete from './Components/Delete';

const TracingTable = () => {
  const intl = useIntl();
  const tableRef = useRef<ActionType>();
  const [isModalOpenQR, setIsModalOpenQR] = useState(false);
  const [currentItem, setCurrentItem] = useState<any>({ name: 'demo' });

  const reloadTable = async () => {
    tableRef.current?.reload();
  };

  const handlePopupQRCode = (entity: any) => {
    setIsModalOpenQR(true);
    setCurrentItem(entity);
  };

  const downloadQRCode = (qr_id: string | undefined) => {
    const canvas = document
      .getElementById(`tracingQR-${qr_id}`)
      ?.querySelector<HTMLCanvasElement>('canvas');
    console.log('canvas qr', document.getElementById(`tracingQR-${qr_id}`));
    if (canvas) {
      const url = canvas.toDataURL();
      const a = document.createElement('a');
      a.download = 'QRCode.png';
      a.href = url;
      document.body.appendChild(a);
      a.click();
      document.body.removeChild(a);
    }
  };

  const columns: any = [
    {
      title: 'STT',
      dataIndex: 'stt',
      valueType: 'index',
      width: 48,
    },
    {
      title: intl.formatMessage({ id: 'common.origin_code' }),
      dataIndex: 'name',
      hideInSearch: true,
      width: 150,
      hideInTable: true,
      // render: (text: string, entity: any) => (
      //   <a
      //     onClick={(e) => {
      //       e.preventDefault();
      //       history.push(`/farming-diary/detail/${entity.name}`);
      //     }}
      //   >
      //     {text}
      //   </a>
      // ),
    },
    {
      title: intl.formatMessage({ id: 'common.origin_name' }),
      dataIndex: 'label',
      _hideInSearch: true,
      width: 150,
      // render: (text: string, entity: any) => (
      //   <a
      //     onClick={(e) => {
      //       e.preventDefault();
      //       history.push(`/farming-diary/detail/${entity.name}`);
      //     }}
      //   >
      //     {text}
      //   </a>
      // ),
    },
    {
      title: intl.formatMessage({ id: 'common.crop' }),
      dataIndex: 'crop_label',
      _hideInSearch: true,
      width: 150,
      // render: (text: string, entity: any) => (
      //   <a
      //     onClick={(e) => {
      //       e.preventDefault();
      //       history.push(`/farming-diary/detail/${entity.name}`);
      //     }}
      //   >
      //     {text}
      //   </a>
      // ),
    },
    {
      title: intl.formatMessage({ id: 'common.scan_times' }),
      dataIndex: 'log_count',
      _hideInSearch: true,
      width: 150,
    },
    {
      title: intl.formatMessage({ id: 'common.created_at' }),
      dataIndex: 'creation',
      _hideInSearch: true,
      render: (text: string, entity: any) => {
        return formatDateDefault(entity.creation);
      },
      width: 150,
    },
    {
      title: intl.formatMessage({ id: 'common.expired_time' }),
      dataIndex: 'expiry_time',
      _hideInSearch: true,
      render: (text: string, entity: any) => {
        return formatDateDefault(entity.expiry_time);
      },
      width: 150,
    },
    {
      title: 'Action',
      dataIndex: 'name',
      width: 150,
      search: false,
      render: (dom: any, entity: any) => (
        <Space>
          {/* <Update key={'edit' + entity.name} refreshFnc={reloadTable} value={entity} /> */}

          <Delete key={'remove' + entity.name} refreshFnc={reloadTable} value={entity} />
        </Space>
      ),
    },
    {
      title: 'QR Code',
      dataIndex: 'qr',
      //   width: 80,
      render(dom: any, entity: any) {
        return <Button icon={<EyeOutlined />} onClick={() => handlePopupQRCode(entity)} />;
      },
      search: false,
      width: 150,
    },
  ];

  return (
    <>
      <Modal
        open={isModalOpenQR}
        onCancel={() => setIsModalOpenQR(false)}
        footer={[]}
        title={`Mã QR của ${currentItem?.label}`}
      >
        <div id={`tracingQR-${currentItem?.name}`}>
          <Space direction="vertical" align="center">
            <Button type="link" onClick={() => downloadQRCode(currentItem?.name)}>
              {currentItem && (
                <QRCode
                  size={400}
                  value={`http://truyxuat.viis.tech?traceId=${currentItem.name}`}
                  bgColor="#fff"
                />
              )}
            </Button>
            <Typography.Text italic>Nhấn vào QR để tải ảnh về máy</Typography.Text>
            <Typography.Text>
              <a
                href={`http://truyxuat.viis.tech?traceId=${currentItem.name}`}
                target="_blank"
                rel="noreferrer"
              >
                Click here to access
              </a>
            </Typography.Text>
          </Space>
        </div>
      </Modal>
      <ProTable<any>
        columns={columns}
        // search={false}
        //   dataSource={props.dataSource}
        toolBarRender={() => [<Create refreshFnc={reloadTable} />]}
        pagination={{
          pageSizeOptions: [10, 20, 50, 100],
          showSizeChanger: true,
          defaultPageSize: 10,
        }}
        actionRef={tableRef}
        request={async (
          params: ParamsType & { pageSize?: number; current?: number; keyword?: string },
          sort: Record<string, SortOrder>,
          filter: Record<string, (string | number)[] | null>,
        ) => {
          try {
            //format params
            // const _params = params;
            // _params.page = _params.current;
            // _params.size = _params.pageSize;
            // _params.filters = [];

            // if (_params.label) {
            //   _params.filters.push(['iot_crop', 'label', 'like', `'${_params.label}'`]);
            // }
            // _params.filters = JSON.stringify(_params.filters);
            const par = getParamsReqTable({
              doc_name: 'iot_crop',
              tableReqParams: {
                params: params,
                sort,
                filter,
              },
            });
            const _params = getParamsReqList(par);
            const res: any = await getCropTracing(_params);
            return {
              data: res.data.data,
              total: res.data.pagination.totalElements,
              success: true,
            };
          } catch (error: any) {
            message.error(`Error when getting Crop Statistic Products: ${error.message}`);
          }
        }}
        rowKey="name"
        // search={{ labelWidth: 'auto' }}
      />
    </>
  );
};

export default TracingTable;
