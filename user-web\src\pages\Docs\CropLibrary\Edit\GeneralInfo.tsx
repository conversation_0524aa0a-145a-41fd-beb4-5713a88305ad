import { InfoTab } from '@/types/infoTab.type';
import { Plant } from '@/types/plant.type';
import { Alert, Form, Space } from 'antd';
import { createStyles } from 'antd-use-styles';
import { FC, useEffect, useState } from 'react';
import EditableFormCard from './components/EditableFormCard';

interface GeneralInfoProps {
  curPlant: Plant;
}
interface UpdatingInforTab extends InfoTab {
  is_new?: boolean;
  is_deleted?: boolean;
}

const useStyles = createStyles(({ token }) => ({
  collapseHeader: {
    backgroundColor: token.colorBgContainer,
    boxShadow: 'none',
    '& .ant-collapse-header': {
      borderBlockEnd: `1px solid ${token.colorBorderSecondary}`,
    },
  },
}));
const GeneralInfo: FC<GeneralInfoProps> = ({ curPlant }) => {
  const styles = useStyles();
  const [inforTabList, setInforTabList] = useState<UpdatingInforTab[] | undefined>([]);
  const form = Form.useFormInstance();
  useEffect(() => {
    const fetchData = () => {
      setInforTabList(curPlant.infor_tab_list);
    };

    fetchData();
  }, [curPlant]);
  const handleMoveUp = (id: number) => {
    if (id === 0) return;
    const updatedItems = inforTabList ? [...inforTabList] : [];
    [updatedItems[id], updatedItems[id - 1]] = [updatedItems[id - 1], updatedItems[id]];
    form.setFieldValue(['infor_tab_list', updatedItems[id].name, 'sort_index'], id);
    form.setFieldValue(['infor_tab_list', updatedItems[id - 1].name, 'sort_index'], id - 1);
    setInforTabList(updatedItems);
  };

  const handleMoveDown = (id: number) => {
    if (!inforTabList || id >= inforTabList.length - 1) return;
    const updatedItems = [...inforTabList];
    [updatedItems[id], updatedItems[id + 1]] = [updatedItems[id + 1], updatedItems[id]];
    form.setFieldValue(['infor_tab_list', updatedItems[id].name, 'sort_index'], id);
    form.setFieldValue(['infor_tab_list', updatedItems[id + 1].name, 'sort_index'], id + 1);
    setInforTabList(updatedItems);
  };
  return (
    <Space direction="vertical" size={16} style={{ width: '100%' }}>
      {inforTabList ? (
        inforTabList.map((info, index) => {
          return (
            <EditableFormCard
              handleMoveDown={() => handleMoveDown(index)}
              handleMoveUp={() => handleMoveUp(index)}
              key={index}
              cardInfo={info}
              index={index}
              infoType="infor_tab_list"
            />
          );
        })
      ) : (
        <Alert message="Cây này chưa có hướng dẫn nào" type="info" />
      )}
    </Space>
  );
};

export default GeneralInfo;
