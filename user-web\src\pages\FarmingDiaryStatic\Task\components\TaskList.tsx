import { DOCTYPE_ERP, TAG_COLOR } from '@/common/contanst/constanst';
import { getCustomerUserList } from '@/services/customerUser';
import { getTaskList, Task } from '@/services/diary-2/task';
import { getParamsReqTable } from '@/services/utils';
import { createEmptyArray } from '@/utils/array';
import { ActionType, ProColumns, ProTable } from '@ant-design/pro-components';
import { Link, useIntl } from '@umijs/max';
import { Button, Space, Tag } from 'antd';
import { FC, ReactNode, useRef, useState } from 'react';
import DeleteTask from './DeleteTask';

interface TaskListProps {
  children?: ReactNode;
}
const getTagColor = (item: string) => {
  switch (item) {
    case 'Priority':
      return TAG_COLOR.PRIORITY;
    case 'Important':
      return TAG_COLOR.IMPORTANT;
    case 'Common':
      return TAG_COLOR.COMMON;
    default:
      return 'default';
  }
};
const TaskList: FC<TaskListProps> = ({ children }) => {
  const actionRef = useRef<ActionType>();
  const handleReload = () => {
    actionRef.current?.reload?.();
  };
  const { formatMessage } = useIntl();
  const [max, setMax] = useState(0);
  return (
    <ProTable
      actionRef={actionRef}
      rowKey={'name'}
      form={{
        labelWidth: 'auto',
      }}
      scroll={{
        x: 1200,
      }}
      pagination={{
        defaultPageSize: 10,
        showSizeChanger: true,
        pageSizeOptions: ['10', '20', '50', '100'],
      }}
      columns={
        [
          {
            title: 'STT',
            render(dom, entity, index, action, schema) {
              return index + 1;
            },
            hideInSearch: true,
            width: 50,
          },
          {
            title: formatMessage({
              id: 'common.task_name',
            }),
            dataIndex: 'label',
            render(dom, entity, index, action, schema) {
              return (
                <Link to={`/farming-diary-static/task/edit/${entity.name}`}>
                  <Button type="link" style={{ padding: 0 }}>
                    {dom}
                  </Button>
                </Link>
              );
            },
            width: 100,
            fixed: 'left',
          },
          {
            title: formatMessage({
              id: 'common.assigned_to',
            }),
            render(dom, entity, index, action, schema) {
              return <>{`${entity.first_name} ${entity.last_name}`}</>;
            },
            dataIndex: 'assigned_to',
            // hideInSearch: true,
            valueType: 'select',
            request: async () => {
              const res = await getCustomerUserList();
              return res.data.map((item) => {
                return {
                  label: `${item.first_name} ${item.last_name}`,
                  value: item.name,
                };
              });
            },
            fieldProps: {
              showSearch: true,
            },
            width: 100,
          },
          {
            title: formatMessage({
              id: 'common.level',
            }),
            dataIndex: 'level',
            render(dom, entity, index, action, schema) {
              return <Tag color={getTagColor(entity.level)}>{dom}</Tag>;
            },
            valueType: 'select',
            request: async () => {
              return [
                {
                  label: formatMessage({ id: 'common.priority' }),
                  value: 'Priority',
                },
                {
                  label: formatMessage({ id: 'common.important' }),
                  value: 'Important',
                },
                {
                  label: formatMessage({ id: 'common.common' }),
                  value: 'Common',
                },
              ];
            },

            width: 100,
          },
          {
            title: formatMessage({
              id: 'common.execution_time',
            }),
            hideInSearch: true,
            dataIndex: 'execution_day',
            render(dom, entity, index, action, schema) {
              return `${formatMessage({
                id: 'common.days',
              })} ${dom}`;
            },
            width: 100,
          },
          ...createEmptyArray(max).map<ProColumns<Task>>((item) => ({
            title: `${formatMessage({
              id: 'common.supplies',
            })} ${item + 1}`,
            render(dom, entity, index, action, schema) {
              const data = entity?.related_items?.[item];
              return ` ${data?.quantity || ''} ${data?.uom_name || ''} ${data?.label || ''}`;
            },
            hideInSearch: true,
            width: 100,
          })),
          {
            title: formatMessage({
              id: 'common.note',
            }),
            hideInSearch: true,
            hideInTable: true,
            dataIndex: 'description',
            width: 100,
          },
          {
            hideInSearch: true,
            hideInForm: true,
            render(dom, entity, index, action, schema) {
              return (
                <Space>
                  <DeleteTask id={entity.name} onSuccess={handleReload} />
                </Space>
              );
            },
            width: 100,
          },
        ] satisfies Array<ProColumns<Task>>
      }
      request={async (params, sort, filter) => {
        const paramsReq: any = getParamsReqTable({
          doc_name: DOCTYPE_ERP.iot_diary_v2_task,
          tableReqParams: {
            params,
            sort,
            filter,
          },
          defaultSort: 'name asc',
        });

        // const paramsReq = {
        //   page: params.current,
        //   size: params.pageSize,
        //   order_by: '',
        //   filters: '',
        // };
        // const filters = [];
        // if (params.label) {
        //   filters.push(['iot_diary_v2_task', 'label', 'like', params.label]);
        // }
        // if(params.assigned_to) {
        //   filters.push(['iot_diary_v2_task', 'assigned_to', 'like', params.assigned_to]);
        // }
        const res = await getTaskList(paramsReq);
        const related_itemsMaxLength = res.data.reduce((max, item) => {
          return Math.max(max, item.related_items.length);
        }, 0);
        setMax(related_itemsMaxLength);
        return {
          data: res.data,
          total: res.pagination.totalElements,
        };
      }}
    />
  );
};

export default TaskList;
