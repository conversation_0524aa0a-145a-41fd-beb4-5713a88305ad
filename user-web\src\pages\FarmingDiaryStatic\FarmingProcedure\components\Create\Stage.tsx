import { useProFormList } from '@/components/Form/Config/pro-form-list';
import { getStageList } from '@/services/diary-2/stage';
import { PlusOutlined } from '@ant-design/icons';
import {
  ProForm,
  ProFormDigit,
  ProFormGroup,
  ProFormList,
  ProFormSelect,
} from '@ant-design/pro-components';
import { history, useIntl, useRequest } from '@umijs/max';
import { Button, Card } from 'antd';
import { FC, ReactNode, useCallback } from 'react';

interface StageFormProps {
  children?: ReactNode;
}

const StageForm: FC<StageFormProps> = () => {
  const { formatMessage } = useIntl();
  const { data: states, loading: statesLoading } = useRequest(getStageList);
  const form = ProForm.useFormInstance();
  const formListProps = useProFormList();

  const handleStageChange = useCallback(
    (selectedStageName: string) => {
      const selectedStage = states?.find((stage) => stage.name === selectedStageName);
      const currentStages = form.getFieldValue('states') || [];
      const updatedStages = currentStages.map((stage: any, index: number) =>
        stage.name === selectedStageName
          ? {
              ...stage,
              idx: index + 1, // Bổ sung index vào mỗi stage
              task_count: selectedStage?.task_count,
              expire_time_in_days: selectedStage?.expire_time_in_days,
            }
          : stage,
      );
      form.setFieldsValue({ states: updatedStages });
    },
    [states, form],
  );

  const renderCreateStageButton = () => (
    <Button
      onClick={() =>
        history.replace('/farming-diary-static/stage-of-crop/create', {
          fromProcedureCreate: true,
        })
      }
      icon={<PlusOutlined />}
      type="default"
    >
      {formatMessage({ id: 'common.create-stage' })}
    </Button>
  );

  return (
    <Card title={formatMessage({ id: 'common.stage' })} extra={renderCreateStageButton()}>
      <ProFormList name="states" {...formListProps}>
        {(stage, index) => (
          <ProFormGroup key={index}>
            <ProFormSelect
              width="md"
              required
              onChange={handleStageChange}
              fieldProps={{ loading: statesLoading }}
              options={states?.map((stage) => ({
                label: stage.label,
                value: stage.name,
              }))}
              name="name"
              label={`${index + 1}. ${formatMessage({ id: 'common.stage' })}`}
              showSearch
            />
            <ProFormSelect
              disabled
              label={formatMessage({ id: 'common.task' })}
              name="task_count"
              width="sm"
            />
            <ProFormDigit
              disabled
              label={formatMessage({ id: 'common.time' })}
              width="sm"
              name="expire_time_in_days"
              fieldProps={{
                suffix: formatMessage({ id: 'common.date' }),
              }}
            />
          </ProFormGroup>
        )}
      </ProFormList>
    </Card>
  );
};

export default StageForm;
