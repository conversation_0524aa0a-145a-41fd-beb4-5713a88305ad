export default {
  'storage-management.product-management.inventory': 'Product Inventory',
  'storage-management.product-management.product_storage': 'Product Storage',
  'storage-management.product-management.import_history': 'Import History',
  'storage-management.product-management.export_history': 'Export History',
  'storage-management.product-management.check_history': 'Inventory Check History',
  'storage-management.product-management.import': 'Import',
  'storage-management.product-management.export': 'Export',
  'storage-management.product-management.check': 'Inventory Check',
  'storage-management.product-management.select_storage': 'Select Storage',
  'storage-management.product-management.inventory_quantity': 'Inventory Quantity',
  'storage-management.product-management.inventory_price': 'Total Price',
  'storage-management.product-management.transaction_date': 'Transaction Date',
  'storage-management.product-management.document_date': 'Document Date',
  'storage-management.product-management.document_code': 'Document Code',
  'storage-management.product-management.employee': 'Employee',
  'storage-management.product-management.import_from_excel': 'Import from Excel',
  'storage-management.product-management.total_quantity': 'Total Quantity',
  'storage-management.product-management.total_price': 'Total Price',
  'storage-management.product-management.download': 'Download Form',
  'storage-management.product-management.update': 'Update Form',
  'storage-management.product-management.check_type': 'Check Type',
  'storage-management.product-management.check_date': 'Inventory Check Date',
  'storage-management.product-management.present_quantity': 'Present Quantity',
  'storage-management.product-management.real_quantity': 'Real Quantity',
  'storage-management.product-management.quality': 'Quality',
};
