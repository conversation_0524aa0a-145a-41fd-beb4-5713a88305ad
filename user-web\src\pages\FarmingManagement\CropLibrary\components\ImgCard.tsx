import { DEFAULT_FALLBACK_IMG } from '@/common/contanst/img';
import { deletePlantAllResources } from '@/services/plants';
import { genDownloadUrl } from '@/utils/file';

import { DeleteOutlined, EditOutlined } from '@ant-design/icons';
import { history, useAccess, useModel } from '@umijs/max';
import { Avatar, Card, message, Popconfirm, Typography } from 'antd';
import Meta from 'antd/es/card/Meta';
import { Fragment } from 'react';
const { Text } = Typography;

interface Props {
  title?: string;
  image?: string;
  id: string;
}
// ! Currently tailored specificly for crop plant
const ImgCard = ({ id, image, title }: Props) => {
  const { setMyPlant } = useModel('MyPlant');
  async function handleDelete() {
    await deletePlantAllResources(id)
      .then((res) => {
        setMyPlant((prev) => prev.filter((item) => item.name !== id));
        message.success(`Xoá cây thành công`);
      })
      .catch((error) => {
        message.error(`Lỗi khi xoá cây ${title}: ${error}`);
      });
  }
  const access = useAccess();
  return (
    <Card
      style={{ overflow: 'hidden' }}
      onClick={() => {
        history.push(`/farming-management/crop-library/${id}/detail`);
      }}
      hoverable
      // TODO move actions to props?
      actions={[
        <EditOutlined key="edit" />,
        <Fragment key="delete">
          {access.canDeleteAllInPageAccess() && (
            <Popconfirm
              title="Xoá cây"
              description={`Bạn có muốn xoá cây ${title}?`}
              onConfirm={() => handleDelete()}
              key="delete"
              onPopupClick={(e) => {
                e.stopPropagation();
              }}
            >
              <DeleteOutlined
                key="delete"
                onClick={(e) => {
                  e.stopPropagation();
                }}
              />
            </Popconfirm>
          )}
        </Fragment>,
      ]}
    >
      <Meta
        avatar={
          <Avatar
            shape="square"
            size={64}
            src={image ? genDownloadUrl(image) : DEFAULT_FALLBACK_IMG}
          />
        }
        title={<Text style={{ whiteSpace: 'normal' }}>{title}</Text>}
      />
    </Card>
  );
};

export default ImgCard;
