// @ts-nocheck
// This file is generated by <PERSON><PERSON> automatically
// DO NOT CHANGE IT MANUALLY!
import dayjs from 'D:/WORK/PYROJECT/VIIS/viis-iot-web-v2/user-web/node_modules/dayjs';
import antdPlugin from 'D:/WORK/PYROJECT/VIIS/viis-iot-web-v2/user-web/node_modules/antd-dayjs-webpack-plugin/src/antd-plugin.js';

import isSameOrBefore from 'D:/WORK/PYROJECT/VIIS/viis-iot-web-v2/user-web/node_modules/dayjs/plugin/isSameOrBefore';
import isSameOrAfter from 'D:/WORK/PYROJECT/VIIS/viis-iot-web-v2/user-web/node_modules/dayjs/plugin/isSameOrAfter';
import advancedFormat from 'D:/WORK/PYROJECT/VIIS/viis-iot-web-v2/user-web/node_modules/dayjs/plugin/advancedFormat';
import customParseFormat from 'D:/WORK/PYROJECT/VIIS/viis-iot-web-v2/user-web/node_modules/dayjs/plugin/customParseFormat';
import weekday from 'D:/WORK/PYROJECT/VIIS/viis-iot-web-v2/user-web/node_modules/dayjs/plugin/weekday';
import weekYear from 'D:/WORK/PYROJECT/VIIS/viis-iot-web-v2/user-web/node_modules/dayjs/plugin/weekYear';
import weekOfYear from 'D:/WORK/PYROJECT/VIIS/viis-iot-web-v2/user-web/node_modules/dayjs/plugin/weekOfYear';
import isMoment from 'D:/WORK/PYROJECT/VIIS/viis-iot-web-v2/user-web/node_modules/dayjs/plugin/isMoment';
import localeData from 'D:/WORK/PYROJECT/VIIS/viis-iot-web-v2/user-web/node_modules/dayjs/plugin/localeData';
import localizedFormat from 'D:/WORK/PYROJECT/VIIS/viis-iot-web-v2/user-web/node_modules/dayjs/plugin/localizedFormat';
import duration from 'D:/WORK/PYROJECT/VIIS/viis-iot-web-v2/user-web/node_modules/dayjs/plugin/duration';

dayjs.extend(isSameOrBefore);
dayjs.extend(isSameOrAfter);
dayjs.extend(advancedFormat);
dayjs.extend(customParseFormat);
dayjs.extend(weekday);
dayjs.extend(weekYear);
dayjs.extend(weekOfYear);
dayjs.extend(isMoment);
dayjs.extend(localeData);
dayjs.extend(localizedFormat);
dayjs.extend(duration);

dayjs.extend(antdPlugin);
