import { Process } from '@/services/diary-2/process';
import { ProForm } from '@ant-design/pro-components';
import { FC, ReactNode, useEffect } from 'react';
import Info from './Info';
import TaskList from './TaskList';

interface DetailProps {
  children?: ReactNode;
  dataState?: Process['states'][0];
}

const Detail: FC<DetailProps> = ({ children, dataState }) => {
  console.log('dataState: ', dataState);
  const [form] = ProForm.useForm();
  useEffect(() => {
    if (dataState) {
      form.setFieldsValue(dataState);
    }
  }, [dataState]);
  return (
    <>
      <ProForm disabled form={form} submitter={false}>
        <Info dataState={dataState} />
      </ProForm>
      <div className="mt-4">
        <TaskList dataStage={dataState} />
      </div>
    </>
  );
};

export default Detail;
