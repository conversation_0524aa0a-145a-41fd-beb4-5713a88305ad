import { useNoticeAlarmData } from '@/components/NoticeIcon/hook/useNoticeAlarmData';
import { useUpdateIsRead } from '@/components/NoticeIcon/hook/useUpdateIsRead';
import { useUpdateReadAll } from '@/components/NoticeIcon/hook/useUpdateReadAll';
import MonitorDevice from '@/pages/IoTDeviceMangement/components/MonitorDevice';
import { NoticeDataRes } from '@/services/web-notification';
import { dayjsUtil } from '@/utils/date';
import { FieldTimeOutlined } from '@ant-design/icons';
import { history, useIntl, useModel } from '@umijs/max';
import { Avatar, Button, Card, Divider, Skeleton, Spin } from 'antd';
import classNames from 'classnames';
import { FC, ReactNode, useState } from 'react';
import { Virtuoso } from 'react-virtuoso';

interface AlarmProps {
  children?: ReactNode;
}

type AlarmItemProps = {
  onClick?: (item: API.NoticeIconItem<NoticeDataRes>) => void;
  data: API.NoticeIconItem<NoticeDataRes>;
};
const AlarmItem: FC<AlarmItemProps> = ({ data, onClick }) => {
  const isWarning = data?.data?.type !== 'task';
  return (
    <div
      className={classNames(
        'flex items-start gap-3 py-1 px-2 cursor-pointer hover:shadow',
        data.read ? 'opacity-50' : '',
      )}
      onClick={() => onClick?.(data)}
    >
      <div className="flex-none">
        <Avatar
          src={isWarning ? '/images/new-dashboard/bell.png' : '/images/new-dashboard/calendar.png'}
          shape="square"
        />
      </div>
      <div className="flex-1 gap-2">
        <div className="font-semibold">{data.title}</div>
        <div className="flex items-center justify-between text-gray-400">
          <div className="flex items-center">
            <span className="mr-1 ">
              <FieldTimeOutlined />
            </span>
            <span>{dayjsUtil(data.datetime).fromNow()}</span>
          </div>
          {/* <div className="flex items-center">
            <span className="mr-1 ">
              <FieldTimeOutlined />
            </span>
            <span>{timeLabel}</span>
          </div> */}
        </div>
      </div>
    </div>
  );
};
const Alarm: FC<AlarmProps> = ({ children }) => {
  const mqttModel = useModel('MQTTNotification');
  const [openNotice, setOpenNotice] = useState(false);

  const {
    alarmRes,
    loadAlarm,
    alarmNotices,
    isLoadingAlarmNotice,
    setAlarmNotices,
    alarmPendingCount,
  } = useNoticeAlarmData();
  const { run: updateIsRead } = useUpdateIsRead();
  const { run: updateReadAll, loading: isUpdatingReadAll } = useUpdateReadAll();
  const hashLoadMoreAlarm =
    (alarmRes?.pagination.pageNumber || 1) < (alarmRes?.pagination?.totalPages || 1);

  const loadMoreAlarm = () => {
    if (isLoadingAlarmNotice) return;
    const pageNumber = (alarmRes?.pagination?.pageNumber || 1) + 1;
    if (alarmRes?.pagination?.totalPages && pageNumber > alarmRes.pagination.totalPages) {
      return;
    }
    loadAlarm({
      page: pageNumber,
    });
  };
  const { formatMessage } = useIntl();

  // const alarmNoticesData = useDeferredValue(getNoticeData(alarmNotices));
  /**
   * @description state for device
   */
  const [openMonitor, setOpenMonitor] = useState(false);
  const [deviceId, setDeviceId] = useState<string | null>(null);

  const openDeviceDetail = (deviceId: string) => {
    setDeviceId(deviceId);
    setOpenMonitor(true);
  };
  const RenderFooter = () => {
    if (!hashLoadMoreAlarm) return <Divider plain>{`Đã tải hết thông báo!`}</Divider>;
    return (
      <div style={{ minHeight: 10 }}>
        {Array.from(new Array(2)).map((_item, index) => (
          <Skeleton avatar paragraph={{ rows: 1 }} active key={index} />
        ))}
      </div>
    );
  };

  return (
    <Card
      title={formatMessage({
        id: 'new-dashboard.notice-and-alarm',
      })}
      extra={
        <Button
          type="link"
          loading={isUpdatingReadAll}
          onClick={async () => {
            await updateReadAll();
          }}
        >
          {formatMessage({
            id: 'new-dashboard.see-all',
          })}
        </Button>
      }
      styles={{
        body: {
          padding: 0,
          paddingBlockEnd: 10,
        },
      }}
    >
      <Spin spinning={isLoadingAlarmNotice}>
        <div className=" py-2 px-6 ">
          {!alarmNotices || alarmNotices.length === 0 ? (
            <div>
              <img
                src="https://gw.alipayobjects.com/zos/rmsportal/sAuJeJzSKbUmHfBQRzmZ.svg"
                alt="not found"
              />
              <div>Chưa có dữ liệu</div>
            </div>
          ) : (
            <Virtuoso
              className={'scrollbar-thin'}
              style={{ height: 350 }}
              data={alarmNotices}
              overscan={500}
              endReached={loadMoreAlarm}
              itemContent={(index, item) => {
                return (
                  <AlarmItem
                    key={item.id}
                    data={item}
                    onClick={async (item) => {
                      if (item.read === false) {
                        try {
                          const dataUpdate = {
                            ...item.data,
                            is_read: true,
                          } as any;
                          await updateIsRead({
                            name: dataUpdate.name,
                            is_read: dataUpdate.is_read,
                          });
                          mqttModel?.handleMessage.noticeHandle.emit.updateNoticeTask(dataUpdate);
                        } catch {}
                        // setAlarmClearId(item.id);
                        // setOpenClearAlarm(true);
                        // // close the notification
                        // setOpenNotice(false);
                      }
                      /**
                       * handle click on notification by type
                       */
                      if ((['approval response', 'approval request'] as any).includes(item.type)) {
                        const taskId = item?.data?.entity;
                        if (taskId) history.push(`/employee-management/approval/details/${taskId}`);
                        return;
                      }
                      if (item.type === 'device') {
                        const deviceId = item?.data?.entity as string;
                        openDeviceDetail(deviceId);
                        return;
                      }
                      //
                      const taskId = item?.data?.entity;
                      if (taskId)
                        history.push(`/farming-management/workflow-management/detail/${taskId}`);
                    }}
                  />
                );
              }}
              components={{ Footer: RenderFooter }}
            />
          )}
        </div>
        {openMonitor && deviceId && (
          <MonitorDevice open={openMonitor} deviceId={deviceId} onOpenChange={setOpenMonitor} />
        )}
      </Spin>
    </Card>
  );
};

export default Alarm;
