import useFormAddress from '@/components/Form/FormAddress';
import FormUploadsPreviewable from '@/components/FormUploadsPreviewable';
import QuillEditor from '@/components/QuillEditor';
import { ProForm, ProFormItem, ProFormText } from '@ant-design/pro-components';
import { history, useIntl } from '@umijs/max';
import { Card, Col, Row, Spin } from 'antd';
import { FC, ReactNode } from 'react';
import useDetail from '../../hooks/useDetail';
import useUpdate from '../../hooks/useUpdate';
import Member from './Member';

interface CreateEnterpriseProps {
  children?: ReactNode;
  id: string;
}
const width = 'xl';
const EditEnterprise: FC<CreateEnterpriseProps> = ({ children, id }) => {
  const { run: doSubmit } = useUpdate({
    onSuccess() {
      history.push('/farming-diary-static/enterprise/list');
    },
  });
  const onFinish = async (values: any) => {
    await doSubmit({
      name: id,
      avatar: values.avatar,
      qr: values.qr,
      business_code: values.business_code,
      label: values.label,
      phone: values.phone,
      email: values.email,
      image: values.image,
      province: values.province,
      district: values.district,
      ward: values.ward,
      address: values.address,
      link: values.link,
      description: values.description,
    });
    return true;
  };
  const [form] = ProForm.useForm();

  const { formatMessage } = useIntl();
  const { loading, data, refresh } = useDetail({
    id: id,
    onSuccess(data) {
      form.setFieldsValue(data);
    },
  });

  const { cityElement, districtElement, wardElement, detailsElement } = useFormAddress({
    form: form,
    formProps: {
      city: {
        name: 'province',
        width: width,
        rules: [{ required: true }],
      },
      district: {
        name: 'district',
        width: width,
        rules: [{ required: true }],
      },
      ward: {
        name: 'ward',
        width: width,
        rules: [{ required: true }],
      },
      address: {
        name: 'address',
        rules: [{ required: true }],
      },
    },
  });

  return (
    <Spin spinning={loading}>
      <ProForm onFinish={onFinish} form={form}>
        <div className="mb-4 space-y-4">
          <Card>
            <Row>
              <Col span={12}>
                <FormUploadsPreviewable
                  isRequired={true}
                  fileLimit={1}
                  formItemName={'avatar'}
                  label={'Logo'}
                  initialImages={data?.avatar}
                />
              </Col>
              <Col span={12}>
                <FormUploadsPreviewable
                  isRequired={true}
                  fileLimit={1}
                  formItemName={'qr'}
                  label={formatMessage({ id: 'common.image_of_business_registration' })}
                  initialImages={data?.qr}
                />
              </Col>
            </Row>
            <Row gutter={24}>
              <Col span={12}>
                {' '}
                <ProFormText
                  rules={[
                    {
                      required: true,
                    },
                  ]}
                  label={formatMessage({ id: 'common.business_code' })}
                  width={width}
                  name="business_code"
                />
              </Col>
              <Col span={12}>
                <ProFormText
                  rules={[
                    {
                      required: true,
                    },
                  ]}
                  label={formatMessage({ id: 'common.business_name' })}
                  width={width}
                  name="label"
                />
              </Col>
              <Col span={12}>
                {' '}
                <ProFormText
                  label={formatMessage({ id: 'common.number_phone' })}
                  width={width}
                  name="phone"
                  rules={[
                    {
                      required: true,
                    },
                  ]}
                />
              </Col>
              <Col span={12}>
                <ProFormText label="Email" width={width} name="email" />
              </Col>
              <Col span={8}>{cityElement}</Col>
              <Col span={8}> {districtElement}</Col>
              <Col span={8}>{wardElement}</Col>
              <Col span={24}> {detailsElement}</Col>
            </Row>
            <FormUploadsPreviewable
              fileLimit={10}
              formItemName={'image'}
              label={formatMessage({ id: 'common.other_images' })}
              initialImages={data?.image}
            />
            <ProFormText label={formatMessage({ id: 'common.web_link' })} name="link" />
            <ProFormItem
              name="description"
              label={formatMessage({
                id: 'common.introduce_business',
              })}
            >
              <QuillEditor />
            </ProFormItem>
          </Card>
          <Member onSuccess={refresh} businessId={id} />
        </div>
      </ProForm>
    </Spin>
  );
};

export default EditEnterprise;
