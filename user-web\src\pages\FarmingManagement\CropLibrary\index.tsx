import FallbackComponent from '@/components/FallbackContent';
import { toLowerCaseNonAccentVietnamese } from '@/services/utils';
import { Plant } from '@/types/plant.type';
import { PageContainer, TableSkeleton } from '@ant-design/pro-components';
import { Access, useAccess, useIntl, useModel } from '@umijs/max';
import { Card, Col, Input, List, Row, Space } from 'antd';
import { debounce, isArray } from 'lodash';
import { FC, ReactNode, Suspense, useEffect, useState } from 'react';
import ImgCard from './components/ImgCard';
import CreateCrop from './CreateCrop';

interface CropLibraryProps {
  children?: ReactNode;
}

const CropLibrary: FC<CropLibraryProps> = ({ children }) => {
  const { myPlant } = useModel('MyPlant');
  const [filteredPlants, setFilteredPlants] = useState<Plant[]>([]);
  const intl = useIntl();
  useEffect(() => {
    if (isArray(myPlant)) {
      setFilteredPlants(myPlant);
    }
  }, [myPlant]);

  const debounceSearch = debounce((searchQuery) => {
    setFilteredPlants(
      myPlant.filter((plant) =>
        toLowerCaseNonAccentVietnamese(plant.label || '').includes(
          toLowerCaseNonAccentVietnamese(searchQuery),
        ),
      ),
    );
  }, 400);

  const handleSearch = (e: any) => {
    const searchQuery = e.target.value;
    debounceSearch(searchQuery);
  };
  const access = useAccess();
  const canCreatePlant = access.canCreateInPlantManagement();
  return (
    <Access accessible={access.canAccessPagePlantManagement()} fallback={<FallbackComponent />}>
      <PageContainer>
        <Space direction="vertical" size="middle" style={{ display: 'flex' }}>
          <Card bordered>
            <Row justify={'space-between'} gutter={16} align="middle">
              <Col span={8} flex={'1 0 25%'}>
                <Input
                  addonBefore={intl.formatMessage({ id: 'plantTab.plant' })}
                  onChange={handleSearch}
                />
              </Col>
              {canCreatePlant && (
                <Col span={8} style={{ textAlign: 'right' }}>
                  <CreateCrop />
                </Col>
              )}
            </Row>
          </Card>
          <Suspense fallback={<TableSkeleton active />}>
            <List
              grid={{
                column: 3,
                gutter: 10,
                md: 2,
                sm: 2,
                xs: 1,
              }}
              dataSource={filteredPlants}
              renderItem={(item) => (
                <List.Item>
                  <ImgCard image={item.image} title={item.label} id={item.name} />
                </List.Item>
              )}
            />
          </Suspense>
        </Space>
      </PageContainer>
    </Access>
  );
};
export default CropLibrary;
