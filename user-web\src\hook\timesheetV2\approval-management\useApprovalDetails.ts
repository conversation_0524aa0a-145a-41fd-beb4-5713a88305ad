import { DOCTYPE_ERP } from '@/common/contanst/constanst';
import { TimesheetApproval, getTimesheetApproval } from '@/services/approval';
import { useIntl,useRequest } from '@umijs/max';
import { App } from 'antd';



export function useApprovalDetails(
  { onSuccess } = {} as { onSuccess?: (data: TimesheetApproval) => void },
) {
  const { message } = App.useApp();
  const { formatMessage } = useIntl();

  return useRequest(
    async (id: string) => {
      const res = await getTimesheetApproval({
        page: 1,
        size: 1,
        name: id,
        // filters: [[DOCTYPE_ERP.iotTimesheetApproval, 'name', '=', id]],
      });
      const data = res.data?.[0];
      if (!data) {
        throw new Error();
      }
      return { data };
    },
    {
      manual: true,
      onError: (err) => {
        message.error(err.message);
      },
      onSuccess: (data) => {
        // message.success(
        //   formatMessage({
        //     id: 'common.success',
        //   }),
        // );
        onSuccess?.(data);
      },
    },
  );
}
