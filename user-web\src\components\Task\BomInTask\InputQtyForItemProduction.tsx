import { TaskProduction, useTaskProductionCreateStore } from '@/stores/TaskProductionCreateStore';
import { ProColumns, ProTable } from '@ant-design/pro-components';
import { useIntl } from '@umijs/max';
import { Form, InputNumber, Select } from 'antd';
import { FC, useEffect, useState } from 'react';

const flattenTaskItems = (taskItems: TaskProduction[]) => {
  const flattenedItems = new Map<string, TaskProduction>();

  taskItems.forEach((item) => {
    flattenedItems.set(item.product_id, item);

    if (item.bom && item.bom.length > 0) {
      item.bom.forEach((bomItem) => {
        bomItem.bom_items.forEach((bom_item) => {
          const flattenedItem = {
            exp_quantity: bom_item.exp_quantity,
            product_id: bom_item.item_code,
            item_name: bom_item.item_name,
            label: bom_item.item_label,
            ratio: bom_item.ratio,
            parentId: item.product_id,
            uom: bom_item.uom,
            uom_label: bom_item.uom_label,
            conversion_factor: bom_item.conversion_factor,
            uoms: bom_item.uoms,
          };
          flattenedItems.set(flattenedItem.product_id, flattenedItem);
        });
      });
    }
  });

  return Array.from(flattenedItems.values());
};

interface QuantityInputListProps {}

const QuantityInputList: FC<QuantityInputListProps> = () => {
  const { taskProduction, setTaskProduction } = useTaskProductionCreateStore();
  const [localTaskProduction, setLocalTaskProduction] = useState<TaskProduction[]>(taskProduction);
  const form = Form.useFormInstance();

  const handleQuantityChange = (itemName: string, exp_quantity: number | null) => {
    const expQuantity = exp_quantity || 0;
    const newItems = taskProduction.map((item) => {
      if (item.product_id === itemName) {
        const updatedItem = { ...item, exp_quantity: expQuantity };

        if (updatedItem.bom) {
          updatedItem.bom.forEach((bomItem) => {
            bomItem.bom_items.forEach((bom_item) => {
              const newQuantity = expQuantity * bom_item.ratio;
              bom_item.exp_quantity = parseFloat(newQuantity.toFixed(2));
            });
          });
        }

        return updatedItem;
      }

      if (item.parentId === itemName) {
        const parentItem = taskProduction.find((parent) => parent.product_id === item.parentId);
        const parentConversionFactor = parentItem ? parentItem.conversion_factor || 1 : 1;
        const newQuantity = expQuantity * (item.ratio || 1) * parentConversionFactor;
        return { ...item, exp_quantity: parseFloat(newQuantity.toFixed(2)) };
      }

      return item;
    });

    const flattenItems = flattenTaskItems(newItems);
    setTaskProduction(flattenItems);
    setLocalTaskProduction(flattenItems);
    form.setFieldsValue({ categories: flattenItems });
  };

  const handleUOMChange = (itemName: string, newUOM: string) => {
    console.log({ itemName, newUOM });

    let updatedParentItem: TaskProduction | undefined;

    const newItems: TaskProduction[] = taskProduction.map((item) => {
      if (item.product_id === itemName) {
        console.log('item', item);
        const uomObj = item.uoms!.find((u) => u.uom === newUOM);
        if (uomObj) {
          const updatedItem = {
            ...item,
            uom: uomObj.uom,
            uom_label: uomObj.uom_label,
            conversion_factor: uomObj.conversion_factor,
          };

          if (updatedItem.bom) {
            updatedItem.bom.forEach((bomItem) => {
              bomItem.bom_items.forEach((bom_item) => {
                const newQuantity =
                  updatedItem.exp_quantity! * uomObj.conversion_factor * bom_item.ratio;
                bom_item.exp_quantity = parseFloat(newQuantity.toFixed(2));
              });
            });
          }

          console.log('updatedItem', updatedItem);
          updatedParentItem = updatedItem;
          return updatedItem;
        }
      }

      if (item.parentId === itemName && updatedParentItem) {
        const parentUomObj = updatedParentItem.uoms!.find((u) => u.uom === updatedParentItem!.uom);
        console.log('parentUomObj', parentUomObj);
        if (parentUomObj) {
          const parentConversionFactor = parentUomObj.conversion_factor;
          const childUomObj = item.uoms![0];
          console.log('childUomObj', childUomObj);
          if (childUomObj) {
            const newQuantity =
              updatedParentItem.exp_quantity! *
              parentConversionFactor *
              (item.ratio || 1) *
              childUomObj.conversion_factor;
            console.log('newQuantity', newQuantity);
            return {
              ...item,
              exp_quantity: parseFloat(newQuantity.toFixed(2)),
              uom: childUomObj.uom,
              uom_label: childUomObj.uom_label,
              conversion_factor: childUomObj.conversion_factor,
            };
          }
        }
      }

      return item;
    });

    console.log('newItems when change UOM', newItems);
    const flattenItems = flattenTaskItems(newItems);
    setTaskProduction(flattenItems);
    setLocalTaskProduction(flattenItems);
    form.setFieldsValue({ categories: flattenItems });
  };

  useEffect(() => {
    const flattenItems = flattenTaskItems(taskProduction);
    setLocalTaskProduction(flattenItems);
    form.setFieldsValue({ categories: flattenItems });
  }, [taskProduction]);

  const { formatMessage } = useIntl();
  const columns: ProColumns<TaskProduction>[] = [
    {
      title: formatMessage({ id: 'common.label' }),
      dataIndex: 'label',
      key: 'label',
    },
    {
      title: formatMessage({ id: 'common.expected_qty' }),
      dataIndex: 'exp_quantity',
      key: 'exp_quantity',
      render: (text: number, record: TaskProduction) => (
        <InputNumber
          min={0}
          value={text}
          onChange={(value) => handleQuantityChange(record.product_id, value)}
        />
      ),
    },
    {
      title: formatMessage({ id: 'common.unit' }),
      dataIndex: 'uom_label',
      render(dom, entity, index, action, schema) {
        return (
          <Select
            defaultValue={entity.uom}
            style={{ width: 120 }}
            options={
              entity.uoms &&
              entity.uoms.map((uom) => {
                return { label: uom.uom_label, value: uom.uom };
              })
            }
            onChange={(value) => handleUOMChange(entity.product_id, value)}
          />
        );
      },
      key: 'uom_label',
    },
  ];

  return (
    <ProTable
      search={false}
      dataSource={localTaskProduction}
      columns={columns}
      rowKey="product_id"
      pagination={false}
      scroll={{ y: 400 }}
    />
  );
};

export default QuantityInputList;
