import { PageContainer, PageContainerProps } from '@ant-design/pro-components';
import { Link, Outlet } from '@umijs/max';
import { TabPaneProps } from 'antd';
import { createStyles } from 'antd-use-styles';
import { FC, ReactNode, Suspense, useMemo } from 'react';
import { useMatch } from 'react-router-dom';

export type PageTaskItemWithPath = TabPaneProps & {
  key: string;
  extraPage?: ReactNode[];
  fallback?: ReactNode;
};
interface PageContainerTabsWithPathProps {
  tabItems?: PageTaskItemWithPath[];
  /**
   * @example '/admin/pages'
   */
  generalPath: string;
  onTabChange?: (activeKey: string) => void;
  defaultTabActive?: string;
}
const useStyles = createStyles(({ token }) => ({
  link: {
    color: 'inherit',
    '&:hover': {
      color: token.colorPrimaryTextHover,
    },
  },
}));
const PageContainerTabsWithPath: FC<PageContainerTabsWithPathProps> = ({
  tabItems,
  generalPath,
  onTabChange,
  defaultTabActive,
}) => {
  const genUrl = (path: string) => `${generalPath}/${path}`;
  const styles = useStyles();
  const matches = useMatch(genUrl('*'));
  /**
   * matches : { *: "log/detail/ciY7e6Z7Kkv_iQR-HntBI"}
   * ['log', 'detail', 'ciY7e6Z7Kkv_iQR-HntBI']
   */
  const urlTabActive =
    matches?.params?.['*']?.split('/').filter((segment) => segment !== '')?.[0] || defaultTabActive;

  const tabItemsFormat = useMemo<PageContainerProps['tabList']>(
    () =>
      tabItems?.map(({ extraPage, fallback, children, ...rest }) => ({
        ...rest,
        tab: (
          <Link to={genUrl(rest.key)} className={styles.link}>
            {rest.tab}
          </Link>
        ),
      })),
    [styles, genUrl],
  );
  const tabActive = tabItems?.find((item) => item.key === urlTabActive) || tabItems?.[0];
  return (
    <PageContainer
      tabList={tabItemsFormat}
      tabActiveKey={tabActive?.key?.toString()}
      onTabChange={onTabChange}
      extra={tabActive?.extraPage}
      childrenContentStyle={{
        padding: '0px 32px',
      }}
    >
      <Suspense fallback={tabActive?.fallback}>{tabActive?.children || <Outlet />}</Suspense>
    </PageContainer>
  );
};

export default PageContainerTabsWithPath;
