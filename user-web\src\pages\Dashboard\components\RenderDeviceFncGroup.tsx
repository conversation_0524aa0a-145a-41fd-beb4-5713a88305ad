import { sscript } from '@/services/sscript';
import { IIotProductionFunction } from '@/types/IIotProductionFunction';
import { PageContainer } from '@ant-design/pro-components';
import { useModel } from '@umijs/max';
import { <PERSON><PERSON>, Card, Col, ConfigProvider, DatePicker, Input, message, Result, Row, Select, Spin, Tabs } from 'antd';
import React, { useEffect, useState } from 'react';
import RenderDeviceFnc from './RenderDeviceFnc';

const RenderDeviceFncGroup = ({ deviceFunctionGroup }: { deviceFunctionGroup: any }) => {
    if (!deviceFunctionGroup.length) return <></>

    return (
        <Row gutter={[20, 20]}>
            {deviceFunctionGroup.map((d: IIotProductionFunction) => {
                return <Col md={d.md_size ? d.md_size : 24} key={"group-" + d.name}>
                    {d.name === "no-group" ? <>
                        <RenderDeviceFnc deviceFunctions={d.function_list} />
                    </> : <>
                        <Card
                            title={d.label}
                        >
                            <RenderDeviceFnc deviceFunctions={d.function_list} />
                        </Card>
                    </>}

                </Col>
            })}
        </Row>

    );
};

export default RenderDeviceFncGroup;
