import FormUploadFiles from '@/components/UploadFIles';
import { useIntl } from '@umijs/max';
import { Card } from 'antd';
import { FC, ReactNode } from 'react';

interface AttachmentProps {
  children?: ReactNode;
}

const Attachment: FC<AttachmentProps> = ({ children }) => {
  const { formatMessage } = useIntl();
  return (
    <Card
      title={formatMessage({ id: 'common.attachments' })}
      style={{ boxShadow: 'none' }}
      bordered={false}
    >
      <FormUploadFiles fileLimit={10} formItemName="document_path" />
    </Card>
  );
};

export default Attachment;
