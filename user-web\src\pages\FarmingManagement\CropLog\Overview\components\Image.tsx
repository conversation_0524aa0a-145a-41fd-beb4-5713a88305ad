import { useModel } from '@umijs/max';
import { Col, Image, Row } from 'antd';
import React from 'react';

const ImageCrop: React.FC = () => {
  const { selectedCrop, setSelectedCrop } = useModel('MyCrop');
  const renderImageCropLayout = (record: any) => {
    try {
      if (selectedCrop) {
        console.log('record is', record);
        const imageCropComponents: JSX.Element[] = [];
        let rowImages: string[] = [];
        let imageLinksArr: any = record.image.split(',');
        console.log('imageLinksArr', imageLinksArr);
        imageLinksArr.forEach((imageLink: string, index: number) => {
          rowImages.push(imageLink);

          if ((index + 1) % 4 === 0 || index === imageLinksArr.length - 1) {
            // When we have 4 images in the row or we have reached the last image
            const imageCropRow = (
              <Row className="gutter-row" gutter={4} key={`row_${index}`}>
                {rowImages.map((image, idx) => (
                  <Col className="gutter-row" key={`col_${index}_${idx}`}>
                    <Image
                      src={'https://iot.viis.tech/api/v2/file/download?file_url=' + image}
                      width={120}
                      height={120}
                      preview={{
                        mask: 'View',
                      }}
                    />
                  </Col>
                ))}
              </Row>
            );
            imageCropComponents.push(imageCropRow);
            rowImages = [];
          }
        });
        return imageCropComponents;
      } else {
        return <></>;
      }
    } catch (error) {
      console.log(error);
    }
  };
  return (
    // <Image
    //     width={200}
    //     src={'https://iot.viis.tech/api/v2/file/download?file_url='}
    // />
    <>{renderImageCropLayout(selectedCrop)}</>
  );
};

export default ImageCrop;
