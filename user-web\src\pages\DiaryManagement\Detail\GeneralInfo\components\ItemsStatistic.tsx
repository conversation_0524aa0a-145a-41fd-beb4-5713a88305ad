import { getCropItemStatistic, ICropItemStatistic } from '@/services/crop';
import { ProColumns, ProTable } from '@ant-design/pro-components';
import { FormattedMessage, useIntl } from '@umijs/max';
import { App } from 'antd';
import TaskItemsModal from './TaskItemsModal';

interface Props {
  cropId: string;
}
const ItemsStatistic = ({ cropId }: Props) => {
  const { message } = App.useApp();
  const intl = useIntl();
  const columns: ProColumns<ICropItemStatistic>[] = [
    {
      title: intl.formatMessage({ id: 'category.material-management.category_list' }),
      dataIndex: 'category_label',
      render(dom, entity, index, action, schema) {
        return (
          <TaskItemsModal
            category_id={entity.category_name}
            category_label={entity.category_label}
            crop_id={cropId}
          />
        );
      },
    },
    {
      title: intl.formatMessage({ id: 'seasonalTab.totalExpected' }),
      dataIndex: 'total_exp_quantity',
    },
    {
      title: intl.formatMessage({ id: 'seasonalTab.totalReality' }),
      dataIndex: 'total_quantity',
    },
    {
      title: intl.formatMessage({ id: 'seasonalTab.totalLoss' }),
      dataIndex: 'total_loss_quantity',
    },
    {
      title: <FormattedMessage id="category.material-management.unit" defaultMessage="unknown" />,
      dataIndex: 'unit_label',
    },
    // {
    //   title: intl.formatMessage({ id: 'seasonalTab.remaining' }),
    //   dataIndex: 'remain_quantity',
    // },
  ];
  return (
    <ProTable<ICropItemStatistic>
      headerTitle={intl.formatMessage({ id: 'seasonalTab.suppliesStatistics' })}
      columns={columns}
      search={false}
      pagination={{
        pageSizeOptions: [10, 20, 50, 100],
        showSizeChanger: true,
      }}
      request={async (params, sorter, filter) => {
        try {
          const res = await getCropItemStatistic({
            page: params.current,
            size: params.pageSize,
            crop_id: cropId,
          });
          return {
            data: res.data,
            success: true,
          };
        } catch (error: any) {
          message.error(`Error when getting Crop Items Statistic: ${error.message}`);
          return {
            success: false,
          };
        }
      }}
      rowKey={'category_name'}
    />
  );
};

export default ItemsStatistic;
