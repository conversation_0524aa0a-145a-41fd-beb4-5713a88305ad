import { COLOR_HEX, DEFAULT_DATE_AND_HH_MM_FORMAT } from '@/common/contanst/constanst';
import { getCropByTask } from '@/services/crop';
import { getCustomerUserList } from '@/services/customerUser';
import { getInventoryV3 } from '@/services/InventoryManagementV3/inventory';
import { updateTaskItemListSQL } from '@/services/item';
import {
  getMaterialIssueTotal,
  getMaterialTotalBalanceForItem,
  saveStockEntry,
  submitStockEntry,
} from '@/services/stock/stockEntry';
import { getWarehouseList } from '@/services/stock/warehouse';
import { useTaskItemUsedUpdateStore } from '@/stores/TaskItemUsedUpdateStore';
import { IIotWarehouseItemTaskUsed } from '@/types/IIotWarehouseItemTaskUsed';
import { IStockItem } from '@/types/warehouse.type';
import { ExclamationCircleOutlined, MinusOutlined } from '@ant-design/icons';
import {
  ModalForm,
  ProFormDateTimePicker,
  ProFormSelect,
  ProFormTextArea,
} from '@ant-design/pro-components';
import { FormattedMessage, useIntl, useModel } from '@umijs/max';
import { App, Button, Divider, Space, Tooltip } from 'antd';
import { useForm } from 'antd/es/form/Form';
import { ButtonType } from 'antd/lib/button';
import moment from 'moment';
import { useEffect, useState } from 'react';
import {
  createMaterialTransferFromWIPVoucher,
  createSubmittingMaterialTransferFromWIPVoucher,
  validateItems,
} from '../Helpers/StockEntryHelpers';
import { IMaterialTransferVoucherItem } from '../Interfaces/StockEntryInterfaces';
import MaterialTransferAndIssueVoucherTable from './MaterialTransferVoucherTable';
interface ITreeNode {
  title: string;
  value: string;
  key: string;
  children?: ITreeNode[];
}
interface IFormData {
  doctype: 'Purchase Receipt';
  posting_date: string;
  company: 'VIIS';
  customer: string;
  warehouse: string;
  description: string;
  employee: string;
  iot_crop: string;
}
interface Props {
  onSuccess?: any;
  buttonType?: ButtonType;
}

const fetchWarehouseList = async () => {
  const warehouse = await getWarehouseList();
  return warehouse.data.map((storage) => ({
    label: storage.label,
    value: storage.name,
  }));
};

const fetchCustomerUserList = async () => {
  const listUser = await getCustomerUserList();
  return listUser.data.map((item: any) => ({
    label: `${item.first_name} ${item.last_name}`,
    value: item.name,
  }));
};

const MaterialTransferFromWIP = ({ onSuccess, buttonType }: Props) => {
  //use zustand useTaskItemUsedUpdateStore
  const { dataSource, setDataSource } = useTaskItemUsedUpdateStore();
  const taskId = dataSource[0]?.task_id;
  const [submitting, setSubmitting] = useState<boolean>(false);
  const [treeData, setTreeData] = useState<ITreeNode[]>();
  const [selectedItems, setSelectedItems] = useState<IMaterialTransferVoucherItem[]>([]);
  const [items, setItems] = useState<IStockItem[]>([]);
  const [selectedWarehouse, setSelectedWarehouse] = useState<string>('');
  const [form] = useForm();
  //current user
  const { initialState } = useModel(`@@initialState`);
  const curentUser = initialState?.currentUser;
  const { message } = App.useApp();

  const materialBalanceTotal = async () => {
    const realQuantityData = await getMaterialTotalBalanceForItem({ task_id: taskId });
    //map dataSource with realQuantityData
    const data = dataSource.map((itemInTask) => {
      const realQuantity = realQuantityData.data.find(
        (d) => d.item_id === itemInTask.item.item_code,
      );
      return {
        ...itemInTask,
        quantity: typeof realQuantity?.total_qty === 'number' ? realQuantity?.total_qty : 0,
      };
    });

    const realIssueQuantityData = await getMaterialIssueTotal({ task_id: taskId });
    //map dataSource with realIssueQuantityData
    const data1 = data.map((itemInTask) => {
      const realQuantity = realIssueQuantityData.data.find(
        (d) => d.item_id === itemInTask.item.item_code,
      );
      return {
        ...itemInTask,
        issued_quantity: typeof realQuantity?.total_qty === 'number' ? realQuantity?.total_qty : 0,
      };
    });
    await handleStoreItem(data1);
    setDataSource(data1);
  };

  const handleStoreItem = async (dataArr: any) => {
    try {
      const updateTaskItemList: IIotWarehouseItemTaskUsed[] = [];
      for (const data of dataArr) {
        updateTaskItemList.push({
          name: data.name,
          quantity: data.quantity,
          description: data.description,
          task_id: data.task_id,
          iot_category_id: data.iot_category_id,
          //exp_quantity: data.exp_quantity,
          loss_quantity: data.loss_quantity,
          issued_quantity: data.issued_quantity,
          total_qty_in_crop: data.total_qty_in_crop,
          //draft_quantity: data.draft_quantity,
        });
      }
      const request = await updateTaskItemListSQL(updateTaskItemList);
      message.success('Lưu vật tư liên quan thành công ');
    } catch (error: any) {
      message.error('Đã có lỗi xảy ra');
    }
  };

  const handleSelectWarehouse = (entity: any) => {
    setSelectedWarehouse(entity);
    setSelectedItems([]);
  };

  //only use loadData when transferFromTask is true
  const loadData = async () => {
    const item_ids: any[] | undefined = dataSource;
    if (!item_ids) {
      message.error(`Vui lòng chọn hàng hóa`);
      return;
    }
    const inventory = await getInventoryV3({
      warehouse: selectedWarehouse,
      page: '1',
      size: '1000',
    });
    for (const obj of item_ids) {
      const filteredItems = inventory.data.filter((item) => item.item_code === obj.item.item_code);
      obj.actual_qty = filteredItems.length > 0 ? filteredItems[0].actual_qty : 0;
    }
    const formatted_items: IMaterialTransferVoucherItem[] = item_ids.map((obj, index) => ({
      conversion_factor: 1,
      item_code: obj.item ? obj.item.name : '',
      item_name: obj.item ? obj.item.item_name : '',
      actual_qty: obj.total_qty_in_crop || 0,
      qty: obj.draft_quantity || 0,
      // rate: obj.item.standard_rate,
      total_price: 0,
      warehouse: selectedWarehouse,
      item_label: obj.item ? obj.item.label : '',
      key: obj.item.name,
      uom_label: obj.item ? obj.item.uom_label : '',
      convert_uom_id: obj.item ? obj.item.stock_uom : '',
      convert_uom_label: obj.item ? obj.item.uom_label : '',
    }));
    setSelectedItems(formatted_items);
    // form.setFieldValue('items', []);
    return;
  };

  //run when submit Form
  const handleFinish = async (values: IFormData) => {
    setSubmitting(true);
    try {
      if (!validateItems(selectedItems)) {
        return;
      }

      let date = moment(values.posting_date, DEFAULT_DATE_AND_HH_MM_FORMAT);
      const posting_date = date.format('YYYY-MM-DD');
      // Lấy giờ và phút từ biến `date`
      const hoursAndMinutes = date.format('HH:mm');

      // Lấy giây tại thời điểm hiện tại
      const currentSeconds = moment().format('ss');

      // Kết hợp giờ, phút và giây để tạo `posting_time`
      const posting_time = `${hoursAndMinutes}:${currentSeconds}`;
      //verify selectedItems if qty > actual_qty, return and show message error
      for (const item of selectedItems) {
        if (item.qty > item.actual_qty) {
          message.error(`Số lượng vật tư chuyển kho không được lớn hơn số lượng thực tế trong kho`);
          return;
        }
      }
      /**
       * Transfer Item from Warehouse to Work In Progress - V Warehouse
       */
      const transferVoucher = createMaterialTransferFromWIPVoucher(
        values,
        selectedItems,
        posting_date,
        posting_time,
        taskId,
      );
      const saveTransferRes = await saveStockEntry(transferVoucher);

      const submittingMaterialTransferVoucher = createSubmittingMaterialTransferFromWIPVoucher(
        saveTransferRes,
        taskId,
      );

      await submitStockEntry(submittingMaterialTransferVoucher);

      /**
       * update item tasked used real quantity
       */
      await materialBalanceTotal();
      message.success(`Success`);
      onSuccess?.();
      return true;
    } catch (error: any) {
      console.log({ error });

      if (error.code === 'ERR_BAD_REQUEST') {
        message.error(
          'Đã có lỗi xảy ra, có thể do số lượng item quá nhiều, hãy thử lại với số lượng ít hơn',
        );
      } else {
        message.error(`Error with Stock Transfer: ${error.message}`);
      }
      return;
    } finally {
      setSubmitting(false);
    }
  };

  useEffect(() => {
    loadData();
  }, [selectedWarehouse]);
  const intl = useIntl();
  return (
    <ModalForm<IFormData>
      title={'Chuyển về kho'}
      modalProps={{
        destroyOnClose: true,
        afterClose: () => form.resetFields(), // Reset form state when Modal closes
      }}
      trigger={
        <Space>
          <Button
            // ghost
            icon={<MinusOutlined />}
            type={buttonType}
            // style={{ backgroundColor: '#59B4C3' }}
          >
            {`Chuyển về kho`}
          </Button>
          <Tooltip
            color={COLOR_HEX.GREEN_TOOLTIP}
            key={COLOR_HEX.GREEN_TOOLTIP}
            title={intl.formatMessage({ id: 'tooltips.add_material_from_crop_button' })}
          >
            <ExclamationCircleOutlined />
          </Tooltip>
        </Space>
      }
      width={1600}
      form={form}
      layout="vertical"
      rowProps={{
        gutter: [16, 0],
      }}
      submitter={{
        render: (props, defaultDoms) => {
          return [
            <Button
              key="submit-material-transfer-and-issue-voucher"
              onClick={() => {
                props.submit();
              }}
              type="primary"
              style={{ width: '100%' }}
              loading={submitting}
            >
              {<FormattedMessage id="common.submit" />}
            </Button>,
          ];
        },
      }}
      autoFocusFirstInput
      grid
      // initialValues={{
      //   posting_date: moment().format(DEFAULT_DATE_FORMAT),
      // }}
      onFinish={handleFinish}
    >
      <ProFormDateTimePicker
        name="posting_date"
        required
        rules={[{ required: true, message: 'Vui lòng chọn ngày hoạch toán' }]}
        label={<FormattedMessage id="warehouse-management.export-voucher.transaction_date" />}
        colProps={{ span: 8 }}
        width="md"
        fieldProps={{
          format: DEFAULT_DATE_AND_HH_MM_FORMAT,
        }}
      />
      <ProFormSelect
        showSearch
        name="warehouse"
        rules={[{ required: true, message: 'Vui lòng chọn kho' }]}
        required
        label={<FormattedMessage id="warehouse-management.to-warehouse-name" />}
        onChange={(value) => handleSelectWarehouse(value)}
        colProps={{ span: 8 }}
        request={async () => {
          return await fetchWarehouseList();
        }}
        width={'md'}
      />
      <ProFormSelect
        showSearch
        name="iot_crop"
        required
        disabled
        label={<FormattedMessage id="common.crop" />}
        // onChange={(value) => handleSelectWarehouse(value)}
        colProps={{ span: 8 }}
        request={async () => {
          const res = await getCropByTask({ task_id: taskId });
          form.setFieldsValue({ iot_crop: res.data.name });
          form.setFieldsValue({
            description: `Chuyển vật tư từ công việc "${res.data.task_label}" của mùa vụ "${res.data.label}" vào kho thành phẩm`,
          });
          return [
            {
              label: res.data.label,
              value: res.data.name,
            },
          ];
        }}
        width={'md'}
      />
      <ProFormSelect
        showSearch
        disabled
        required
        initialValue={curentUser?.user_id}
        label={<FormattedMessage id="warehouse-management.export-voucher.employee" />}
        request={async (option) => {
          return await fetchCustomerUserList();
        }}
        name="employee"
        // onChange={handleChooseItem}
        colProps={{ span: 8 }}
        width={'md'}
      />

      <ProFormTextArea
        name="description"
        label={<FormattedMessage id={'common.form.description'} />}
        colProps={{ span: 8 }}
        width={'md'}
      />
      <Divider />
      <MaterialTransferAndIssueVoucherTable data={selectedItems} setData={setSelectedItems} />
    </ModalForm>
  );
};

export default MaterialTransferFromWIP;
