import { COLOR_HEX } from '@/common/contanst/constanst';
import { getItemByGroup, getProductItemV3 } from '@/services/InventoryManagementV3/product-item';
import { createTaskItemListSQL } from '@/services/item';
import { TaskItemUsed, useTaskItemUsedCreateStore } from '@/stores/TaskItemUsedCreateStore';
import { IIotWarehouseItemTaskUsed } from '@/types/IIotWarehouseItemTaskUsed';
import { ExclamationCircleOutlined, PlusOutlined } from '@ant-design/icons';
import { useIntl } from '@umijs/max';
import { Button, Col, Form, message, Modal, Row, Space, Tooltip } from 'antd';
import { ButtonType } from 'antd/lib/button';
import { useEffect, useState } from 'react';
import { useTaskItemUsedUpdateStore } from '../../../stores/TaskItemUsedUpdateStore';
import CategoryQuantitySelector from './CategoryQuantitySelector';

/**
 * GIAO DIỆN THÊM VẬT TƯ LIÊN QUAN TRONG GIAO DIỆN UPDATE TASK
 */

const { Item } = Form;

interface ITreeNode {
  title: string;
  value: string;
  key: string;
  normalized_title: string;
  children?: ITreeNode[];
  fullObject?: any;
}

const CreateItemUpdateView = (params: {
  refreshFnc: any;
  task_id: string;
  buttonType: ButtonType;
}) => {
  //use zustand useTaskItemUsedUpdateStore
  const { dataSource, setDataSource } = useTaskItemUsedUpdateStore();
  const [loading, setLoading] = useState(false);
  const [isOpen, setOpen] = useState(false);
  const [form] = Form.useForm();
  const [treeData, setTreeData] = useState<ITreeNode[]>([]);
  const { taskItemUsed, setTaskItemUsed } = useTaskItemUsedCreateStore();

  const getImportCategoryList = async () => {
    const dataGroup = await getItemByGroup({});
    if (dataGroup.data) {
      setTreeData(
        dataGroup.data.map((d) => ({
          title: d.item_group_label || '',
          value: d.item_group,
          key: d.item_group,
          normalized_title: (d.item_group_label || '')
            .toLowerCase()
            .normalize('NFD')
            .replace(/[\u0300-\u036f]/g, ''),
          children: d.item.map((d) => ({
            title: d.label || '',
            value: d.name || '',
            key: d.name || '',
            normalized_title: (d.label || '')
              .toLowerCase()
              .normalize('NFD')
              .replace(/[\u0300-\u036f]/g, ''),
            fullObject: d,
          })),
        })),
      );
    }
  };
  useEffect(() => {
    const fetchData = async () => {
      getImportCategoryList();
    };
    fetchData();
  }, []);
  useEffect(() => {
    form.resetFields(['categories']);
  }, [treeData]);
  const showModal = () => {
    setOpen(true);
  };
  const hideModal = () => {
    setOpen(false);
  };
  const handleOk = () => {
    form.submit();
  };
  const handleCancel = () => {
    hideModal();
    form.resetFields();
  };

  // useEffect(() => {
  //   params.refreshFnc();
  // }, [isOpen]);
  const findNodeByKey = (key: string, data: ITreeNode[]): ITreeNode | undefined => {
    for (const node of data) {
      if (node.key === key) return node;
      if (node.children) {
        const found = findNodeByKey(key, node.children);
        if (found) return found;
      }
    }
  };

  const handleSelectChange = (selectedWithBOM: React.Key[], selectedWithoutBOM: React.Key[]) => {
    const combinedSelected = [...selectedWithBOM, ...selectedWithoutBOM];
    const selectedItems = combinedSelected
      .map((key) => {
        const item = findNodeByKey(key.toString(), treeData);
        if (item && !item.children) {
          return {
            iot_category_id: key.toString(),
            item_name: item.fullObject.item_name,
            label: item.title,
            uom_label: item.fullObject.uom_label,
            uom: item.fullObject.uom,
            conversion_factor: item.fullObject.conversion_factor,
            exp_quantity: 0,
            bom: item.fullObject.bom || [],
            uoms: item.fullObject.uoms,
          };
        }
      })
      .filter(Boolean) as TaskItemUsed[];
    setTaskItemUsed([...taskItemUsed, ...selectedItems]);
  };

  const onFinish = async (values: any) => {
    setLoading(true); // Bắt đầu loading
    try {
      // Get selected categories
      const categories: (TaskItemUsed & { item: any })[] = values.categories;
      // Create filters to fetch product details for selected categories
      const filters = [
        ['Item', 'name', 'in', categories.map((category) => category.iot_category_id)],
      ];
      const categoryInfo = await getProductItemV3({ filters });
      console.log('categoryInfo', categoryInfo);
      const newData: IIotWarehouseItemTaskUsed[] = [];

      // Check for duplicate categories
      const duplicateCategories = categories.filter((category: any) =>
        dataSource.some((item: any) => item.iot_category_id === category.iot_category_id),
      );

      if (duplicateCategories.length > 0) {
        // If there are any duplicates, show an error message and don't add the items
        message.error('Vật tư thêm vào đã tồn tại trong danh sách, vui lòng kiểm tra lại');
        setLoading(false); // Kết thúc loading nếu có lỗi
        return;
      }

      for (const category of categories) {
        const fullInfo = categoryInfo.data.find(
          (info: any) => info.name === category.iot_category_id,
        );
        category.item = fullInfo;
        newData.push({
          quantity: 0,
          description: '',
          task_id: params.task_id,
          iot_category_id: category.iot_category_id,
          exp_quantity: category.exp_quantity! * category.conversion_factor! || 0,
          loss_quantity: 0,
          issued_quantity: 0,
          draft_quantity: 0,
          total_qty_in_crop: 0,
        });
      }

      const req = await createTaskItemListSQL(newData);
      const newDataTable = req.map((data: any) => {
        categories.map((category: any) => {
          if (data[0].iot_category_id === category.iot_category_id) {
            data[0].item = category.item;
          }
        });
        return data[0];
      });
      //không cần set datasource vì đã set trong refreshFnc
      // setDataSource([...dataSource, ...newDataTable]);
      params.refreshFnc();
      setOpen(false);
      form.resetFields();
    } catch (error) {
      message.error('Đã có lỗi xảy ra, vui lòng thử lại sau');
    } finally {
      setLoading(false); // Kết thúc loading khi hoàn thành
    }
  };
  const intl = useIntl();
  return (
    <>
      <Space>
        <Button type={params.buttonType} onClick={showModal}>
          <PlusOutlined></PlusOutlined>
          {intl.formatMessage({ id: 'common.add_relative_item' })}
        </Button>
        <Tooltip
          color={COLOR_HEX.GREEN_TOOLTIP}
          key={COLOR_HEX.GREEN_TOOLTIP}
          title={intl.formatMessage({ id: 'tooltips.add_expected_item_quantity_button' })}
        >
          <ExclamationCircleOutlined />
        </Tooltip>
      </Space>
      <Modal
        width={800}
        title={intl.formatMessage({ id: 'common.add_relative_item' })}
        open={isOpen}
        onOk={() => {
          onFinish(form.getFieldsValue());
        }}
        onCancel={handleCancel}
        confirmLoading={loading}
      >
        <Form
          layout="horizontal"
          labelCol={{ span: 24 }}
          labelAlign="left"
          form={form}
          onFinish={onFinish}
        >
          <Row gutter={5}>
            <Col className="gutter-row" md={24}>
              <Item labelCol={{ span: 24 }} name="categories">
                <CategoryQuantitySelector treeData={treeData || []} onCheck={handleSelectChange} />
              </Item>
            </Col>
          </Row>
        </Form>
      </Modal>
    </>
  );
};

export default CreateItemUpdateView;
