import FormUploadsPreviewable from '@/components/FormUploadsPreviewable';
import { ProFormText } from '@ant-design/pro-components';
import { useIntl } from '@umijs/max';
import { Card, Col, Row } from 'antd';
import { FC, ReactNode } from 'react';

interface InfoProps {
  children?: ReactNode;
  image?: string;
}
const w = 'xl';
const Info: FC<InfoProps> = ({ children, image }) => {
  const { formatMessage } = useIntl();
  return (
    <Card
      title={formatMessage({
        id: 'task.detailed_info',
      })}
    >
      <FormUploadsPreviewable
        fileLimit={5}
        label={formatMessage({
          id: 'common.images',
        })}
        formItemName={'image'}
        initialImages={image}
      />
      <Row gutter={24}>
        <Col span={12}>
          {' '}
          <ProFormText
            width={w}
            label={formatMessage({
              id: 'common.name',
            })}
            name="label"
            required
          />
        </Col>
        <Col span={12}>
          {' '}
          <ProFormText
            width={w}
            label={formatMessage({
              id: 'common.note',
            })}
            name="description"
          />
        </Col>
      </Row>
    </Card>
  );
};

export default Info;
