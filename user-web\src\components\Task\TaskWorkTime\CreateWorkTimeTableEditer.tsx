import WorkTypeCategoryCard from '@/components/WorkTaskCategory';
import { DeleteOutlined } from '@ant-design/icons';
import type { ProColumns } from '@ant-design/pro-components';
import { ProTable } from '@ant-design/pro-components';
import { FormattedMessage, useModel } from '@umijs/max';
import { Button } from 'antd';
import React from 'react';
import { v4 } from 'uuid';
import CreateWorkTime from './CreateWorkTime';
import UpdateWorkTime from './UpdateWorkTime';

type DataSourceType = {
  id: React.Key;
  title?: string;
  decs?: string;
  state?: string;
  created_at?: string;
  children?: DataSourceType[];
};

export default ({ dataSource, setDataSource }: { dataSource: any; setDataSource: any }) => {
  const { myWorkType } = useModel('MyWorkType');

  const columns: ProColumns<DataSourceType>[] = [
    {
      title: '<PERSON>ại công việc',
      dataIndex: 'work_type_id',
      render(dom, entity, index, action, schema) {
        return <>{myWorkType.find((el) => el.name === dom)?.label}</>;
      },
    },
    {
      title: 'Số công/giờ làm dự kiến',
      dataIndex: 'exp_quantity',
    },
    {
      title: 'Số công/giờ thực tế',
      dataIndex: 'quantity',
    },
    {
      title: <FormattedMessage id="category.material-management.unit" defaultMessage="unknown" />,
      dataIndex: 'type',
      render: (dom: any, entity: any) => {
        if (dom === 'Workday') return <>Công</>;
        if (dom === 'Hour') return <>Giờ</>;
        return '';
      },
    },
    {
      title: 'Chi phí',
      dataIndex: 'cost',
    },
    {
      title: 'Mô tả',
      dataIndex: 'description',
      formItemProps: {
        rules: [
          {
            required: true,
            whitespace: true,
            message: 'Mô tả không được để trống',
          },
        ],
      },
    },
    {
      title: 'Hành động',
      dataIndex: 'name',
      render: (dom: any, entity: any) => {
        return (
          <>
            <UpdateWorkTime onFinish={handlerEdit} data={entity} />
            <Button
              danger
              size="small"
              style={{ display: 'flex', alignItems: 'center' }}
              onClick={() => handlerRemove(dom)}
            >
              <DeleteOutlined />
            </Button>
          </>
        );
      },
    },
  ];

  const handlerAdd = async (values: any) => {
    try {
      values.name = v4();
      const _newData = dataSource;
      _newData.push(values);
      setDataSource([..._newData]);
    } catch (error) {
      console.log(error);
    }
  };

  const handlerEdit = async (values: any) => {
    try {
      const _newData = dataSource.map((d: any) => {
        return d.name === values.name ? values : d;
      });
      console.log('_newData', _newData);
      setDataSource([..._newData]);
    } catch (error) {
      console.log(error);
    }
  };
  const handlerRemove = async (name: any) => {
    try {
      const _newData = dataSource.filter((d: any) => {
        return d.name !== name;
      });
      setDataSource([..._newData]);
    } catch (error) {
      console.log(error);
    }
  };
  return (
    <>
      <ProTable
        headerTitle="Nhân công dự kiến"
        columns={columns}
        rowKey="name"
        dataSource={[...dataSource]}
        toolBarRender={() => {
          return [
            <WorkTypeCategoryCard key="create-cag" />,
            <CreateWorkTime key="create-new" onFinish={handlerAdd} />,
          ];
        }}
        search={false}
      />
    </>
  );
};
