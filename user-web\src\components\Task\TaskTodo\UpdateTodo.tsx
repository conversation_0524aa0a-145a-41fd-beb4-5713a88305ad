import { getCustomerUserList } from '@/services/customerUser';
import { EditOutlined } from '@ant-design/icons';
import { ProFormSelect } from '@ant-design/pro-components';
import { FormattedMessage } from '@umijs/max';
import { Button, Col, Form, Input, Modal, Row } from 'antd';
import { useState } from 'react';
const { Item } = Form;

const UpdateTodo = (params: { data: any; onFinish: any }) => {
  const [loading, setLoading] = useState(false);
  const [isOpen, setOpen] = useState(false);
  const [form] = Form.useForm();

  const showModal = () => {
    form.setFieldsValue(params.data);
    setOpen(true);
  };

  const hideModal = () => {
    setOpen(false);
  };
  const handleOk = () => {
    form.submit();
  };
  const handleCancel = () => {
    hideModal();
    form.resetFields();
  };

  return (
    <>
      <Button
        size="small"
        //type="dashed"
        style={{ display: 'flex', alignItems: 'center' }}
        onClick={showModal}
      >
        <EditOutlined></EditOutlined>
      </Button>
      <Modal
        title={`Chỉnh sửa công việc con`}
        open={isOpen}
        onOk={handleOk}
        onCancel={handleCancel}
        confirmLoading={loading}
      >
        <Form
          layout="horizontal"
          labelCol={{ span: 24 }}
          labelAlign="left"
          form={form}
          onFinish={async (values: any) => {
            values.name = params.data?.name;
            await params?.onFinish(values);
            setOpen(false);
            form.resetFields();
          }}
        >
          <Row gutter={5}>
            <Col className="gutter-row" md={24}>
              <Item
                label="Tên công việc"
                labelCol={{ span: 24 }}
                rules={[
                  {
                    required: true,
                    message: 'Bắt buộc điền',
                  },
                ]}
                name="label"
              >
                <Input />
              </Item>
            </Col>
            <Col className="gutter-row" md={24}>
              {/* <Item
                label={<FormattedMessage id={'common.form.customer_user_id'} />}
                labelCol={{ span: 24 }}
                name="customer_user_id"
              >
                <Input.TextArea />
              </Item> */}
              <ProFormSelect
                label={'Người thực hiện'}
                showSearch
                name="customer_user_id"
                request={async () => {
                  const result = await getCustomerUserList();
                  return result.data.map((item: any) => {
                    return {
                      label: item.last_name + ' ' + item.first_name,
                      value: item.name,
                    };
                  });
                  //return test options
                  // return [
                  //   { label: 'Nguyễn Văn A', value: 'Nguyễn Văn A' },
                  //   { label: 'Nguyễn Văn B', value: 'Nguyễn Văn B' },
                  //   { label: 'Nguyễn Văn C', value: 'Nguyễn Văn C' },
                  // ];
                }}
              />
            </Col>
            <Col className="gutter-row" md={24}>
              <Item
                label={<FormattedMessage id={'common.form.description'} />}
                labelCol={{ span: 24 }}
                name="description"
              >
                <Input.TextArea />
              </Item>
            </Col>
          </Row>
        </Form>
      </Modal>
    </>
  );
};

export default UpdateTodo;
