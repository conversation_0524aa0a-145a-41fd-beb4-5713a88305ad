import { useIntl } from '@umijs/max';
import { Tabs } from 'antd';
import { FC, useMemo } from 'react';
import Note from '../Note';
import CropPlanDetail from './components/Care';

//create interface
interface LogsProps {
  cropId?: string;
  cacheKey?: string;
}
const Logs: FC<LogsProps> = ({ cropId, cacheKey }) => {
  const intl = useIntl();
  const sections = useMemo(
    () => [
      {
        label: intl.formatMessage({ id: 'common.task' }),
        key: 'care',
        children: <CropPlanDetail />,
      },
      {
        label: intl.formatMessage({ id: 'seasonalTab.note' }),
        key: 'note',
        children: <Note cropId={cropId} />,
      },
    ],
    [cropId],
  );
  return (
    <div
      style={{
        display: 'flex',
        flexDirection: 'column',
        gap: 16,
      }}
    >
      {/* {sections.map((item) => (
        <Card
          bodyStyle={{
            paddingTop: 2,
            paddingBottom: 5,
          }}
          key={item.title}
          title={
            <Typography.Title style={{ marginBottom: 0 }} level={4}>
              {item.title}
            </Typography.Title>
          }
        >
          {item.content}
        </Card>
      ))} */}
      <Tabs defaultActiveKey="1" type="card" size={'small'} items={sections} />
    </div>
  );
};

export default Logs;
