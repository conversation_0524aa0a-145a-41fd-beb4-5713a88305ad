import FormUploadsPreviewable from '@/components/FormUploadsPreviewable';
import { useIntl } from '@umijs/max';
import { Card } from 'antd';
import { FC, ReactNode } from 'react';

interface ImgVideosProps {
  children?: ReactNode;
  initialValue?: any;
}

const ImgVideos: FC<ImgVideosProps> = ({ children, initialValue }) => {
  const { formatMessage } = useIntl();
  return (
    <Card title={formatMessage({ id: 'common.image_preview' })}>
      <FormUploadsPreviewable
        label={''}
        fileLimit={10}
        formItemName={'image'}
        initialImages={initialValue}
      />
    </Card>
  );
};

export default ImgVideos;
