import { ProForm } from '@ant-design/pro-components';
import { history, useLocation } from '@umijs/max';
import { FC, ReactNode, useEffect } from 'react';
import useCreate from '../../hooks/useCreate';
import DetailedInfo from './DetailedInfo';
import Task from './Task';

interface StageOfCropCreateProps {
  children?: ReactNode;
  onSuccess?: () => void;
  setIsFormDirty: (dirty: boolean) => void;
}

const StageOfCropCreate: FC<StageOfCropCreateProps> = ({
  children,
  onSuccess,
  setIsFormDirty = () => {},
}) => {
  const location = useLocation();
  const state: any = location.state;
  const { run } = useCreate({
    onSuccess: () => {
      if (state?.fromProcedureCreate) {
        history.replace('/farming-diary-static/procedure/create');
      }
      if (state?.fromProcedureEdit) {
        history.replace(`/farming-diary-static/procedure/edit/${state.id}`);
      }
      onSuccess && onSuccess();
    },
  });
  const [form] = ProForm.useForm();

  useEffect(() => {
    form.setFieldsValue({});
    setIsFormDirty(false);
  }, []);

  return (
    <ProForm
      form={form}
      onValuesChange={() => setIsFormDirty(true)}
      submitter={{
        render: (_, dom) => {
          return (
            <div style={{ textAlign: 'right', margin: 24 }}>
              {dom.map((item, index) => (
                <span key={index} style={{ marginRight: index === 0 ? 8 : 0 }}>
                  {item}
                </span>
              ))}
            </div>
          );
        },
      }}
      onFinish={async (values: any) => {
        await run({
          label: values.label,
          description: values.description,
          image: values.image,
          task_count: values.tasks ? values.tasks.length : 0,
          is_deleted: 0,
          expire_time_in_days: values.expire_time_in_days || 0,
          tasks: values.tasks
            ? values?.tasks.map((task: any) => ({
                name: task.name,
                idx: task.idx || 0,
              }))
            : [],
        });
        setIsFormDirty(false);
        return true;
      }}
    >
      <div className="space-y-4 mb-4">
        <DetailedInfo />
        <Task />
      </div>
    </ProForm>
  );
};

export default StageOfCropCreate;
