import { DOCTYPE_ERP } from '@/common/contanst/constanst';
import FallbackComponent from '@/components/FallbackContent';
import { getFarmingPlanList } from '@/services/farming-plan';
import { ActionType,ProList } from '@ant-design/pro-components';
import { Access,useAccess } from '@umijs/max';

import { useDebounceFn } from 'ahooks';
import { Card,Col,Input,List,Row,Space } from 'antd';
import { FC,ReactNode,useRef,useState } from 'react';
import CreateCropPlan from '../Create';
import GeneralPlanCard from './GeneralPlanCard';


interface CropPlanProps {
  children?: ReactNode;
}

const CropPlanList: FC<CropPlanProps> = ({ children }) => {
  const [searchPlan, setSearchPlan] = useState('');
  const actionRef = useRef<ActionType>(null);
  const handleReload = () => {
    actionRef.current?.reload();
  };
 const { run: handleSearch } = useDebounceFn((e: React.ChangeEvent<HTMLInputElement>) => {
   setSearchPlan(e.target.value);
   handleReload();
 },{
  wait: 400
 });


  const access = useAccess();
  const canCreatePlan = access.canCreateInPlanManagement();

  return (
    <Access accessible={access.canAccessPagePlanManagement()} fallback={<FallbackComponent />}>
      <Space direction="vertical" size="middle" style={{ display: 'flex' }}>
        <Card bordered>
          <Row justify={'space-between'} gutter={16} align="middle">
            <Col span={8} flex={'1 0 25%'}>
              <Input addonBefore="Tên kế hoạch" onChange={handleSearch} />
            </Col>
            <Col span={8} style={{ textAlign: 'right' }}>
              {canCreatePlan && <CreateCropPlan onSuccess={handleReload} />}
            </Col>
          </Row>
        </Card>
        <ProList
          actionRef={actionRef}
          grid={{
            column: 3,
            gutter: 10,
            md: 3,
            sm: 3,
            xs: 1,
          }}
          pagination={{
            pageSize: 20,
          }}
          rowKey={'name'}
          // dataSource={data as any}
          request={async (params) => {
            const filters = searchPlan
              ? `[["${DOCTYPE_ERP.iotFarmingPlan}", "label", "like", "%${searchPlan}%"]]`
              : `[]`;

            const order_by = 'start_date DESC';
            const requestData = await getFarmingPlanList({
              page: params.current,
              size: params.pageSize,
              filters,
              order_by,
            });

            return { data: requestData.data, total: requestData.pagination.totalElements };
          }}
          renderItem={(item: any) => (
            <List.Item>
              <GeneralPlanCard {...item} />
            </List.Item>
          )}
        />
      </Space>
    </Access>
  );
};

export default CropPlanList;
