import { PageContainer } from '@ant-design/pro-components';
import { FC, ReactNode } from 'react';
import CreateProdProcedure from './components/Create';

interface CreateProdProcedurePageProps {
  children?: ReactNode;
}

const CreateProdProcedurePage: FC<CreateProdProcedurePageProps> = ({ children }) => {
  return (
    <PageContainer>
      <CreateProdProcedure />
    </PageContainer>
  );
};

export default CreateProdProcedurePage;
