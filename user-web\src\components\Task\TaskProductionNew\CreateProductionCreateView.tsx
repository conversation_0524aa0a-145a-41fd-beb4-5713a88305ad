import { getItemByGroup } from '@/services/InventoryManagementV3/product-item';
import { TaskProduction, useTaskProductionCreateStore } from '@/stores/TaskProductionCreateStore';
import { PlusOutlined } from '@ant-design/icons';
import { useIntl } from '@umijs/max';
import { Button, Col, Form, Modal, Row } from 'antd';
import { useEffect, useState } from 'react';
import ProductionQuantitySelector from './ProductionQuantitySelector';
const { Item } = Form;

interface ITreeNode {
  title: string;
  value: string;
  key: string;
  normalized_title: string;
  children?: ITreeNode[];
  fullObject?: any;
}

const CreateProductionCreateView = () => {
  const [loading, setLoading] = useState(false);
  const [isOpen, setOpen] = useState(false);
  const [treeData, setTreeData] = useState<ITreeNode[]>([]);
  const { setTaskProduction } = useTaskProductionCreateStore();

  const intl = useIntl();

  useEffect(() => {
    const fetchData = async () => {
      const dataGroup = await getItemByGroup({});
      if (dataGroup.data) {
        setTreeData(
          dataGroup.data.map((item) => ({
            title: item.item_group_label,
            value: item.item_group,
            key: item.item_group,
            normalized_title: item.item_group_label
              .toLowerCase()
              .normalize('NFD')
              .replace(/[\u0300-\u036f]/g, ''),
            children: item.item.map((child) => ({
              title: child.label || '',
              value: child.name || '',
              key: child.name || '',
              normalized_title: (child.label || '')
                .toLowerCase()
                .normalize('NFD')
                .replace(/[\u0300-\u036f]/g, ''),
              fullObject: child,
            })),
          })),
        );
      }
    };
    fetchData();
  }, []);

  const showModal = () => setOpen(true);
  const hideModal = () => setOpen(false);

  const handleOk = () => setOpen(false);

  const findNodeByKey = (key: string, data: ITreeNode[]): ITreeNode | undefined => {
    for (const node of data) {
      if (node.key === key) return node;
      if (node.children) {
        const found = findNodeByKey(key, node.children);
        if (found) return found;
      }
    }
  };

  const handleSelectChange = (selectedWithBOM: React.Key[], selectedWithoutBOM: React.Key[]) => {
    const combinedSelected = [...selectedWithBOM, ...selectedWithoutBOM];
    const selectedItems = combinedSelected
      .map((key) => {
        const item = findNodeByKey(key.toString(), treeData);
        if (item && !item.children) {
          return {
            product_id: key.toString(),
            item_name: item.fullObject.item_name,
            label: item.title,
            uom: item.fullObject.uom,
            uom_label: item.fullObject.uom_label,
            conversion_factor: item.fullObject.conversion_factor,
            exp_quantity: 0,
            bom: item.fullObject.bom || [],
            uoms: item.fullObject.uoms,
          };
        }
      })
      .filter(Boolean) as TaskProduction[];
    setTaskProduction([...selectedItems]);
  };

  return (
    <>
      <Button type="primary" style={{ display: 'flex', alignItems: 'center' }} onClick={showModal}>
        <PlusOutlined />
        {intl.formatMessage({ id: 'common.add_production' })}
      </Button>
      <Modal
        width={800}
        title={intl.formatMessage({ id: 'common.add_production' })}
        open={isOpen}
        onOk={handleOk}
        onCancel={hideModal}
        confirmLoading={loading}
      >
        <Row gutter={5}>
          <Col className="gutter-row" md={24}>
            <Item labelCol={{ span: 24 }} name="products">
              <ProductionQuantitySelector treeData={treeData || []} onCheck={handleSelectChange} />
            </Item>
          </Col>
        </Row>
      </Modal>
    </>
  );
};

export default CreateProductionCreateView;
