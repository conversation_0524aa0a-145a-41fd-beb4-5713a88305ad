import {
  getCropIndividualWorksheetStatistic,
  getCropStatisticWorksheet,
} from '@/services/cropStatistic';
import { ICropStatisticWorksheet } from '@/types/cropStatisticWorksheet.type';
import { EyeOutlined } from '@ant-design/icons';
import { ActionType, ProColumns, ProTable } from '@ant-design/pro-components';
import { App, Button } from 'antd';
import moment from 'moment';
import { useEffect, useRef, useState } from 'react';
import { IFilter } from '../CustomizeInfoCard';
import PopupStatisticChart from '../PopupStatisticChart';
import RealityColumnRender from '../RealityColumnRender';
export interface IData {
  labels: string[];
  exp_quantities: number[];
  quantities: number[];
}
interface Props {
  filter: IFilter;
}
const CropWorksheetTable = ({ filter }: Props) => {
  const { message } = App.useApp();
  const actionRef = useRef<ActionType>();
  const [currentItem, setCurrentItem] = useState<ICropStatisticWorksheet>();
  const [isModalOpen, setIsModalOpen] = useState(false);
  const queryingData = async (record: ICropStatisticWorksheet, filter: IFilter) => {
    const res = await getCropIndividualWorksheetStatistic({
      cropList: filter.cropList,
      work_type: record?.work_type_id || '',
      end_date: filter.end_date,
      start_date: filter.start_date,
      type: filter.type,
      group_by: 'work_type_id',
    });
    const data = res?.data.reduce(
      (acc, ele) => {
        acc.labels.push(moment(ele.interval_start).format('DD/MM/YYYY'));
        acc.exp_quantities.push(Number(ele.total_exp_quantity));
        acc.quantities.push(Number(ele.total_quantity));

        return acc;
      },
      { labels: [], exp_quantities: [], quantities: [] } as IData,
    );
    if (data) {
      const formattedData = {
        labels: data.labels,
        datasets: [
          {
            type: 'bar' as const,
            label: 'Giá trị dự kiến',
            data: data.exp_quantities,
            backgroundColor: '#FFD859',
          },
          {
            type: 'bar' as const,
            label: 'Giá trị thực tế',
            data: data.quantities,
            backgroundColor: '#44C4A1',
          },
        ],
      };
      return {
        generalInfos: res.sum,
        selectedData: formattedData,
      };
    }
    return {
      generalInfos: undefined,
      selectedData: undefined,
    };
  };
  const handlePopup = async (e: ICropStatisticWorksheet) => {
    setIsModalOpen(true);
    setCurrentItem(e);
  };
  const columns: ProColumns<ICropStatisticWorksheet>[] = [
    {
      title: 'Xem chi tiết',
      dataIndex: 'quantity',
      render(dom, entity, index, action, schema) {
        return <Button icon={<EyeOutlined />} onClick={() => handlePopup(entity)}></Button>;
      },
      search: false,
      width: 80,
    },
    {
      title: 'Công việc',
      dataIndex: 'label',
      width: 200,
    },

    {
      title: 'Tổng chi phí',
      dataIndex: 'total_cost',
      width: 130,
    },
    {
      title: 'Tổng dự kiến',
      dataIndex: 'total_exp_quantity',
      width: 130,
    },
    {
      title: 'Tổng thực tế',
      dataIndex: 'total_quantity',
      width: 130,
      render(dom, entity, index, action, schema) {
        return <RealityColumnRender>{dom}</RealityColumnRender>;
      },
    },
  ];
  useEffect(() => {
    actionRef.current?.reload();
  }, [filter]);

  return (
    <>
      <PopupStatisticChart
        currentItem={currentItem}
        inputFilter={filter}
        isModalOpen={isModalOpen}
        queryingData={queryingData}
        setIsModalOpen={setIsModalOpen}
        title={`Thống kê nhân công - ${currentItem?.label}`}
      />
      <ProTable<ICropStatisticWorksheet>
        size="small"
        headerTitle="Thống kê nhân công"
        columns={columns}
        search={false}
        actionRef={actionRef}
        pagination={false}
        rowKey={'key'}
        request={async (params) => {
          try {
            const res = await getCropStatisticWorksheet({
              page: 1,
              size: 1000,
              cropList: filter.cropList,
              end_date: filter.end_date,
              start_date: filter.start_date,
              type: filter.type,
            });
            return {
              data: res.data.map((item, index) => ({
                ...item,
                key: index,
              })),
              success: true,
            };
          } catch (error: any) {
            message.error(`Error when getting Crop Statistic Items: ${error.message}`);
            return {
              success: false,
            };
          }
        }}
      />
    </>
  );
};

export default CropWorksheetTable;
