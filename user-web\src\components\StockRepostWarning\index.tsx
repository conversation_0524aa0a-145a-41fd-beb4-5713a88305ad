import { WarningOutlined } from '@ant-design/icons';
import { Alert } from 'antd';
import React from 'react';

type IStockRepostWarningProps = {
  type: 'sleReCal' | 'fullRecal';
};

const StockRepostWarning: React.FC<IStockRepostWarningProps> = ({ type }) => {
  const messages = {
    sleReCal: `Lưu ý: Số liệu tồn kho đang được tính toán lại và có thể chưa phản ánh chính xác tình trạng hiện tại. 
            Vui lòng làm mới trang sau vài giây để xem dữ liệu mới nhất.`,
    fullRecal: `Lưu ý: Số liệu tồn kho được tính lại vào 00h00 hàng ngày và có thể chưa phản ánh chính xác tình trạng hiện tại.`,
  };

  return (
    <Alert
      message={
        <div style={{ display: 'flex', alignItems: 'center', gap: '8px' }}>
          <WarningOutlined />
          <span>{messages[type]}</span>
        </div>
      }
      type="warning"
      showIcon={false}
      style={{ marginBottom: 24 }}
    />
  );
};

export default StockRepostWarning;
