import { ProForm } from '@ant-design/pro-components';
import { history, useIntl, useLocation } from '@umijs/max';
import { FC, ReactNode, useEffect } from 'react';
import useCreate from '../../hooks/useCreate';
import Attachment from './Attachment';
import DetailedInfo from './DetailedInfo';

interface StageOfCountEditProps {
  children?: ReactNode;
  onSuccess?: () => void;
  setIsFormDirty: (dirty: boolean) => void;
}

const NoteCreate: FC<StageOfCountEditProps> = ({
  children,
  onSuccess,
  setIsFormDirty = () => {},
}) => {
  const { formatMessage } = useIntl();
  const location = useLocation();
  const state: any = location.state;
  const { run } = useCreate({
    onSuccess: () => {
      if (state?.fromProcedureCreate) {
        history.replace('/farming-diary-static/procedure/create');
      }
      if (state?.fromProcedureEdit) {
        history.replace(`/farming-diary-static/procedure/edit/${state.id}`);
      }
      onSuccess && onSuccess();
    },
  });
  useEffect(() => {
    setIsFormDirty(false);
  }, []);
  return (
    <ProForm
      submitter={{
        searchConfig: {
          // resetText: formatMessage({ id: 'common.reset' }),
          // submitText: formatMessage({ id: 'common.submit' }),
        },
        render: (_, dom) => {
          return (
            <div style={{ textAlign: 'right', margin: 24 }}>
              {dom.map((item, index) => (
                <span key={index} style={{ marginRight: index === 0 ? 8 : 0 }}>
                  {item}
                </span>
              ))}
            </div>
          );
        },
      }}
      onValuesChange={() => setIsFormDirty(true)}
      onFinish={async (values) => {
        await run({
          // name: string;
          label: values.label,
          description: values.description,
          image: values.image,
          is_deleted: 0,
          file: values.file,
          product_id: values.product_id,
          states: [
            {
              name: values.state_id,
            },
          ],
        });
        onSuccess?.();
        setIsFormDirty(false);
        return true;
      }}
    >
      <div className="mb-4 space-y-4">
        <DetailedInfo />
        {/* <Task /> */}
        <Attachment />
      </div>
    </ProForm>
  );
};

export default NoteCreate;
