import { getCrop, getCropPest } from '@/services/crop';
import { IIotPest } from '@/types/IIotPest';
import { FormattedMessage } from '@umijs/max';
import { Card, Col, Form, Input, Row } from 'antd';
import { FC, useEffect, useState } from 'react';
import ImageCropNote from './Image';

const { Item } = Form;
interface DetailCropPestTabProps {
  cropPestID: string;
}
const DetailCropPestTab: FC<DetailCropPestTabProps> = (props) => {
  const [loading, setLoading] = useState(false);
  const [cropPest, setCropPest] = useState<Partial<IIotPest>>({});
  const [cropList, setCropList] = useState<any[]>([]);
  const [imageLinks, setImageLinks] = useState<any[]>([]);
  const [form] = Form.useForm();

  const cropPestName = props.cropPestID;

  const renderImageCropPestLayout = () => {
    const ImageCropNoteComponents: JSX.Element[] = [];
    let rowImages: string[] = [];

    imageLinks.forEach((imageLink, index) => {
      rowImages.push(imageLink);

      if ((index + 1) % 4 === 0 || index === imageLinks.length - 1) {
        // When we have 4 images in the row or we have reached the last image
        const ImageCropNoteRow = (
          <Row className="gutter-row" gutter={4} key={`row_${index}`}>
            {rowImages.map((image, idx) => (
              <Col className="gutter-row" key={`col_${index}_${idx}`}>
                <ImageCropNote imageLink={image} index={idx} />
              </Col>
            ))}
          </Row>
        );
        ImageCropNoteComponents.push(ImageCropNoteRow);
        rowImages = [];
      }
    });

    return ImageCropNoteComponents;
  };

  const initCropNote = async () => {
    try {
      setLoading(true);
      let [params, sort, filters]: any[] = ['', '', [['iot_pest', 'name', 'like', cropPestName]]];
      const res: any = await getCropPest({
        page: 1,
        size: 1000,
        fields: ['*'],
        filters: JSON.stringify(filters),
        or_filters: [],
        order_by: '',
        group_by: '',
      });
      let data: any = res.data.data;
      console.log('receive pest', data[0]);
      setCropPest(data[0]);
      if (data[0].image) {
        setImageLinks(data[0].image.split(','));
      }
      form.setFieldsValue(data[0]);
    } catch (error) {
      console.log(error);
    } finally {
      setLoading(false);
    }
  };

  const initCropList = async () => {
    try {
      setLoading(true);
      // let [params, sort, filters]: any[] = ['', '', [["iot_Crop_note", "name", "like", cropPestName]]];
      const res: any = await getCrop({
        page: 1,
        size: 1000,
        fields: ['*'],
        filters: JSON.stringify([]),
        or_filters: [],
        order_by: '',
        group_by: '',
      });
      let data: any = res.data.data;
      console.log('receive data', data);
      setCropList(data);
    } catch (error) {
      console.log(error);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    initCropNote();
    initCropList();
  }, [cropPestName]);

  return (
    <Card
      title={cropPest ? cropPest.name : 'Loading...'} // Hoặc "N/A" hoặc giá trị mặc định khác tuỳ ý
      loading={loading}
    >
      <Form size="small" layout="horizontal" labelCol={{ span: 24 }} labelAlign="left" form={form}>
        <Row gutter={5}>
          {/* <Col className="gutter-row" md={8}  >
                        <Item
                            label="ID"
                            labelCol={{ span: 24 }}
                            name="name"

                        >
                            <Input disabled />
                        </Item>
                    </Col> */}

          <Col className="gutter-row" md={8}>
            <Item
              label="Tên dịch hại"
              labelCol={{ span: 24 }}
              name="label"
              style={{ marginBottom: '16px', fontWeight: 'bold' }} // Add the fontWeight property here
            >
              <Input readOnly />
            </Item>
          </Col>
          <Col className="gutter-row" md={8}>
            <Item
              label="Tên vụ mùa"
              labelCol={{ span: 24 }}
              name="crop_name"
              style={{ marginBottom: '16px', fontWeight: 'bold' }} // Add the fontWeight property here
            >
              <Input readOnly />
              {/* <ProFormSelect showSearch options={cropList.map((crop: any) => ({ value: crop.name, label: crop.label }))}></ProFormSelect> */}
            </Item>
          </Col>
        </Row>
        <Row gutter={5}>
          <Col className="gutter-row" md={24}>
            <Item
              label="Nội dung ghi chú dịch hại"
              labelCol={{ span: 24 }}
              name="description"
              style={{ marginBottom: '16px', fontWeight: 'bold' }} // Add the fontWeight property here
            >
              <Input.TextArea readOnly />
            </Item>
          </Col>
        </Row>
        <Col className="gutter-row">
          <Item
            label={<FormattedMessage id={'common.form.image'} />}
            labelCol={{ span: 24 }}
            name="image"
            style={{ marginBottom: '16px', fontWeight: 'bold' }} // Add the fontWeight property here
          >
            {renderImageCropPestLayout()}
          </Item>
        </Col>
      </Form>
    </Card>
  );
};
export default DetailCropPestTab;
