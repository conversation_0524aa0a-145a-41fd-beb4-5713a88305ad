import { ProFormGroup, ProFormList, ProFormSelect, ProFormText } from '@ant-design/pro-components';
import { Card } from 'antd';
import React, { FC,ReactNode } from 'react'

interface MaterialUseProps {
  children?:ReactNode;

}
const w = 'md'
const MaterialUse: FC<MaterialUseProps> = ({ children,  }) => {
  return (
    <Card title="Vật tư sử dụng">
      <ProFormList
        name="pest"
        creatorButtonProps={{
          creatorButtonText: 'Thêm',
        }}
        alwaysShowItemLabel
      >
        {() => {
          return (
            <ProFormGroup>
              <ProFormSelect width={w} label="Chọn vật tư" name="material" />
              <ProFormText width={w} label="Khối lượng sử dụng" name="label" />
              <ProFormSelect width={w} label="Đơn vị" name="unit" />
            </ProFormGroup>
          );
        }}
      </ProFormList>
    </Card>
  );
}

export default MaterialUse;
