import ProTableCommon from '@/components/ProTableCommon';
import { getCropNote } from '@/services/crop';
import { ProColumns } from '@ant-design/pro-components';
import { FormattedMessage, useModel } from '@umijs/max';
import { Image, Table, Tooltip } from 'antd';
import React, { FC, ReactNode, useEffect, useState } from 'react';

interface CropNoteListTableProps {
  children?: ReactNode;
  genLinkDetail?: (itemId: string) => string;
  keepSearchParams?: boolean;
}

type IDataTable = {
  id: string;
  name: string;
  executor: string;
  project: string;
  area: string;
  content: string;
  image: string;
  startDate: string;
  endDate: string;
};

const CropNoteListTable: FC<CropNoteListTableProps> = ({ genLinkDetail, keepSearchParams }) => {
  const { selectedCrop, setSelectedCrop } = useModel('MyCrop');

  const [dataReal, setDataReal] = useState<IDataTable[]>([]);
  const [expandedContentIds, setExpandedContentIds] = useState<string[]>([]);
  const formatDateTime = (dateTimeString: string) => {
    const dateTime = new Date(dateTimeString);
    if (isNaN(dateTime.getTime())) {
      return '';
    }

    const time = dateTime.toLocaleTimeString('en-GB', { hour12: false });
    const date = dateTime.toLocaleDateString('en-GB', {
      day: '2-digit',
      month: '2-digit',
      year: 'numeric',
    });

    return `${time}, ${date}`;
  };
  useEffect(() => {
    const fetchData: any = async () => {
      let [params, sort, filters]: any[] = [
        '',
        '',
        [['iot_Crop_note', 'crop', 'like', selectedCrop.name]],
      ];
      const res: any = await getCropNote({
        page: 1,
        size: 1000,
        fields: ['*'],
        filters: JSON.stringify(filters),
        or_filters: [],
        order_by: '',
        group_by: '',
      });
      setDataReal(res.data.data);
    };
    fetchData();
  }, [selectedCrop]);

  const handleExpandContent = (id: string) => {
    if (expandedContentIds.includes(id)) {
      setExpandedContentIds((prevIds: any) => prevIds.filter((prevId: any) => prevId !== id));
    } else {
      setExpandedContentIds((prevIds: any) => [...prevIds, id]);
    }
  };

  const columns: ProColumns<IDataTable>[] = [
    {
      title: 'STT',
      dataIndex: 'index',
      renderText(_text, record, index, _action) {
        return index + 1;
      },
    },
    {
      title: 'Tên ghi chú',
      dataIndex: 'label',
    },
    {
      title: 'Tên mùa vụ',
      dataIndex: 'crop_name',
    },
    {
      title: 'Nội dung ghi chú',
      dataIndex: 'note',
      render: (text: any, record, index, action) => {
        const isExpanded = expandedContentIds.includes(record.id);
        const truncatedContent = text.length > 50 ? text.substring(0, 50) + '...' : text;

        return (
          <div style={{ maxHeight: isExpanded ? 'none' : '60px', overflow: 'hidden' }}>
            <Tooltip title={text}>{isExpanded ? text : truncatedContent}</Tooltip>
            {text.length > 50 && (
              <span
                style={{ color: '#1890ff', cursor: 'pointer', marginLeft: '8px' }}
                onClick={() => handleExpandContent(record.id)}
              >
                {isExpanded ? 'Rút gọn' : 'Xem thêm'}
              </span>
            )}
          </div>
        );
      },
    },
    {
      title: <FormattedMessage id="common.form.image" defaultMessage="Image" />,
      dataIndex: 'image',
      render: (text: any, record, index, action) => (
        <Image
          src={'https://iot.viis.tech/api/v2/file/download?file_url=' + text}
          width={30}
          height={30}
          preview={{
            mask: 'View',
          }}
        />
        // <Tooltip title={text} overlayStyle={{ display: 'flex', alignItems: 'center' }}>
        // </Tooltip>
      ),
    },
    {
      title: 'Ngày tạo ghi chú',
      dataIndex: 'creation',
      render: (text: any, record, index, action) => formatDateTime(text),
    },
    {
      title: 'Lần cuối chỉnh sửa',
      dataIndex: 'modified',
      render: (text: any, record, index, action) => formatDateTime(text),
    },
  ];

  const [selectedRows, setSelectedRows] = useState<string[]>([]);
  const handleSelectRows = (selectedRowKeys: React.Key[], selectedRows: IDataTable[]) => {
    // Update the selected rows state when the checkboxes are clicked
    setSelectedRows(selectedRowKeys as string[]);
  };
  const rowSelection = {
    selectedRowKeys: selectedRows,
    onChange: handleSelectRows,
    selections: [Table.SELECTION_ALL, Table.SELECTION_INVERT, Table.SELECTION_NONE],
  };
  return (
    <ProTableCommon
      rowSelection={rowSelection}
      dataSource={dataReal}
      headerTitle="Danh sách ghi chú mùa vụ"
      columns={columns}
    />
  );
};

export default CropNoteListTable;
