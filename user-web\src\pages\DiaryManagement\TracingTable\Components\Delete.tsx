import ActionPopConfirm from '@/components/ActionPopConfirm';
import { DeleteFilled } from '@ant-design/icons';
import { Form, message } from 'antd';
const { Item } = Form;

import { deleteCropTracing } from '@/services/tracing';
import { IIotTraceability } from '@/types/IIotTraceability';

const Delete = (params: { refreshFnc: any; value: IIotTraceability }) => {
  const removeData = async () => {
    try {
      await deleteCropTracing(params.value.name);
    } catch (error: any) {
      message.error(error.toString());
    }
  };

  return (
    <ActionPopConfirm
      actionCall={removeData}
      refreshData={params.refreshFnc}
      text={<DeleteFilled />}
      //buttonType={'dashed'}
      danger={true}
      size="small"
    ></ActionPopConfirm>
  );
};

export default Delete;
