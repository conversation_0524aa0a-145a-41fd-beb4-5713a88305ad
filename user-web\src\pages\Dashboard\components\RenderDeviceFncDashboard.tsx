import { sscript } from '@/services/sscript';
import { PageContainer } from '@ant-design/pro-components';
import { useModel } from '@umijs/max';
import { <PERSON><PERSON>, Card, Col, ConfigProvider, DatePicker, Input, message, Result, Row, Select, Spin, Tabs } from 'antd';
import React, { useEffect, useState } from 'react';
import RenderDeviceFncTabs from './RenderDeviceFncTabs';

const timeTypeOption = [
    {
        value: "5p-now",
        label: "5 phút trước - hiện tại"
    },
    {
        value: "1h-now",
        label: "1h trước - hiện tại"
    },
    {
        value: "2h-now",
        label: "2h trước - hiện tại"
    },
    {
        value: "1d-now",
        label: "Ngày hôm trước - hiện tại"
    },
    {
        value: "custom",
        label: "Tuỳ chỉnh"
    }
];

const RenderDeviceFncDashboard: React.FC = () => {
    const {
        deviceFunction
    } = useModel('MyResource');

    const { initialState, setInitialState } = useModel('@@initialState');

    useEffect(() => {
        if (deviceFunction) {
            console.log("deviceFunction", deviceFunction);
        }
    }, [deviceFunction]);

    useEffect(() => {
        setInitialState({ ...initialState, collapsed: true });
    }, []);

    return (
        <Row gutter={[5, 5]}>
            <Col md={24}>
                <Card>
                    <RenderDeviceFncTabs deviceFunction={deviceFunction} />
                </Card>
            </Col>
        </Row>
    );
};

export default RenderDeviceFncDashboard;
