// import { WSS_URL_DEV, WSS_URL_PROD } from '@/common/contanst/index';
// import { useModel } from '@umijs/max';
// import { Modal } from 'antd';
// import mqtt, { OnConnectCallback, OnMessageCallback } from 'mqtt';
// import { useEffect, useMemo } from 'react';

// export default () => {
//   const connectUrl = process.env.NODE_ENV === 'production' ? WSS_URL_PROD : WSS_URL_DEV;
//   const { initialState } = useModel('@@initialState');
//   const currentUser = initialState?.currentUser;
//   const userdata = JSON.parse(localStorage.getItem('token') || '{}');

//   const options = {
//     clean: true, // retain session
//     connectTimeout: 4000, // Timeout period
//     reconnectPeriod: 4000,
//     // Authentication information
//     clientId: 'tungtest' + Math.random().toString(16),
//     username: 'admin',
//     password: userdata?.accessToken || '',
//   };

//   const client = useMemo(() => mqtt.connect(connectUrl, options), []);

//   const mqttConnect: OnConnectCallback = () => {
//     console.log('MQTT client connected');
//     client.subscribe('viis/web-update', function (err) {
//       if (err) {
//         console.log('subscribe', 'error:', err);
//       }
//     });
//   };

//   const handleMessageMQTT: OnMessageCallback = (topic, message) => {
//     const data = JSON.parse(message.toString());
//     console.log('handleMessageMQTT data status update', data);
//     if (data.update) {
//       Modal.confirm({
//         cancelButtonProps: { style: { display: 'none' } },
//         title: 'Cập nhật mới!',
//         content: 'Đã có bản cập nhật mới, vui lòng tải lại trang để cập nhật!',
//         centered: true,
//         onOk: () => window.location.reload(),
//         okButtonProps: {
//           type: 'primary',
//           style: { backgroundColor: '#44c4a1', borderColor: '#44c4a1' },
//         },
//       });
//     }
//   };

//   const initMqtt = () => {
//     if (!currentUser) return;
//     if (!client) return;

//     client.on('connect', mqttConnect);
//     client.on('message', handleMessageMQTT);
//     client.on('close', () => {
//       // console.log('MQTT connection closed');
//       reconnect();
//     });
//     client.on('error', (error) => {
//       console.log('MQTT error:', error);
//     });
//   };
//   let isReconnecting = false;


//     client.on('connect', mqttConnect);
//     client.on('message', handleMessageMQTT);
//     client.on('close', () => {
//       console.log('MQTT connection closed');
//       reconnect();
//     });
//     client.on('error', (error) => {
//       console.log('MQTT error:', error);
//     });
//   };
//   let isReconnecting = false;

//   const reconnect = () => {
//     if (isReconnecting) return; // If the client is already reconnecting, do nothing

//     console.log('Attempting to reconnect...');
//     isReconnecting = true; // Set the flag to true

//     client.end(); // Close the current connection
//     const newClient = mqtt.connect(connectUrl, options); // Create a new MQTT client

//     newClient.on('connect', () => {
//       console.log('MQTT client connected');
//       isReconnecting = false; // Reset the flag when the client successfully reconnects
//       newClient.subscribe('viis/web-update', function (err) {
//         if (err) {
//           console.log('subscribe', 'error:', err);
//         }
//       });
//     });

//     newClient.on('message', handleMessageMQTT);
//     newClient.on('close', () => {
//       console.log('MQTT connection closed');
//       reconnect();
//     });
//     newClient.on('error', (error) => {
//       console.log('MQTT error:', error);
//     });
//   };
//   const clearEffect = () => {
//     console.log('clearEffect');
//     client?.removeAllListeners?.();
//   };

//   useEffect(() => {
//     initMqtt();
//     return () => {
//       clearEffect();
//     };
//   }, [client, currentUser]);

//   return {
//     mqttClient: client,
//   };
// };
