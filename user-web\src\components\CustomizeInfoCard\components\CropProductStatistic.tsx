import CropProductTable from '@/components/CropProductTable';
import SimpleMultitypeChart from '@/components/SimpleMultitypeChart';
import { getCropIndividualProductStatistic } from '@/services/cropStatistic';
import { Alert } from 'antd';
import { ChartData } from 'chart.js';
import moment from 'moment';
import { useEffect, useState } from 'react';
import { IFilter } from '..';

interface Props {
  filter: IFilter;
}
export interface IData {
  labels: string[];
  exp_quantities: number[];
  quantities: number[];
}
const CropProductStatistic = ({ filter }: Props) => {
  const [selectedQuery, setSelectedQuery] = useState<{
    product_id: string;
    product_label: string;
    unit_label: string;
  }>();
  const [selectedData, setSelectedData] = useState<ChartData<'bar' | 'line', number[], string>>();

  const queryingData = async () => {
    if (!selectedQuery) {
      return;
    }
    const res = await getCropIndividualProductStatistic({
      cropList: filter.cropList,
      product_id: selectedQuery?.product_id || '',
      end_date: filter.end_date,
      start_date: filter.start_date,
      type: filter.type,
    });
    const data = res?.reduce(
      (acc, ele) => {
        acc.labels.push(moment(ele.interval_start).format('DD/MM/YYYY'));
        acc.exp_quantities.push(Number(ele.total_exp_quantity));
        acc.quantities.push(Number(ele.total_quantity));

        return acc;
      },
      { labels: [], exp_quantities: [], quantities: [] } as IData,
    );
    if (data) {
      const formattedData: ChartData<'bar' | 'line', number[], string> = {
        labels: data.labels,
        datasets: [
          {
            type: 'line' as const,
            label: 'Giá trị dự kiến',
            data: data.exp_quantities,
            borderColor: 'rgb(255, 99, 132)',
            borderWidth: 2,
            fill: false,
          },
          {
            type: 'bar' as const,
            label: 'Giá trị thực tế',
            data: data.quantities,
            backgroundColor: 'rgb(53, 162, 235)',
          },
        ],
      };
      setSelectedData(formattedData);
    }
  };
  useEffect(() => {
    // reset chart when filter changed
    setSelectedQuery(undefined);
    setSelectedData(undefined);
  }, [filter]);

  useEffect(() => {
    queryingData();
  }, [selectedQuery]);
  return (
    <>
      {selectedData ? (
        <SimpleMultitypeChart
          data={selectedData}
          chart_label={`Chi tiết sản phẩm ${selectedQuery?.product_label}`}
        />
      ) : (
        <Alert message="Nhấn chọn 1 dòng ở bảng bên dưới để xem chi tiết" type="info" />
      )}
      <CropProductTable setSelectedQuery={setSelectedQuery} filter={filter} />
    </>
  );
};

export default CropProductStatistic;
