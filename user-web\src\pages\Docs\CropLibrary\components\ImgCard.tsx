import { DEFAULT_FALLBACK_IMG } from '@/common/contanst/img';
import { deletePlantAllResources } from '@/services/plants';
import { genDownloadUrl } from '@/utils/file';

import { DeleteOutlined, EditOutlined } from '@ant-design/icons';
import { history, useAccess, useModel } from '@umijs/max';
import { Avatar, Button, Card, message, Popconfirm, Typography } from 'antd';
import Meta from 'antd/es/card/Meta';
import { Fragment, useState } from 'react';
import EditDocCard from './EditCard';
const { Text } = Typography;

interface Props {
  title?: string;
  image?: string;
  id: string;
  onDeleteSuccess?: () => void;
}
// ! Currently tailored specificly for crop plant
const ImgCard = ({ id, image, title, onDeleteSuccess }: Props) => {
  const { setMyPlant, isAccessEditDocs } = useModel('Docs');
  async function handleDelete() {
    await deletePlantAllResources(id)
      .then((res) => {
        setMyPlant((prev) => prev.filter((item) => item.name !== id));
        message.success(`Xoá  thành công`);
      })
      .catch((error) => {
        message.error(`Lỗi khi xoá  ${title}: ${error}`);
      });
  }
  const access = useAccess();
  const [open, setOpen] = useState(false);
  const canDelete = access.canDeleteAllInPageAccess() && isAccessEditDocs;
  return (
    <>
      {open && <EditDocCard id={id} open={open} openChange={setOpen} key="edit" />}
      <Card
        style={{ overflow: 'hidden' }}
        hoverable
        // TODO move actions to props?
        actions={!isAccessEditDocs ? undefined : [
          <Fragment key="edit">
            {isAccessEditDocs && (
              <Button
                type="text"
                icon={<EditOutlined />}
                onClick={(e) => setOpen(true)}
                key="edit"
              ></Button>
            )}
          </Fragment>,

          <Fragment key="delete">
            {canDelete && (
              <Popconfirm
                title="Xoá tài liệu"
                description={`Bạn có muốn xoá ${title}?`}
                onConfirm={async () => {
                  await handleDelete();
                  onDeleteSuccess?.();
                }}
                key="delete"
                onPopupClick={(e) => {
                  e.stopPropagation();
                }}
              >
                <DeleteOutlined
                  key="delete"
                  onClick={(e) => {
                    e.stopPropagation();
                  }}
                />
              </Popconfirm>
            )}
          </Fragment>,
        ]}
      >
        <div
          onClick={() => {
            history.push(`/documents/${id}/detail`);
          }}
        >
          <Meta
            avatar={
              <Avatar
                shape="square"
                size={64}
                src={image ? genDownloadUrl(image) : DEFAULT_FALLBACK_IMG}
              />
            }
            title={<Text style={{ whiteSpace: 'normal' }}>{title}</Text>}
          />
        </div>
      </Card>
    </>
  );
};

export default ImgCard;
