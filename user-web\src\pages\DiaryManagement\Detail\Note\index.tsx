import { DEFAULT_PAGE_SIZE_ALL } from '@/common/contanst/constanst';
import { getStatisticNoteList } from '@/services/cropManager';
import { getListFileUrlFromString } from '@/services/utils';
import { ActionType } from '@ant-design/pro-components';
import { useIntl, useRequest } from '@umijs/max';
import { Spin } from 'antd';
import { FC, useEffect, useRef } from 'react';
import SeasonalNoteEmpty from './SeasonalNoteEmpty';
import { SeasonalNoteInfoProps } from './SeasonalNoteInfo';
import SeasonalNoteInfoList from './SeasonalNoteInfoList';

interface NoteProps {
  cropId?: string;
  cacheKey?: string;
}

const Note: FC<NoteProps> = ({ cropId, cacheKey }) => {
  const { run, loading, data, refresh } = useRequest(
    ({ crop_id }: { crop_id?: string }) =>
      getStatisticNoteList({
        page: 1,
        size: DEFAULT_PAGE_SIZE_ALL,
        order_by: 'creation desc',
        cropId: crop_id,
      }),
    {
      manual: true,
    },
  );
  const actionRef = useRef<ActionType>();
  useEffect(() => {
    if (cropId) {
      actionRef.current?.reload();
      run({
        crop_id: cropId,
      });
    }
  }, [cropId, cacheKey]);
  const intl = useIntl();
  return (
    <Spin spinning={loading}>
      {!loading && (data || []).length === 0 ? (
        <SeasonalNoteEmpty
          cropId={cropId}
          onCreateSuccess={() => {
            refresh();
          }}
        />
      ) : (
        <SeasonalNoteInfoList
          onDeleteSuccess={() => {
            refresh();
          }}
          data={data?.map<SeasonalNoteInfoProps['data']>((item) => ({
            id: item.note_id,
            title: item.note_label,
            description: item.note,
            listImg: getListFileUrlFromString({ arrUrlString: item.image }).map((url) => ({
              src: url,
            })),
          }))}
        />
      )}
    </Spin>
  );
};

export default Note;
