import action from './en-US/action';
import bom from './en-US/bom';
import categoryTab from './en-US/categoryGroupTab/categoryTab';
import producerTab from './en-US/categoryGroupTab/producerTab';
import productTab from './en-US/categoryGroupTab/productTab';
import common from './en-US/common';
import component from './en-US/component';
import demoMonitor from './en-US/demoMonitor';
import seasonalTab from './en-US/farmingTab/seasonalTab';
import workflowTab from './en-US/farmingTab/workflowTab';
import globalHeader from './en-US/globalHeader';
import home from './en-US/homeTab/home';
import menu from './en-US/menu';
import newDashboard from './en-US/new-dashboard';
import pages from './en-US/pages';
import pwa from './en-US/pwa';
import settingDrawer from './en-US/settingDrawer';
import settings from './en-US/settings';
import categoryManageTab from './en-US/storageManageGroupTab/categoryManageTab';
import productManageTab from './en-US/storageManageGroupTab/productManageTab';
import storageListTab from './en-US/storageManageGroupTab/storageListTab';
import tooltips from './en-US/tooltips';
import warehouseManagement from './en-US/warehouseManagement';
import plantTab from './vi-VN/farmingTab/plantTab';
export default {
  'navBar.lang': 'Languages',
  'fuc.dm': 'Mother fucker but in english',

  'layout.user.link.help': 'Help',
  'layout.user.link.privacy': 'Privacy',
  'layout.user.link.terms': 'Terms',
  'app.copyright.produced': 'Produced by Ant Financial Experience Department',
  'app.preview.down.block': 'Download this page to your local project',
  'app.welcome.link.fetch-blocks': 'Get all block',
  'app.welcome.link.block-list': 'Quickly build standard, pages based on `block` development',
  ...globalHeader,
  ...menu,
  ...settingDrawer,
  ...settings,
  ...pwa,
  ...component,
  ...pages,
  ...common,
  ...demoMonitor,
  ...categoryTab,
  ...producerTab,
  ...productTab,
  ...categoryManageTab,
  ...productManageTab,
  ...storageListTab,
  ...action,
  ...home,
  ...seasonalTab,
  ...workflowTab,
  ...plantTab,
  ...warehouseManagement,
  ...tooltips,
  ...newDashboard,
  ...bom,
};
