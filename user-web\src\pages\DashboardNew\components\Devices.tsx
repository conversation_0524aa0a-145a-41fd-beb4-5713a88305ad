import { DEFAULT_PAGE_SIZE_ALL } from '@/common/contanst/constanst';
import { DEFAULT_FALLBACK_IMG } from '@/common/contanst/img';
import MonitorDevice from '@/pages/IoTDeviceMangement/components/MonitorDevice';
import { useDeviceOnlineChange } from '@/pages/IoTDeviceMangement/hooks/useDeviceOnlineChange';
import { deviceInProjectList, I_IOTDevice } from '@/services/devices';
import { genDownloadUrl } from '@/utils/file';
import { useIntl, useRequest } from '@umijs/max';
import { Avatar, Card, Skeleton, Spin } from 'antd';
import classNames from 'classnames';
import { FC, ReactNode, useState } from 'react';

interface DevicesProps {
  children?: ReactNode;
}
export const Tag: FC<{
  isOffline?: boolean;
  children?: ReactNode;
}> = ({ isOffline, children }) => {
  const textColor = !isOffline ? 'text-[#52c41a]' : 'text-[#ff0000]';
  const bgColor = !isOffline ? 'bg-[#edf9f0]' : 'bg-[#feefef]';
  return (
    <div className={classNames('px-0 py-1 text-xs font-medium', textColor)}>
      {children ? children : isOffline ? 'Offline' : 'Online'}
    </div>
  );
};

const DeviceItem: FC<{
  data: I_IOTDevice;
  onOpenMonitor?: (deviceId: string) => void;
}> = ({ data, onOpenMonitor }) => {
  const lastedDataOnline = !!data.latest_data.find(
    (item) => item.key === 'online' && item.value === true,
  );
  const { isOnline } = useDeviceOnlineChange({
    deviceId: data.device_id_thingsboard,
    initOnline: lastedDataOnline,
  });

  return (
    <div
      onClick={() => onOpenMonitor?.(data.name)}
      className="border bg-[#ffffff] p-4 min-w-[14rem] max-w-[14rem] h-24 flex flex-col justify-between cursor-pointer transition-all duration-300 hover:border-[#9DDBB1] hover:shadow-sm"
    >
      <div className="flex items-start gap-3">
        <div className="flex-1 min-w-0">
          <div className="text-base font-medium text-gray-900 truncate">{data.label}</div>
          <div className="text-sm text-gray-500 mt-1 truncate">{data.zone_label || '-'}</div>
        </div>
        <div className="flex-none w-12">
          <Avatar
            shape="square"
            size={44} // giảm size avatar xuống một chút
            src={
              data?.device_profile_image
                ? genDownloadUrl(data.device_profile_image)
                : DEFAULT_FALLBACK_IMG
            }
            className="!bg-white border"
          />
        </div>
      </div>
      <div className="flex items-center justify-between">
        <Tag isOffline={!isOnline} />
      </div>
    </div>
  );
};
const Devices: FC<DevicesProps> = ({ children }) => {
  const { data, loading } = useRequest(async () => {
    const res = await deviceInProjectList({
      filters: [],
      page: 1,
      size: DEFAULT_PAGE_SIZE_ALL,
      fields: ['*'],
      order_by: 'online',
      // project_id: projectId,
    });
    return {
      data: {
        total: res.length,
        data: res.sort((a, b) => Number(b.online) - Number(a.online)),
        deviceOnline: res.reduce((acc, item) => (item.online ? acc + 1 : acc), 0),
      },
    };
  });
  /**
   * @description state for device
   */
  const [openMonitor, setOpenMonitor] = useState(false);
  const [deviceId, setDeviceId] = useState<string | null>(null);

  const openDeviceDetail = (deviceId: string) => {
    setDeviceId(deviceId);
    setOpenMonitor(true);
  };
  const { formatMessage } = useIntl();
  return (
    <Card
      className="border-0 shadow-sm"
      title={
        <div className="px-2">
          {loading ? (
            <Skeleton.Input />
          ) : (
            <div className="flex items-center gap-2">
              <span className="text-lg font-medium">
                <span className="text-green-500">{data?.deviceOnline}</span>/{data?.total}
              </span>
              <span className="text-gray-600">
                {formatMessage({ id: 'new-dashboard.active-iot-devices' })}
              </span>
            </div>
          )}
        </div>
      }
    >
      <Spin spinning={loading}>
        <div className="flex gap-4 overflow-x-auto scrollbar-thin scrollbar-thumb-gray-200 scrollbar-track-transparent py-2 px-2">
          {data?.data?.map((item) => (
            <DeviceItem onOpenMonitor={openDeviceDetail} data={item} key={item.name} />
          ))}
        </div>
        {openMonitor && deviceId && (
          <MonitorDevice open={openMonitor} deviceId={deviceId} onOpenChange={setOpenMonitor} />
        )}
      </Spin>
    </Card>
  );
};

export default Devices;
