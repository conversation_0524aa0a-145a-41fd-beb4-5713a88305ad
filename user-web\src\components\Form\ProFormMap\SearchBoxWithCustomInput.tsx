import { useDebounceFn } from 'ahooks';
import { AutoComplete, Input, InputRef } from 'antd';
import { FC, ReactNode, useEffect, useRef, useState } from 'react';

interface SearchBoxProps {
  children?: ReactNode;
  mapsAPI: any;
  mapAPI: any;
}

const SearchBox: FC<SearchBoxProps> = ({ children, mapsAPI, mapAPI }) => {
  const inputRef = useRef<InputRef>(null);
  const initialSearchBox = () => {
    try {
      const input = inputRef.current;
      const searchBox = new mapsAPI.places.SearchBox(input);
      mapAPI.controls[mapsAPI.ControlPosition.TOP_LEFT].push(input);

      // Bias the SearchBox results towards current map's viewport.
      mapAPI.addListener('bounds_changed', () => {
        searchBox.setBounds(mapAPI.getBounds());
      });

      let markers: any[] = [];

      // Listen for the event fired when the user selects a prediction and retrieve
      // more details for that place.
      searchBox.addListener('places_changed', () => {
        const places = searchBox.getPlaces();

        if (places.length === 0) {
          return;
        }

        // Clear out the old markers.
        markers.forEach((marker) => {
          marker.setMap(null);
        });
        markers = [];

        // For each place, get the icon, name and location.
        const bounds = new mapsAPI.LatLngBounds();

        places.forEach((place: any) => {
          if (!place.geometry || !place.geometry.location) {
            console.log('Returned place contains no geometry');
            return;
          }

          const icon = {
            url: place.icon as string,
            size: new mapsAPI.Size(71, 71),
            origin: new mapsAPI.Point(0, 0),
            anchor: new mapsAPI.Point(17, 34),
            scaledSize: new mapsAPI.Size(25, 25),
          };

          // Create a marker for each place.
          markers.push(
            new mapsAPI.Marker({
              mapAPI,
              icon,
              title: place.name,
              position: place.geometry.location,
            }),
          );

          if (place.geometry.viewport) {
            // Only geocodes have viewport.
            bounds.union(place.geometry.viewport);
          } else {
            bounds.extend(place.geometry.location);
          }
        });
        mapAPI.fitBounds(bounds);
      });
    } catch (error) {
      console.log('error: ', error);
    }
  };
  useEffect(() => {
    // Create the search box and link it to the UI element.
    // initialSearchBox()
  }, []);
  const [options, setOptions] = useState<any[]>([]);
  const { run: handleSearch } = useDebounceFn(
    async (value) => {
      try {
        const res =
          new mapsAPI.places.AutocompleteService() as google.maps.places.AutocompleteService;
        const predictions = await new Promise((resolve) => {
          res.getPlacePredictions(
            {
              input: value,
              types: ['geocode'],
              componentRestrictions: { country: 'vn' },
            },
            (predictions, status) => {
              if (status === mapsAPI.places.PlacesServiceStatus.OK) {
                resolve(predictions);
              } else {
                resolve([]);
              }
            },
          );
        });
        console.log('predictions: ', predictions);

        const options = predictions.map((prediction: any) => ({
          value: prediction.description,
          label: prediction.description,
          placeId: prediction.place_id, // Add place ID to the options
        }));
        setOptions(options);
      } catch (error) {
        console.log('error: ', error);
      }
    },
    { wait: 500 },
  );

  const handleSelect = async (value: string) => {
    try {
      const res = new mapsAPI.places.AutocompleteService();
      const predictions = await new Promise((resolve) => {
        res.getPlacePredictions(
          {
            input: value,
            types: ['geocode'],
            componentRestrictions: { country: 'vn' },
          },
          (predictions, status) => {
            if (status === mapsAPI.places.PlacesServiceStatus.OK) {
              resolve(predictions);
            } else {
              resolve([]);
            }
          },
        );
      });
      if (predictions.length > 0) {
        const placeId = predictions[0].place_id;
        const geocoder = new mapsAPI.Geocoder();
        geocoder.geocode({ placeId }, (results: any, status: any) => {
          if (status === mapsAPI.GeocoderStatus.OK) {
            const lat = results[0].geometry.location.lat();
            const lng = results[0].geometry.location.lng();
            console.log('lat:', lat);
            console.log('lng:', lng);
          }
        });
      }
    } catch (error) {
      console.log('error: ', error);
    }
  };

  return (
    <div
      style={{
        zIndex: 5,
        position: 'absolute',
        top: 0,
        // center horizontally
        left: '50%',
        transform: 'translateX(-50%)',
      }}
    >
      <AutoComplete
        popupMatchSelectWidth={252}
        style={{ width: 300 }}
        options={options}
        onSelect={handleSelect}
        onSearch={handleSearch}
        size="large"
      >
        <Input.Search size="large" placeholder="input here" enterButton />
      </AutoComplete>
    </div>
  );
};

export default SearchBox;

// gg map api get places from string when input change
// https://developers.google.com/maps/documentation/javascript/places-autocomplete
