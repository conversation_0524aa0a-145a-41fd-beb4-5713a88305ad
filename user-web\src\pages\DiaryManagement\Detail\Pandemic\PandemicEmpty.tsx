import wormSrc from '@/assets/img/worm-img.png';
import { PlusOutlined } from '@ant-design/icons';
import { Button, Typography } from 'antd';
import { createStyles } from 'antd-use-styles';
import { FC } from 'react';
import CreatePandemicModal from './Create';
interface PandemicEmptyProps {
  cropId?: string;
  onCreateSuccess?: () => void;
}
const useStyles = createStyles(({ token }) => ({
  container: {
    width: '100%',
    height: '100%',
    minHeight: 700,
    display: 'flex',
    alignItems: 'center',
    justifyContent: 'center',
    padding: token.padding,
    backgroundColor: '#fcfcfd',
  },
  wrapperContent: {
    width: 221,
    textAlign: 'center',
    display: 'flex',
    flexDirection: 'column',
    gap: 24,
  },
  img: {
    objectFit: 'contain',
  },
}));
const PandemicEmpty: FC<PandemicEmptyProps> = ({ cropId, onCreateSuccess }) => {
  const styles = useStyles();
  return (
    <div className={styles.container}>
      <div className={styles.wrapperContent}>
        <img className={styles.img} alt="icon" src={wormSrc} />
        <Typography.Paragraph type="secondary">
          Cập nhật tình trạng sâu bệnh của cây trồng để theo dõi, chăm sóc và cải thiện cây trồng
          tốt hơn
        </Typography.Paragraph>

        <CreatePandemicModal
          trigger={
            <Button block size="large" type="primary" icon={<PlusOutlined />}>
              Tạo thông tin dịch hại
            </Button>
          }
          onSuccess={onCreateSuccess}
          cropId={cropId}
        />
      </div>
    </div>
  );
};

export default PandemicEmpty;
