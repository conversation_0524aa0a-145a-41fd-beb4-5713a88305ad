import ActionPopConfirm from '@/components/ActionPopConfirm';
import { DeleteFilled } from '@ant-design/icons';
import { FC } from 'react';
import useDeleteMemberType from '../hooks/useDeleteMemberType';

interface DeleteBusinessMemberTypeProps {
  id: string;
  onSuccess?: () => void;
  onClick?: (e: any) => void; // Thêm prop onClick
}

const DeleteBusinessMemberType: FC<DeleteBusinessMemberTypeProps> = ({
  id,
  onSuccess,
  onClick,
}) => {
  const { run } = useDeleteMemberType({
    onSuccess,
  });
  return (
    <ActionPopConfirm
      onClick={onClick}
      actionCall={() => {
        run(id);
      }}
      refreshData={''}
      text={<DeleteFilled />}
      //buttonType={'dashed'}
      danger={true}
      size="small"
    ></ActionPopConfirm>
  );
};

export default DeleteBusinessMemberType;
