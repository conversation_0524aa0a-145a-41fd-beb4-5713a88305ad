import { sscript, sscriptGeneralList } from '@/services/sscript';
import { PageContainer, ProTable } from '@ant-design/pro-components';
import React, { useState, useEffect, useRef } from 'react';
import type { ProColumns } from '@ant-design/pro-components';
import { Button, Card, ConfigProvider, List, Row, message } from 'antd';
import moment from 'moment';

import CreateUnit from './Components/Create';
import UpdateUnit from './Components/Update';
import DeleteUnit from './Components/Delete';
import { useSearchParams } from 'react-router-dom';
import { createItem, listAllItem, updateItem, removeItem } from '@/services/workType';
import { IIotWorkType } from '@/services/workType/type';
import { useModel } from '@umijs/max';
interface ActionType {
    reload: (resetPageIndex?: boolean) => void;
    reloadAndRest: () => void;
    reset: () => void;
    clearSelected?: () => void;
    startEditable: (rowKey: String) => boolean;
    cancelEditable: (rowKey: String) => boolean;
}


const WorkTypeCategoryManagement: React.FC = () => {
    const [categories, setCategories] = useState<IIotWorkType[]>([]);
    const [loading, setLoading] = useState<boolean>(false);
    const [searchParams, setSearchParams] = useSearchParams();
    const { fetchAllWorkType, myWorkType } = useModel('MyWorkType');

    const fetchData = async () => {
        try {
            setLoading(true);
            await fetchAllWorkType();
        } catch (error) {
            message.error("Có lỗi xảy ra, vui lòng thử lại")
        } finally {
            setLoading(false);
        }
    }

    useEffect(() => {
        setCategories(myWorkType)
    }, [myWorkType]);



    return (
        <Card
            loading={loading}
            title={"Loại nhân công"}
            extra={<CreateUnit refreshFnc={fetchData}></CreateUnit>}
        >
            <List
                itemLayout="horizontal"
                dataSource={[...categories]}
                renderItem={(item, index) => (
                    <List.Item
                        actions={[<UpdateUnit
                            key={"edit" + item.name}
                            refreshFnc={fetchData}
                            value={item}
                        ></UpdateUnit>, <DeleteUnit key={"remove" + item.name} refreshFnc={fetchData}
                            value={item} />]}
                    >
                        <List.Item.Meta
                            title={item.label}
                        />
                    </List.Item>
                )}
            />
        </Card>
    );
};

export default WorkTypeCategoryManagement;