import treeGreen from '@/assets/img/icons/tree-green.svg';
import { getTemplateCropList } from '@/services/cropManager';
import {
  getFarmingPlan,
  getFarmingPlanState,
  getTemplateTaskManagerList,
} from '@/services/farming-plan';
import { getFileUrlV2 } from '@/services/utils';
import { PlusOutlined } from '@ant-design/icons';
import {
  ActionType,
  ProColumns,
  ProForm,
  ProFormSelect,
  ProTable,
} from '@ant-design/pro-components';
import { history, Link, useAccess, useIntl } from '@umijs/max';
import { Avatar, Button, Col, Row, Space, Tag, Tooltip } from 'antd';
import { createStyles } from 'antd-use-styles';
import moment from 'moment';
import { FC, useEffect, useRef, useState } from 'react';
import { useSearchParams } from 'react-router-dom';
import CreateWorkflow from '../../WorkflowManagement/Create';
import DeleteMultiTask from './DeleteMultiTaskConfirm';
import UpdateStatusMultiTask from './UpdateStatusMultiTask';

interface TaskTemplateTableDetailProps {
  cropId?: string;
  createNewTaskInModal?: boolean;
}

const useStyles = createStyles(() => ({
  table: {
    '& .ant-pro-table-list-toolbar-left': {
      flex: 'none',
    },
  },
}));
const dateRangeFilterKey = 'dateRange';
const TableTaskTemplateTableDetail: FC<TaskTemplateTableDetailProps> = ({
  cropId,
  createNewTaskInModal,
}) => {
  const styles = useStyles();
  const [searchParams, setSearchParams] = useSearchParams();
  const farmingPlanState = searchParams.get('pl_state_name');
  const dateRangeFilter = searchParams.get(dateRangeFilterKey);
  const [planId, setPlanId] = useState<string | undefined>('');
  const [formFilter] = ProForm.useForm();
  const [selectedRowKeys, setSelectedRowKeys] = useState<React.Key[]>([]);
  const tableRef = useRef<ActionType>();
  const [enumCropValues, setEnumCropValues] = useState({});
  const [enumStateValues, setEnumStateValues] = useState({});
  const intl = useIntl();
  useEffect(() => {
    // Gọi API để lấy danh sách giá trị cho valueEnum
    const fetchData = async () => {
      try {
        const response = await getTemplateCropList({ page: 1, size: 1000 });
        const data: { name: string; label: string }[] = response.data;

        const enumCropObject: { [key: string]: string } = {};
        data.forEach((item: any) => {
          enumCropObject[item.name] = item.label;
        });

        setEnumCropValues(enumCropObject);
        //get plan_id
        const filters = [['iot_farming_plan', 'crop', 'like', cropId]];
        const plan = await getFarmingPlan('', filters);
        const planId = plan.data.name;
        setPlanId(planId);
        //similar for state
        const responseState = await getFarmingPlanState({ page: 1, size: 1000 });
        const dataState: { label: string }[] = responseState.data;
        const enumStateObject: { [key: string]: string } = {};
        dataState.forEach((item) => {
          enumStateObject[item.label] = item.label;
        });
        setEnumStateValues(enumStateObject);
      } catch (error) {
        console.error('Error fetching enum values:', error);
      }
    };

    fetchData();
  }, []); // useEffect chỉ chạy một lần khi component được mount

  const columns: ProColumns<any>[] = [
    {
      title: intl.formatMessage({ id: 'common.index' }),
      renderText(text, record, index, action) {
        return index + 1;
      },
      search: false,
    },
    {
      title: intl.formatMessage({ id: 'workflowTab.workName' }),
      dataIndex: 'label',
      renderText(_text, record, index, action) {
        return (
          <Link
            to={`/farming-management/seasonal-management/detail/${cropId}/workflow-management/detail/${record.name}`}
          >
            <Space>
              <img src={treeGreen} /> {_text}
            </Space>
          </Link>
        );
      },
      sorter: true,
    },
    {
      title: intl.formatMessage({ id: 'common.status' }),
      dataIndex: 'status',
      valueEnum: {
        Plan: { text: 'Kế hoạch', status: 'Default' },
        Done: { text: 'Đã hoàn thành', status: 'Success' },
        'In progress': { text: 'Đang thực hiện', status: 'Warning' },
        Pending: { text: 'Trì hoãn', status: 'Default' },
      },
      render(dom, entity, index) {
        switch (entity.status) {
          case 'Plan':
            return <Tag color="cyan">Kế hoạch</Tag>;
          case 'Done':
            return <Tag color="success">Đã hoàn thành</Tag>;
          case 'In progress':
            return <Tag color="warning">Đang thực hiện</Tag>;
          case 'Pending':
            return <Tag color="default">Trì hoãn</Tag>;
          default:
            return <Tag></Tag>;
        }
      },
      sorter: true,
    },
    {
      title: intl.formatMessage({ id: 'common.start_date' }),
      dataIndex: 'start_date',
      render(dom, entity) {
        return moment(entity.start_date).format('YYYY-MM-DD HH:mm'); // Format date to 'YYYY-MM-DD HH:mm'
      },
      sortDirections: ['ascend', 'descend', 'ascend'],
      sorter: true,
      defaultSortOrder: 'descend',
      valueType: 'dateTime',
    },
    {
      title: intl.formatMessage({ id: 'common.end_date' }),
      dataIndex: 'end_date',
      render(dom, entity) {
        return moment(entity.end_date).format('YYYY-MM-DD HH:mm'); // Format date to 'YYYY-MM-DD HH:mm'
      },
      sortDirections: ['ascend', 'descend', 'ascend'],
      sorter: true,
      valueType: 'dateTime',
    },
    {
      title: intl.formatMessage({ id: 'workflowTab.executor' }),
      dataIndex: 'assigned_to',
      render(value, record) {
        const info = record.assigned_to_info?.[0];
        if (!info) {
          return null;
        }

        return (
          <Space>
            {info.user_avatar && (
              <Avatar
                size={'small'}
                src={getFileUrlV2({
                  src: info.user_avatar,
                })}
              />
            )}

            <span>{`${info.first_name || ''} ${info.last_name || ''} `}</span>
          </Space>
        );
      },
      search: false,
      sorter: true,
    },
    {
      title: 'Thành viên liên quan',
      hideInTable: true,
      dataIndex: 'involve_in_users',
      render(value, record) {
        try {
          const involveInArr = record.involve_in_users;
          const userNames = involveInArr.map((data: any) => `${data.first_name} ${data.last_name}`);
          return (
            <>
              <Avatar.Group>
                {involveInArr.map((data: any, index: number) => (
                  <Tooltip
                    key={'avt' + index}
                    title={`${
                      data.full_name
                        ? data.full_name
                        : `${data.last_name || ''} ${data.first_name || ''}`
                    }`}
                    placement="top"
                  >
                    <Avatar style={{ backgroundColor: '#87d068' }}>{data.first_name}</Avatar>
                  </Tooltip>
                ))}
              </Avatar.Group>
            </>
          );
        } catch (error) {
          return null;
        }
      },
      search: false,
      width: 100,
    },
    {
      title: 'Vụ mùa',
      hideInTable: true,
      dataIndex: 'crop_id',
      valueEnum: enumCropValues,
      renderFormItem: (_, { defaultRender, ...rest }) => {
        return <ProFormSelect options={rest.options} showSearch />;
      },
      search: cropId ? false : undefined,
      sorter: true,
    },
    {
      title: 'Giai đoạn',
      hideInTable: true,
      hideInSearch: true,
      dataIndex: 'state_name',
      valueEnum: enumStateValues,
      sorter: true,
      renderFormItem: (_, { defaultRender, ...rest }) => {
        return <ProFormSelect options={rest.options} showSearch />;
      },
    },
    {
      title: intl.formatMessage({ id: 'common.completion_level' }),
      dataIndex: 'todo_done',
      search: false,
      sorter: true,
      render(value, record) {
        return `${record.todo_done || 0}/${record.todo_total || 0}`;
      },
    },
  ];
  const actionRef = useRef<ActionType>();
  // reload when form filter change
  useEffect(() => {
    if (actionRef.current) {
      actionRef.current.reload();
    }
  }, [dateRangeFilter]);

  const reloadTable = async () => {
    actionRef.current?.reload();
    setSelectedRowKeys([]); // Clear the selection
  };
  // modal create new task
  const [openModalCreateNewTask, setOpenModalCreateNewTask] = useState(false);

  const access = useAccess();
  const canCreate = access.canCreateInWorkFlowManagement();

  return (
    <>
      <ProTable
        actionRef={actionRef}
        className={styles.table}
        form={{
          syncToUrl: true,
          defaultCollapsed: false,
          initialValues: {
            pl_state_name: farmingPlanState,
          },
        }}
        search={{
          labelWidth: 'auto',
          span: {
            xs: 24,
            sm: 24,
            md: 12,
            lg: 12,
            xl: 12,
            xxl: 12,
          },
        }}
        toolbar={{
          filter: (
            <ProForm
              form={formFilter}
              name="crop-detail:table-filter"
              onValuesChange={(changeValue) => {
                if (changeValue.dateRange) {
                  searchParams.set(dateRangeFilterKey, JSON.stringify(changeValue.dateRange));
                } else {
                  searchParams.delete(dateRangeFilterKey);
                }
                setSearchParams(searchParams);
              }}
              layout="inline"
              submitter={false}
            >
              <Space size={'large'}>
                {canCreate && (
                  <Button
                    icon={<PlusOutlined />}
                    onClick={() => {
                      if (createNewTaskInModal) {
                        setOpenModalCreateNewTask(true);
                        return;
                      }
                      const urlParams = new URLSearchParams(window.location.search);
                      const currentFarmingPlanState = urlParams.get('pl_state_name');

                      history.push(
                        `/farming-management/workflow-management/create${
                          currentFarmingPlanState
                            ? `?farming_plan_state=${currentFarmingPlanState}`
                            : ''
                        }`,
                      );
                    }}
                  >
                    {intl.formatMessage({ id: 'workflowTab.createWork' })}
                  </Button>
                )}
              </Space>
            </ProForm>
          ),
        }}
        rowSelection={{
          selectedRowKeys,
          onChange: (selectedKeys) => setSelectedRowKeys(selectedKeys),
        }}
        tableAlertOptionRender={({ selectedRowKeys, selectedRows, onCleanSelected }) => {
          return (
            <>
              <Row justify="space-between" gutter={16}>
                <Col className="gutter-row">
                  <UpdateStatusMultiTask
                    refreshFnc={reloadTable}
                    tasks_id={selectedRows.map((d) => d.name)}
                  />
                </Col>
                <Col className="gutter-row">
                  <DeleteMultiTask
                    refreshFnc={reloadTable}
                    tasks_id={selectedRows.map((d) => d.name)}
                  />
                </Col>
              </Row>
            </>
          );
        }}
        request={async (params, sort, filter) => {
          console.log('params', params);
          let startDateRange = params?.start_date_range;
          const startDateFrom = startDateRange?.[0];
          const startDateTo = startDateRange?.[1];
          params.start_date_from = startDateFrom;
          params.start_date_to = startDateTo;

          let endDateRange = params?.end_date_range;
          const endDateFrom = endDateRange?.[0];
          const endDateTo = endDateRange?.[1];
          params.end_date_from = endDateFrom;
          params.end_date_to = endDateTo;

          if (sort?.start_date) {
            params.order_by = `start_date ${sort?.start_date === 'ascend' ? 'asc' : 'desc'} `;
          } else if (sort?.end_date) {
            params.order_by = `end_date ${sort?.end_date === 'ascend' ? 'asc' : 'desc'} `;
          } else if (sort?.label) {
            params.order_by = `label ${sort?.label === 'ascend' ? 'asc' : 'desc'} `;
          } else if (sort?.status) {
            params.order_by = `status ${sort?.status === 'ascend' ? 'asc' : 'desc'} `;
          } else if (sort?.assigned_to) {
            const direction = sort?.assigned_to === 'ascend' ? 'asc' : 'desc';
            params.order_by = `assigned_to ${direction}`;
          } else if (sort?.crop_id) {
            const direction = sort?.crop_id === 'ascend' ? 'asc' : 'desc';
            params.order_by = `crop_id ${direction}`;
          } else if (sort?.state_name) {
            const direction = sort?.state_name === 'ascend' ? 'asc' : 'desc';
            params.order_by = `state_name ${direction}`;
          } else if (sort?.todo_done) {
            const direction = sort?.todo_done === 'ascend' ? 'asc' : 'desc';
            params.order_by = `todo_done ${direction}`;
          } else {
            params.order_by = 'start_date desc';
          }
          if (cropId) {
            params.crop_id = cropId;
          }
          const planState = params.pl_state_name;
          const filterParams: any = getFilterTaskAll(params);
          const res: any = await getTemplateTaskManagerList(filterParams);
          let data = res.data;

          return {
            data: data,
            success: true,
            total: res.pagination.totalElements,
          };
        }}
        pagination={{
          showSizeChanger: true,
          pageSizeOptions: [10, 20, 50, 100],
        }}
        headerTitle={intl.formatMessage({ id: 'workflowTab.workList' })}
        columns={columns}
        rowKey={'name'}
        scroll={{
          x: 'max-content',
        }}
      />
      {openModalCreateNewTask && createNewTaskInModal && (
        <CreateWorkflow
          mode="modal"
          open={openModalCreateNewTask}
          onOpenChange={setOpenModalCreateNewTask}
          onCreateSuccess={reloadTable}
          planId={planId}
          cropId={cropId}
          isTemplateTask={true}
        />
      )}
    </>
  );
};

export default TableTaskTemplateTableDetail;
const getLikeFilter = (params: any) => {
  return `%${params}%`;
};
function getFilterTaskAll(params: any) {
  let filterArr: any[] = [];
  if (params.label) {
    filterArr.push(['iot_farming_plan_task', 'label', 'like', getLikeFilter(params.label)]);
  }
  if (params.start_date_from) {
    filterArr.push(['iot_farming_plan_task', 'start_date', '>=', params.start_date_from]);
  }
  if (params.start_date_to) {
    filterArr.push(['iot_farming_plan_task', 'start_date', '<=', params.start_date_to]);
  }
  if (params.end_date_from) {
    filterArr.push(['iot_farming_plan_task', 'end_date', '>=', params.end_date_from]);
  }
  if (params.end_date_to) {
    filterArr.push(['iot_farming_plan_task', 'end_date', '<=', params.end_date_to]);
  }
  if (params.start_date && params.end_date) {
    const [_startDate, _endDate] = [new Date(params.start_date), new Date(params.end_date)].sort(
      (a: any, b: any) => a - b,
    );
    filterArr.push(['iot_farming_plan_task', 'start_date', '<=', _endDate]);
    filterArr.push(['iot_farming_plan_task', 'end_date', '>=', _startDate]);
  } else {
    if (params.start_date) {
      filterArr.push(['iot_farming_plan_task', 'start_date', '<=', params.start_date]);
    }
    if (params.end_date) {
      filterArr.push(['iot_farming_plan_task', 'end_date', '>=', params.end_date]);
    }
  }

  if (params.status) {
    switch (params.status) {
      case 'Plan':
        filterArr.push(['iot_farming_plan_task', 'status', 'like', 'Plan']);
        break;
      case 'Done':
        filterArr.push(['iot_farming_plan_task', 'status', 'like', 'Done']);
        break;
      case 'In progress':
        filterArr.push(['iot_farming_plan_task', 'status', 'like', 'In progress']);
        break;
      case 'Pending':
        filterArr.push(['iot_farming_plan_task', 'status', 'like', 'Pending']);
        break;
      default:
        break;
    }
  }
  if (params.project_name) {
    filterArr.push(['iot_project', 'label', 'like', getLikeFilter(params.project_name)]);
  }
  if (params.zone_name) {
    filterArr.push(['iot_zone', 'label', 'like', getLikeFilter(params.zone_name)]);
  }
  if (params.state_name) {
    filterArr.push(['iot_farming_plan_state', 'label', 'like', getLikeFilter(params.state_name)]);
  }
  if (params.plan_name) {
    filterArr.push(['iot_farming_plan', 'label', 'like', getLikeFilter(params.plan_name)]);
  }
  if (params.crop_name) {
    filterArr.push(['iot_crop', 'label', 'like', params.crop_name]);
  }
  if (params.crop_id) {
    filterArr.push(['iot_crop', 'name', 'like', params.crop_id]);
  }
  const returnObj: any = {
    page: params.current,
    size: params.pageSize,
    filters: JSON.stringify(filterArr),
    order_by: params.order_by,
  };
  return returnObj;
}
