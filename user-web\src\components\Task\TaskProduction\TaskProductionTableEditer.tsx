import { getProductItemV3 } from '@/services/InventoryManagementV3/product-item';
import { getAllTaskProduction } from '@/services/TaskAndTodo';
import { IIotProductionQuantity } from '@/types/IIotProductionQuantity';
import type { ProColumns } from '@ant-design/pro-components';
import { ProTable } from '@ant-design/pro-components';
import { FormattedMessage, useAccess } from '@umijs/max';
import { message } from 'antd';
import React, { useEffect, useState } from 'react';
import CreateProductionUpdateView from './CreateProductionUpdateView';
import DeleteProductionTask from './DeleteProductionTask';
import UpdateProductionForTask from './UpdateProductionForTask';

type DataSourceType = {
  id: React.Key;
  title?: string;
  decs?: string;
  state?: string;
  created_at?: string;
  children?: DataSourceType[];
};

export default ({ task_id }: { task_id: string }) => {
  const [dataSource, setDataSource] = useState([]);
  const [editableKeys, setEditableRowKeys] = useState<React.Key[]>();
  const [loading, setLoading] = useState(false);
  const getTaskProduction = async () => {
    try {
      setLoading(true);
      const itemData = await getProductItemV3();

      const resData = await getAllTaskProduction(task_id);
      setDataSource(
        resData.data.map((d: any) => {
          d.item = itemData.data.find((item) => {
            if (item.name === d.product_id) return item;
          });
          return d;
        }),
      );
      // setDataSource(myProducts as any);
    } catch (error: any) {
      message.error(error.toString());
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    getTaskProduction();
  }, []);
  const access = useAccess();
  const canCreateTask = access.canCreateInWorkFlowManagement();
  const canUpdateTask = access.canUpdateInWorkFlowManagement();
  const canDeleteTask = access.canDeleteInWorkFlowManagement();
  const columns: ProColumns<DataSourceType & IIotProductionQuantity>[] = [
    {
      title: <FormattedMessage id="common.crop_product" />,
      dataIndex: 'product_id',
      render: (dom: any, entity: any) => {
        return <>{entity.item.label}</>;
      },
    },
    {
      title: <FormattedMessage id="storage-management.category-management.expected_quantity" />,
      dataIndex: 'exp_quantity',
    },
    {
      title: <FormattedMessage id="storage-management.category-management.real_quantity" />,
      dataIndex: 'quantity',
    },
    {
      title: <FormattedMessage id="storage-management.category-management.loss_quantity" />,
      dataIndex: 'lost_quantity',
      // render(dom, entity, index, action, schema) {
      //   // const lostQuantity = (entity.exp_quantity ?? 0) - (entity.quantity ?? 0);
      //   // return <>{lostQuantity}</>;
      // },
    },
    {
      title: <FormattedMessage id="category.material-management.unit" defaultMessage="unknown" />,
      dataIndex: 'stock_uom',
      render: (dom: any, entity: any) => {
        return <>{entity.item.uom_label}</>;
      },
    },
    // {
    //     title: <FormattedMessage id="category.material-management.packing_unit" defaultMessage="unknown" />,
    //     dataIndex: 'packing_unit',
    //     render: (dom: any, entity: any) => {
    //         return <>
    //             {entity._packingUnit?.label}
    //         </>
    //     },
    // },
    {
      title: <FormattedMessage id="common.description" defaultMessage="unknown" />,
      dataIndex: 'description',
      formItemProps: {
        rules: [
          {
            required: true,
            whitespace: true,
            message: 'Mô tả không được để trống',
          },
        ],
      },
    },
    {
      title: <FormattedMessage id="common.action" defaultMessage="unknown" />,
      dataIndex: 'name',
      render: (dom: any, entity: any) => {
        return (
          <>
            {canUpdateTask && (
              <UpdateProductionForTask
                refreshFnc={getTaskProduction}
                task_id={task_id}
                data={entity}
              />
            )}
            {'  '}
            {canDeleteTask && <DeleteProductionTask refreshFnc={getTaskProduction} value={dom} />}
          </>
        );
      },
    },
  ];

  const toolBarRenderButtons: any = [];
  if (canCreateTask || canUpdateTask) {
    toolBarRenderButtons.push(
      <CreateProductionUpdateView
        key="create-task-prod"
        refreshFnc={getTaskProduction}
        task_id={task_id}
      />,
      // <ProductCategoryCard key="create-category" />,
    );
  }
  return (
    <>
      <ProTable
        loading={loading}
        headerTitle={<FormattedMessage id="common.production" />}
        columns={columns}
        rowKey="name"
        dataSource={[...dataSource]}
        toolBarRender={() => {
          return toolBarRenderButtons;
        }}
        search={false}
      />
    </>
  );
};
