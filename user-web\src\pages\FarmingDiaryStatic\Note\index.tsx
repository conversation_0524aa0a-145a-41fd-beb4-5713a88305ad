import useUnsavedChangesModal from '@/components/UnsavedChangesModal';
import { PlusOutlined } from '@ant-design/icons';
import { PageContainer } from '@ant-design/pro-components';
import { useIntl } from '@umijs/max';
import { <PERSON><PERSON>, Col, Row } from 'antd';
import { nanoid } from 'nanoid';
import { FC, ReactNode, useState } from 'react';
import NoteCreate from './components/Create';
import NoteEdit from './components/Edit';
import NoteList from './components/NoteList';

interface IndexProps {
  children?: ReactNode;
}

const Index: FC<IndexProps> = ({ children }) => {
  const [tableReloadKey, setTableReloadKey] = useState<string | null>();
  const [selectItem, setSelectItemId] = useState<string | null>(null);
  const [isCreate, setIsCreate] = useState<boolean>(false);
  const [isFormDirty, setIsFormDirty] = useState<boolean>(false);

  const { formatMessage } = useIntl();
  const handleReload = () => {
    setTableReloadKey(nanoid());
  };

  const confirmNavigation = useUnsavedChangesModal(isFormDirty);
  const handleCreateClick = () => {
    confirmNavigation(() => {
      setIsCreate(true);
      setSelectItemId(null);
    });
  };

  const handleSelectItemClick = (stageId: string) => {
    confirmNavigation(() => {
      setSelectItemId(stageId);
      setIsCreate(false);
    });
  };
  return (
    <PageContainer
      extra={
        <Button type="primary" icon={<PlusOutlined />} onClick={handleCreateClick}>
          {formatMessage({
            id: 'common.add-note',
          })}
        </Button>
      }
    >
      <div className="bg-white">
        <Row gutter={[16, 16]}>
          <Col span={9}>
            <NoteList onSelect={handleSelectItemClick} reloadKey={tableReloadKey} />
          </Col>
          <Col span={15}>
            {isCreate || !selectItem ? (
              <NoteCreate
                onSuccess={handleReload}
                key={tableReloadKey}
                setIsFormDirty={setIsFormDirty}
              />
            ) : (
              <NoteEdit
                id={selectItem}
                onSuccess={handleReload}
                key={tableReloadKey}
                setIsFormDirty={setIsFormDirty}
              />
            )}
          </Col>
        </Row>
      </div>
    </PageContainer>
  );
};

export default Index;
