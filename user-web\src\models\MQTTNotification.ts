// src/models/count.ts
import { WSS_URL_DEV, WSS_URL_PROD } from '@/common/contanst/index';
import { MqttDeviceEventEmitterControl } from '@/events/mqtt/mqtt-device-eventemitter';
import { MqttNoticeEventEmitterControl } from '@/events/mqtt/mqtt-notice-eventemitter';
import { MqttNoticeEventReadAllControl } from '@/events/mqtt/mqtt-notice-read-all';

import { IRealTimeReminder } from '@/types/ITeamRemider.type';
import { getUserIdFromToken } from '@/utils/localStorage';
import {
  genNoticeReadAllTopic,
  genNoticeSubTopicCustomerId,
  genNoticeUpdateTopic,
} from '@/utils/mqtt';
import { useModel } from '@umijs/max';
import { notification } from 'antd';
import jwtDecode from 'jwt-decode';

import mqtt, { OnConnectCallback, OnErrorCallback, OnMessageCallback } from 'mqtt';
import { useEffect, useMemo, useState } from 'react';

export default () => {
  const [isConnected, setIsConnected] = useState(false);
  const [notiMsg, sendNotification] = useState<IRealTimeReminder>();
  const [api, contextHolder] = notification.useNotification();
  const connectUrl = process.env.NODE_ENV === 'production' ? WSS_URL_PROD : WSS_URL_DEV;
  const { initialState } = useModel('@@initialState');
  const currentUser = initialState?.currentUser;
  const userdata = JSON.parse(localStorage.getItem('token') || '{}');

  const options = {
    clean: true, // retain session
    connectTimeout: 4000, // Timeout period
    // Authentication information
    clientId: Math.random().toString(16),

    username: 'unuseda',
    password: userdata?.accessToken || '',
  };

  const client = useMemo(() => mqtt.connect(connectUrl, options), []);
  /**
   * @description get topic
   */
  const decoded: any = (() => {
    try {
      return jwtDecode(userdata?.accessToken);
    } catch (error) {}
  })();
  const deviceTopics = decoded?.acl?.sub;
  const noticeTopics: string[] = [
    // genNoticeSubTopic()
  ];
  const allTopic = [...deviceTopics, ...noticeTopics];
  if (userdata.customer_id) {
    allTopic.push(genNoticeSubTopicCustomerId());
  }
  /**
   * @description handleMessage
   */
  const mqttConnect: OnConnectCallback = () => {
    /**
     * @description set setMaxListeners
     */
    client.setMaxListeners(20);
    if (!Array.isArray(allTopic)) return;
    client.subscribe(allTopic, function (err) {
      if (err) {
        console.log('subscribe', 'error:', err);
        setIsConnected(false);
      } else {
        console.log(allTopic);
        setIsConnected(true);
      }
    });
  };
  const disconnect = () => {
    setIsConnected(false);
  };
  const onError: OnErrorCallback = (error) => {
    setIsConnected(false);
    console.log('Connection failed:', error);
  };
  const handleMessage = useMemo(
    () => ({
      deviceHandle: {
        receive: new MqttDeviceEventEmitterControl(),
        emit: {},
      },
      noticeHandle: {
        receive: new MqttNoticeEventEmitterControl(),
        emit: {
          updateNoticeTask: (message: {
            name: string;
            message: string; // Data
            created_at: string; // Datetime
            entity: string; // Data
            type: 'task'; // Data
            is_read: boolean; // Check
          }) => {
            client.publish(
              genNoticeUpdateTopic(),
              JSON.stringify({
                ...message,
                is_read: true,
                customer_user: getUserIdFromToken(),
              }),
              (err) => {
                if (err) console.log('err: ', err);
              },
            );
          },
        },
      },
      noticeHandleReadAll: {
        receive: new MqttNoticeEventReadAllControl(),
        emit: {
          readAll: (message: { isReadAll: boolean; time: number }) => {
            client.publish(genNoticeReadAllTopic(), JSON.stringify(message), (err) => {
              if (err) console.log('err: ', err);
            });
          },
        },
      },
    }),
    [],
  );
  const handleMessageMQTT: OnMessageCallback = async (...args) => {
    try {
      // console.log('handleMessageMQTT: ', args);
      await Promise.allSettled(
        Object.values(handleMessage).map((handle) => handle.receive.handleReceiveMessage(...args)),
      );
    } catch (error) {
      console.log('error: ', error);
    }
  };

  //======================================

  const initMqtt = () => {
    if (!currentUser) return;
    if (!client) return;

    client.on('connect', mqttConnect);
    client.on('message', handleMessageMQTT);
    client.on('disconnect', disconnect);
    client.on('close', disconnect);
    client.on('offline', disconnect);
    client.on('reconnect', () => {
      console.log('reconnecting:');
    });

    client.on('error', onError);
  };

  const clearEffect = () => {
    client?.removeAllListeners?.();
    Promise.allSettled(
      Object.values(handleMessage).map((handle) => handle.receive.removeAllListeners()),
    );
  };

  useEffect(() => {
    initMqtt();
    return () => {
      clearEffect();
    };
  }, [client, currentUser]);

  return {
    sendNotification,
    mqttClient: client,
    isConnected,
    handleMessage,
  };
};
