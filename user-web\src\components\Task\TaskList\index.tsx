import bellYellow from '@/assets/img/icons/bell-yellow.svg';
import harvest from '@/assets/img/icons/harvest.svg';
import seeds from '@/assets/img/icons/seeds.svg';
import spraying from '@/assets/img/icons/spraying.svg';
import sprinklers from '@/assets/img/icons/sprinklers.svg';
import sunflower from '@/assets/img/icons/sunflower.svg';
import treeGreen from '@/assets/img/icons/tree-green.svg';
import ProTableCommon from '@/components/ProTableCommon';
import { createEmptyArray } from '@/utils/array';
import { AntDesignOutlined,UserOutlined } from '@ant-design/icons';
import { ProColumns } from '@ant-design/pro-components';
import { faker } from '@faker-js/faker/locale/vi';
import { Link } from '@umijs/max';
import { Avatar,Space,Table,Tag,Tooltip } from 'antd';
import { FC,ReactNode } from 'react';
import LinkWithQuery from '../LinkWithQuery';

interface TaskListTableProps {
  children?: ReactNode;
  genLinkDetail?: (itemId: string) => string;
  keepSearchParams?: boolean;
}
type IDataTable = {
  id: string;
  name: string;
  executor: string;
  project: string;
  area: string;
};
const data = createEmptyArray(55).map<IDataTable>(() => ({
  id: faker.string.nanoid(),
  name: 'Tưới cho dâu',
  executor: faker.person.fullName(),
  project: 'Dự án xanh',
  area: 'Vườn Đà Lạt',
  startDate: new Date().toISOString(),
  endDate: new Date().toISOString(),
}));

const TaskListTable: FC<TaskListTableProps> = ({ genLinkDetail, keepSearchParams }) => {
  const columns: ProColumns<IDataTable>[] = [
    {
      title: 'STT',
      renderText(text, record, index, action) {
        return index + 1;
      },
    },
    {
      title: 'Tên công việc',
      dataIndex: 'name',
      renderText(_text, record, index, action) {
        const text = (
          <Space>
            <img src={treeGreen} /> {_text}
          </Space>
        );
        return !genLinkDetail ? (
          text
        ) : !keepSearchParams ? (
          <Link to={genLinkDetail(record.id)}>{text}</Link>
        ) : (
          <LinkWithQuery to={genLinkDetail(record.id)}>{text}</LinkWithQuery>
        );
      },
    },
    {
      title: 'Người thực hiện',
      render() {
        return (
          <Space>
            <Avatar size={'small'} />
            <span>Trân Thị Thu Trang</span>
          </Space>
        );
      },
    },
    {
      title: 'Thành viên liên quan',
      render() {
        return (
          <Avatar.Group>
            <Avatar src="https://xsgames.co/randomusers/avatar.php?g=pixel&key=1" />
            <a href="https://ant.design">
              <Avatar style={{ backgroundColor: '#f56a00' }}>K</Avatar>
            </a>
            <Tooltip title="Ant User" placement="top">
              <Avatar style={{ backgroundColor: '#87d068' }} icon={<UserOutlined />} />
            </Tooltip>
            <Avatar style={{ backgroundColor: '#1677ff' }} icon={<AntDesignOutlined />} />
          </Avatar.Group>
        );
      },
    },
    {
      title: 'Dự án',
      render(text, record, index, action) {
        return (
          <Space>
            <img src={sunflower} />
            <span>Dự án xanh</span>
          </Space>
        );
      },
    },
    {
      title: 'Khu vực ',
      renderText() {
        return 'Vườn Đà Lạt';
      },
    },
    {
      title: 'Vụ mùa',
      renderText() {
        return 'Vụ mùa dâu tây';
      },
    },
    {
      title: 'Loại Giai đoạn mẫu',
      render(dom, entity, index, action, schema) {
        if (index === 1)
          return (
            <Space>
              <img src={sprinklers} /> <span>Tưới Nước</span>
            </Space>
          );
        if (index === 2)
          return (
            <Space>
              <img src={spraying} /> <span>Bơm thuốc</span>
            </Space>
          );
        if (index === 3)
          return (
            <Space>
              <img src={harvest} /> <span>Thu hoạch</span>
            </Space>
          );
        return (
          <Space>
            <img src={seeds} /> <span> Gieo hạt</span>
          </Space>
        );
      },
    },
    {
      title: 'Ngày bắt đầu ',
      dataIndex: 'startDate',
      valueType: 'date',
    },
    {
      title: 'Ngày kết thúc',
      dataIndex: 'endDate',
      valueType: 'date',
    },
    {
      title: 'Trạng thái',
      render(dom, entity, index) {
        switch (index) {
          case 0:
            return <Tag color="success">Đã hoàn thành</Tag>;
          case 1:
            return <Tag color="warning">Đang thực hiện</Tag>;
          case 2:
            <Tag color="danger">Trì hoãn</Tag>;

          default:
            return <Tag>Đã hủy</Tag>;
        }
      },
    },
    {
      title: 'Mức độ hoàn thành',
      render() {
        return '5/5';
      },
    },
    {
      title: 'Hẹn giờ',
      render(dom, entity, index, action, schema) {
        return (
          <Space>
            <img src={bellYellow} alt="" /> <span>8:00 AM, hằng ngày</span>
          </Space>
        );
      },
    },
  ];
  return (
    <ProTableCommon
      rowSelection={{
        // 自定义选择项参考: https://ant.design/components/table-cn/#components-table-demo-row-selection-custom
        // 注释该行则默认不显示下拉选项
        selections: [Table.SELECTION_ALL, Table.SELECTION_INVERT],
      }}
      dataSource={data}
      headerTitle="Danh sách công việc"
      columns={columns}
      rowKey={'id'}
    />
  );
};

export default TaskListTable;
