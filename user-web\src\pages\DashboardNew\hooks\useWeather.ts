import { getCurrentWeather } from '@/services/weather';
import { useEffect, useState } from 'react';

// Type for use in component state
type WeatherType = {
  date: string;
  temperature: number;
  weather: string;
  icon: string;
  windSpeed: number;
  precipitation: number;
  humidity: number;
  uvIndex: number;
};

export function useWeather(cityQuery: string) {
  const [location, setLocation] = useState({ lat: '', lot: '' });
  const [weather, setWeather] = useState<WeatherType | null>(null);

  useEffect(() => {
    navigator.geolocation.getCurrentPosition(
      (position) => {
        setLocation({
          lat: position.coords.latitude.toString(),
          lot: position.coords.longitude.toString(),
        });
      },
      (error) => {
        console.error('Error obtaining location', error);
      },
    );
  }, []);

  useEffect(() => {
    const fetchWeather = async () => {
      try {
        if (!location.lat || !location.lot) return;
        const response = await getCurrentWeather({ q: `${location.lat},${location.lot}` });
        const { current } = response.data;
        const date = new Date().toLocaleDateString('en-US', {
          weekday: 'long',
          year: 'numeric',
          month: '2-digit',
          day: '2-digit',
        });

        const newWeather: WeatherType = {
          date,
          temperature: current.temp_c,
          weather: current.condition.text,
          icon: current.condition.icon,
          windSpeed: current.wind_kph,
          precipitation: current.precip_mm,
          humidity: current.humidity,
          uvIndex: current.uv,
        };
        setWeather(newWeather);
      } catch (error) {
        console.error('Failed to fetch weather data:', error);
      }
    };

    fetchWeather();
  }, [location]);

  return {
    weather,
  };
}
