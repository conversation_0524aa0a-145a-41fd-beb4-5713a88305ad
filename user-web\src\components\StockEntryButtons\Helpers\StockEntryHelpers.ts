import { message } from 'antd';
import {
  IMaterialTransferVoucherItem,
  IMaterialVoucherItem,
} from '../Interfaces/StockEntryInterfaces';
interface IFormData {
  doctype: 'Purchase Receipt';
  posting_date: string;
  company: 'VIIS';
  customer: string;
  warehouse: string;
  description: string;
  employee: string;
  iot_crop: string;
}

//write a function to generate random string with 6 characters
export const generateRandomString = () => {
  return Math.random().toString(36).substring(2, 8);
};

/**
 * Helper functions for handleFinish material transfer
 */
export const validateItems = (selectedItems: IMaterialTransferVoucherItem[]) => {
  for (const item of selectedItems) {
    if (item.qty === null || item.rate === null) {
      message.error(`Hãy cung cấp đầy đủ số lượng và đơn giá của hàng hóa`);
      return false;
    }
    if (item.qty <= 0) {
      message.error(`<PERSON><PERSON> lượng của hàng hóa phải lớn hơn 0`);
      return false;
    }
  }
  return true;
};

export const createMaterialTransferVoucher = (
  values: IFormData,
  selectedItems: IMaterialTransferVoucherItem[],
  posting_date: string,
  posting_time: string,
  taskId: string,
) => {
  return {
    __isLocal: 1,
    __unsaved: 1,
    posting_date,
    posting_time,
    set_posting_time: 1,
    company: 'VIIS',
    description: values.description,
    iot_customer_user: values.employee,
    items: selectedItems.map((item) => ({
      // ...item,
      conversion_factor: item.conversion_factor,
      uom: item.convert_uom_id,
      item_code: item.item_code,
      qty: item.qty,
      doctype: 'Stock Entry Detail',
      name: `new-stock-entry-detail-${generateRandomString()}`,
      s_warehouse: values.warehouse,
      t_warehouse: 'Work In Progress - V',
      // rate: item.rate,
    })),
    doctype: 'Stock Entry',
    purpose: 'Material Transfer',
    stock_entry_type: 'Material Transfer',
    iot_crop: values.iot_crop,
    iot_farming_plan_task: taskId,
  };
};

export const createSubmittingMaterialTransferVoucher = (saveRes: any, taskId: string) => {
  return {
    set_posting_time: 1,
    name: saveRes.name,
    owner: saveRes.owner,
    creation: saveRes.creation,
    modified: saveRes.modified,
    modified_by: saveRes.modified_by,
    naming_series: saveRes.naming_series,
    posting_date: saveRes.posting_date,
    posting_time: saveRes.posting_time,
    status: 'Draft',
    company: saveRes.company,
    description: saveRes.description,
    iot_crop: saveRes.iot_crop,
    iot_farming_plan_task: taskId,
    doctype: 'Stock Entry',
    purpose: 'Material Transfer',
    stock_entry_type: 'Material Transfer',
    items: saveRes.items.map((item: any) => ({
      // ...item,
      name: item.name,
      item_code: item.item_code,
      qty: item.qty,
      conversion_factor: item.conversion_factor,
      uom: item.uom,
      // rate: item.rate,
      warehouse: item.warehouse,
      doctype: item.doctype,
      s_warehouse: item.s_warehouse,
      t_warehouse: 'Work In Progress - V',
    })),
  };
};

/**
 * transfer qty from Work In Progress - V to Selected Warehouse
 * @param values
 * @param selectedItems
 * @param posting_date
 * @param posting_time
 * @param taskId
 * @returns
 */
export const createMaterialTransferFromWIPVoucher = (
  values: IFormData,
  selectedItems: IMaterialTransferVoucherItem[],
  posting_date: string,
  posting_time: string,
  taskId: string,
) => {
  return {
    __isLocal: 1,
    __unsaved: 1,
    posting_date,
    posting_time,
    set_posting_time: 1,
    company: 'VIIS',
    description: values.description,
    iot_customer_user: values.employee,
    doctype: 'Stock Entry',
    purpose: 'Material Transfer',
    stock_entry_type: 'Material Transfer',
    iot_crop: values.iot_crop,
    iot_farming_plan_task: taskId,
    items: selectedItems.map((item) => ({
      // ...item,
      conversion_factor: item.conversion_factor,
      uom: item.convert_uom_id,
      item_code: item.item_code,
      qty: item.qty,
      doctype: 'Stock Entry Detail',
      name: `new-stock-entry-detail-${generateRandomString()}`,
      s_warehouse: 'Work In Progress - V',
      t_warehouse: values.warehouse,
      // rate: item.rate,
    })),
  };
};

export const createSubmittingMaterialTransferFromWIPVoucher = (saveRes: any, taskId: string) => {
  return {
    set_posting_time: 1,
    name: saveRes.name,
    owner: saveRes.owner,
    creation: saveRes.creation,
    modified: saveRes.modified,
    modified_by: saveRes.modified_by,
    naming_series: saveRes.naming_series,
    posting_date: saveRes.posting_date,
    posting_time: saveRes.posting_time,
    status: 'Draft',
    company: saveRes.company,
    description: saveRes.description,
    iot_crop: saveRes.iot_crop,
    iot_farming_plan_task: taskId,
    doctype: 'Stock Entry',
    purpose: 'Material Transfer',
    stock_entry_type: 'Material Transfer',
    items: saveRes.items.map((item: any) => ({
      // ...item,
      name: item.name,
      item_code: item.item_code,
      qty: item.qty,
      conversion_factor: item.conversion_factor,
      uom: item.uom,
      // rate: item.rate,
      s_warehouse: item.s_warehouse,
      t_warehouse: item.t_warehouse,
      doctype: item.doctype,
    })),
  };
};

export const createMaterialIssueVoucher = (
  values: IFormData,
  selectedItems: IMaterialVoucherItem[],
  posting_date: string,
  posting_time: string,
  taskId: string,
) => {
  const commonFields = {
    __isLocal: 1,
    __unsaved: 1,
    posting_date,
    posting_time,
    set_posting_time: 1,
    company: 'VIIS',
    description: values.description,
    iot_customer_user: values.employee,
    items: selectedItems.map((item) => ({
      conversion_factor: item.conversion_factor,
      item_code: item.item_code,
      qty: item.qty,
      uom: item.convert_uom_id,
      // basic_rate: item.rate,
    })),
  };

  return {
    ...commonFields,
    doctype: 'Stock Entry',
    purpose: 'Material Issue',
    stock_entry_type: 'Material Issue',
    iot_crop: values.iot_crop,
    iot_farming_plan_task: taskId,
    items: commonFields.items.map((item) => ({
      ...item,
      doctype: 'Stock Entry Detail',
      name: `new-stock-entry-detail-${generateRandomString()}`,
      s_warehouse: 'Work In Progress - V',
    })),
  };
};

export const createSubmittingMaterialIssueVoucher = (saveRes: any, taskId: string) => {
  const commonFields = {
    set_posting_time: 1,
    name: saveRes.name,
    owner: saveRes.owner,
    creation: saveRes.creation,
    modified: saveRes.modified,
    modified_by: saveRes.modified_by,
    naming_series: saveRes.naming_series,
    posting_date: saveRes.posting_date,
    posting_time: saveRes.posting_time,
    status: 'Draft',
    company: saveRes.company,
    description: saveRes.description,
    iot_crop: saveRes.iot_crop,
    iot_farming_plan_task: taskId,
    items: saveRes.items.map((item: any) => ({
      name: item.name,
      item_code: item.item_code,
      qty: item.qty,
      conversion_factor: item.conversion_factor,
      uom: item.uom,
      // basic_rate: item.basic_rate,
      warehouse: item.warehouse,
      s_warehouse: item.s_warehouse,
      doctype: item.doctype,
    })),
  };
  return {
    ...commonFields,
    doctype: 'Stock Entry',
    purpose: 'Material Issue',
    stock_entry_type: 'Material Issue',
    items: commonFields.items.map((item: any) => ({
      ...item,
      s_warehouse: 'Work In Progress - V',
    })),
  };
};

export const createMaterialReceiptVoucher = (
  values: IFormData,
  selectedItems: IMaterialVoucherItem[],
  posting_date: string,
  posting_time: string,
  taskId: string,
) => {
  const commonFields = {
    __isLocal: 1,
    __unsaved: 1,
    posting_date,
    posting_time,
    set_posting_time: 1,
    company: 'VIIS',
    description: values.description,
    iot_customer_user: values.employee,
    items: selectedItems.map((item) => ({
      __islocal: 1,
      __unsaved: 1,
      conversion_factor: item.conversion_factor,

      item_code: item.item_code,
      qty: item.qty,
      basic_rate: (item.rate || 0) / item.conversion_factor, // Provide a default value of 0 if item.rate is undefined
      basic_amount: (item.rate || 0) * item.conversion_factor,
      transfer_qty: item.qty * item.conversion_factor,
      uom: item.convert_uom_id,
      // stock_uom: '2271aeb8d7',
    })),
  };

  return {
    ...commonFields,
    doctype: 'Stock Entry',
    purpose: 'Material Receipt',
    stock_entry_type: 'Material Receipt',
    iot_crop: values.iot_crop,
    iot_farming_plan_task: taskId,
    items: commonFields.items.map((item) => ({
      ...item,
      doctype: 'Stock Entry Detail',
      name: `new-stock-entry-detail-${generateRandomString()}`,
      t_warehouse: 'Work In Progress - V',
    })),
  };
};

export const createSubmittingMaterialReceiptVoucher = (saveRes: any, taskId: string) => {
  const commonFields = {
    set_posting_time: 1,
    name: saveRes.name,
    owner: saveRes.owner,
    creation: saveRes.creation,
    modified: saveRes.modified,
    modified_by: saveRes.modified_by,
    naming_series: saveRes.naming_series,
    posting_date: saveRes.posting_date,
    posting_time: saveRes.posting_time,
    status: 'Draft',
    company: saveRes.company,
    description: saveRes.description,
    iot_crop: saveRes.iot_crop,
    iot_farming_plan_task: taskId,
    items: saveRes.items.map((item: any) => ({
      name: item.name,
      item_code: item.item_code,
      qty: item.qty,
      conversion_factor: item.conversion_factor,
      basic_rate: item.basic_rate,
      warehouse: item.warehouse,
      t_warehouse: item.t_warehouse,
      doctype: item.doctype,
      uom: item.uom,
    })),
  };
  return {
    ...commonFields,
    doctype: 'Stock Entry',
    purpose: 'Material Receipt',
    stock_entry_type: 'Material Receipt',
    items: commonFields.items.map((item: any) => ({
      ...item,
      t_warehouse: 'Work In Progress - V',
    })),
  };
};
