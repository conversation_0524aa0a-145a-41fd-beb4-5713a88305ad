import treeGreen from '@/assets/img/icons/tree-green.svg';
import { DOCTYPE_ERP } from '@/common/contanst/constanst';
import { getTaskManagerList } from '@/services/farming-plan';
import { generalUpdate } from '@/services/sscript';
import { getFileUrlV2 } from '@/services/utils';
import { ActionType, ProForm, ProList } from '@ant-design/pro-components';
import { FormattedMessage, Link, useIntl } from '@umijs/max';
import { Avatar, Col, message, Row, Select, Space, Tag, Typography } from 'antd';
import { createStyles } from 'antd-use-styles';
import moment from 'moment';
import type { ReactText } from 'react';
import { FC, useRef, useState } from 'react';
import TaskTodoTableEditer from '../Task/TaskTodo/TaskTodoTableEditer';

interface CropPlanDetailProps {
  cropId?: string;
}

const useStyles = createStyles(() => ({
  table: {
    '& .ant-pro-table-list-toolbar-left': {
      flex: 'none',
    },
  },
}));
const dateRangeFilterKey = 'dateRange';

const getStatus = (key: string) => {
  switch (key) {
    case 'Plan':
      return <Tag color="success">Kế hoạch</Tag>;
    case 'Done':
      return <Tag color="success">Đã hoàn thành</Tag>;
    case 'In progress':
      return <Tag color="blue">Đang thực hiện</Tag>;
    case 'Pending':
      return <Tag color="warning">Trì hoãn</Tag>;
    default:
      return <Tag>{key}</Tag>;
  }
};
const TodayTasks: FC<CropPlanDetailProps> = ({ cropId }) => {
  const styles = useStyles();
  const intl = useIntl();
  // const [searchParams, setSearchParams] = useSearchParams();
  // const farmingPlanState = searchParams.get('pl_state_name');
  // const dateRangeFilter = searchParams.get(dateRangeFilterKey);
  const [formFilter] = ProForm.useForm();
  const [dataSource, setDataSource] = useState([]);
  const [loading, setLoading] = useState(false);

  const [expandedRowKeys, setExpandedRowKeys] = useState<readonly ReactText[]>([]);
  const [selectedRowKeys, setSelectedRowKeys] = useState<ReactText[]>([]);
  const rowSelection = {
    selectedRowKeys,
    onChange: (keys: ReactText[]) => setSelectedRowKeys(keys),
  };

  const actionRef = useRef<ActionType>();
  // reload when form filter change
  // useEffect(() => {
  //   if (actionRef.current) {
  //     actionRef.current.reload();
  //   }
  // }, [dateRangeFilter]);

  return (
    <>
      <ProList<{
        title: string;
        subTitle: JSX.Element;
        actions: JSX.Element[];
        description: JSX.Element;
        type?: 'top' | 'inline' | 'new';
        avatar: string;
        children: JSX.Element;
      }>
        actionRef={actionRef}
        request={async (params) => {
          // const today = moment().toISOString();
          // const startTime  = dayjs().startOf('day').toISOString();
          // const endTime = dayjs().endOf('day').toISOString();
          const today = moment().format('YYYY-MM-DD');
          // const startDate = moment().startOf('date').toISOString();
          // const endDate = moment().endOf('date').toISOString();
          const filters = [
            [DOCTYPE_ERP.iotFarmingPlanTask, 'end_date', '>=', today],
            [DOCTYPE_ERP.iotFarmingPlanTask, 'start_date', '<=', today],
          ];
          if (cropId) {
            filters.push([DOCTYPE_ERP.iotCrop, 'name', 'like', cropId]);
          }
          const res: any = await getTaskManagerList({
            page: params.current,
            size: params.pageSize,
            filters: filters,
            or_filters: [],
          });

          return {
            data: res.data,
            total: res.pagination.totalElements,
          };
        }}
        metas={{
          title: {
            dataIndex: 'label',
            search: false,
            render: (dom, entity) => {
              return (
                <Link
                  target="_blank"
                  to={'/farming-management/workflow-management/detail/' + entity.name}
                >
                  {dom}
                </Link>
              );
            },
            search: false,
          },
          subTitle: {
            dataIndex: 'status',
            render: (dom, entity) => {
              return (
                <Space size={0}>
                  {getStatus(dom)}
                  <Tag color="blue">
                    {' '}
                    {intl.formatMessage({ id: 'workflowTab.executionTime' })}{' '}
                    {moment(entity.start_date).format('HH:mm DD/MM/YYYY')}{' '}
                    {intl.formatMessage({ id: 'common.to' })}{' '}
                    {moment(entity.end_date).format('HH:mm DD/MM/YYYY')}
                  </Tag>
                </Space>
              );
            },
            search: false,
          },
          type: {},
          description: {
            render(value, record) {
              const info = record.assigned_to_info?.[0];
              if (!info) {
                return null;
              }
              const involveInArr = record.involve_in_users || [];
              const userNames =
                involveInArr?.map((data: any) => `${data.last_name} ${data.first_name} `) || [];

              return (
                <Space>
                  {info.user_avatar && (
                    <Avatar
                      size={'small'}
                      src={getFileUrlV2({
                        src: info.user_avatar,
                      })}
                    />
                  )}

                  <Tag>
                    <span>
                      Người thực hiện: {info.last_name || ''} {`${info.first_name || ''} `}
                    </span>
                  </Tag>
                  <Tag>
                    <span>Người liên quan: {userNames.join(', ')}</span>
                  </Tag>
                </Space>
              );
            },
          },
          avatar: {
            render: () => (
              <>
                <img src={treeGreen} />
              </>
            ),
          },
          content: {
            render: (dom, entity) => (
              <Row gutter={[20, 20]}>
                <Col md={18}>
                  <TaskTodoTableEditer task_id={entity.name} showToolbar={false} />
                </Col>
                <Col md={6}>
                  <Row gutter={[10, 10]}>
                    <Col md={24}>
                      <Typography.Text>Mùa vụ: {entity.crop_name}</Typography.Text>
                    </Col>
                    <Col md={24}>
                      <Typography.Text>Giai đoạn: {entity.state_name}</Typography.Text>
                    </Col>
                    <Col md={24}>
                      <Select
                        style={{ width: '100%' }}
                        options={[
                          {
                            label: 'Lên kế hoạch',
                            value: 'Plan',
                          },
                          {
                            label: 'Đang thực hiện',
                            value: 'In progress',
                          },
                          {
                            label: 'Hoàn tất',
                            value: 'Done',
                          },
                          {
                            label: 'Trì hoãn',
                            value: 'Pending',
                          },
                        ]}
                        onChange={async (v) => {
                          try {
                            await generalUpdate('iot_farming_plan_task', entity.name, {
                              data: {
                                name: entity.name,
                                status: v,
                              },
                            });
                            actionRef.current?.reload();
                          } catch (error) {
                            message.error(error?.toString?.());
                          }
                        }}
                        value={entity.status}
                      />
                    </Col>
                    <Col md={24}>
                      <Link
                        target="_blank"
                        to={'/farming-management/workflow-management/detail/' + entity.name}
                      >
                        <FormattedMessage id="workflowTab.detail" />
                      </Link>
                    </Col>
                  </Row>
                </Col>
              </Row>
            ),
          },
        }}
        rowKey="name"
        headerTitle={intl.formatMessage({ id: 'workflowTab.workList' })}
        itemLayout="vertical"
        pagination={{}}
      />
    </>
  );
};

export default TodayTasks;
