import { getCropWorksheetStatistic, ICropWorksheetStatistic } from '@/services/crop';
import { ProColumns, ProTable } from '@ant-design/pro-components';
import { FormattedMessage, useIntl } from '@umijs/max';
import { App } from 'antd';

interface Props {
  cropId: string;
}
const WorksheetStatistic = ({ cropId }: Props) => {
  const { message } = App.useApp();
  const intl = useIntl();
  const columns: ProColumns<ICropWorksheetStatistic>[] = [
    {
      title: intl.formatMessage({ id: 'seasonalTab.laborList' }),
      dataIndex: 'work_type_label',
    },
    {
      title: intl.formatMessage({ id: 'seasonalTab.totalExpectedLabor' }),
      dataIndex: 'total_exp_quantity',
    },
    {
      title: intl.formatMessage({ id: 'seasonalTab.totalReality' }),
      dataIndex: 'total_quantity',
    },
    {
      title: <FormattedMessage id="category.material-management.unit" defaultMessage="unknown" />,
      dataIndex: 'type',
    },
    {
      title: intl.formatMessage({ id: 'seasonalTab.totalCost' }),
      dataIndex: 'cost',
    },
  ];
  return (
    <ProTable<ICropWorksheetStatistic>
      headerTitle={intl.formatMessage({ id: 'seasonalTab.laborAndCostStatistics' })}
      columns={columns}
      search={false}
      pagination={{
        pageSizeOptions: [10, 20, 50, 100],
        showSizeChanger: true,
      }}
      request={async (params, sorter, filter) => {
        try {
          const res = await getCropWorksheetStatistic({
            page: params.current,
            size: params.pageSize,
            crop_id: cropId,
          });
          return {
            data: res.data,
            success: true,
          };
        } catch (error: any) {
          message.error(`Error when getting Crop Items Statistic: ${error.message}`);
          return {
            success: false,
          };
        }
      }}
      rowKey={'name'}
    />
  );
};

export default WorksheetStatistic;
