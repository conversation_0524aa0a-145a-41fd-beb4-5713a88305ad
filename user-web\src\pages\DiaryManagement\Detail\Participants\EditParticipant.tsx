import { DOCTYPE_ERP } from '@/common/contanst/constanst';
import { updateParticipantsInCrop } from '@/services/cropManager';
import { getCustomerUserList } from '@/services/customerUser';
import { getParamsReqTable } from '@/services/utils';
import { EditOutlined } from '@ant-design/icons';
import { ModalForm, ProForm, ProFormSelect } from '@ant-design/pro-components';
import { App, Button } from 'antd';
import { FC, useEffect } from 'react';

interface EditParticipantProps {
  id: string;
  cropId: string;
  userId: string;
  onSuccess?: () => void;
  trigger?: JSX.Element;
  open?: boolean;
  onOpenChange?: (open: boolean) => void;
}
interface IFormData {
  iot_customer_user: string;
}
const EditParticipant: FC<EditParticipantProps> = ({
  id,
  cropId,
  userId,
  onSuccess,
  trigger,
  open,
  onOpenChange,
}) => {
  const [form] = ProForm.useForm();
  const { message } = App.useApp();

  useEffect(() => {
    form.setFieldsValue({
      iot_customer_user: userId,
    });
  }, [userId]);
  return (
    <ModalForm<IFormData>
      form={form}
      title={'Chỉnh sửa người tham gia'}
      open={open}
      onOpenChange={onOpenChange}
      trigger={trigger || <Button type="primary" icon={<EditOutlined />} />}
      name="seasonal_management:detail:add_participants"
      onFinish={async (values) => {
        try {
          await updateParticipantsInCrop({
            name: id,
            iot_crop: cropId,
            iot_customer_user: values.iot_customer_user,
          });
          onSuccess?.();
          return true;
        } catch (error: any) {
          if (
            error?.response?.status === 409 &&
            (error.response?.data?.exc as string)?.includes(
              'duplicate key value violates unique constraint',
            )
          ) {
            message.error('Người dùng đã đã tồn tại');
          }
          return false;
        }
      }}
      // modalProps={{
      //   destroyOnClose: true,
      // }}
    >
      <ProFormSelect
        rules={[
          {
            required: true,
          },
        ]}
        label="Chọn người tham gia"
        name="iot_customer_user"
        request={async (params) => {
          const listKey = ['first_name', 'last_name', 'email', 'name'];
          // filter like table
          const paramsFilter =
            params.keyWords && params.keyWords !== ''
              ? listKey.reduce(
                  (acc, item) => ({
                    ...acc,
                    [item]: params.keyWords,
                  }),
                  {},
                )
              : {};

          const paramsReq = getParamsReqTable({
            doc_name: DOCTYPE_ERP.iotCustomerUser,
            tableReqParams: {
              filter: {},
              sort: {},
              params: paramsFilter,
            },
          });
          const res = await getCustomerUserList({
            ...paramsReq,
            filters: undefined,
            or_filters: paramsReq.filters,
            fields: listKey,
          });

          return res.data.map((item) => ({
            label: `${item.first_name} ${item.last_name} - ${item.email}`,
            value: item.name,
          }));
        }}
        showSearch
        debounceTime={200}
      />
    </ModalForm>
  );
};

export default EditParticipant;
