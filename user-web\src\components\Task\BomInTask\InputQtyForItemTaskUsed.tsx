import { TaskItemUsed, useTaskItemUsedCreateStore } from '@/stores/TaskItemUsedCreateStore';
import { ProColumns, ProTable } from '@ant-design/pro-components';
import { useIntl } from '@umijs/max';
import { Form, InputNumber, Select } from 'antd';
import { FC, useEffect, useState } from 'react';

const flattenTaskItems = (taskItems: TaskItemUsed[]) => {
  const flattenedItems = new Map<string, TaskItemUsed>();

  taskItems.forEach((item) => {
    flattenedItems.set(item.iot_category_id, item);

    if (item.bom && item.bom.length > 0) {
      item.bom.forEach((bomItem) => {
        bomItem.bom_items.forEach((bom_item) => {
          const flattenedItem = {
            exp_quantity: bom_item.exp_quantity,
            iot_category_id: bom_item.item_code,
            item_name: bom_item.item_name,
            label: bom_item.item_label,
            ratio: bom_item.ratio,
            parentId: item.iot_category_id,
            uom: bom_item.uom,
            uom_label: bom_item.uom_label,
            conversion_factor: bom_item.conversion_factor,
            uoms: bom_item.uoms,
          };
          flattenedItems.set(flattenedItem.iot_category_id, flattenedItem);
        });
      });
    }
  });

  return Array.from(flattenedItems.values());
};

interface QuantityInputListProps {}

const QuantityInputList: FC<QuantityInputListProps> = () => {
  const { formatMessage } = useIntl();
  const { taskItemUsed, setTaskItemUsed } = useTaskItemUsedCreateStore();
  const [localTaskItemUsed, setLocalTaskItemUsed] = useState<TaskItemUsed[]>(taskItemUsed);

  const form = Form.useFormInstance();

  const handleQuantityChange = (itemName: string, exp_quantity: number | null) => {
    const expQuantity = exp_quantity || 0;
    const newItems = taskItemUsed.map((item) => {
      // nếu đúng với item thì cập nhật lại số lượng, cập nhật lại số lượng cho các item con
      if (item.iot_category_id === itemName) {
        const updatedItem = { ...item, exp_quantity: expQuantity };

        if (updatedItem.bom && updatedItem.bom.length > 0) {
          updatedItem.bom.forEach((bomItem) => {
            bomItem.bom_items.forEach((bom_item) => {
              const newQuantity = expQuantity * bom_item.ratio;
              bom_item.exp_quantity = parseFloat(newQuantity.toFixed(2));
            });
          });
        }

        return updatedItem;
      }

      // nếu là item con thì cập nhật lại số lượng theo tỉ lệ
      if (item.parentId === itemName) {
        const parentItem = taskItemUsed.find((parent) => parent.iot_category_id === item.parentId);
        const parentConversionFactor = parentItem ? parentItem.conversion_factor || 1 : 1;
        const newQuantity = expQuantity * (item.ratio || 1) * parentConversionFactor;
        return { ...item, exp_quantity: parseFloat(newQuantity.toFixed(2)) };
      }

      return item;
    });

    const flattenItems = flattenTaskItems(newItems);
    setTaskItemUsed(flattenItems);
    setLocalTaskItemUsed(flattenItems);
    form.setFieldsValue({ categories: flattenItems });
  };

  const handleUOMChange = (itemName: string, newUOM: string) => {
    console.log({ itemName, newUOM });

    let updatedParentItem: TaskItemUsed | undefined;

    const newItems: TaskItemUsed[] = taskItemUsed.map((item) => {
      // nếu đúng với item thì cập nhật lại uom, cập nhật lại uom cho các item con
      if (item.iot_category_id === itemName) {
        console.log('item', item);
        const uomObj = item.uoms.find((u) => u.uom === newUOM);
        if (uomObj) {
          const updatedItem = {
            ...item,
            uom: uomObj.uom,
            uom_label: uomObj.uom_label,
            conversion_factor: uomObj.conversion_factor,
          };

          if (updatedItem.bom && updatedItem.bom.length > 0) {
            updatedItem.bom.forEach((bomItem) => {
              bomItem.bom_items.forEach((bom_item) => {
                const newQuantity =
                  updatedItem.exp_quantity! * uomObj.conversion_factor * bom_item.ratio;
                bom_item.exp_quantity = parseFloat(newQuantity.toFixed(2));
              });
            });
          }

          console.log('updatedItem', updatedItem);
          updatedParentItem = updatedItem; // Lưu lại parent item đã được cập nhật
          return updatedItem;
        }
      }

      // Nếu có item con thì cập nhật lại uom và qty cho item con
      if (item.parentId === itemName && updatedParentItem) {
        const parentUomObj = updatedParentItem.uoms.find((u) => u.uom === updatedParentItem!.uom);
        console.log('parentUomObj', parentUomObj);
        if (parentUomObj) {
          const parentConversionFactor = parentUomObj.conversion_factor;
          const childUomObj = item.uoms[0]; //lấy uom cơ bản, vì newUOM là uom của item parent đang được chọn
          console.log('childUomObj', childUomObj);
          if (childUomObj) {
            const newQuantity =
              updatedParentItem.exp_quantity! *
              parentConversionFactor *
              (item.ratio || 1) *
              childUomObj.conversion_factor;
            console.log('newQuantity', newQuantity);
            return {
              ...item,
              exp_quantity: parseFloat(newQuantity.toFixed(2)),
              uom: childUomObj.uom,
              uom_label: childUomObj.uom_label,
              conversion_factor: childUomObj.conversion_factor,
            };
          }
        }
      }

      return item;
    });

    console.log('newItems when change UOM', newItems);
    const flattenItems = flattenTaskItems(newItems);
    setTaskItemUsed(flattenItems);
    setLocalTaskItemUsed(flattenItems);
    form.setFieldsValue({ categories: flattenItems });
  };

  useEffect(() => {
    const flattenItems = flattenTaskItems(taskItemUsed);
    setLocalTaskItemUsed(flattenItems);
    form.setFieldsValue({ categories: flattenItems });
  }, [taskItemUsed]);

  const columns: ProColumns<TaskItemUsed>[] = [
    {
      title: formatMessage({ id: 'common.label' }),
      dataIndex: 'label',
      key: 'label',
    },
    {
      title: formatMessage({ id: 'common.expected_qty' }),
      dataIndex: 'exp_quantity',
      key: 'exp_quantity',
      render(dom, entity, index, action, schema) {
        return (
          <InputNumber
            min={0}
            value={entity.exp_quantity}
            onChange={(value) => handleQuantityChange(entity.iot_category_id, value)}
          />
        );
      },
    },
    {
      title: formatMessage({ id: 'common.unit' }),
      dataIndex: 'uom_label',
      render(dom, entity, index, action, schema) {
        return (
          <Select
            defaultValue={entity.uom}
            style={{ width: 120 }}
            options={
              entity.uoms &&
              entity.uoms.map((uom) => {
                return { label: uom.uom_label, value: uom.uom };
              })
            }
            onChange={(value) => handleUOMChange(entity.iot_category_id, value)}
          />
        );
      },
      key: 'uom_label',
    },
  ];

  return (
    <ProTable
      search={false}
      dataSource={localTaskItemUsed}
      columns={columns}
      rowKey="iot_category_id"
      pagination={false}
      scroll={{ y: 400 }}
    />
  );
};

export default QuantityInputList;
