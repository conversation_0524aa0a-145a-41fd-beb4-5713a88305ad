import { createNote } from '@/services/diary-2/note';
import { createTrace } from '@/services/diary-2/trace';
import { useIntl, useRequest } from '@umijs/max';
import { App } from 'antd';

export default function useCreate(
  { onSuccess, onError } = {} as {
    onSuccess?: () => void;
    onError?: () => void;
  },
) {
  const { formatMessage } = useIntl();
  const { message } = App.useApp();
  return useRequest(createTrace, {
    manual: true,
    onSuccess(data, params) {
      message.success(
        formatMessage({
          id: 'common.success',
        }),
      );
      onSuccess?.();
    },
    onError: (error) => {
      //message.error(error.message);
      onError?.();
    },
  });
}
