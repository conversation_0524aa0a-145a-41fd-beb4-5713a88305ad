import { nonAccentVietnamese } from '@/utils/string';
import { PageContainer, PageContainerProps } from '@ant-design/pro-components';
import { useSearchParams } from '@umijs/max';
import React, { FC, ReactNode, Suspense, useMemo } from 'react';

export type PageTabItem = {
  label: string;
  tabKey?: string;
  component?: () => React.JSX.Element;
  extraPage?: ReactNode[];
  fallback?: ReactNode;
};

interface PageContainerTabsProps {
  tabsItems?: PageTabItem[];
  searchParamsUrlKey?: string;
  autoFormatTabKey?: boolean;
  onTabChange?: (tabKey: string | number) => void;
  pageTitle?: ReactNode;
}

const PageContainerTabsWithSearch: FC<PageContainerTabsProps> = ({
  tabsItems,
  searchParamsUrlKey = 'tab',
  onTabChange,
  pageTitle,
}) => {
  const tabsItemsFormat = useMemo(
    () =>
      tabsItems?.map((item) => ({
        ...item,
        tabKey: item.tabKey || nonAccentVietnamese(item.label),
      })),
    [tabsItems],
  );
  const [searchParams, setSearchParams] = useSearchParams();
  const tabActive = searchParamsUrlKey ? searchParams.get(searchParamsUrlKey) : undefined;
  console.log('tabActive', tabActive);
  const setTabActive = searchParamsUrlKey
    ? (tabActiveVal: string | number) => {
        searchParams.set(searchParamsUrlKey, tabActiveVal.toString());
        setSearchParams(searchParams);
      }
    : undefined;

  const tabList: PageContainerProps['tabList'] = tabsItemsFormat?.map((item) => ({
    tab: item.label,
    key: item.tabKey,
    tabKey: item.tabKey,
  }));

  const tabPageActive = searchParamsUrlKey
    ? tabsItemsFormat?.find((item) => item.tabKey === tabActive) || tabsItemsFormat?.[0]
    : undefined;
  const ComponentActive = tabPageActive?.component;

  return (
    <PageContainer
      fixedHeader
      extra={tabPageActive?.extraPage}
      tabActiveKey={tabPageActive?.tabKey}
      tabList={tabList}
      onTabChange={(tabActiveVal) => {
        onTabChange?.(tabActiveVal);
        if (searchParamsUrlKey) setTabActive?.(tabActiveVal);
      }}
      title={pageTitle}
    >
      <Suspense fallback={tabPageActive?.fallback || null}>
        {ComponentActive && <ComponentActive />}
      </Suspense>
    </PageContainer>
  );
};

export default PageContainerTabsWithSearch;
