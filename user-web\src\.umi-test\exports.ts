// @ts-nocheck
// This file is generated by Umi automatically
// DO NOT CHANGE IT MANUALLY!
// defineApp
export { defineApp } from './core/defineApp'
export type { RuntimeConfig } from './core/defineApp'
// plugins
export { Access, useAccess, useAccessMarkedRoutes } from 'D:/WORK/PYROJECT/VIIS/viis-iot-web-v2/user-web/src/.umi-test/plugin-access';
export { addLocale, setLocale, getLocale, getIntl, useIntl, injectIntl, formatMessage, FormattedMessage, getAllLocales, FormattedDate, FormattedDateParts, FormattedDisplayName, FormattedHTMLMessage, FormattedList, FormattedNumber, FormattedNumberParts, FormattedPlural, FormattedRelativeTime, FormattedTime, FormattedTimeParts, IntlProvider, RawIntlProvider, SelectLang } from 'D:/WORK/PYROJECT/VIIS/viis-iot-web-v2/user-web/src/.umi-test/plugin-locale';
export { Provider, useModel } from 'D:/WORK/PYROJECT/VIIS/viis-iot-web-v2/user-web/src/.umi-test/plugin-model';
export { useRequest, UseRequestProvider, request, getRequestInstance } from 'D:/WORK/PYROJECT/VIIS/viis-iot-web-v2/user-web/src/.umi-test/plugin-request';
// plugins types.d.ts
export * from 'D:/WORK/PYROJECT/VIIS/viis-iot-web-v2/user-web/src/.umi-test/plugin-access/types.d';
export * from 'D:/WORK/PYROJECT/VIIS/viis-iot-web-v2/user-web/src/.umi-test/plugin-antd/types.d';
export * from 'D:/WORK/PYROJECT/VIIS/viis-iot-web-v2/user-web/src/.umi-test/plugin-layout/types.d';
export * from 'D:/WORK/PYROJECT/VIIS/viis-iot-web-v2/user-web/src/.umi-test/plugin-request/types.d';
// @umijs/renderer-*
export { createBrowserHistory, createHashHistory, createMemoryHistory, Helmet, HelmetProvider, createSearchParams, generatePath, matchPath, matchRoutes, Navigate, NavLink, Outlet, resolvePath, useLocation, useMatch, useNavigate, useOutlet, useOutletContext, useParams, useResolvedPath, useRoutes, useSearchParams, useAppData, useClientLoaderData, useRouteProps, useSelectedRoutes, useServerLoaderData, renderClient, __getRoot, Link, useRouteData, __useFetcher, withRouter } from 'D:/WORK/PYROJECT/VIIS/viis-iot-web-v2/user-web/node_modules/@umijs/renderer-react';
export type { History } from 'D:/WORK/PYROJECT/VIIS/viis-iot-web-v2/user-web/node_modules/@umijs/renderer-react'
// umi/client/client/plugin
export { ApplyPluginsType, PluginManager } from 'D:/WORK/PYROJECT/VIIS/viis-iot-web-v2/user-web/node_modules/umi/client/client/plugin.js';
export { history, createHistory } from './core/history';
export { terminal } from './core/terminal';
// react ssr
export const useServerInsertedHTML: Function = () => {};
// test
export { TestBrowser } from './testBrowser';
