import vietnam_location from '@/helpers/tree.json';

export const getCityCodeByName = (cityName?: string): string | undefined =>
  Object.keys(vietnam_location).find((key) => vietnam_location[key].name === cityName);
export const getDistrictCodeByName = ({
  cityCode,
  districtName,
}: {
  cityCode?: string;
  districtName?: string;
}): string | undefined => {
  if (cityCode && districtName) {
    return Object.keys(vietnam_location[cityCode]['quan-huyen']).find(
      (districtKey) => vietnam_location?.[cityCode]?.['quan-huyen']?.[districtKey]?.name === districtName,
    );
  }
  return undefined;
};
export const getDistrictOptions = (cityCode?: string) => {
  if (cityCode) {
    return Object.keys(vietnam_location?.[cityCode]?.['quan-huyen'] || {}).map((key) => ({
      label: vietnam_location[cityCode]['quan-huyen'][key].name,
      value: vietnam_location[cityCode]['quan-huyen'][key].name,
      code: key,
    }));
  }
  return [];
};
export const getWardOptions = ({
  cityCode,
  districtCode,
}: {
  districtCode?: string;
  cityCode?: string;
}) => {

  if (districtCode && cityCode) {
    return Object.keys(
      vietnam_location[cityCode as string]?.['quan-huyen']?.[districtCode]?.['xa-phuong'] || {},
    ).map((key) => {
      const districtName =
        vietnam_location[cityCode as string]['quan-huyen'][districtCode]['xa-phuong'][key].name;
      return {
        value: districtName,
        label: districtName,
      };
    });
  }
  return [];
};
