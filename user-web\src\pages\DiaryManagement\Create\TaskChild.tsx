import { PlusOutlined } from '@ant-design/icons';
import {
  FormListActionType,
  ProFormCheckbox,
  ProFormList,
  ProFormText,
} from '@ant-design/pro-components';
import { Button, Card, Space } from 'antd';
import { FC, ReactNode, useRef } from 'react';

interface TaskChildProps {
  children?: ReactNode;
}

const TaskChild: FC<TaskChildProps> = () => {
  const actionRef = useRef<
    FormListActionType<{
      name: string;
    }>
  >();
  return (
    <Card
      title="Công việc con"
      extra={
        <Button
          key="Add"
          icon={<PlusOutlined />}
          onClick={() => {
            actionRef.current?.add();
          }}
        >
          Thêm công việc con
        </Button>
      }
    >
      <ProFormList
        actionRef={actionRef}
        name="users"
        initialValue={[
          {
            task: '',
            status: false,
          },
        ]}
        creatorButtonProps={false}
        deleteIconProps={{
          tooltipText: 'Xóa',
        }}
        copyIconProps={{
          tooltipText: 'Sao chép',
        }}

        // creatorRecord={{
        //   useMode: 'none',
        // }}
      >
        {() =>
          // Basic information of the current row {name: number; key: number}
          // meta,
          // current line number
          // index,

          // action,
          // total number of rows
          // count,
          {
            return (
              <Space>
                {/* <ProForm.Group grid rowProps={{
                  gutter:[]
                }}> */}
                <ProFormCheckbox
                  colProps={{
                    span: '53px',
                  }}
                  name="status"
                />
                <ProFormText name="task" width={'xl'} />
                {/* </ProForm.Group> */}
              </Space>
            );
          }
        }
      </ProFormList>
    </Card>
  );
};

export default TaskChild;
