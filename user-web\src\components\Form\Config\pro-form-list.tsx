import { ProFormListProps } from '@ant-design/pro-components';
import { useIntl } from '@umijs/max';
import { useMemo } from 'react';

export const useProFormList = () => {
  const { formatMessage } = useIntl();

  return useMemo<Partial<ProFormListProps<any>>>(
    () => ({
      // copyIconProps: {
      //   tooltipText: formatMessage({
      //     id: 'common.copy',
      //   }),
      // },
      copyIconProps: false,
      deleteIconProps: {
        tooltipText: formatMessage({
          id: 'common.delete',
        }),
      },
      creatorButtonProps: {
        children: formatMessage({
          id: 'common.add',
        }),
      },
      alwaysShowItemLabel: true,
      // itemRender: (dom, listMeta) => (
      //   <Card extra={dom.action} title={listMeta?.record?.name}>
      //     {dom.listDom}
      //   </Card>
      // ),
    }),
    [formatMessage],
  );
};
