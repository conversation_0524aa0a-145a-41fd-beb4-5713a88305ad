import {
  ProFormDigit,
  ProFormGroup,
  ProFormList,
  ProFormSelect,
  ProFormText,
} from '@ant-design/pro-components';
import { useIntl } from '@umijs/max';
import { Card } from 'antd';
import { FC, ReactNode } from 'react';

interface TaskProps {
  children?: ReactNode;
}

const Task: FC<TaskProps> = ({ children }) => {
  const { formatMessage } = useIntl();
  return (
    <Card
      title={formatMessage({
        id: 'common.task',
      })}
    >
      <ProFormList name={'task'}>
        {() => {
          return (
            <ProFormGroup>
              <ProFormText
                label={formatMessage({
                  id: 'common.task_name',
                })}
              />
              <ProFormSelect
                mode="tags"
                label={formatMessage({
                  id: 'common.level',
                })}
              />
              <ProFormDigit
                label={formatMessage({
                  id: 'common.time',
                })}
                width={'xs'}
              />
              <ProFormText
                label={formatMessage({
                  id: 'common.supplies',
                })}
                width={'xs'}
              />
            </ProFormGroup>
          );
        }}
      </ProFormList>
    </Card>
  );
};

export default Task;
