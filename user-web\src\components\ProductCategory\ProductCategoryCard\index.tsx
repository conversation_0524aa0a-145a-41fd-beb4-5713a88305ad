import React, { useRef, useState } from 'react';

import UnitAndPacking from '@/pages/ItemManagement/UnitAndPacking';
import { listAllItem } from '@/services/products';
import { IIotAgricultureProduct } from '@/services/products/type';
import { generateAPIPath } from '@/services/utils';
import { EyeOutlined } from '@ant-design/icons';
import { ProColumns, ProTable } from '@ant-design/pro-components';
import { FormattedMessage, useAccess } from '@umijs/max';
import { Button, Modal, QRCode, Space, Typography } from 'antd';
import numeral from 'numeral';
import { useSearchParams } from 'react-router-dom';
import Create from './Components/Create';
import Delete from './Components/Delete';
import Update from './Components/Update';

interface ActionType {
  reload: (resetPageIndex?: boolean) => void;
  reloadAndRest: () => void;
  reset: () => void;
  clearSelected?: () => void;
  startEditable: (rowKey: String) => boolean;
  cancelEditable: (rowKey: String) => boolean;
}

const ProductCategoryManagement: React.FC = () => {
  const [categories, setCategories] = useState<IIotAgricultureProduct[]>([]);
  const [loading, setLoading] = useState<boolean>(false);
  const [searchParams, setSearchParams] = useSearchParams();
  //   const { myProducts, fetchAllProducts } = useModel('MyProducts');
  const tableRef = useRef<ActionType>();
  const [isModalOpenQR, setIsModalOpenQR] = useState(false);
  const [currentItem, setCurrentItem] = useState<any>();

  const downloadQRCode = (category_id: string | undefined) => {
    const canvas = document
      .getElementById(`myqrcode-${category_id}`)
      ?.querySelector<HTMLCanvasElement>('canvas');
    console.log('canvas qr', document.getElementById(`myqrcode-${category_id}`));
    if (canvas) {
      const url = canvas.toDataURL();
      const a = document.createElement('a');
      a.download = 'QRCode.png';
      a.href = url;
      document.body.appendChild(a);
      a.click();
      document.body.removeChild(a);
    }
  };
  const handlePopupQRCode = (entity: any) => {
    setIsModalOpenQR(true);
    setCurrentItem(entity);
  };
  //   useEffect(() => {
  //     setCategories(myProducts);
  //   }, [myProducts]);

  const reloadTable = async () => {
    tableRef.current?.reload();
  };

  const columns: ProColumns<any>[] = [
    {
      title: 'ID',
      dataIndex: 'name',
      width: 100,
      hideInSearch: true,
      hideInTable: true,
    },
    {
      title: <FormattedMessage id="common.form.image" defaultMessage="Image" />,
      dataIndex: 'image',
      width: 100,
      search: false,
      render: (dom: any, entity: any) =>
        entity.image ? (
          <img
            width={'70px'}
            src={generateAPIPath('api/v2/file/download?file_url=' + entity.image)}
          ></img>
        ) : (
          ''
        ),
    },
    {
      title: <FormattedMessage id="category.product.product_code" defaultMessage="unknown" />,
      dataIndex: 'iot_product_id',
      width: 120,
    },
    {
      title: <FormattedMessage id="category.product.product_name" defaultMessage="unknown" />,
      dataIndex: 'label',
      width: 120,
    },

    {
      title: (
        <FormattedMessage id="category.material-management.packing_unit" defaultMessage="unknown" />
      ),
      dataIndex: 'packing_unit_label',
      width: 100,
      search: false,
    },
    {
      title: <FormattedMessage id="category.material-management.unit" defaultMessage="unknown" />,
      dataIndex: 'unit_label',
      width: 100,
      search: false,
    },
    {
      title: (
        <FormattedMessage
          id="category.material-management.conversion_factor"
          defaultMessage="unknown"
        />
      ),
      dataIndex: 'conversion_factor',
      width: 100,
      render(dom, entity, index, action, schema) {
        return <>{numeral(entity.conversion_factor).format('0,0.00')}</>;
      },
      search: false,
    },
    {
      title: (
        <FormattedMessage id="category.material-management.unit_price" defaultMessage="unknown" />
      ),
      dataIndex: 'product_price',
      width: 100,
      search: false,
      render: (dom: any, entity: any) => {
        if (entity.product_price) {
          return <>{numeral(entity.product_price).format('0,0.00')}</>;
        } else return <>-</>;
      },
    },
    {
      title: 'Action',
      dataIndex: 'name',
      width: 100,
      search: false,
      render: (dom: any, entity: any) => (
        <>
          <>
            {canUpdateProduct && (
              <Update key={'edit' + entity.name} refreshFnc={reloadTable} value={entity} />
            )}
          </>
          <>
            {canDeleteProduct && (
              <Delete key={'remove' + entity.name} refreshFnc={reloadTable} value={entity} />
            )}
          </>
        </>
      ),
    },
    {
      title: 'QR Code',
      dataIndex: 'qr',
      //   width: 80,
      render(dom, entity, index, action, schema) {
        return <Button icon={<EyeOutlined />} onClick={() => handlePopupQRCode(entity)} />;
      },
      search: false,
    },
  ];
  const access = useAccess();
  const canCreateProduct = access.canCreateInProductManagement();
  const canUpdateProduct = access.canUpdateInProductManagement();
  const canDeleteProduct = access.canDeleteInProductManagement();

  const toolBarRender: any = [];
  if (canCreateProduct) {
    toolBarRender.push(
      <UnitAndPacking key={'unit'} refreshFnc={reloadTable} />,
      <Create refreshFnc={reloadTable} />,
    );
  }
  return (
    <>
      <Modal
        open={isModalOpenQR}
        onCancel={() => setIsModalOpenQR(false)}
        footer={[]}
        title={`Mã QR của ${currentItem?.label}`}
      >
        <div id={`myqrcode-${currentItem?.name}`}>
          <Space direction="vertical" align="center">
            <Button type="link" onClick={() => downloadQRCode(currentItem?.name)}>
              {currentItem && (
                <QRCode size={400} value={`category,${currentItem.name}`} bgColor="#fff" />
              )}
            </Button>
            <Typography.Text italic>Nhấn vào QR để tải ảnh về máy</Typography.Text>
          </Space>
        </div>
      </Modal>
      <ProTable<API.User, API.PageParams>
        // scroll={{ x: 960 }}
        expandable={{}}
        ellipsis
        size="small"
        actionRef={tableRef}
        rowKey="name"
        // rowSelection={rowSelection}
        // loading={loading}
        // dataSource={[...users]}
        request={async (
          params: API.PageParams & {
            pageSize?: number | undefined;
            current?: number | undefined;
            keyword?: string | undefined;
            name?: string | undefined;
            iot_product_id?: string | undefined;
            label?: string | undefined;
          },
          sort,
          filter,
        ) => {
          try {
            const pageSize = params.pageSize;
            const req = await listAllItem(params);
            return {
              data: req.data,
              success: true,
              total: req.pagination.totalElements,
            };
          } catch (error) {
            console.log(error);
          } finally {
          }
        }}
        columns={columns}
        search={{ span: 8, labelWidth: 'auto' }}
        headerTitle={<FormattedMessage id="category.product.product_list" />}
        rowSelection={{}}
        tableAlertOptionRender={({ selectedRowKeys, selectedRows, onCleanSelected }) => {
          return <></>;
        }}
        toolBarRender={() => toolBarRender}
        pagination={{
          defaultPageSize: 20,
          showSizeChanger: true,
          pageSizeOptions: ['20', '50', '100'],
        }}
      />
    </>
  );
};

export default ProductCategoryManagement;
