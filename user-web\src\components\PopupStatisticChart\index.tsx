import { ICropStatisticGeneral } from '@/types/cropStatisticGeneral.type';
import { ProForm, ProFormDateRangePicker, ProFormSelect } from '@ant-design/pro-components';
import { Card, Form, List, Modal, Skeleton, Space, Typography } from 'antd';
import { useEffect, useState } from 'react';
import { IFilter } from '../CustomizeInfoCard';
import SimpleMultitypeChart from '../SimpleMultitypeChart';
import { DEFAULT_DATE_FORMAT_WITHOUT_TIME } from '@/common/contanst/constanst';
const { Text } = Typography;
const bodyStyle: React.CSSProperties = {
  fontSize: 24,
  color: '#44C4A1',
  fontWeight: 'bold',
  backgroundColor: 'none',
};
export interface Props {
  isModalOpen: any;
  setIsModalOpen: any;
  currentItem: any;
  inputFilter: any;
  queryingData: (
    record: any,
    filter: IFilter,
  ) => Promise<{
    generalInfos: ICropStatisticGeneral | undefined;
    selectedData:
      | {
          labels: string[];
          datasets: {
            type: 'bar';
            label: string;
            data: number[];
            backgroundColor: string;
          }[];
        }
      | undefined;
  }>;
  title: string;
}
interface IFormData {
  cropList: string[];
  type: 'YEAR' | 'MONTH' | 'WEEK';
  date_range: string[];
}
const PopupStatisticChart = ({
  isModalOpen,
  setIsModalOpen,
  currentItem,
  inputFilter,
  queryingData,
  title,
}: Props) => {
  const [formattedGeneralInfos, setFormattedGeneralInfos] = useState<any>();
  const [filter, setFilter] = useState<IFilter>();

  const [type, setType] = useState<'year' | 'month' | 'week'>('week');
  const [selectedData, setSelectedData] = useState<any>();
  const [isLoading, setIsLoading] = useState(true);
  const [form] = Form.useForm();
  const handleChooseType = (e: string) => {
    setType(e.toLowerCase() as any);
    form.resetFields(['date_range']);
  };
  useEffect(() => {
    const fetchData = async () => {
      if (filter) {
        console.log({ filter });
        const { generalInfos, selectedData } = await queryingData(currentItem, filter);
        const contentList = [
          { title: 'Tổng dự kiến', value: generalInfos?.totalExpectedQuantity },
          { title: 'Tổng thực tế', value: generalInfos?.totalRealQuantity },
          {
            title: 'Đơn vị tính',
            value:
              currentItem?.unit_label || currentItem?.item_unit_label || currentItem?.label || '',
          },
        ];
        setFormattedGeneralInfos(contentList);
        setSelectedData(selectedData);
        setIsLoading(false);
      }
    };
    fetchData();
  }, [currentItem, filter]);

  useEffect(() => {
    form.setFieldValue('type', inputFilter?.type);
    form.setFieldValue('date_range', [inputFilter?.start_date, inputFilter?.end_date]);
    setFilter(inputFilter);
  }, [inputFilter, currentItem]);

  return (
    <Modal
      open={isModalOpen}
      onCancel={() => setIsModalOpen(false)}
      footer={[]}
      title={title}
      width="1000px"
    >
      <Skeleton loading={isLoading} active>
        <ProForm<IFormData>
          key="filter"
          form={form}
          layout="horizontal"
          submitter={{
            render: (_) => {
              return [];
            },
          }}
          onValuesChange={async (e) => {
            setFilter({
              ...filter,
              start_date: e?.date_range[0],
              end_date: e?.date_range[1],
              ...e,
            });
          }}
        >
          <ProForm.Group>
            <ProFormSelect
              name={'type'}
              options={[
                { label: 'Tuần', value: 'WEEK' },
                { label: 'Tháng', value: 'MONTH' },
                { label: 'Năm', value: 'YEAR' },
              ]}
              onChange={handleChooseType}
              placeholder={'Tuần/Tháng/Năm'}
              fieldProps={{
                style: {
                  width: 100,
                },
              }}
            />

            <ProFormDateRangePicker
              name="date_range"
              fieldProps={{
                format: DEFAULT_DATE_FORMAT_WITHOUT_TIME,
                picker: type,
                style: {
                  width: 240,
                },
              }}
              initialValue={['2023-01-01', '2023-12-31']}
            />
          </ProForm.Group>
        </ProForm>
      </Skeleton>
      <div
        style={{
          marginTop: 20,
          backgroundImage: 'linear-gradient(rgba(228,255,244,1), rgba(228,255,244,0))',
        }}
      >
        <List
          grid={{
            column: 3,
            gutter: 10,
            md: 3,
            sm: 2,
            xs: 1,
          }}
          dataSource={formattedGeneralInfos}
          renderItem={(item: any) => (
            <List.Item>
              <Card
                bodyStyle={bodyStyle}
                style={{
                  background: 'none',
                  border: 'none',
                }}
              >
                <Space direction="vertical" size={'small'}>
                  <Text style={{ whiteSpace: 'normal' }}>{item.title}</Text>
                  <Text style={bodyStyle}>{item.value}</Text>
                </Space>
              </Card>
            </List.Item>
          )}
        />
      </div>
      {selectedData ? <SimpleMultitypeChart data={selectedData} chart_label={``} /> : 'Loading...'}
    </Modal>
  );
};

export default PopupStatisticChart;
