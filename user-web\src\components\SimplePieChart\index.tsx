import { List, Typography } from 'antd';
import { ArcElement, Chart as ChartJS, Legend, Tooltip } from 'chart.js';
import { useEffect, useRef } from 'react';
import { Pie } from 'react-chartjs-2';

ChartJS.register(ArcElement, Tooltip, Legend);
const colors = [
  // '#80DEEA', // Cyan 200
  '#A5D6A7', // Green 200
  '#F48FB1', // Pink 200
  '#FFCC80', // Orange 200
  '#A5D6A7', // Green 200
  '#B39DDB', // Deep Purple 200
  '#90CAF9', // Blue 200
  '#FFAB91', // Deep Orange 200
  '#CE93D8', // Purple 200
  '#9FA8DA', // Indigo 200
  '#80CBC4', // Teal 200
  '#C5E1A5', // Light Green 200
  '#FFE082', // Amber 200
  '#EF9A9A', // Red 200
  '#B39DDB', // Deep Purple 200
];
export interface Props {
  data: any;
  chart_label: string;
  height?: string;
}
// Tạo màu hover bằng cách thêm alpha 0.8
const hoverColors = colors.map((color) => color + 'CC'); // CC là hex cho alpha 0.8

const SimplePieChart = ({ chart_label, data, height }: Props) => {
  const options = {
    maintainAspectRatio: false,
    plugins: {
      legend: {
        display: false,
      },
      tooltip: {
        callbacks: {
          label: function (context: any) {
            return `${context.label}: ${context.formattedValue} VNĐ`;
          },
        },
      },
    },
    elements: {
      arc: {
        borderWidth: 1,
        borderColor: '#fff',
      },
    },
    hover: {
      mode: 'nearest' as const,
    },
  };

  const formattedData = {
    ...data,
    datasets: [
      {
        ...data.datasets[0],
        backgroundColor: colors.slice(0, data.labels.length),
        hoverBackgroundColor: hoverColors.slice(0, data.labels.length),
        borderWidth: 1,
        borderColor: '#fff',
        hoverBorderWidth: 1,
        hoverBorderColor: '#fff',
      },
    ],
  };

  const chartRef = useRef<ChartJS<'pie'>>(null);

  useEffect(() => {
    chartRef.current?.update();
  }, [data]);

  return (
    <div style={{ height: '100%', display: 'flex', flexDirection: 'column' }}>
      <div style={{ flex: '1', minHeight: '360px' }}>
        <Pie options={options} data={formattedData} ref={chartRef} />
      </div>

      <List
        size="small"
        dataSource={data.list}
        style={{ marginTop: '16px' }}
        renderItem={(item: any, index: number) => (
          <List.Item style={{ padding: '4px 0', border: 'none' }}>
            <div style={{ display: 'flex', alignItems: 'center', width: '100%' }}>
              <div
                style={{
                  width: '12px',
                  height: '12px',
                  backgroundColor: colors[index % colors.length],
                  marginRight: '8px',
                  borderRadius: '2px',
                }}
              />
              <Typography.Text style={{ flex: 1 }}>{item[0]}</Typography.Text>
              <Typography.Text type="secondary">{item[2]}</Typography.Text>
            </div>
          </List.Item>
        )}
      />
    </div>
  );
};

export default SimplePieChart;
