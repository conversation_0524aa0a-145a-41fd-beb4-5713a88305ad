export default {
  'warehouse-management.reconciliation-voucher.object_code': 'Item code',
  'warehouse-management.reconciliation-voucher.object_name': 'Item name',
  'warehouse-management.reconciliation-voucher.import_id': 'Import ID',
  'warehouse-management.reconciliation-voucher.export_id': 'Export ID',
  'warehouse-management.reconciliation-voucher.check_id': 'Check ID',
  'warehouse-management.reconciliation-voucher.inventory': 'Category Inventory',
  'warehouse-management.reconciliation-voucher.detail': 'Detail',
  'warehouse-management.reconciliation-voucher.quantity': 'Quantity',
  'warehouse-management.reconciliation-voucher.type': 'Type',
  'warehouse-management.reconciliation-voucher.category_storage': 'Material Storage',
  'warehouse-management.reconciliation-voucher.select_storage': 'Select Storage',
  'warehouse-management.reconciliation-voucher.inventory_quantity': 'Inventory Quantity',
  'warehouse-management.reconciliation-voucher.inventory_price': 'Total Price',
  'warehouse-management.reconciliation-voucher.transaction_date': 'Reconciliation Date',
  'warehouse-management.reconciliation-voucher.document_date': 'Document Date',
  'warehouse-management.reconciliation-voucher.document_code': 'Document Code',
  'warehouse-management.reconciliation-voucher.employee': 'Employee',
  'warehouse-management.reconciliation-voucher.import_from_excel': 'Import from Excel',
  'warehouse-management.reconciliation-voucher.total_quantity': 'Total Quantity',
  'warehouse-management.reconciliation-voucher.total_price': 'Total Price',
  'warehouse-management.reconciliation-voucher.download': 'Download Form',
  'warehouse-management.reconciliation-voucher.update': 'Update Form',
  'warehouse-management.reconciliation-voucher.check_type': 'Check Type',
  'warehouse-management.reconciliation-voucher.check_date': 'Inventory Check Date',
  'warehouse-management.reconciliation-voucher.present_quantity': 'Present Quantity',
  'warehouse-management.reconciliation-voucher.real_quantity': 'Real Quantity',
  'warehouse-management.reconciliation-voucher.quality': 'Quality',
  'warehouse-management.reconciliation-voucher.total_amount': 'Total Amount',
  'warehouse-management.reconciliation-voucher.item_list': 'Item list',
  'warehouse-management.reconciliation-voucher.add_item': 'Add item(s)',
  'warehouse-management.reconciliation-voucher.supplier': 'Supplier',
  'warehouse-management.reconciliation-voucher.item_name': 'Item',
  'warehouse-management.reconciliation-voucher.conversion_factor': 'Conversion Factor',
  'warehouse-management.reconciliation-voucher.rate': 'Rate',
  'warehouse-management.reconciliation-voucher.item_code': 'Item Code',
  'warehouse-management.reconciliation-voucher.price': 'Price',
  'warehouse-management.reconciliation-voucher.actual_qty': 'Actual Quantity',
  'warehouse-management.reconciliation-voucher.recorded_qty': 'Recorded Quantity',
  'warehouse-management.reconciliation-voucher.reconciliation': 'Reconciliation',
  'warehouse-management.reconciliation-voucher.select_accounting_date':
    'Please select the accounting date',
};
