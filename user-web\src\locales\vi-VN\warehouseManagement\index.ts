import exportHistoryTab from './exportHistoryTab';
import exportVoucher from './exportVoucher';
import importHistoryTab from './importHistoryTab';
import importVoucher from './importVoucher';
import ItemTable from './ItemTable';
import reconcilicationVoucher from './reconcilicationVoucher';
import stockEntryHistory from './stockEntryHistory';
import stockLedgerTab from './stockLedgerTab';
import warehouseList from './warehouseList';

export default {
  ...importHistoryTab,
  ...stockLedgerTab,
  ...importVoucher,
  ...exportVoucher,
  ...warehouseList,
  ...ItemTable,
  ...exportHistoryTab,
  ...reconcilicationVoucher,
  ...stockEntryHistory,
  'warehouse-management.import-voucher': 'Nhập hàng',
  'warehouse-management.export-voucher': 'Xuất hàng',
  'common.material-transfer-voucher': 'Chuyển kho',
  'common.material-transfer-to-warehouse': 'Giảm SL sử dụng',
  'warehouse-management.material-issue-item': 'Tiêu hao vật tư',
  'warehouse-management.quick-material-issue-item': 'Tiêu hao vật tư nhanh',
  'common.material-transfer-add-finished': 'Thêm SL thu hoạch',
  'common.material-transfer-quick-add-finished': 'Thêm SL thu hoạch nhanh',
  'warehouse-management.export-voucher-voucher': 'Thêm SL thực tế',
  'warehouse-management.material-issue-voucher': 'Xuất huỷ',
  'warehouse-management.material-receipt': 'Nhận trực tiếp',
  'warehouse-management.material-issue-damaged': 'Thêm SL hỏng huỷ',
  'warehouse-management.import-history': 'Nhập kho',
  'warehouse-management.export-history': 'Xuất kho',
  'warehouse-management.inventory-list': 'Tồn kho',
  'warehouse-management.reconciliation-history': 'Kiểm kho',
  'warehouse-management.warehouse-name': 'Tên kho',
  'warehouse-management.from-warehouse-name': 'Từ kho',
  'warehouse-management.to-warehouse-name': 'Đến kho',
  'warehouse-management.reconciliation-voucher': 'Phiếu Kiểm kho',
  'warehouse-management.create-voucher': 'Tạo phiếu',

  //xuất huỷ
  'warehouse-management.material-issue': 'Xuất hủy',

  'warehouse-management.total_price': 'Tổng giá trị hàng hóa', // Total price (duplicate) -> Total price (duplicate)
  'warehouse-management.dashboard': 'Tổng quan kho', // Total price (duplicate) -> Total price (duplicate)
  'warehouse-management.material-transfer': 'Chuyển kho'

};
