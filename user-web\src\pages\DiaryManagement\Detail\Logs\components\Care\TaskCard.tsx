import { DEFAULT_FALLBACK_IMG } from '@/common/contanst/img';
import { IDiaryTask, ITaskTodo } from '@/services/farming-plan';
import { getFileUrlV2 } from '@/services/utils';
import { ProColumns, ProList } from '@ant-design/pro-components';
import { useIntl } from '@umijs/max';
import { Card, Checkbox, Col, Image, Row, Space, Tag, Typography } from 'antd';
import moment from 'moment';
import { useState } from 'react';
import TaskModal from './TaskModal';
interface Props {
  item: IDiaryTask;
}
const getStatus = (key: string) => {
  switch (key) {
    case 'Plan':
      return <Tag color="success"><PERSON><PERSON> hoạch</Tag>;
    case 'Done':
      return <Tag color="success">Đ<PERSON> hoàn thành</Tag>;
    case 'In progress':
      return <Tag color="blue"><PERSON><PERSON> thực hiện</Tag>;
    case 'Pending':
      return <Tag color="warning"><PERSON><PERSON><PERSON></Tag>;
    default:
      return <Tag>{key}</Tag>;
  }
};

const TaskCard = ({ item }: Props) => {
  const intl = useIntl();
  const [showModal, setShowModal] = useState(false);
  const columns: ProColumns<ITaskTodo>[] = [
    {
      title: intl.formatMessage({ id: 'common.done' }),
      dataIndex: 'status',
      render: (dom, entity, index, action, schema) => {
        return <Checkbox checked={entity.status === '1'} disabled />;
      },
      width: 80,
    },

    { title: intl.formatMessage({ id: 'common.name' }), dataIndex: 'label', width: 200 },
    {
      title: intl.formatMessage({ id: 'common.description' }),
      dataIndex: 'description',
      width: 400,
    },
  ];
  return (
    <>
      <Card
        title={
          <Space direction="vertical">
            <Space wrap>
              {item.label} {getStatus(item.status)}
              <Tag color="blue">
                {intl.formatMessage({ id: 'workflowTab.executionTime' })}{' '}
                {moment(item.start_date).format('HH:mm DD/MM/YYYY')}{' '}
                {intl.formatMessage({ id: 'common.to' })}{' '}
                {moment(item.end_date).format('HH:mm DD/MM/YYYY')}
              </Tag>
              <Tag color="blue">
                {intl.formatMessage({ id: 'common.state' })}
                {': '}
                {item.text_state || item.state_name}
              </Tag>
              <Tag color="blue">
                {intl.formatMessage({ id: 'common.project' })}
                {': '}
                {item.project_name}
              </Tag>
              <Tag color="blue">
                {intl.formatMessage({ id: 'common.zone' })}
                {': '}
                {item.zone_name}
              </Tag>
            </Space>
            <Space>
              <Tag>
                {intl.formatMessage({ id: 'common.assigned_to' })}
                {': '}
                {item.text_assign_user ||
                  (item.assigned_to_info
                    ? `${item.assigned_to_info?.at(0)?.first_name} ${
                        item.assigned_to_info?.at(0)?.last_name
                      }`
                    : intl.formatMessage({ id: 'common.missing_info' }))}
              </Tag>
              <Tag>
                {intl.formatMessage({ id: 'common.people_involved' })}
                {': '}
                {item?.involve_in_users?.reduce(
                  (acc, ele) => acc + `${ele.first_name} ${ele.last_name}, `,
                  '',
                )}
              </Tag>
            </Space>
          </Space>
        }
        style={{ marginTop: 10 }}
        // extra={`${moment(item.start_date).format('HH:mm DD/MM/YYYY')} - ${moment(
        //   item.end_date,
        // ).format('HH:mm DD/MM/YYYY')}`}
        hoverable
        onClick={() => {
          setShowModal(true);
        }}
      >
        <Row gutter={[16, 16]}>
          <Col md={8} sm={24}>
            <Typography.Title level={5}>
              {intl.formatMessage({ id: 'common.task_child' })}
            </Typography.Title>
            {item.todo_list ? (
              <ProList<ITaskTodo>
                columns={columns}
                dataSource={item.todo_list}
                toolBarRender={false}
                search={false}
                tooltip={false}
                renderItem={(item) => {
                  return <p>{`+ ${item.label}`}</p>;
                }}
              />
            ) : (
              ''
            )}
          </Col>
          <Col md={8} sm={24}>
            {' '}
            <Typography.Title level={5}>
              {intl.formatMessage({ id: 'common.category' })}
            </Typography.Title>
            <Image.PreviewGroup>
              {item.item_list?.map((item, index) => {
                if (index > 3) return '';
                return (
                  <Image
                    src={getFileUrlV2({ src: item.category?.image }) || DEFAULT_FALLBACK_IMG}
                    width={50}
                    key={index}
                    onClick={(e) => {
                      e.stopPropagation();
                      e.preventDefault();
                    }}
                  />
                );
              })}
            </Image.PreviewGroup>
          </Col>
          <Col md={8} sm={24}>
            {' '}
            <Typography.Title level={5}>
              {intl.formatMessage({ id: 'common.product' })}
            </Typography.Title>
            <Image.PreviewGroup>
              {item.prod_quantity_list?.map((item, index) => {
                if (index > 3) return '';
                return (
                  <Image
                    src={
                      getFileUrlV2({ src: item.agriculture_product?.image }) || DEFAULT_FALLBACK_IMG
                    }
                    width={50}
                    key={index}
                  />
                );
              })}
            </Image.PreviewGroup>
          </Col>
        </Row>
      </Card>
      <TaskModal showModal={showModal} setShowModal={setShowModal} item={item} />
    </>
  );
};

export default TaskCard;
