import { sscript } from '@/services/sscript';
import { IIotProductionFunction } from '@/types/IIotProductionFunction';
import { PageContainer } from '@ant-design/pro-components';
import { useModel } from '@umijs/max';
import { Button, Card, Col, ConfigProvider, DatePicker, Input, message, Result, Row, Select, Spin, Tabs } from 'antd';
import React, { useEffect, useState } from 'react';
import FuncRenderEnum from './FuncRenderEnum';
import FuncRenderBool from './FuncRenderBool';
import FuncRenderValue from './FuncRenderValue';

const CheckTypeAndRender = ({ fnc }: { fnc: IIotProductionFunction }) => {
    switch (fnc.data_type) {
        case 'Enum':
            return <FuncRenderEnum fnc={fnc} />
        case 'Bool':
            return <FuncRenderBool fnc={fnc} />
        case 'Value':
            return <FuncRenderValue fnc={fnc} />
        case 'Raw':
            return <FuncRenderValue fnc={fnc} />
        default:
            return <></>
    }
}

const RenderDeviceFnc = ({ deviceFunctions }: { deviceFunctions: any[] }) => {
    if (!deviceFunctions.length) return <></>
    return (
        <Row gutter={[5, 5]}>
            {deviceFunctions.map((d: IIotProductionFunction) => {
                return <Col md={d.md_size ? d.md_size : 12} key={"func-" + d.name}>
                    <CheckTypeAndRender fnc={d} />
                </Col>
            })}
        </Row>
    );
};

export default RenderDeviceFnc;
