import { DOCTYPE_ERP } from '@/common/contanst/constanst';
import { getNoteList, Note } from '@/services/diary-2/note';
import { useRequest } from '@umijs/max';

export default function useDetail(
  { id, onSuccess } = {} as {
    onSuccess?: (data: Note) => void;
    id: string;
  },
) {
  return useRequest(
    async () => {
      if (!id) return {
        data: null
      };
      const res = await getNoteList({
        filters: [[DOCTYPE_ERP.iot_diary_v2_note, 'name', '=', id]],
        order_by: 'name asc',
        page: 1,
        size: 1,
      });
      const data = res?.data?.[0];
      if (!data) throw new Error('Not found');
      return {
        data: data,
      };
    },
    {
      onSuccess: (data) => {
        if (data) onSuccess?.(data);
      },
      refreshDeps: [id],
    },
  );
}
