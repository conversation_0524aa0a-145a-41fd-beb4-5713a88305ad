import { PlusOutlined } from '@ant-design/icons';
import { Card, message, Modal, Upload } from 'antd';
import { useEffect, useState } from 'react';

import { downloadFile, getListFileByDocname, removeFile, uploadFile } from '@/services/uploadFile';
import type { RcFile, UploadFile, UploadProps } from 'antd/es/upload/interface';

import { HOST } from '@/services/utils';
import { useAccess } from '@umijs/max';

const getBase64 = (file: RcFile): Promise<string> =>
  new Promise((resolve, reject) => {
    const reader = new FileReader();
    reader.readAsDataURL(file);
    reader.onload = () => resolve(reader.result as string);
    reader.onerror = (error) => reject(error);
  });

const AttachPictureOfDoctype = ({
  doctype,
  docname,
  is_private = 1,
  folder = 'Home/Attachments',
  optimize = false,
}: {
  doctype: string;
  docname: string;
  is_private?: number;
  folder?: string;
  optimize?: boolean;
}) => {
  const [loading, setLoading] = useState(false);
  const [uploading, setUploading] = useState(false);
  const [fileList, setFileList] = useState<UploadFile[]>([]);
  const [previewOpen, setPreviewOpen] = useState(false);
  const [previewImage, setPreviewImage] = useState('');
  const [previewTitle, setPreviewTitle] = useState('');
  const [updating, setUpdating] = useState(false);

  const user_token = JSON.parse(localStorage.getItem('token')).token;
  const getFileList = async () => {
    try {
      setUpdating(true);
      const result = await getListFileByDocname({ doctype, name: docname });
      // console.log(result);
      let privateImg: any = [];
      const _fileList = result.map((f: any) => {
        if (f.file_url.search('private') !== -1) {
          privateImg.push({
            uid: f.name,
            name: f.file_name,
            type: 'image/png',
            status: 'done',
            url:
              `${HOST}/api/v2/file/download?token=` +
              user_token +
              `&file_url=` +
              f.file_url +
              '&dt=' +
              doctype +
              '&dn=' +
              docname,
          });
        }
        return {
          uid: f.name,
          name: f.file_name,
          type: 'image/png',
          status: 'done',
          url:
            `${HOST}/api/v2/file/download?token=` +
            user_token +
            `&file_url=` +
            f.file_url +
            '&dt=' +
            doctype +
            '&dn=' +
            docname,
        };
      });
      setFileList(privateImg);
      // setFileList(_fileList);
    } catch (error) {
    } finally {
      setUpdating(false);
    }
  };
  useEffect(() => {
    getFileList();
  }, [uploading]);

  const handleChange: UploadProps['onChange'] = (info) => {
    let newFileList = [...info.fileList];

    // 1. Limit the number of uploaded files
    // Only to show two recent uploaded files, and old ones will be replaced by the new
    // 2. Read from response and show file link
    newFileList = newFileList.map((file: any) => {
      //   console.log(file);
      if (file.response) {
        // Component will show file.url as link
        file.uid = file.response.name;
        file.url =
          `${HOST}/api/v2/file/download?token=` +
          user_token +
          `&file_url=` +
          file.response.file_url +
          '&dt=' +
          doctype +
          '&dn=' +
          docname;
      }
      return file;
    });
    console.log(newFileList);
    setFileList(newFileList);
  };

  const handleUpload = async (options: any) => {
    const { onSuccess, onError, file, onProgress } = options;
    try {
      setUploading(true);
      const res = await uploadFile({
        file,
        doctype,
        docname,
        is_private,
        folder,
        optimize,
      });

      onSuccess(res.message);
    } catch (err) {
      console.log('Eroor: ', err);
      const error = new Error('Some error');
      onError({ err });
    } finally {
      setUploading(false);
    }
  };

  const handleCancel = () => setPreviewOpen(false);

  const handlePreview = async (file: UploadFile) => {
    if (!file.url && !file.preview) {
      file.preview = await getBase64(file.originFileObj as RcFile);
    }
    console.log('File URL:', file.url);
    setPreviewImage(file.url || (file.preview as string));
    setPreviewOpen(true);
    setPreviewTitle(file.name || file.url!.substring(file.url!.lastIndexOf('/') + 1));
  };

  const props: UploadProps = {
    onRemove: async (file) => {
      if (file.status === 'done') {
        try {
          await removeFile({
            fid: file.uid,
            dt: doctype,
            dn: docname,
          });
          const newFileList = fileList.filter((f) => f.uid !== file.uid);
          console.log('newFileList', newFileList);
          setFileList([...newFileList]);
        } catch (error) {
          message.error('Delete Error,try again!');
        } finally {
        }
      }
    },
    fileList: fileList,
    multiple: true,
    customRequest: handleUpload,

    onChange: handleChange,
    // showUploadList: {
    //   showDownloadIcon: true,
    //   downloadIcon: <DownloadOutlined></DownloadOutlined>,
    // },
    onPreview: handlePreview,
    listType: 'picture-card',
    onDownload: async (file: any) => {
      //   console.log('download', file);
      try {
        const res = await downloadFile({
          file_url: file.url,
          fid: file.fid,
          dt: doctype,
          dn: docname,
        });
        console.log(res);
      } catch (err) {
        console.log('Eroor: ', err);
      } finally {
      }
    },
  };
  const uploadButton = (
    <div>
      <PlusOutlined />
      <div style={{ marginTop: 8 }}>Upload</div>
    </div>
  );
  const access = useAccess();
  const canUpdate = access.canUpdateInVisitorManagement();
  return (
    <>
      <Card title="Ảnh" loading={loading}>
        {canUpdate && <Upload {...props}>{fileList.length >= 8 ? null : uploadButton}</Upload>}
        <Modal open={previewOpen} title={previewTitle} footer={null} onCancel={handleCancel}>
          <img alt="example" style={{ width: '100%' }} src={previewImage} />
        </Modal>
      </Card>
    </>
  );
};

export default AttachPictureOfDoctype;
