import { DEFAULT_PAGE_SIZE_ALL } from '@/common/contanst/constanst';
import FormUploadsPreviewable from '@/components/FormUploadsPreviewable';
import QuillEditor from '@/components/QuillEditor';
import { getUOM_v3 } from '@/services/InventoryManagementV3/uom';
import {
  ProForm,
  ProFormDigit,
  ProFormItem,
  ProFormSelect,
  ProFormText,
  ProFormTextArea,
} from '@ant-design/pro-components';
import { history, useIntl } from '@umijs/max';
import { Card, Col, Row } from 'antd';
import { FC, ReactNode } from 'react';
import useDetail from '../../hooks/useDetail';
import useUpdate from '../../hooks/useUpdate';

interface EditEnterpriseProps {
  children?: ReactNode;
  id: string;
}
const width = 'xl';
const EditProdProcedure: FC<EditEnterpriseProps> = ({ id }) => {
  const { run: doSubmit } = useUpdate({
    onSuccess() {
      history.push('/farming-diary-static/product-procedure/list');
    },
  });
  const onFinish = async (values: any) => {
    await doSubmit({
      name: id,
      avatar: values.avatar,
      qr: values.qr,
      label: values.label,
      packing_unit: values.packing_unit,
      packing_unit_name: values.packing_unit_name,
      link: values.link,

      description: values.description,
      note: values.note,
      net_weight: values.net_weight,
      instruction: values.instruction,
      expire_time: values.expire_time,
      expire_time_unit: values.expire_time_unit,
      image: values.image,
      unit: values.unit,
      unit_name: values.unit_name,
    });
    return true;
  };
  const [form] = ProForm.useForm();

  const { formatMessage } = useIntl();
  const { loading, data } = useDetail({
    id: id,
    onSuccess(data) {
      form.setFieldsValue(data);
    },
  });

  return (
    <Card>
      <ProForm onFinish={onFinish} form={form} loading={loading}>
        <Row>
          <Col span={12}>
            <FormUploadsPreviewable
              fileLimit={1}
              formItemName={'avatar'}
              initialImages={data?.avatar}
              label={formatMessage({
                id: 'common.product_avatar',
              })}
            />
          </Col>
          <Col span={12}>
            <FormUploadsPreviewable
              fileLimit={1}
              formItemName={'qr'}
              initialImages={data?.qr}
              label={formatMessage({ id: 'common.image_of_product_registration' })}
            />
          </Col>
        </Row>
        <Row gutter={24}>
          <Col span={24}>
            <ProFormText
              label={formatMessage({ id: 'common.product_name' })}
              name="label"
              rules={[
                {
                  required: true,
                },
              ]}
            />
          </Col>
          <Col span={8}>
            <ProFormSelect
              label={formatMessage({ id: 'common.packaging_unit' })}
              name="packing_unit"
              showSearch
              request={async () => {
                const res = await getUOM_v3({
                  page: 1,
                  size: DEFAULT_PAGE_SIZE_ALL,
                });
                return res.data.map((item) => ({
                  label: item.uom_name,
                  value: item.name,
                }));
              }}
              rules={[
                {
                  required: true,
                },
              ]}
            />
          </Col>
          <Col span={8}>
            <ProFormDigit
              label={formatMessage({ id: 'common.net_weight' })}
              name="net_weight"
              rules={[
                {
                  required: true,
                },
              ]}
            />
          </Col>
          <Col span={8}>
            <ProFormSelect
              label={formatMessage({ id: 'common.unit' })}
              name="unit"
              showSearch
              request={async () => {
                const res = await getUOM_v3({
                  page: 1,
                  size: DEFAULT_PAGE_SIZE_ALL,
                });
                return res.data.map((item) => ({
                  label: item.uom_name,
                  value: item.name,
                }));
              }}
              rules={[
                {
                  required: true,
                },
              ]}
            />
          </Col>
        </Row>
        <FormUploadsPreviewable
          fileLimit={10}
          formItemName={'image'}
          initialImages={data?.image}
          label={formatMessage({ id: 'common.other_images' })}
        />
        <ProFormText label={formatMessage({ id: 'common.web_link' })} width={width} name="link" />
        <ProFormItem
          name="description"
          rules={[
            {
              required: true,
            },
          ]}
          label={formatMessage({ id: 'common.product_introduction' })}
        >
          <QuillEditor />
        </ProFormItem>
        <Row gutter={24}>
          <Col span={12}>
            <ProFormDigit label={formatMessage({ id: 'common.expiry_date' })} name="expire_time" />
          </Col>
          <Col span={12}>
            <ProFormSelect
              request={async () => {
                return ['day', 'week', 'month', 'year'].map((item) => ({
                  label: formatMessage({ id: `common.${item}` }), // Sử dụng formatMessage để đa ngôn ngữ
                  value: item,
                }));
              }}
              label={formatMessage({ id: 'common.time' })}
              name="expire_time_unit"
            />
          </Col>
        </Row>

        <ProFormItem
          name="instruction"
          rules={[
            {
              required: true,
            },
          ]}
          label={formatMessage({ id: 'common.user_manual' })}
        >
          <QuillEditor />
        </ProFormItem>
        <ProFormTextArea label={formatMessage({ id: 'common.note' })} name="note" />
      </ProForm>
    </Card>
  );
};

export default EditProdProcedure;
