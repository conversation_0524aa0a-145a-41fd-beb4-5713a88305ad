import ImagePreviewGroupCommon, { IImagePreview } from '@/components/ImagePreviewGroupCommon';
import { FormattedMessage, useIntl } from '@umijs/max';
import { App, Card, Typography } from 'antd';
import { FC, ReactNode } from 'react';

export interface NoteInfoProps {
  data: {
    id: string;
    title?: ReactNode;
    time?: string;
    stage?: ReactNode;
    description?: ReactNode;
    state_list?: ReactNode[];
    category_list?: ReactNode[];
    involved_in_users?: ReactNode[];
    listImg?: IImagePreview[];
    // cropId?: string;
  };
  onSuccess?: (id: string) => void;
}

const PestInfo: FC<NoteInfoProps> = ({ data, onSuccess }) => {
  const {
    id,
    title,
    time,
    stage,
    description,
    listImg,
    state_list = [],
    category_list = [],
    involved_in_users = [],
    // cropId,
  } = data;

  const { message, modal } = App.useApp();
  // const onDelete = () => {
  //   modal.confirm({
  //     content: 'Are you sure you want to delete this information',
  //     onOk: async () => {
  //       try {
  //         await deletePest({
  //           name: id,
  //         });
  //         message.success({
  //           content: 'Delete success',
  //         });
  //         onSuccess?.(id);
  //         return true;
  //       } catch (error) {
  //         message.error({
  //           content: 'Delete error, please try again',
  //         });
  //         return false;
  //       }
  //     },
  //     okButtonProps: {
  //       danger: true,
  //     },
  //   });
  // };

  // const access = useAccess();
  const intl = useIntl();

  return (
    <Card
      title={title}
      // extra={
      //   <Space>
      //     <span>{time ? `Chỉnh sửa lúc ${dayjs(time).format('hh:mm:ss ,DD/MM/YYYY')}` : null}</span>
      //     {access.canDeleteAllInPageAccess() && (
      //       <Button
      //         size="middle"
      //         icon={<DeleteOutlined />}
      //         danger
      //         type="primary"
      //         onClick={onDelete}
      //       />
      //     )}
      //     <UpdateNoteModal
      //       data={data}
      //       key="update-pandemic"
      //       cropId={cropId}
      //       onSuccess={onSuccess}
      //     />
      //   </Space>
      // }
    >
      {category_list?.length > 0 && (
        <Typography.Paragraph>
          <Typography.Text strong>
            {intl.formatMessage({ id: 'seasonalTab.relatedSupplies' })}:
          </Typography.Text>
          <ul>
            {category_list.map((category: any, index) => (
              <li key={index}>
                <Typography.Text>{`${category.label}`}</Typography.Text>
              </li>
            ))}
          </ul>
        </Typography.Paragraph>
      )}

      {state_list?.length > 0 && (
        <Typography.Paragraph>
          <Typography.Text strong>
            <FormattedMessage id="seasonalTab.relatedStage" />:
          </Typography.Text>
          <ul>
            {state_list.map((state: any, index) => (
              <li key={index}>
                <Typography.Text>{`${state.label}`}</Typography.Text>
              </li>
            ))}
          </ul>
        </Typography.Paragraph>
      )}
      {involved_in_users?.length > 0 && (
        <Typography.Paragraph>
          <Typography.Text strong>
            {' '}
            {intl.formatMessage({ id: 'common.people_involved' })}:
          </Typography.Text>
          <ul>
            {involved_in_users.map((user: any, index) => (
              <li key={index}>
                <Typography.Text>{`${user.last_name} ${user.first_name}`}</Typography.Text>
              </li>
            ))}
          </ul>
        </Typography.Paragraph>
      )}

      <Typography.Paragraph>
        <Typography.Text strong>
          {intl.formatMessage({ id: 'common.form.description' })}:
        </Typography.Text>{' '}
        <Typography.Text>{description}</Typography.Text>
      </Typography.Paragraph>

      <ImagePreviewGroupCommon listImg={listImg} />
    </Card>
  );
};

export default PestInfo;
