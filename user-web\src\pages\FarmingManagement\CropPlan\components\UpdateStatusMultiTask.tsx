import { updateTaskStatusArray } from '@/services/TaskAndTodo';
import { message, Select } from 'antd';

const UpdateStatusMultiTask = (params: { refreshFnc: any; tasks_id: string[] }) => {
  const onChange = async (statusValue: string) => {
    try {
      const taskStatusArray = params.tasks_id.map((task_id) => {
        return {
          name: task_id,
          status: statusValue,
        };
      });
      console.log('taskStatusArray', taskStatusArray);
      const updateStatusMultiTask = await updateTaskStatusArray({ tasks: taskStatusArray });
      message.success('Cập nhật trạng thái thành công');
    } catch (error: any) {
      message.error(error.toString());
    } finally {
      if (params.refreshFnc) {
        await params.refreshFnc();
      }
    }
  };

  const onSearch = (value: string) => {
    console.log('search:', value);
  };

  // Filter `option.label` match the user type `input`
  const filterOption = (input: string, option?: { label: string; value: string }) =>
    (option?.label ?? '').toLowerCase().includes(input.toLowerCase());

  return (
    <Select
      showSearch
      placeholder="Cập nhật trạng thái"
      style={{ width: 120 }}
      optionFilterProp="children"
      onChange={onChange}
      onSearch={onSearch}
      filterOption={filterOption}
      options={[
        {
          label: 'Lên kế hoạch',
          value: 'Plan',
        },
        {
          label: 'Đang xử lý',
          value: 'In progress',
        },
        {
          label: 'Hoàn tất',
          value: 'Done',
        },
        {
          label: 'Trì hoãn',
          value: 'Pending',
        },
      ]}
    />
  );
};

export default UpdateStatusMultiTask;
