import action from './vi-VN/action';
import bom from './vi-VN/bom';
import categoryTab from './vi-VN/categoryGroupTab/categoryTab';
import producerTab from './vi-VN/categoryGroupTab/producerTab';
import productTab from './vi-VN/categoryGroupTab/productTab';
import common from './vi-VN/common';
import component from './vi-VN/component';
import demoMonitor from './vi-VN/demoMonitor';
import plantTab from './vi-VN/farmingTab/plantTab';
import seasonalTab from './vi-VN/farmingTab/seasonalTab';
import workflowTab from './vi-VN/farmingTab/workflowTab';
import globalHeader from './vi-VN/globalHeader';
import home from './vi-VN/homeTab/home';
import menu from './vi-VN/menu';
import newDashboard from './vi-VN/new-dashboard';
import pages from './vi-VN/pages';
import pwa from './vi-VN/pwa';
import settingDrawer from './vi-VN/settingDrawer';
import settings from './vi-VN/settings';
import categoryManageTab from './vi-VN/storageManageGroupTab/categoryManageTab';
import productManageTab from './vi-VN/storageManageGroupTab/productManageTab';
import storageListTab from './vi-VN/storageManageGroupTab/storageListTab';
import tooltips from './vi-VN/tooltips';
import warehouseManagement from './vi-VN/warehouseManagement';
export default {
  'navBar.lang': 'Ngôn ngữ',
  'fuc.dm': 'Mother fucker',
  'layout.user.link.help': 'Hỗ trợ',
  'layout.user.link.privacy': 'Privacy',
  'layout.user.link.terms': 'Terms',
  'app.copyright.produced': 'Produced by Ant Financial Experience Department',
  'app.preview.down.block': 'Download this page to your local project',
  'app.welcome.link.fetch-blocks': 'Get all block',
  'app.welcome.link.block-list': 'Quickly build standard, pages based on `block` development',
  ...globalHeader,
  ...menu,
  ...settingDrawer,
  ...settings,
  ...pwa,
  ...component,
  ...pages,
  ...common,
  ...demoMonitor,
  ...categoryTab,
  ...producerTab,
  ...productTab,
  ...categoryManageTab,
  ...productManageTab,
  ...storageListTab,
  ...action,
  ...home,
  ...seasonalTab,
  ...workflowTab,
  ...plantTab,
  ...warehouseManagement,
  ...tooltips,
  ...newDashboard,
  ...bom,
};
