import { sscriptGeneralList } from '@/services/sscript';
import type { ProColumns } from '@ant-design/pro-components';
import { ProTable } from '@ant-design/pro-components';
import { Link } from '@umijs/max';
import { ConfigProvider } from 'antd';
import React, { useEffect, useRef } from 'react';

interface ActionType {
  reload: (resetPageIndex?: boolean) => void;
  reloadAndRest: () => void;
  reset: () => void;
  clearSelected?: () => void;
  startEditable: (rowKey: String) => boolean;
  cancelEditable: (rowKey: String) => boolean;
}

const Customer: React.FC = () => {
  const tableRef = useRef<ActionType>();

  useEffect(() => {}, []);

  const columns: ProColumns<API.User>[] = [
    {
      title: 'uuid',
      dataIndex: 'name',
      render: (dom: any, entity: any) => {
        return (
          <Link key="detail" to={`/customer/detail?customer_name=${entity.name}`}>
            <span>{dom}</span>
          </Link>
        );
      },
      fixed: 'left',
      sorter: true,
      sortDirections: ['ascend', 'descend'],
    },
    {
      title: <FormattedMessage id="storage-management.category-management.object_name" />,
      dataIndex: 'customer_name',
      sorter: true,
      sortDirections: ['ascend', 'descend'],
    },
    {
      title: 'Email',
      dataIndex: 'email',
      sorter: true,
      sortDirections: ['ascend', 'descend'],
    },
    {
      title: 'Phone',
      dataIndex: 'phone',
      sorter: true,
      sortDirections: ['ascend', 'descend'],
    },
    {
      title: 'Province',
      dataIndex: 'province',
      sorter: true,
      sortDirections: ['ascend', 'descend'],
    },
  ];
  const reloadTable = async () => {
    tableRef.current?.reload();
  };

  return (
    <ConfigProvider>
      <ProTable<API.User, API.PageParams>
        // scroll={{ x: 1200, y: 600 }}
        size="small"
        actionRef={tableRef}
        rowKey="name"
        // loading={loading}
        // dataSource={[...users]}
        request={async (params, sort, filter) => {
          let order_by = 'modified desc';
          if (Object.keys(sort).length) {
            order_by = `${Object.keys(sort)[0]} ${
              Object.values(sort)[0] === 'ascend' ? 'asc' : 'desc'
            }`;
          }
          const { current, pageSize } = params;
          type fieldKeyType = keyof typeof params;
          const searchFields = Object.keys(params).filter((field: string) => {
            const value = params[field as fieldKeyType];
            return field !== 'current' && field !== 'pageSize' && value !== 'all';
          });
          const filterArr = searchFields.map((field) => {
            const value = params[field as fieldKeyType];
            return ['Customer Company', field, 'like', `%${value}%`];
          });
          try {
            const result = await sscriptGeneralList({
              doc_name: 'iot_customer',
              filters: filterArr,
              page: current ? current : 0 + 1,
              size: pageSize,
              fields: ['*'],
              order_by: order_by,
            });
            return {
              data: result.data,
              success: true,
              total: result.pagination.totalElements,
            };
          } catch (error) {
            console.log(error);
          } finally {
          }
        }}
        columns={columns}
        search={{
          labelWidth: 'auto',
        }}
        headerTitle={''}
        toolBarRender={() => [
          // <CreateCompany refreshFnc={reloadTable} />
        ]}
        pagination={{
          defaultPageSize: 20,
          showSizeChanger: true,
          pageSizeOptions: ['20', '50', '100'],
        }}
      />
    </ConfigProvider>
  );
};

export default Customer;
