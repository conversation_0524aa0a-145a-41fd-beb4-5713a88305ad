import { Process } from '@/services/diary-2/process';
import { getFullImgUrlArrString } from '@/utils/image';
import { FC, ReactNode } from 'react';
import NoteInfo from './NoteInfo';

interface IndexProps {
  children?: ReactNode;
  data?: Process | null;
}

const Index: FC<IndexProps> = ({ children, data }) => {
  return (
    <div className="flex flex-col gap-2">
      {data?.notes?.map((ite) => (
        <NoteInfo
          key={ite.name}
          data={{
            id: ite.name,
            title: ite.label,
            description: ite.description,
            listImg: getFullImgUrlArrString(ite.image).map((item) => ({
              src: item,
            })),
          }}
        />
      ))}
    </div>
  );
};

export default Index;
