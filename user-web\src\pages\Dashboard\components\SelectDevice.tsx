
import { Page<PERSON>ontainer, ProCard, CheckCard } from '@ant-design/pro-components';
import { Link, useModel } from '@umijs/max';
import { Button, Row, Select, Col, Card, Collapse, Badge, Typography, List, Divider, Form, Pagination } from 'antd';
import { FC, ReactNode, useEffect, useState } from 'react';
import { getProjectList, IProjectRes } from '@/services/project';
import { deviceInProjectList } from '@/services/devices';
import { DEFAULT_PAGE_SIZE_ALL } from '@/common/contanst/constanst';
import RenderDeviceFncDashboard from './RenderDeviceFncDashboard';
import _, { get, set } from 'lodash';
import { DEFAULT_DEVICE_IN_ZONE_PAGE_SIZE } from '../constants/constants';


interface WorkflowManagementProps {
    children?: ReactNode;
}

const { Title } = Typography;

const SelectDevice: FC<WorkflowManagementProps> = ({ children }) => {
    const [projectList, setProjectList] = useState([{}]);
    // const [projectOptions, setProjectOptions] = useState<any>([]);
    const [zoneList, setZoneList] = useState([{
        key: 0,
        label: "Tất cả thiết bị",
        devices: []
    }]);
    const [deviceListInProject, setDeviceListInProject] = useState([{}]);
    const [loading, setLoading] = useState(false);
    const [tabActive, setTabActive] = useState(0);
    const [online, setOnline] = useState(false);
    const [chosenDevice, setChosenDevice] = useState<any>("");
    const {
        myProject,
        myZone,
        setSelectedProject,
        selectedDevice,
        setSelectedZone,
        setSelectedDevice,
    } = useModel('MyResource');

    const [pageActive, setPageActive] = useState(1);

    const getProjectsList = async () => {
        setLoading(true);
        if (myProject?.length) {
            let projectOptions = [{
                value: "all",
                label: "Tất cả"
            }];
            myProject?.map((d: any) => {
                console.log(d);
                projectOptions.push({
                    value: d.id,
                    label: d.title
                });
            });
            console.log(projectOptions);
            setProjectList(projectOptions);
        }
    };

    const handlePageChange = (page: number) => {
        setPageActive(page);
    };

    const getZoneListInProject = (resData: any, value: any) => {
        setTabActive(0);
        setSelectedDevice('');
        const zoneInProject = value == 'all' ? myZone : myZone.filter((d: any) => d.project_id === value);
        const dataZoneList = zoneInProject.map((zone: any, index: number) => {
            const deviceListInZone = resData.filter((d: any) => d.zone_id === zone.id);
            return {
                key: index + 1,
                value: zone.id,
                label: zone.title,
                devices: deviceListInZone
            }
        });
        const allZoneList = {
            key: 0,
            label: "Tất cả thiết bị",
            devices: resData
        }
        setZoneList([allZoneList, ...dataZoneList]);
    }

    const getDeviceListInProject = async (projectId: string) => {
        setLoading(true);
        try {
            const res = await deviceInProjectList({ size: DEFAULT_PAGE_SIZE_ALL, project_id: projectId });
            setDeviceListInProject(res);
            getZoneListInProject(res, projectId)
        } catch (error) {
            throw error;
        }
        finally {
            setLoading(false);
        }
    };

    const handleChange = (value: string) => {
        setSelectedZone('all');
        setSelectedDevice('');
        setSelectedProject(value);
        setTabActive(0);
        setPageActive(1);
        getDeviceListInProject(value);
    };

    const handleTabsChange = (key: number) => {
        setSelectedDevice('');
        setTabActive(key);
        setPageActive(1);
    }

    const createDeviceCard = (device: any) => {
        const deviceStatus = getDeviceStatus(device);
        const badgeStatus = deviceStatus ? "success" : "default";
        const badgeText = deviceStatus ? "online" : "offline";
        return <Col span={24}>
            <Row gutter={[16, 16]} justify="space-between">
                <Col span={12}>{device.zone_name}</Col>
                <Col span={12}>
                    <Badge status={badgeStatus} text={badgeText} />
                </Col>
            </Row>
        </Col>
    }

    const renderDeviceInfoCard = (device: any) => {
        return <Col span={24}>
            <RenderDeviceFncDashboard />
        </Col>

    }

    const handleCollapseChange = (key: any) => {
        if (key && key.length) {
            setSelectedDevice(key);
            setChosenDevice(key);
        } else {
            setSelectedDevice('');
            setChosenDevice('');
        }
    }

    const createCardList = (devices: any) => {
        const deviceInPage = devices.slice((pageActive - 1) * DEFAULT_DEVICE_IN_ZONE_PAGE_SIZE, pageActive * DEFAULT_DEVICE_IN_ZONE_PAGE_SIZE);
        return devices.length > 0 && <>
            <CheckCard.Group
                onChange={handleCollapseChange}
                size='default'
            >
                <List
                    grid={{
                        gutter: 16, column: 4
                    }}
                    style={{ width: '100%' }}
                    dataSource={deviceInPage}
                    renderItem={(item: any) => {
                        return <List.Item
                            style={{ margin: 0 }}
                        >
                            <CheckCard
                                style={{
                                    width: '100%',
                                    margin: 0
                                }}
                                key={item.id || " "}
                                value={item.name || " "}
                                title={item.title || "No name"}
                                avatar={item.image || "https://mcdn.coolmate.me/image/October2021/meme-cheems-1.png"}
                                description={createDeviceCard(item)}
                            >
                            </CheckCard>
                        </List.Item>
                    }}
                >
                </List>
            </CheckCard.Group>
        </>
    }

    const getDeviceStatus = (device: any) => {
        const latestDeviceValue = _.get(device, "latest_data", []);
        const online = _.get(_.find(latestDeviceValue, (d) => {
            return d.key === "online"
        }), "value", false);
        return online;
    }

    useEffect(() => {
        // call api
        getProjectsList();
        console.log(projectList);
    }, []);

    const convertDeviceToCollapseItem = (device: any) => {
        return {
            key: 0,
            label: "Thông tin thiết bị",
            children: device && renderDeviceInfoCard(device),
        }
    }


    const renderDeviceDashboard = () => {
        return selectedDevice && <Collapse activeKey={[0]} items={[convertDeviceToCollapseItem(selectedDevice)]} />
    }

    const renderPagination = () => {
        const total = Math.floor(zoneList[tabActive].devices.length / DEFAULT_DEVICE_IN_ZONE_PAGE_SIZE) + 1;

        return zoneList[tabActive].devices.length &&
            <Pagination
                current={pageActive}
                pageSize={DEFAULT_DEVICE_IN_ZONE_PAGE_SIZE}
                total={zoneList[tabActive].devices.length}
                onChange={handlePageChange}
            />
    }


    return (
        <PageContainer
            fixedHeader
            tabList={zoneList}
            tabActiveKey={tabActive.toString()}
            tabBarExtraContent={renderPagination()}
            tabProps={
                {
                    style: { margin: 0 }
                }
            }
            onTabChange={handleTabsChange}
            extra={[
                <Form>
                    <Form.Item>
                        <Select
                            style={{ width: '12rem' }}
                            defaultValue="Chọn dự án"
                            options={projectList}
                            onChange={handleChange}
                        />
                    </Form.Item>
                </Form>
            ]}
        >
            <Col span={24} >
                <Row gutter={[16, 16]}>
                    <Col span={24}>
                        {createCardList(zoneList[tabActive].devices)}
                    </Col>
                </Row>
                <Divider />
                <Row>
                    <Col span={24}>
                        {renderDeviceDashboard()}
                    </Col>
                </Row>
            </Col>
        </PageContainer>
    );
};

export default SelectDevice;
