import AttachmentsOfDoctype from '@/components/AttachmentsOfDoctype/AttachmentsOfDoctype';
import { myLazy } from '@/utils/lazy';
import { CameraFilled, LoadingOutlined } from '@ant-design/icons';
import { ListPageSkeleton, PageContainer, ProFormSelect, ProFormUploadButton } from '@ant-design/pro-components';
import { Link, useSearchParams, history } from '@umijs/max';
import { Button, Card, Col, Form, Input, Radio, Row, Select, Tabs, UploadFile, message } from 'antd';
import { FC, Suspense, useEffect, useState } from 'react';
import RemoveCropNote from './components/RemoveCropNote';
import { IIotCropNote } from '@/types/IIotCropNote';
import { getCrop, getCropNote } from '@/services/crop';
import ImageCropNote from './components/Image';
import { generalUpdate } from '@/services/sscript';
import UploadImageCropNote from './components/UploadImageCropNote';
import { updateCropNote } from '@/services/cropManager';
import { DOCTYPE_ERP } from '@/common/contanst/constanst';
import { uploadFile } from '@/services/fileUpload';
import DetailCropNoteTab from './components/DetailCropNoteTab';
const { Item } = Form;

const DetailCropNote: FC = () => {
  const [tabActive, setTabActive] = useState("1");

  const [searchParams, setSearchParams] = useSearchParams();
  const [loading, setLoading] = useState(false);
  const [cropNote, setCropNote] = useState<Partial<IIotCropNote>>({})
  const [cropList, setCropList] = useState<any[]>([])
  const [imageLinks, setImageLinks] = useState<any[]>([])
  const [form] = Form.useForm();

  const cropNoteName = searchParams.get("crop_note_id") ?? '';

  // const deleteImageLink = (indexToDelete: number) => {
  //   // Create a new array without the image link at the specified index
  //   const newImageLinks = imageLinks.filter((_, index) => index !== indexToDelete);
  //   setImageLinks(newImageLinks);
  // };

  // const renderImageCropNoteLayout = () => {
  //   const ImageCropNoteComponents: JSX.Element[] = [];
  //   let rowImages: string[] = [];

  //   imageLinks.forEach((imageLink, index) => {
  //     rowImages.push(imageLink);

  //     if ((index + 1) % 4 === 0 || index === imageLinks.length - 1) {
  //       // When we have 4 images in the row or we have reached the last image
  //       const ImageCropNoteRow = (
  //         <Row className="gutter-row" gutter={4} key={`row_${index}`}>
  //           {rowImages.map((image, idx) => (
  //             <Col className="gutter-row" key={`col_${index}_${idx}`}>
  //               <ImageCropNote imageLink={image} index={idx} callbackFunc={deleteImageLink} />
  //             </Col>
  //           ))}
  //         </Row>
  //       );
  //       ImageCropNoteComponents.push(ImageCropNoteRow);
  //       rowImages = [];
  //     }
  //   });

  //   return ImageCropNoteComponents;
  // };

  // const initCropNote = async () => {
  //   try {
  //     setLoading(true);
  //     let [params, sort, filters]: any[] = ['', '', [["iot_Crop_note", "name", "like", cropNoteName]]];
  //     const res: any = await getCropNote({
  //       page: 1,
  //       size: 1000,
  //       fields: ['*'],
  //       filters: JSON.stringify(filters),
  //       or_filters: [],
  //       order_by: '',
  //       group_by: '',
  //     })
  //     let data: any = res.data.data
  //     console.log("receive data", data[0])
  //     setCropNote(data[0])
  //     if(data[0].image)
  // {
  //   setImageLinks(data[0].image.split(','));
  // }
  //     form.setFieldsValue(data[0])
  //   } catch (error) {
  //     console.log(error)
  //   } finally {
  //     setLoading(false);
  //   }
  // }

  // const initCropList = async () => {
  //   try {
  //     setLoading(true);
  //     // let [params, sort, filters]: any[] = ['', '', [["iot_Crop_note", "name", "like", cropNoteName]]];
  //     const res: any = await getCrop({
  //       page: 1,
  //       size: 1000,
  //       fields: ['*'],
  //       filters: JSON.stringify([]),
  //       or_filters: [],
  //       order_by: '',
  //       group_by: '',
  //     })
  //     let data: any = res.data.data
  //     console.log("receive data", data)
  //     setCropList(data)
  //   } catch (error) {
  //     console.log(error)
  //   } finally {
  //     setLoading(false);
  //   }
  // }

  // useEffect(() => {
  //   initCropNote();
  //   initCropList();
  // }, [cropNoteName])

  return (
    <Suspense fallback={<ListPageSkeleton />}>
      <>
        <PageContainer>
          <Tabs activeKey={tabActive}
            onChange={(e) => {
              setTabActive(e)
            }}>
            <Tabs.TabPane tab="Thông tin chi tiết ghi chú" key="1">
              <DetailCropNoteTab cropNoteID={cropNoteName} />
            </Tabs.TabPane>
            {/* <Tabs.TabPane tab="Thông tin khác" key="2">
              Oh no
            </Tabs.TabPane> */}
          </Tabs>

        </PageContainer>
      </>

    </Suspense>
  );
};

export default DetailCropNote;
