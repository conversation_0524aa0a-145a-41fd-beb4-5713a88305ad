import { DEFAULT_DATE_FORMAT_WITHOUT_TIME } from '@/common/contanst/constanst';
import { CameraFilled } from '@ant-design/icons';
import {
  ProForm,
  ProFormDateRangePicker,
  ProFormSelect,
  ProFormTimePicker,
  ProFormUploadButton,
} from '@ant-design/pro-components';
import { Card, Space } from 'antd';
import { FC, ReactNode } from 'react';
import DetailedInfo from './DetailedInfo';
import SelectDevice from './SelectDevice';
import TaskChild from './TaskChild';

interface DetailLogProps {
  children?: ReactNode;
}
type IFormData = {};
const DetailLog: FC<DetailLogProps> = () => {
  const onFinish = async (values: IFormData) => {};
  return (
    <ProForm<IFormData> onFinish={onFinish} submitter={false}>
      <Space
        size={'large'}
        direction="vertical"
        style={{
          width: '100%',
        }}
      >
        <DetailedInfo />
        <TaskChild />
        <Card title="Hình ảnh / Video mô tả ">
          <ProFormUploadButton
            listType="picture-card"
            icon={<CameraFilled style={{ fontSize: '24px' }} />}
            title=""
          />
        </Card>
        <Card title="Vật tư liên quan">
          <ProFormSelect label="Chọn vật tư liên quan" />
        </Card>
        <Card title="Hẹn giờ">
          <ProForm.Group>
            <ProFormTimePicker label="Chọn giờ" width={'lg'} />
            <ProFormDateRangePicker
              label={'Thời gian'}
              width={'lg'}
              fieldProps={{
                format: DEFAULT_DATE_FORMAT_WITHOUT_TIME,
              }}
            />
          </ProForm.Group>
        </Card>
        <SelectDevice />
      </Space>
    </ProForm>
  );
};

export default DetailLog;
