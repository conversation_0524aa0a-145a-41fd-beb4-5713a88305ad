import { ProForm } from '@ant-design/pro-components';
import { useIntl } from '@umijs/max';
import { message, Spin } from 'antd';
import { FC, ReactNode, useEffect } from 'react';
import useDetail from '../../hooks/useDetail';
import useUpdate from '../../hooks/useUpdate';
import Attachment from './Attachment';
import DetailedInfo from './DetailedInfo';

interface StageOfCountEditProps {
  children?: ReactNode;
  id: string;
  onSuccess?: () => void;
  setIsFormDirty: (dirty: boolean) => void;
}

const DocumentEdit: FC<StageOfCountEditProps> = ({
  children,
  id,
  onSuccess,
  setIsFormDirty = () => {},
}) => {
  const { formatMessage } = useIntl();
  const [form] = ProForm.useForm();
  const { run } = useUpdate({
    onSuccess,
  });
  const { data, loading } = useDetail({
    id: id,
    onSuccess(data) {
      if (data) form.setFieldsValue(data);
    },
  });
  useEffect(() => {
    setIsFormDirty(false);
  }, [data]);
  return (
    <Spin spinning={loading}>
      <ProForm
        loading={loading}
        form={form}
        onValuesChange={() => setIsFormDirty(true)}
        submitter={{
          searchConfig: {
            // resetText: formatMessage({ id: 'common.reset' }),
            // submitText: formatMessage({ id: 'common.submit' }),
          },
          render: (_, dom) => {
            return (
              <div style={{ textAlign: 'right', margin: 24 }}>
                {dom.map((item, index) => (
                  <span key={index} style={{ marginRight: index === 0 ? 8 : 0 }}>
                    {item}
                  </span>
                ))}
              </div>
            );
          },
        }}
        onFinish={async (values) => {
          //cần check issue_date phải nhỏ hơn expiry_date
          if (values.issue_date > values.expiry_date) {
            message.error('Ngày hết hạn phải lớn hơn ngày chứng nhận');
            return;
          }
          await run({
            name: id,
            label: values.label,
            issue_date: values.issue_date,
            expiry_date: values.expiry_date,
            document_path: values.document_path,
          });
          setIsFormDirty(false);
          return true;
        }}
      >
        <div className="mb-4 space-y-4">
          <DetailedInfo />
          {/* <Task /> */}
          <Attachment initialFile={data?.document_path} />
        </div>
      </ProForm>
    </Spin>
  );
};

export default DocumentEdit;
