import { TAG_COLOR } from '@/common/contanst/constanst';
import { getCustomerUserList } from '@/services/customerUser';
import {
  ProFormDigit,
  ProFormSelect,
  ProFormText,
  ProFormTextArea,
} from '@ant-design/pro-components';
import { useIntl } from '@umijs/max';
import { Card, Col, Row, Tag } from 'antd';
import { FC, ReactNode } from 'react';

interface DetailedInfoProps {
  children?: ReactNode;
}
const w = 'md';
const getTagColor = (item: string) => {
  switch (item) {
    case 'Priority':
      return TAG_COLOR.PRIORITY;
    case 'Important':
      return TAG_COLOR.IMPORTANT;
    case 'Common':
      return TAG_COLOR.COMMON;
    default:
      return 'default';
  }
};
const DetailedInfo: FC<DetailedInfoProps> = ({ children }) => {
  const { formatMessage } = useIntl();
  return (
    <Card
      title={formatMessage({
        id: 'task.detailed_info',
      })}
    >
      <Row gutter={24}>
        <Col span={12}>
          {' '}
          <ProFormText
            label={formatMessage({
              id: 'common.task_name',
            })}
            name="label"
            rules={[
              {
                required: true,
              },
            ]}
          />
        </Col>
        <Col span={6}>
          {' '}
          <ProFormDigit
            // label={`${formatMessage({
            //   id: 'common.execution_time',
            // })} (${formatMessage({
            //   id: 'common.from_the_start_of_the_crop',
            // })})`}
            label={`${formatMessage({
              id: 'common.execution_time',
            })}`}
            name="execution_day"
            fieldProps={{
              prefix: formatMessage({
                id: 'common.days',
              }),
            }}
            rules={[
              {
                required: true,
              },
            ]}
          />
        </Col>
        <Col span={6}>
          {' '}
          <ProFormDigit
            label={`${formatMessage({
              id: 'common.expire_time_in_days',
            })}`}
            name="expire_time_in_days"
            fieldProps={{
              suffix: formatMessage({
                id: 'common.days',
              }),
            }}
            rules={[
              {
                required: true,
              },
            ]}
          />
        </Col>
        <Col span={12}>
          {' '}
          <ProFormSelect
            label={formatMessage({
              id: 'common.priority_level',
            })}
            name="level"
            rules={[
              {
                required: true,
              },
            ]}
            request={async () => {
              return [
                {
                  label: (
                    <Tag color={getTagColor('Common')}>
                      {formatMessage({ id: 'common.common' })}
                    </Tag>
                  ),
                  value: 'Common',
                },
                {
                  label: (
                    <Tag color={getTagColor('Important')}>
                      {formatMessage({ id: 'common.important' })}
                    </Tag>
                  ),
                  value: 'Important',
                },
                {
                  label: (
                    <Tag color={getTagColor('Priority')}>
                      {formatMessage({ id: 'common.priority' })}
                    </Tag>
                  ),
                  value: 'Priority',
                },
              ];
            }}
          />
        </Col>
        <Col span={12}>
          <ProFormSelect
            label={formatMessage({
              id: 'common.assigned_to',
            })}
            name="assigned_to"
            rules={[
              {
                required: true,
              },
            ]}
            showSearch
            request={async (option) => {
              const listUser = await getCustomerUserList();
              return listUser.data.map((item: any) => ({
                label: `${item.first_name} ${item.last_name}`,
                value: item.name,
              }));
            }}
          />
        </Col>

        <Col span={24}>
          <ProFormTextArea
            label={formatMessage({
              id: 'common.note',
            })}
            name="description"
          />
        </Col>
      </Row>
    </Card>
  );
};

export default DetailedInfo;
