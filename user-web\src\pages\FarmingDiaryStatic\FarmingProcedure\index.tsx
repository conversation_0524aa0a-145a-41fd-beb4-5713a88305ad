import { PlusOutlined } from '@ant-design/icons';
import { PageContainer } from '@ant-design/pro-components';
import { Link, useIntl } from '@umijs/max';
import { Button } from 'antd';
import { FC, ReactNode } from 'react';
import ProcedureList from './components/ProcedureRead/ProcedureList';

interface IndexProps {
  children?: ReactNode;
}

const Index: FC<IndexProps> = ({ children }) => {
  const { formatMessage } = useIntl();
  return (
    <PageContainer
      extra={[
        <Link to="/farming-diary-static/procedure/create" key="create">
          <Button icon={<PlusOutlined />} type="primary">
            {formatMessage({ id: 'common.create' })}
          </Button>
        </Link>,
      ]}
    >
      <ProcedureList />
    </PageContainer>
  );
};

export default Index;
