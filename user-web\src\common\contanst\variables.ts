import { ACTION_PERMISSION, USER_TYPE } from './enum';

export const PERMISSIONS: {
  [key in USER_TYPE]: {
    actions: ACTION_PERMISSION[];
  };
} = {
  [USER_TYPE.AXCELA_ACADEMIC_LEADER]: {
    actions: [
      ACTION_PERMISSION.CREATE,
      ACTION_PERMISSION.READ,
      ACTION_PERMISSION.UPDATE,
      ACTION_PERMISSION.DELETE,
    ],
  },
  [USER_TYPE.AXCELA_ACADEMIC]: {
    actions: [ACTION_PERMISSION.READ, ACTION_PERMISSION.UPDATE],
  },
  [USER_TYPE.AXCELA_COACH_LEADER]: {
    actions: [ACTION_PERMISSION.READ, ACTION_PERMISSION.UPDATE, ACTION_PERMISSION.DELETE],
  },
  [USER_TYPE.AXCELA_COACH_FULLTIME]: {
    actions: [ACTION_PERMISSION.READ, ACTION_PERMISSION.UPDATE],
  },
  [USER_TYPE.AXCELA_COACH_MEMBER]: {
    actions: [],
  },
  [USER_TYPE.CUSTOMER_BUSINESS_HR]: {
    actions: [],
  },
  [USER_TYPE.AXCELA_SALE]: {
    actions: [ACTION_PERMISSION.CREATE, ACTION_PERMISSION.READ, ACTION_PERMISSION.UPDATE],
  },
  [USER_TYPE.AXCELA_SALE_LEADER]: {
    actions: [
      ACTION_PERMISSION.CREATE,
      ACTION_PERMISSION.READ,
      ACTION_PERMISSION.UPDATE,
      ACTION_PERMISSION.DELETE,
    ],
  },
  [USER_TYPE.AXCELA_STUDENT]: {
    actions: [],
  },
  [USER_TYPE.AXCELA_TEACHER]: {
    actions: [ACTION_PERMISSION.READ],
  },
  [USER_TYPE.AXCELA_HR]: {
    actions: [ACTION_PERMISSION.READ],
  },
  [USER_TYPE.AXCELA_SYSTEM_ADMIN]: {
    actions: [
      ACTION_PERMISSION.CREATE,
      ACTION_PERMISSION.READ,
      ACTION_PERMISSION.UPDATE,
      ACTION_PERMISSION.DELETE,
    ],
  },
};


export const STUDENT_PERMISSIONS: {
  [key in USER_TYPE]: {
    actions: ACTION_PERMISSION[];
  };
} = {
  [USER_TYPE.AXCELA_ACADEMIC_LEADER]: {
    actions: [
      ACTION_PERMISSION.CREATE,
      ACTION_PERMISSION.READ,
      ACTION_PERMISSION.UPDATE,
      ACTION_PERMISSION.DELETE,
      ACTION_PERMISSION.DOWNLOAD_FILE,
    ],
  },
  [USER_TYPE.AXCELA_ACADEMIC]: {
    actions: [
      ACTION_PERMISSION.READ,
      ACTION_PERMISSION.UPDATE,
      ACTION_PERMISSION.CREATE,
    ],
  },
  [USER_TYPE.AXCELA_COACH_LEADER]: {
    actions: [
      ACTION_PERMISSION.CREATE,
      ACTION_PERMISSION.READ,
      ACTION_PERMISSION.UPDATE,
      ACTION_PERMISSION.DELETE
    ],
  },
  [USER_TYPE.AXCELA_COACH_FULLTIME]: {
    actions: [
      ACTION_PERMISSION.READ,
      ACTION_PERMISSION.UPDATE,
      ACTION_PERMISSION.CREATE,

    ],
  },
  [USER_TYPE.AXCELA_COACH_MEMBER]: {
    actions: [],
  },
  [USER_TYPE.CUSTOMER_BUSINESS_HR]: {
    actions: [],
  },
  [USER_TYPE.AXCELA_SALE]: {
    actions: [
      ACTION_PERMISSION.CREATE,
      ACTION_PERMISSION.READ,
      ACTION_PERMISSION.UPDATE],
  },
  [USER_TYPE.AXCELA_SALE_LEADER]: {
    actions: [
      ACTION_PERMISSION.CREATE,
      ACTION_PERMISSION.READ,
      ACTION_PERMISSION.UPDATE,
      ACTION_PERMISSION.DELETE,
    ],
  },
  [USER_TYPE.AXCELA_STUDENT]: {
    actions: [],
  },
  [USER_TYPE.AXCELA_TEACHER]: {
    actions: [ACTION_PERMISSION.READ],
  },
  [USER_TYPE.AXCELA_HR]: {
    actions: [ACTION_PERMISSION.READ],
  },
  [USER_TYPE.AXCELA_SYSTEM_ADMIN]: {
    actions: [
      ACTION_PERMISSION.CREATE,
      ACTION_PERMISSION.READ,
      ACTION_PERMISSION.UPDATE,
      ACTION_PERMISSION.DELETE,
    ],
  },
};

export const COMPANY_PERMISSIONS: {
  [key in USER_TYPE]: {
    actions: ACTION_PERMISSION[];
  };
} = {
  [USER_TYPE.AXCELA_ACADEMIC_LEADER]: {
    actions: [
      ACTION_PERMISSION.CREATE,
      ACTION_PERMISSION.READ,
      ACTION_PERMISSION.UPDATE,
    ],
  },
  [USER_TYPE.AXCELA_ACADEMIC]: {
    actions: [
      ACTION_PERMISSION.READ,
      ACTION_PERMISSION.UPDATE,
      ACTION_PERMISSION.CREATE,
    ],
  },
  [USER_TYPE.AXCELA_COACH_LEADER]: {
    actions: [
      ACTION_PERMISSION.CREATE,
      ACTION_PERMISSION.READ,
      ACTION_PERMISSION.UPDATE,
    ],
  },
  [USER_TYPE.AXCELA_COACH_FULLTIME]: {
    actions: [
      ACTION_PERMISSION.READ,
      ACTION_PERMISSION.UPDATE,
      ACTION_PERMISSION.CREATE,

    ],
  },
  [USER_TYPE.AXCELA_COACH_MEMBER]: {
    actions: [],
  },
  [USER_TYPE.CUSTOMER_BUSINESS_HR]: {
    actions: [],
  },
  [USER_TYPE.AXCELA_SALE]: {
    actions: [
      ACTION_PERMISSION.CREATE,
      ACTION_PERMISSION.READ,
      ACTION_PERMISSION.UPDATE,
      ACTION_PERMISSION.DELETE,
    ],
  },
  [USER_TYPE.AXCELA_SALE_LEADER]: {
    actions: [
      ACTION_PERMISSION.CREATE,
      ACTION_PERMISSION.READ,
      ACTION_PERMISSION.UPDATE,
      ACTION_PERMISSION.DELETE,
    ],
  },
  [USER_TYPE.AXCELA_STUDENT]: {
    actions: [],
  },
  [USER_TYPE.AXCELA_TEACHER]: {
    actions: [],
  },
  [USER_TYPE.AXCELA_HR]: {
    actions: [
      ACTION_PERMISSION.READ,
      ACTION_PERMISSION.UPDATE,
    ],
  },
  [USER_TYPE.AXCELA_SYSTEM_ADMIN]: {
    actions: [
      ACTION_PERMISSION.CREATE,
      ACTION_PERMISSION.READ,
      ACTION_PERMISSION.UPDATE,
      ACTION_PERMISSION.DELETE,
    ],
  },
};

export const COURSE_PERMISSIONS: {
  [key in USER_TYPE]: {
    actions: ACTION_PERMISSION[];
  };
} = {
  [USER_TYPE.AXCELA_ACADEMIC_LEADER]: {
    actions: [
      ACTION_PERMISSION.CREATE,
      ACTION_PERMISSION.READ,
      ACTION_PERMISSION.UPDATE,
      ACTION_PERMISSION.DELETE,
    ],
  },
  [USER_TYPE.AXCELA_ACADEMIC]: {
    actions: [
      ACTION_PERMISSION.READ,
      ACTION_PERMISSION.UPDATE,
      ACTION_PERMISSION.CREATE,
      ACTION_PERMISSION.DELETE,

    ],
  },
  [USER_TYPE.AXCELA_COACH_LEADER]: {
    actions: [
      ACTION_PERMISSION.CREATE,
      ACTION_PERMISSION.READ,
      ACTION_PERMISSION.UPDATE,
    ],
  },
  [USER_TYPE.AXCELA_COACH_FULLTIME]: {
    actions: [
      ACTION_PERMISSION.READ,
      ACTION_PERMISSION.UPDATE,
      ACTION_PERMISSION.CREATE,

    ],
  },
  [USER_TYPE.AXCELA_COACH_MEMBER]: {
    actions: [
      ACTION_PERMISSION.READ,
      ACTION_PERMISSION.UPDATE,
    ],
  },
  [USER_TYPE.CUSTOMER_BUSINESS_HR]: {
    actions: [
      ACTION_PERMISSION.READ,
    ],
  },
  [USER_TYPE.AXCELA_SALE]: {
    actions: [
      ACTION_PERMISSION.CREATE,
      ACTION_PERMISSION.READ,
      ACTION_PERMISSION.UPDATE,
    ],
  },
  [USER_TYPE.AXCELA_SALE_LEADER]: {
    actions: [
      ACTION_PERMISSION.CREATE,
      ACTION_PERMISSION.READ,
      ACTION_PERMISSION.UPDATE,
    ],
  },
  [USER_TYPE.AXCELA_STUDENT]: {
    actions: [
      ACTION_PERMISSION.READ,
      ACTION_PERMISSION.UPDATE,
    ],
  },
  [USER_TYPE.AXCELA_TEACHER]: {
    actions: [
      ACTION_PERMISSION.READ,
      ACTION_PERMISSION.UPDATE,
    ],
  },
  [USER_TYPE.AXCELA_HR]: {
    actions: [
      ACTION_PERMISSION.READ,
      ACTION_PERMISSION.UPDATE,
    ],
  },
  [USER_TYPE.AXCELA_SYSTEM_ADMIN]: {
    actions: [
      ACTION_PERMISSION.CREATE,
      ACTION_PERMISSION.READ,
      ACTION_PERMISSION.UPDATE,
      ACTION_PERMISSION.DELETE,
    ],
  },
};

export const COACH_LESSON_PERMISSIONS: {
  [key in USER_TYPE]: {
    actions: ACTION_PERMISSION[];
  };
} = {
  [USER_TYPE.AXCELA_ACADEMIC_LEADER]: {
    actions: [
      ACTION_PERMISSION.CREATE,
      ACTION_PERMISSION.READ,
      ACTION_PERMISSION.UPDATE,
      ACTION_PERMISSION.UPLOAD_FILE,
      ACTION_PERMISSION.DOWNLOAD_FILE,
    ],
  },
  [USER_TYPE.AXCELA_ACADEMIC]: {
    actions: [
      ACTION_PERMISSION.READ,
      ACTION_PERMISSION.UPDATE,
      ACTION_PERMISSION.UPLOAD_FILE,
      ACTION_PERMISSION.DOWNLOAD_FILE,
    ],
  },
  [USER_TYPE.AXCELA_COACH_LEADER]: {
    actions: [
      ACTION_PERMISSION.CREATE,
      ACTION_PERMISSION.READ,
      ACTION_PERMISSION.UPDATE,
      ACTION_PERMISSION.DELETE,
      ACTION_PERMISSION.UPLOAD_FILE,
      ACTION_PERMISSION.DOWNLOAD_FILE,
    ],
  },
  [USER_TYPE.AXCELA_COACH_FULLTIME]: {
    actions: [
      ACTION_PERMISSION.CREATE,
      ACTION_PERMISSION.READ,
      ACTION_PERMISSION.UPDATE,
      ACTION_PERMISSION.DELETE,
      ACTION_PERMISSION.UPLOAD_FILE,
      ACTION_PERMISSION.DOWNLOAD_FILE,

    ],
  },
  [USER_TYPE.AXCELA_COACH_MEMBER]: {
    actions: [
      ACTION_PERMISSION.READ,
      ACTION_PERMISSION.UPDATE,
      ACTION_PERMISSION.UPLOAD_FILE,
      ACTION_PERMISSION.DOWNLOAD_FILE,
    ],
  },
  [USER_TYPE.CUSTOMER_BUSINESS_HR]: {
    actions: [],
  },
  [USER_TYPE.AXCELA_SALE]: {
    actions: [
      ACTION_PERMISSION.UPDATE,
    ],
  },
  [USER_TYPE.AXCELA_SALE_LEADER]: {
    actions: [
      ACTION_PERMISSION.UPDATE,
    ],
  },
  [USER_TYPE.AXCELA_STUDENT]: {
    actions: [
      ACTION_PERMISSION.READ,
      ACTION_PERMISSION.UPDATE,
    ],
  },
  [USER_TYPE.AXCELA_TEACHER]: {
    actions: [
      ACTION_PERMISSION.UPDATE,
    ],
  },
  [USER_TYPE.AXCELA_HR]: {
    actions: [
      ACTION_PERMISSION.UPDATE,
    ],
  },
  [USER_TYPE.AXCELA_SYSTEM_ADMIN]: {
    actions: [
      ACTION_PERMISSION.CREATE,
      ACTION_PERMISSION.READ,
      ACTION_PERMISSION.UPDATE,
      ACTION_PERMISSION.DELETE,
      ACTION_PERMISSION.UPLOAD_FILE,
      ACTION_PERMISSION.DOWNLOAD_FILE,
    ],
  },
};

export const TEACHER_LESSON_PERMISSIONS: {
  [key in USER_TYPE]: {
    actions: ACTION_PERMISSION[];
  };
} = {
  [USER_TYPE.AXCELA_ACADEMIC_LEADER]: {
    actions: [
      ACTION_PERMISSION.CREATE,
      ACTION_PERMISSION.READ,
      ACTION_PERMISSION.UPDATE,
      ACTION_PERMISSION.DELETE,
      ACTION_PERMISSION.UPLOAD_FILE,
      ACTION_PERMISSION.DOWNLOAD_FILE,
    ],
  },
  [USER_TYPE.AXCELA_ACADEMIC]: {
    actions: [
      ACTION_PERMISSION.CREATE,
      ACTION_PERMISSION.READ,
      ACTION_PERMISSION.UPDATE,
      ACTION_PERMISSION.UPLOAD_FILE,
      ACTION_PERMISSION.DOWNLOAD_FILE,
    ],
  },
  [USER_TYPE.AXCELA_COACH_LEADER]: {
    actions: [
      ACTION_PERMISSION.READ,
      ACTION_PERMISSION.UPDATE,
    ],
  },
  [USER_TYPE.AXCELA_COACH_FULLTIME]: {
    actions: [
      ACTION_PERMISSION.READ,
      ACTION_PERMISSION.UPDATE,
    ],
  },
  [USER_TYPE.AXCELA_COACH_MEMBER]: {
    actions: [
      ACTION_PERMISSION.UPDATE,
    ],
  },
  [USER_TYPE.CUSTOMER_BUSINESS_HR]: {
    actions: [],
  },
  [USER_TYPE.AXCELA_SALE]: {
    actions: [
      ACTION_PERMISSION.UPDATE,
    ],
  },
  [USER_TYPE.AXCELA_SALE_LEADER]: {
    actions: [
      ACTION_PERMISSION.UPDATE,
    ],
  },
  [USER_TYPE.AXCELA_STUDENT]: {
    actions: [
      ACTION_PERMISSION.UPDATE,
    ],
  },
  [USER_TYPE.AXCELA_TEACHER]: {
    actions: [
      ACTION_PERMISSION.READ,
      ACTION_PERMISSION.UPDATE,
    ],
  },
  [USER_TYPE.AXCELA_HR]: {
    actions: [
      ACTION_PERMISSION.READ,
      ACTION_PERMISSION.UPDATE,
    ],
  },
  [USER_TYPE.AXCELA_SYSTEM_ADMIN]: {
    actions: [
      ACTION_PERMISSION.CREATE,
      ACTION_PERMISSION.READ,
      ACTION_PERMISSION.UPDATE,
      ACTION_PERMISSION.DELETE,
      ACTION_PERMISSION.UPLOAD_FILE,
      ACTION_PERMISSION.DOWNLOAD_FILE,
    ],
  },
};

export const DYNED_APP_PERMISSIONS: {
  [key in USER_TYPE]: {
    actions: ACTION_PERMISSION[];
  };
} = {
  [USER_TYPE.AXCELA_ACADEMIC_LEADER]: {
    actions: [
      ACTION_PERMISSION.CREATE,
      ACTION_PERMISSION.READ,
      ACTION_PERMISSION.UPDATE,
      ACTION_PERMISSION.DELETE,
    ],
  },
  [USER_TYPE.AXCELA_ACADEMIC]: {
    actions: [
      ACTION_PERMISSION.CREATE,
      ACTION_PERMISSION.READ,
    ],
  },
  [USER_TYPE.AXCELA_COACH_LEADER]: {
    actions: [
      ACTION_PERMISSION.CREATE,
      ACTION_PERMISSION.READ,
      ACTION_PERMISSION.UPDATE,
      ACTION_PERMISSION.DELETE,
    ],
  },
  [USER_TYPE.AXCELA_COACH_FULLTIME]: {
    actions: [
      ACTION_PERMISSION.CREATE,
      ACTION_PERMISSION.READ,
      ACTION_PERMISSION.UPDATE,
    ],
  },
  [USER_TYPE.AXCELA_COACH_MEMBER]: {
    actions: [
      ACTION_PERMISSION.READ,
    ],
  },
  [USER_TYPE.CUSTOMER_BUSINESS_HR]: {
    actions: [],
  },
  [USER_TYPE.AXCELA_SALE]: {
    actions: [
      ACTION_PERMISSION.READ,
    ],
  },
  [USER_TYPE.AXCELA_SALE_LEADER]: {
    actions: [
      ACTION_PERMISSION.READ,
    ],
  },
  [USER_TYPE.AXCELA_STUDENT]: {
    actions: [
      ACTION_PERMISSION.READ,
    ],
  },
  [USER_TYPE.AXCELA_TEACHER]: {
    actions: [
      ACTION_PERMISSION.READ,
    ],
  },
  [USER_TYPE.AXCELA_HR]: {
    actions: [
      ACTION_PERMISSION.READ,
    ],
  },
  [USER_TYPE.AXCELA_SYSTEM_ADMIN]: {
    actions: [
      ACTION_PERMISSION.CREATE,
      ACTION_PERMISSION.READ,
      ACTION_PERMISSION.UPDATE,
      ACTION_PERMISSION.DELETE,
    ],
  },
};

export const CLASS_PERMISSIONS: {
  [key in USER_TYPE]: {
    actions: ACTION_PERMISSION[];
  };
} = {
  [USER_TYPE.AXCELA_ACADEMIC_LEADER]: {
    actions: [
      ACTION_PERMISSION.CREATE,
      ACTION_PERMISSION.READ,
      ACTION_PERMISSION.UPDATE,
      ACTION_PERMISSION.DELETE,
    ],
  },
  [USER_TYPE.AXCELA_ACADEMIC]: {
    actions: [
      ACTION_PERMISSION.CREATE,
      ACTION_PERMISSION.READ,
      ACTION_PERMISSION.UPDATE,
    ],
  },
  [USER_TYPE.AXCELA_COACH_LEADER]: {
    actions: [
      ACTION_PERMISSION.CREATE,
      ACTION_PERMISSION.READ,
      ACTION_PERMISSION.UPDATE,
      ACTION_PERMISSION.DELETE,
    ],
  },
  [USER_TYPE.AXCELA_COACH_FULLTIME]: {
    actions: [
      ACTION_PERMISSION.CREATE,
      ACTION_PERMISSION.READ,
      ACTION_PERMISSION.UPDATE,
    ],
  },
  [USER_TYPE.AXCELA_COACH_MEMBER]: {
    actions: [],
  },
  [USER_TYPE.CUSTOMER_BUSINESS_HR]: {
    actions: [],
  },
  [USER_TYPE.AXCELA_SALE]: {
    actions: [
      ACTION_PERMISSION.CREATE,
      ACTION_PERMISSION.READ,
      ACTION_PERMISSION.UPDATE,
      ACTION_PERMISSION.DELETE,
    ],
  },
  [USER_TYPE.AXCELA_SALE_LEADER]: {
    actions: [
      ACTION_PERMISSION.CREATE,
      ACTION_PERMISSION.READ,
      ACTION_PERMISSION.UPDATE,
      ACTION_PERMISSION.DELETE,
    ],
  },
  [USER_TYPE.AXCELA_STUDENT]: {
    actions: [
      ACTION_PERMISSION.READ,
    ],
  },
  [USER_TYPE.AXCELA_TEACHER]: {
    actions: [],
  },
  [USER_TYPE.AXCELA_HR]: {
    actions: [
      ACTION_PERMISSION.READ,
    ],
  },
  [USER_TYPE.AXCELA_SYSTEM_ADMIN]: {
    actions: [
      ACTION_PERMISSION.CREATE,
      ACTION_PERMISSION.READ,
      ACTION_PERMISSION.UPDATE,
      ACTION_PERMISSION.DELETE,
    ],
  },
};

export const EMPLOYEE_PERMISSIONS: {
  [key in USER_TYPE]: {
    actions: ACTION_PERMISSION[];
  };
} = {
  [USER_TYPE.AXCELA_ACADEMIC_LEADER]: {
    actions: [
      ACTION_PERMISSION.CREATE,
      ACTION_PERMISSION.READ,
      ACTION_PERMISSION.UPDATE,
      ACTION_PERMISSION.DELETE,
    ],
  },
  [USER_TYPE.AXCELA_ACADEMIC]: {
    actions: [
      ACTION_PERMISSION.READ,
      ACTION_PERMISSION.UPDATE,
    ],
  },
  [USER_TYPE.AXCELA_COACH_LEADER]: {
    actions: [
      ACTION_PERMISSION.CREATE,
      ACTION_PERMISSION.READ,
      ACTION_PERMISSION.UPDATE,
      ACTION_PERMISSION.DELETE,
    ],
  },
  [USER_TYPE.AXCELA_COACH_FULLTIME]: {
    actions: [
      ACTION_PERMISSION.CREATE,
      ACTION_PERMISSION.READ,
      ACTION_PERMISSION.UPDATE,
    ],
  },
  [USER_TYPE.AXCELA_COACH_MEMBER]: {
    actions: [],
  },
  [USER_TYPE.CUSTOMER_BUSINESS_HR]: {
    actions: [],
  },
  [USER_TYPE.AXCELA_SALE]: {
    actions: [
      ACTION_PERMISSION.CREATE,
      ACTION_PERMISSION.READ,
      ACTION_PERMISSION.UPDATE,
    ],
  },
  [USER_TYPE.AXCELA_SALE_LEADER]: {
    actions: [
      ACTION_PERMISSION.CREATE,
      ACTION_PERMISSION.READ,
      ACTION_PERMISSION.UPDATE,
    ],
  },
  [USER_TYPE.AXCELA_STUDENT]: {
    actions: [],
  },
  [USER_TYPE.AXCELA_TEACHER]: {
    actions: [],
  },
  [USER_TYPE.AXCELA_HR]: {
    actions: [
      ACTION_PERMISSION.CREATE,
      ACTION_PERMISSION.READ,
      ACTION_PERMISSION.UPDATE,
      ACTION_PERMISSION.DELETE,
    ],
  },
  [USER_TYPE.AXCELA_SYSTEM_ADMIN]: {
    actions: [
      ACTION_PERMISSION.CREATE,
      ACTION_PERMISSION.READ,
      ACTION_PERMISSION.UPDATE,
      ACTION_PERMISSION.DELETE,
    ],
  },
};

export const BOOKING_TEACHER_PERMISSIONS: {
  [key in USER_TYPE]: {
    actions: ACTION_PERMISSION[];
  };
} = {
  [USER_TYPE.AXCELA_ACADEMIC_LEADER]: {
    actions: [
      ACTION_PERMISSION.CREATE,
      ACTION_PERMISSION.READ,
      ACTION_PERMISSION.UPDATE,
      ACTION_PERMISSION.DELETE,
    ],
  },
  [USER_TYPE.AXCELA_ACADEMIC]: {
    actions: [
      ACTION_PERMISSION.CREATE,
      ACTION_PERMISSION.READ,
      ACTION_PERMISSION.UPDATE,
      ACTION_PERMISSION.DELETE,
    ],
  },
  [USER_TYPE.AXCELA_COACH_LEADER]: {
    actions: [
      ACTION_PERMISSION.CREATE,
      ACTION_PERMISSION.READ,
    ],
  },
  [USER_TYPE.AXCELA_COACH_FULLTIME]: {
    actions: [
      ACTION_PERMISSION.CREATE,
      ACTION_PERMISSION.READ,
    ],
  },
  [USER_TYPE.AXCELA_COACH_MEMBER]: {
    actions: [],
  },
  [USER_TYPE.CUSTOMER_BUSINESS_HR]: {
    actions: [],
  },
  [USER_TYPE.AXCELA_SALE]: {
    actions: [],
  },
  [USER_TYPE.AXCELA_SALE_LEADER]: {
    actions: [],
  },
  [USER_TYPE.AXCELA_STUDENT]: {
    actions: [],
  },
  [USER_TYPE.AXCELA_TEACHER]: {
    actions: [],
  },
  [USER_TYPE.AXCELA_HR]: {
    actions: [
      ACTION_PERMISSION.READ,
    ],
  },
  [USER_TYPE.AXCELA_SYSTEM_ADMIN]: {
    actions: [
      ACTION_PERMISSION.CREATE,
      ACTION_PERMISSION.READ,
      ACTION_PERMISSION.UPDATE,
      ACTION_PERMISSION.DELETE,
    ],
  },
};


export const BOOKING_COACH_PERMISSIONS: {
  [key in USER_TYPE]: {
    actions: ACTION_PERMISSION[];
  };
} = {
  [USER_TYPE.AXCELA_ACADEMIC_LEADER]: {
    actions: [
      ACTION_PERMISSION.CREATE,
      ACTION_PERMISSION.READ,
      ACTION_PERMISSION.UPDATE,
      ACTION_PERMISSION.DELETE,
    ],
  },
  [USER_TYPE.AXCELA_ACADEMIC]: {
    actions: [
      ACTION_PERMISSION.CREATE,
      ACTION_PERMISSION.READ,
      ACTION_PERMISSION.UPDATE,
    ],
  },
  [USER_TYPE.AXCELA_COACH_LEADER]: {
    actions: [
      ACTION_PERMISSION.CREATE,
      ACTION_PERMISSION.READ,
      ACTION_PERMISSION.UPDATE,
      ACTION_PERMISSION.DELETE,
    ],
  },
  [USER_TYPE.AXCELA_COACH_FULLTIME]: {
    actions: [
      ACTION_PERMISSION.CREATE,
      ACTION_PERMISSION.READ,
      ACTION_PERMISSION.UPDATE,
    ],
  },
  [USER_TYPE.AXCELA_COACH_MEMBER]: {
    actions: [],
  },
  [USER_TYPE.CUSTOMER_BUSINESS_HR]: {
    actions: [],
  },
  [USER_TYPE.AXCELA_SALE]: {
    actions: [],
  },
  [USER_TYPE.AXCELA_SALE_LEADER]: {
    actions: [],
  },
  [USER_TYPE.AXCELA_STUDENT]: {
    actions: [],
  },
  [USER_TYPE.AXCELA_TEACHER]: {
    actions: [],
  },
  [USER_TYPE.AXCELA_HR]: {
    actions: [
      ACTION_PERMISSION.READ,
    ],
  },
  [USER_TYPE.AXCELA_SYSTEM_ADMIN]: {
    actions: [
      ACTION_PERMISSION.CREATE,
      ACTION_PERMISSION.READ,
      ACTION_PERMISSION.UPDATE,
      ACTION_PERMISSION.DELETE,
    ],
  },
};