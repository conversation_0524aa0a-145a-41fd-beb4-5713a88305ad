import { DEFAULT_FALLBACK_IMG } from '@/common/contanst/img';
import QRCodeModal from '@/pages/FarmingManagement/WorkflowManagement/Detail/components/QRCodeModal';
import { IDiaryTask } from '@/services/farming-plan';
import { getFileUrlV2 } from '@/services/utils';
import {
  ProForm,
  ProFormDatePicker,
  ProFormSelect,
  ProFormText,
  ProFormTextArea,
} from '@ant-design/pro-components';
import { useIntl } from '@umijs/max';
import { Card, Col, Form, Image, Modal, Row, Space } from 'antd';
import { useEffect } from 'react';

interface Props {
  showModal: boolean;
  setShowModal: any;
  item: IDiaryTask;
}
const TaskModal = ({ showModal, setShowModal, item }: Props) => {
  const [form] = Form.useForm();
  useEffect(() => {
    form.setFieldsValue(item);
    form.setFieldValue('state_name', item.text_state || item.state_name);
  }, [item]);
  const intl = useIntl();

  return (
    <Modal open={showModal} width={1200} onCancel={() => setShowModal(false)} footer={false}>
      <Row gutter={[5, 5]} style={{ marginTop: 30 }}>
        <Col md={24}>
          <Card title="Thông tin chi tiết">
            <Row>
              <Space>
                <QRCodeModal taskId={item.name} />
              </Space>
            </Row>

            <Row gutter={[5, 5]}>
              <Col md={24}>
                <ProForm form={form} disabled grid={true} submitter={false}>
                  <ProFormText
                    label={'Tên công việc'}
                    rules={[
                      {
                        required: true,
                      },
                    ]}
                    name="label"
                    colProps={{
                      md: 8,
                      sm: 24,
                    }}
                  />

                  <ProFormSelect
                    label="Chọn kế hoạch"
                    rules={[
                      {
                        required: true,
                      },
                    ]}
                    name="plan_name"
                    colProps={{
                      md: 8,
                      sm: 24,
                    }}
                  />
                  <ProFormSelect
                    label="Chọn giai đoạn"
                    rules={[
                      {
                        required: true,
                      },
                    ]}
                    name="state_name"
                    colProps={{ md: 8, sm: 24 }}
                  />

                  <ProFormDatePicker
                    fieldProps={{ format: 'HH:mm MM/DD/YYYY', showTime: true }}
                    label="Thời gian thực hiện"
                    rules={[
                      {
                        required: true,
                      },
                    ]}
                    name="start_date"
                    colProps={{
                      md: 8,
                      sm: 24,
                    }}
                    width={'xl'}
                  />
                  <ProFormDatePicker
                    label="Thời gian kết thúc"
                    rules={[
                      {
                        required: true,
                      },
                    ]}
                    name="end_date"
                    fieldProps={{ showTime: true, format: 'HH:mm MM/DD/YYYY' }}
                    colProps={{
                      md: 8,
                      sm: 24,
                    }}
                    width={'xl'}
                  />

                  <ProFormText
                    label="Người thực hiện"
                    name={'assigned_to_user'}
                    initialValue={
                      item.text_assign_user ||
                      (item.assigned_to_info
                        ? `${item.assigned_to_info?.at(0)?.first_name} ${
                            item.assigned_to_info?.at(0)?.last_name
                          }`
                        : intl.formatMessage({ id: 'common.missing_info' }))
                    }
                    rules={[
                      {
                        required: true,
                      },
                    ]}
                    colProps={{ md: 8, sm: 24 }}
                  />
                  <ProFormText
                    label="Thành viên liên quan (nếu có)"
                    name={'involved_in_users'}
                    initialValue={item?.involve_in_users?.reduce(
                      (acc, ele) => acc + `${ele.first_name} ${ele.last_name}, `,
                      '',
                    )}
                    colProps={{ md: 8, sm: 24 }}
                  />
                  <ProFormSelect
                    rules={[
                      {
                        required: true,
                      },
                    ]}
                    label="Trạng thái"
                    name="status"
                    options={[
                      {
                        label: 'Lên kế hoạch',
                        value: 'Plan',
                      },
                      {
                        label: 'Đang xử lý',
                        value: 'In progress',
                      },
                      {
                        label: 'Hoàn tất',
                        value: 'Done',
                      },
                      {
                        label: 'Trì hoãn',
                        value: 'Pending',
                      },
                    ]}
                    initialValue={'Plan'}
                    colProps={{ md: 8, sm: 24 }}
                  />
                  <ProFormTextArea label="Ghi chú" name="description" />
                </ProForm>
              </Col>
            </Row>
          </Card>
        </Col>
      </Row>
      <Card title={intl.formatMessage({ id: 'common.category' })} style={{ marginTop: 30 }}>
        <Image.PreviewGroup>
          {item.item_list?.map((item, index) => {
            return (
              <Image
                src={getFileUrlV2({ src: item.category?.image }) || DEFAULT_FALLBACK_IMG}
                width={100}
                key={index}
                onClick={(e) => {
                  e.stopPropagation();
                  e.preventDefault();
                }}
              />
            );
          })}
        </Image.PreviewGroup>
      </Card>{' '}
      <Card title={intl.formatMessage({ id: 'common.product' })} style={{ marginTop: 30 }}>
        <Image.PreviewGroup>
          {item.prod_quantity_list?.map((item, index) => {
            if (index > 3) return '';
            return (
              <Image
                src={getFileUrlV2({ src: item.agriculture_product?.image }) || DEFAULT_FALLBACK_IMG}
                width={50}
                key={index}
              />
            );
          })}
        </Image.PreviewGroup>
      </Card>
    </Modal>
  );
};

export default TaskModal;
