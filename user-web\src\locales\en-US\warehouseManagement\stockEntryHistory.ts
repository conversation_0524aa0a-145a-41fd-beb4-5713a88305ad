export default {
  'warehouse-management.stock-entry-history.id': 'Id',
  'warehouse-management.stock-entry-history.date': 'Posting date',
  'warehouse-management.stock-entry-history.customer': 'Customer',
  'warehouse-management.stock-entry-history.total-qty': 'Total quantity',
  'warehouse-management.stock-entry-history.total-price': 'Total Price',
  'warehouse-management.stock-entry-history.item': 'Item', // Item Code
  'warehouse-management.stock-entry-history.accepted-quantity': 'Accepted Quantity', // Accepted Quantity
  'warehouse-management.stock-entry-history.rejected-quantity': 'Rejected Quantity', // Rejected Quantity
  'warehouse-management.stock-entry-history.rate': 'Rate', // Rate
  'warehouse-management.stock-entry-history.amount': 'Amount',
  'warehouse-management.stock-entry-history.detail': 'Detail',
  'warehouse-management.stock-entry-history.total_quantity': 'Total quantity',
  'warehouse-management.stock-entry-history.total_price': 'Total price',
  'warehouse-management.stock-entry-history.item_id': 'Item ID',
  'warehouse-management.stock-entry-history.item_label': 'Item',
  'warehouse-management.stock-entry-history.warehouse_label': 'Warehouse',
  'warehouse-management.stock-entry-history.purpose': 'Purpose',
  'warehouse-management.stock-entry': 'Stock Entry',
};
