import { DeleteFilled, DownOutlined, UpOutlined } from '@ant-design/icons';
import { Button, Collapse, CollapseProps, Space, Typography } from 'antd';
import { createStyles } from 'antd-use-styles';
import { FC, ReactNode } from 'react';

interface CardCollapseProps {
  children?: ReactNode;
  title?: string;
  titleIcon?: ReactNode;
  expandIcon?: CollapseProps['expandIcon'];
  extra?: ReactNode;
  handleMoveUp?: () => void;
  handleMoveDown?: () => void;
}

const useStyles = createStyles(({ token }) => ({
  collapseHeader: {
    backgroundColor: token.colorBgContainer,
    boxShadow: 'none',
    '& .ant-collapse-header': {
      borderBlockEnd: `1px solid ${token.colorBorderSecondary}`,
    },
  },
}));
const CardCollapse: FC<CardCollapseProps> = ({
  children,
  title,
  titleIcon,
  expandIcon,
  extra,
  handleMoveDown,
  handleMoveUp,
}) => {
  const styles = useStyles();
  const items: CollapseProps['items'] = [
    {
      key: '1',
      label: (
        <Space align="baseline" style={{ alignItems: 'center' }}>
          {handleMoveDown && handleMoveUp && (
            <Space direction="vertical" size={2}>
              <Button icon={<UpOutlined />} size="small" onClick={handleMoveUp} />
              <Button icon={<DownOutlined />} size="small" onClick={handleMoveDown} />
            </Space>
          )}
          {titleIcon}
          <Typography.Title level={4}> {title}</Typography.Title>
        </Space>
      ),
      extra: typeof extra === 'undefined' ? <Button icon={<DeleteFilled />} /> : extra,
      children: children,
      showArrow: true,
    },
  ];
  return (
    <Collapse
      defaultActiveKey={'1'}
      bordered={false}
      collapsible="icon"
      items={items}
      expandIconPosition="end"
      className={styles.collapseHeader}
      expandIcon={expandIcon}
    />
  );
};

export default CardCollapse;
