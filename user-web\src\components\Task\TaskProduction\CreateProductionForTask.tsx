import { getProductItemV3 } from '@/services/InventoryManagementV3/product-item';
import { generalCreate } from '@/services/sscript';
import { PlusOutlined } from '@ant-design/icons';
import { ProFormSelect } from '@ant-design/pro-components';
import { FormattedMessage, useIntl } from '@umijs/max';
import { Button, Col, Form, Input, InputNumber, message, Modal, Row } from 'antd';
import { useState } from 'react';
const { Item } = Form;

const CreateProductionUpdateView = (params: { refreshFnc: any; task_id: string }) => {
  const [loading, setLoading] = useState(false);
  const [isOpen, setOpen] = useState(false);
  const [form] = Form.useForm();
  const [uinits, setAllUnits] = useState([]);

  const showModal = () => {
    setOpen(true);
  };
  const hideModal = () => {
    setOpen(false);
  };
  const handleOk = () => {
    form.submit();
  };
  const handleCancel = () => {
    hideModal();
    form.resetFields();
  };
  const intl = useIntl();
  return (
    <>
      <Button type="primary" onClick={showModal} style={{ display: 'flex', alignItems: 'center' }}>
        <PlusOutlined></PlusOutlined>
        {intl.formatMessage({ id: 'common.add_production' })}
      </Button>
      <Modal
        title={intl.formatMessage({ id: 'common.add_production' })}
        open={isOpen}
        onOk={handleOk}
        onCancel={handleCancel}
        confirmLoading={loading}
      >
        <Form
          layout="horizontal"
          labelCol={{ span: 24 }}
          labelAlign="left"
          form={form}
          onFinish={async (value: any) => {
            try {
              value.task_id = params.task_id;
              value._uinit = uinits.find((d: any) => d.name === value.unit);
              const result = await generalCreate('iot_production_quantity', {
                data: value,
              });
              form.resetFields();
              hideModal();
              message.success('Success!');
              if (params.refreshFnc) {
                await params.refreshFnc();
              }
            } catch (error: any) {
              message.error(error.toString());
            } finally {
              setLoading(false);
            }
          }}
        >
          <Row gutter={5}>
            <Col className="gutter-row" md={24}>
              <ProFormSelect
                rules={[
                  {
                    required: true,
                    message: 'Bắt buộc điền',
                  },
                ]}
                allowClear
                label={intl.formatMessage({
                  id: 'common.crop_product',
                })}
                name="product_id"
                request={async () => {
                  const res = await getProductItemV3();
                  return res.data.map((item: any) => {
                    return {
                      label: item.label,
                      value: item.name,
                    };
                  });
                }}
                showSearch
              />
            </Col>
            <Col className="gutter-row" md={24}>
              <Item
                label={intl.formatMessage({
                  id: 'storage-management.category-management.expected_quantity',
                })}
                labelCol={{ span: 24 }}
                name="exp_quantity"
              >
                <InputNumber style={{ width: '100%' }} min={0} />
              </Item>
            </Col>
            <Col className="gutter-row" md={24}>
              <Item
                label={intl.formatMessage({
                  id: 'storage-management.category-management.real_quantity',
                })}
                labelCol={{ span: 24 }}
                name="quantity"
              >
                <InputNumber style={{ width: '100%' }} min={0} />
              </Item>
            </Col>
            <Col className="gutter-row" md={24}>
              <Item
                label={intl.formatMessage({
                  id: 'storage-management.category-management.loss_quantity',
                })}
                labelCol={{ span: 24 }}
                name="lost_quantity"
              >
                <InputNumber style={{ width: '100%' }} min={0} />
              </Item>
            </Col>
            <Col className="gutter-row" md={24}>
              <Item
                label={<FormattedMessage id={'common.form.description'} />}
                labelCol={{ span: 24 }}
                name="description"
              >
                <Input.TextArea />
              </Item>
            </Col>
          </Row>
        </Form>
      </Modal>
    </>
  );
};

export default CreateProductionUpdateView;
