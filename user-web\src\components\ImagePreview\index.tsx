import { Modal } from 'antd';
import { FC, useState } from 'react';

interface Props {
  imageUrls: string[];
  size?: number;
}

const ImagePreview: FC<Props> = ({ imageUrls, size = 100 }) => {
  const [previewOpen, setPreviewOpen] = useState(false);
  const [previewImage, setPreviewImage] = useState('');
  const [previewTitle, setPreviewTitle] = useState('');

  const handlePreview = (url: string) => {
    setPreviewImage(`https://iot.viis.tech/api/v2/file/download?file_url=${url}`);
    setPreviewOpen(true);
    setPreviewTitle(url.substring(url.lastIndexOf('/') + 1));
  };

  const handleCancel = () => setPreviewOpen(false);

  return (
    <>
      <div
        style={{
          display: 'flex',
          flexWrap: 'wrap',
          gap: '10px',
        }}
      >
        {imageUrls.map((url, index) => (
          <div
            key={index}
            style={{
              // Container to maintain consistent size while allowing image to keep ratio
              width: size,
              height: size,
              position: 'relative',
              overflow: 'hidden',
              backgroundColor: '#f5f5f5', // Light background for transparent images
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center',
            }}
          >
            <img
              src={`https://iot.viis.tech/api/v2/file/download?file_url=${url}`}
              alt={`Image ${index + 1}`}
              style={{
                cursor: 'pointer',
                maxWidth: '100%',
                maxHeight: '100%',
                objectFit: 'contain', // This preserves aspect ratio
                position: 'absolute',
              }}
              onClick={() => handlePreview(url)}
            />
          </div>
        ))}
      </div>
      <Modal
        open={previewOpen}
        title={previewTitle}
        footer={null}
        onCancel={handleCancel}
        width="auto"
        style={{ maxWidth: '90vw', maxHeight: '90vh' }}
      >
        <img
          alt="preview"
          style={{
            maxWidth: '100%',
            maxHeight: '80vh',
            objectFit: 'contain',
          }}
          src={previewImage}
        />
      </Modal>
    </>
  );
};

export default ImagePreview;
