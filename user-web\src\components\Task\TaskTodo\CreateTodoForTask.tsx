import { getCustomerUserList } from '@/services/customerUser';
import { generalCreate } from '@/services/sscript';
import { PlusOutlined } from '@ant-design/icons';
import { ProForm, ProFormSelect } from '@ant-design/pro-components';
import { FormattedMessage, useIntl } from '@umijs/max';
import { Button, Col, Form, Input, message, Modal, Row } from 'antd';
import { useState } from 'react';
const { Item } = Form;

const CreateTodoForTask = (params: { refreshFnc: any; task_id: string }) => {
  const [loading, setLoading] = useState(false);
  const [isOpen, setOpen] = useState(false);
  const [form] = ProForm.useForm();
  const intl = useIntl();

  const showModal = () => {
    setOpen(true);
  };
  const hideModal = () => {
    setOpen(false);
  };
  const handleOk = () => {
    form.submit();
  };
  const handleCancel = () => {
    hideModal();
    form.resetFields();
  };

  return (
    <>
      <Button type="primary" onClick={showModal} style={{ display: 'flex', alignItems: 'center' }}>
        <PlusOutlined /> <FormattedMessage id="common.add_sub_task" />
      </Button>
      <Modal
        title={intl.formatMessage({ id: 'common.add_sub_task' })}
        open={isOpen}
        onOk={handleOk}
        onCancel={handleCancel}
        confirmLoading={loading}
      >
        <ProForm
          submitter={false}
          layout="horizontal"
          labelCol={{ span: 24 }}
          labelAlign="left"
          form={form}
          onFinish={async (value: any) => {
            try {
              value.farming_plan_task = params.task_id;
              const result = await generalCreate('iot_todo', {
                data: value,
              });
              form.resetFields();
              hideModal();
              message.success(intl.formatMessage({ id: 'common.success' }));
              if (params.refreshFnc) {
                await params.refreshFnc();
              }
            } catch (error: any) {
              message.error(error.toString());
            } finally {
              setLoading(false);
            }
          }}
        >
          <Row gutter={5}>
            <Col className="gutter-row" md={24}>
              <Item
                label={<FormattedMessage id="task.task_name" />}
                labelCol={{ span: 24 }}
                rules={[
                  {
                    required: true,
                    message: intl.formatMessage({ id: 'common.required' }),
                  },
                ]}
                name="label"
              >
                <Input />
              </Item>
            </Col>
            <Col className="gutter-row" md={24}>
              <ProFormSelect
                label={<FormattedMessage id="task.executor" />}
                showSearch
                name="customer_user_id"
                request={async () => {
                  const result = await getCustomerUserList();
                  return result.data.map((item: any) => {
                    return {
                      label: item.last_name + ' ' + item.first_name,
                      value: item.name,
                    };
                  });
                }}
              />
            </Col>
            <Col className="gutter-row" md={24}>
              <Item
                label={<FormattedMessage id="common.description" />}
                labelCol={{ span: 24 }}
                name="description"
              >
                <Input.TextArea />
              </Item>
            </Col>
          </Row>
        </ProForm>
      </Modal>
    </>
  );
};

export default CreateTodoForTask;
