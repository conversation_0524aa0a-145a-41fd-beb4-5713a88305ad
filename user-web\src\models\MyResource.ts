// src/models/count.ts
import { deviceFunctionList } from '@/services/deviceFunction';
import { deviceList } from '@/services/devices';
import { projectList } from '@/services/projects';
import { zoneList } from '@/services/zones';
import { IIotProductionFunction } from '@/types/IIotProductionFunction';
import { useModel } from '@umijs/max';
import { message } from 'antd';
import jwt_decode from 'jwt-decode';
import moment, { Moment } from 'moment';
import { useCallback, useEffect, useMemo, useState } from 'react';

export default () => {
  //get token decode from localstorage
  const token = localStorage.getItem('token');
  // //only decode if token is not null
  const decodedToken: any = token ? jwt_decode(token) : null;
  const sections = decodedToken ? decodedToken.sections : 'NULL';

  if (
    sections &&
    (sections.includes('SYSTEM_ADMIN') ||
      (sections.includes('PROJECT') && sections.includes('ZONE')))
  ) {
    const [myProject, setMyProject] = useState<any>([]);
    const [myZone, setMyZone] = useState<any>([]);
    const [myDevice, setMyDevice] = useState<any>([]);

    const [selectedProject, setSelectedProject] = useState<any>('');
    const [selectedZone, setSelectedZone] = useState<any>('');
    const [selectedDevice, setSelectedDevice] = useState<any>('');
    const [selectedDeviceThingsboard, setSelectedDeviceThingsboard] = useState<any>('');

    const [selectedTimeType, setSelectedTimeType] = useState<
      '5p-now' | '1h-now' | '2h-now' | '1d-now' | 'custom'
    >('1h-now');
    const [timeRange, setTimeRange] = useState<Moment[]>([moment().add(-1, 'h'), moment()]);
    const [deviceFunction, setDeviceFunction] = useState<any>([]);
    const [latestDeviceValue, setLatestDeviceValue] = useState<any>([]);
    const [loadingResource, setLoadingResource] = useState<any>(false);
    const [newDataFromMQTT, setNewDataFromMqtt] = useState<any>({});
    const { initialState } = useModel('@@initialState');

    useMemo(() => {
      switch (selectedTimeType) {
        case '5p-now':
          setTimeRange([moment().add(-5, 'm'), moment()]);
          break;
        case '1h-now':
          setTimeRange([moment().add(-1, 'h'), moment()]);
          break;
        case '2h-now':
          setTimeRange([moment().add(-2, 'h'), moment()]);
          break;
        case '1d-now':
          setTimeRange([moment().add(-1, 'd'), moment()]);
          break;
        case 'custom':
          break;
        default:
          break;
      }
    }, [selectedTimeType]);

    const getMyProject = async () => {
      try {
        setLoadingResource(true);
        const data = await projectList({
          filters: [],
          page: 1,
          size: 1000,
          fields: ['*'],
          order_by: 'creation',
        });
        setMyProject(data.data);
      } catch (error: any) {
        message.error(error?.toString());
      } finally {
        setLoadingResource(false);
      }
    };

    const getMyZone = async () => {
      try {
        setLoadingResource(true);
        const data = await zoneList({
          filters: [],
          page: 1,
          size: 1000,
          fields: ['*'],
          order_by: 'creation',
        });
        setMyZone(data.data);
      } catch (error: any) {
        message.error(error?.toString());
      } finally {
        setLoadingResource(false);
      }
    };

    const getMyDevice = async () => {
      try {
        setLoadingResource(true);
        const data = await deviceList({
          filters: [],
          page: 1,
          size: 1000,
          fields: ['*'],
          order_by: 'creation',
        });
        setMyDevice(data.data);
      } catch (error: any) {
        message.error(error?.toString());
      } finally {
        setLoadingResource(false);
      }
    };

    const getDeviceFunction = async () => {
      try {
        const device = myDevice.filter((d: any) => d.name === selectedDevice);

        if (device.length) {
          setSelectedDeviceThingsboard(device[0].device_id_thingsboard);
          const result = await deviceFunctionList({
            page: 1,
            size: 1000,
            filters: [
              ['product_function', 'device_profile_id', 'like', device[0].device_profile_id],
            ],
            order_by: 'index_sort asc',
          });
          const listBytabs = await buildTabsByFunction(
            result.data,
            device[0].device_id_thingsboard,
          );
          setDeviceFunction(listBytabs);
        }
      } catch (error: any) {
        message.error(error.toString());
      }
    };

    const getLatesDataOfDevice = async () => {
      try {
        const latesDataDevice = await deviceList({
          filters: [['iot_device', 'name', 'like', selectedDevice]],
          page: 1,
          size: 1000,
          fields: ['*'],
          order_by: 'creation',
        });
        console.log('latesDataDevice from api', latesDataDevice);

        setLatestDeviceValue(latesDataDevice.data[0]?.latest_data);
      } catch (error: any) {
        message.error(error.toString());
      }
    };
    const setLatestDeviceValueByid = (latestData: any, device_id_thingsboard: string) => {
      setNewDataFromMqtt({
        data: latestData,
        device_id_thingsboard: device_id_thingsboard,
      });
    };

    const checkAndSetLatestData = useCallback(
      (latestData: any, device_id_thingsboard: any) =>
        setLatestDeviceValueByid(latestData, device_id_thingsboard),
      [],
    );

    useEffect(() => {
      if (selectedDevice) {
        getDeviceFunction();
        getLatesDataOfDevice();
      }
    }, [selectedDevice]);

    useEffect(() => {
      if (initialState?.currentUser) {
        getMyProject();
        getMyZone();
        getMyDevice();
      }
    }, [initialState]);

    useMemo(() => {
      if (Object.keys(newDataFromMQTT).length) {
        if (newDataFromMQTT.device_id_thingsboard === selectedDeviceThingsboard) {
          let new_data = newDataFromMQTT.data;
          let old_data = latestDeviceValue;
          console.log({
            new_data,
            old_data,
          });
          let latestData: any = [];
          if (old_data && old_data?.length) {
            old_data?.map((d: any) => {
              const match_key = new_data.filter((e: any) => {
                return e.key === d.key;
              });
              if (match_key.length) {
                let _new = match_key[0];
                if (_new.ts >= parseInt(d.ts.toString())) {
                  _new.deviceId = selectedDeviceThingsboard;
                  latestData.push(_new);
                } else {
                  latestData.push(d);
                }
              } else {
                latestData.push(d);
              }
            });
          } else {
            new_data.map((e: any) => {
              e.deviceId = selectedDeviceThingsboard;
              latestData.push(e);
            });
          }

          console.log('latestData', latestData);
          setLatestDeviceValue(latestData);
        }
      }
    }, [newDataFromMQTT]);

    return {
      myProject,
      myZone,
      myDevice,
      selectedProject,
      selectedZone,
      selectedDevice,
      setSelectedProject,
      setSelectedZone,
      setSelectedDevice,
      latestDeviceValue,
      loadingResource,
      deviceFunction,
      selectedTimeType,
      setSelectedTimeType,
      setLatestDeviceValue,
      selectedDeviceThingsboard,
      timeRange,
      setTimeRange,
      checkAndSetLatestData,
    };
  } else {
  }
};

const buildTabsByFunction = async (
  functionList: IIotProductionFunction[],
  device_id_thingsboard: string,
) => {
  try {
    if (!functionList.length) return [];

    functionList = functionList.map((d: IIotProductionFunction) => {
      d.index_sort = parseInt(d?.index_sort?.toString?.()||"0");
      d.md_size = parseInt(d.md_size?.toString() || '0');
      d.device_id_thingsboard = device_id_thingsboard;
      return d;
    });
    let tabs: any = functionList.filter((d: IIotProductionFunction) => {
      return d?.data_type === 'Tab Break';
    });
    if (!tabs.length) {
      tabs.push({
        name: 'no-tab',
        label: 'Tất cả dữ liệu',
        index_sort: functionList[0].index_sort,
      });
    }
    tabs = tabs.map((d: IIotProductionFunction) => {
      return {
        name: d.name,
        label: d.label,
        index_sort: d.index_sort,
      };
    });

    let listBytabs = tabs.map((d: any, index: number) => {
      const curTab = d;
      const nextTab = index < tabs.length - 1 ? tabs[index + 1] : null;
      const idenOfTabs = functionList.filter((e: IIotProductionFunction) => {
        if (nextTab) {
          if (index == 0)
            return (
              (e.name !== curTab.name &&
                e.name !== nextTab.name &&
                e.index_sort >= curTab.index_sort &&
                e.index_sort <= nextTab.index_sort) ||
              (e.name !== curTab.name && e.index_sort < curTab.index_sort)
            );
          else
            return (
              e.name !== curTab.name &&
              e.name !== nextTab.name &&
              e.index_sort >= curTab.index_sort &&
              e.index_sort <= nextTab.index_sort
            );
        } else {
          if (index == 0)
            return (
              (e.name !== curTab.name && e.index_sort >= curTab.index_sort) ||
              (e.name !== curTab.name && e.index_sort < curTab.index_sort)
            );
          else return e.name !== curTab.name && e.index_sort >= curTab.index_sort;
        }
      });
      let groups: any = idenOfTabs.filter((e: IIotProductionFunction) => {
        return e.data_type === 'Group Break';
      });
      d.function_list = idenOfTabs;

      if (!groups.length) {
        groups.push({
          name: 'no-group',
          label: 'Tất cả dữ liệu',
          index_sort: idenOfTabs[0].index_sort,
          function_list: idenOfTabs,
        });
      }
      groups = groups.map((d: IIotProductionFunction) => {
        return {
          name: d.name,
          label: d.label,
          index_sort: d.index_sort,
          md_size: d.md_size,
        };
      });
      d.groups = groups;
      d.groups = groups.map((e: any, index_group: number) => {
        const curGroup = e;
        const nextGroup = index_group < groups.length - 1 ? groups[index_group + 1] : null;
        const idenOfGroup = idenOfTabs.filter((func: IIotProductionFunction) => {
          if (nextGroup) {
            if (index_group == 0)
              return (
                (func.name !== curGroup.name &&
                  func.name !== nextGroup.name &&
                  func.index_sort >= curGroup.index_sort &&
                  func.index_sort <= nextGroup.index_sort) ||
                (func.name !== curGroup.name && func.index_sort < curGroup.index_sort)
              );
            else
              return (
                func.name !== curGroup.name &&
                func.name !== nextGroup.name &&
                func.index_sort >= curGroup.index_sort &&
                func.index_sort <= nextGroup.index_sort
              );
          } else {
            if (index_group == 0)
              return (
                (func.name !== curGroup.name && func.index_sort >= curGroup.index_sort) ||
                (func.name !== curGroup.name && func.index_sort < curGroup.index_sort)
              );
            else return func.name !== curGroup.name && func.index_sort >= curGroup.index_sort;
          }
        });
        e.function_list = idenOfGroup;
        return e;
      });

      return d;
    });
    return listBytabs;
  } catch (error) {
    throw error;
  }
};
