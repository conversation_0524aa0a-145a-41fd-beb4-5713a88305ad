import { sscript, sscriptGeneralList } from '@/services/sscript';
import { PageContainer, ProTable } from '@ant-design/pro-components';
import React, { useState, useEffect, useRef } from 'react';
import type { ProColumns } from '@ant-design/pro-components';
import { Link, useSearchParams, history } from '@umijs/max';
import { Card, Col, ConfigProvider, Result, Row, Tabs } from 'antd';
import moment from 'moment';
import { Avatar, Button } from 'antd';
import { UserOutlined } from '@ant-design/icons';
import ActionPopConfirm from '@/components/ActionPopConfirm';
import { allAxcelaUserTypeEnum, AxcelaEnglishLevel, booleanStatusEnum, StudentStatus } from '@/services/valueEnums';
import { toogleUserStatus } from '@/services/user';
import RemoveCompany from '../../Components/RemoveCompany';
import AttachmentsOfDoctype from '@/components/AttachmentsOfDoctype/AttachmentsOfDoctype';
import UpdateCustomerForm from '../Components/UpdateCustomerForm';

interface ActionType {
    reload: (resetPageIndex?: boolean) => void;
    reloadAndRest: () => void;
    reset: () => void;
    clearSelected?: () => void;
    startEditable: (rowKey: String) => boolean;
    cancelEditable: (rowKey: String) => boolean;
}

const CustomerInfor: React.FC = () => {
    const [loading, setLoading] = useState(false);
    const [searchParams, setSearchParams] = useSearchParams();
    const [company, setCompany] = useState({});

    const name = searchParams.get("customer_name");

    const refreshData = async () => {
        try {
            setLoading(true);
            const result = await sscriptGeneralList({
                doc_name: 'iot_customer',
                filters: [["iot_customer", "name", "like", `${name}`]],
                page: 1,
                size: 1,
                fields: ["*"]
            });
            if (result.data.length > 0) {
                const customer_infor = result.data[0];
                setCompany(customer_infor);
            }
        } catch (error) {
            console.log(error)
        } finally {
            setLoading(false);
        }
    };

    useEffect(() => {
        refreshData();
    }, []);

    if (name) {
        return (
            <PageContainer>
                <Card
                    title={name}
                    loading={loading}
                // extra={<RemoveCompany name={name} refreshFnc={() => { history.push("/customer/company") }} />}
                >
                    {Object.keys(company).length ? <>
                        <Tabs defaultActiveKey="1">
                            <Tabs.TabPane tab="Thông tin chung" key="1">
                                <UpdateCustomerForm refreshFnc={refreshData} company={company}></UpdateCustomerForm>
                            </Tabs.TabPane>
                            <Tabs.TabPane tab="Dự án - khu vực" key="2">

                            </Tabs.TabPane>
                            <Tabs.TabPane tab="Thiết bị" key="3">

                            </Tabs.TabPane>
                            <Tabs.TabPane tab="Người dùng" key="4">

                            </Tabs.TabPane>
                            <Tabs.TabPane tab="Tệp đính kèm" key="5">
                                <Row>
                                    <Col md={12}>
                                        <AttachmentsOfDoctype docname={name} doctype="iot_customer" />
                                    </Col>
                                </Row>
                            </Tabs.TabPane>
                        </Tabs>
                    </> : <>
                        Unkown Error, Reload and try Again
                    </>}
                </Card>
            </PageContainer>
        )
    }
    else {
        return (
            <>
                <Result
                    status="404"
                    title="404"
                    subTitle="Sorry, the page you visited does not exist."
                    extra={<Link to={"/customer/company"} type="primary">Go Back</Link>}
                />
            </>
        )
    }
};

export default CustomerInfor;