import CreateTodoTableEditer from '@/pages/FarmingManagement/WorkflowManagement/Components/CreateTodoTableEditer';
import { PlusOutlined } from '@ant-design/icons';
import {
  FormListActionType,
  ProFormCheckbox,
  ProFormList,
  ProFormText,
} from '@ant-design/pro-components';
import { Button, Card, Space } from 'antd';
import { FC, ReactNode, useRef } from 'react';

interface TaskChildProps {
  children?: ReactNode;
}

const TaskChild: FC<TaskChildProps> = () => {

  return (
    <Card
      title="Công việc con"
    >
      <CreateTodoTableEditer />
    </Card>
  );
};

export default TaskChild;
