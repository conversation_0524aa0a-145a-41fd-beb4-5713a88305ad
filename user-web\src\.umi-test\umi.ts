// @ts-nocheck
// This file is generated by Um<PERSON> automatically
// DO NOT CHANGE IT MANUALLY!
import './core/polyfill';
import 'D:/WORK/PYROJECT/VIIS/viis-iot-web-v2/user-web/src/global.less';
import 'D:/WORK/PYROJECT/VIIS/viis-iot-web-v2/user-web/src/global.tsx';
import 'antd/dist/reset.css';
import { renderClient } from 'D:/WORK/PYROJECT/VIIS/viis-iot-web-v2/user-web/node_modules/@umijs/renderer-react';
import { getRoutes } from './core/route';
import { createPluginManager } from './core/plugin';
import { createHistory } from './core/history';
import { ApplyPluginsType } from 'umi';
import 'D:/WORK/PYROJECT/VIIS/viis-iot-web-v2/user-web/src/.umi-test/plugin-tailwindcss/tailwind.css';

const publicPath = "/";
const runtimePublicPath = false;

async function render() {
  const pluginManager = createPluginManager();
  const { routes, routeComponents } = await getRoutes(pluginManager);

  // allow user to extend routes
  await pluginManager.applyPlugins({
    key: 'patchRoutes',
    type: ApplyPluginsType.event,
    args: {
      routes,
      routeComponents,
    },
  });

  const contextOpts = pluginManager.applyPlugins({
    key: 'modifyContextOpts',
    type: ApplyPluginsType.modify,
    initialValue: {},
  });

  const basename = contextOpts.basename || '/';
  const historyType = contextOpts.historyType || 'browser';

  const history = createHistory({
    type: historyType,
    basename,
    ...contextOpts.historyOpts,
  });

  return (pluginManager.applyPlugins({
    key: 'render',
    type: ApplyPluginsType.compose,
    initialValue() {
      const context = {
        routes,
        routeComponents,
        pluginManager,
        rootElement: contextOpts.rootElement || document.getElementById('root'),
        publicPath,
        runtimePublicPath,
        history,
        historyType,
        basename,
        callback: contextOpts.callback,
      };
      const modifiedContext = pluginManager.applyPlugins({
        key: 'modifyClientRenderOpts',
        type: ApplyPluginsType.modify,
        initialValue: context,
      });
      return renderClient(modifiedContext);
    },
  }))();
}

import './plugin-moment2dayjs/runtime.tsx'
render();

window.g_umi = {
  version: '4.1.2',
};
