import styles from './index.less';

export const InfoCard = ({ big, list, bg, style, customStyleLabel, customStyleNumber }: any) => (
  <div className={styles.card} style={{ backgroundImage: `url('${bg}')`, ...(style || {}) }}>
    {big.map((b: any) => (
      <span key={b.label}>
        <p className={customStyleLabel ? styles[customStyleLabel] : styles.label}>{b.label}</p>
        <p className={customStyleNumber ? styles[customStyleNumber] : styles.bignumber}>{b.value}</p>
      </span>
    ))}
    {list.map((l: any) => (
      <p key={Math.random()} className={customStyleLabel ? styles[customStyleLabel] : styles.label}>
        {l.label}
        {l.label ? ':' : '.'} <b>{l.value}</b>
      </p>
    ))}
  </div>
);
