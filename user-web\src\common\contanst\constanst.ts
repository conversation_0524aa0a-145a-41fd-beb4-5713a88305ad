export const DEFAULT_PAGE_SIZE_ALL = 10000;
export const DEFAULT_DATE_FORMAT = 'DD-MM-YYYY HH:mm:ss';
export const DEFAULT_DATE_AND_HH_MM_FORMAT = 'DD-MM-YYYY HH:mm';
export const DEFAULT_DATE_FORMAT_WITHOUT_TIME = 'DD-MM-YYYY';
export const STANDARD_DATE_FORMAT_WITHOUT_TIME = 'YYYY-MM-DD';

export const DOCTYPE_ERP = {
  iotTicket: 'iot_ticket',
  iotTicketMessage: 'iot_ticket_message',
  iotPest: 'iot_pest',
  iotFarmingPlan: 'iot_farming_plan',
  iotFarmingPlanTask: 'iot_farming_plan_task',
  iotFarmingPlanState: 'iot_farming_plan_state',
  iotCrop: 'iot_crop',
  iotCropNote: 'iot_Crop_note',
  iotCropDiary: 'iot_crop_diary',
  iotPlant: 'iot_plant',
  iotProject: 'iot_project',
  iotStorage: 'iot_storage',
  iotRole: 'iot_role',
  iotCustomerUser: 'iot_customer_user',
  iotEmployeeInCrop: 'iot_employee_in_crop',
  iotZone: 'iot_zone',
  iotDevice: 'iot_device',
  iotCropDocument: 'iot_crop_document',
  iotEmployeeTimeManagement: 'iot_employee_time_management',
  iotEmployeeTimesheet: 'iot_employee_timesheet',
  iotTimesheetTask: 'iot_timesheet_task',
  iotTimesheetApproval: 'iot_timesheet_approval',
  Item: 'Item',
  Customer: 'Customer',
  CustomerGroup: 'Customer Group',
  SalesOrder: 'Sales Order',
  iotWarehouse: 'Warehouse',
  iotDiaryV2Business: 'iot_diary_v2_business',
  iotDiaryV2Product: 'iot_diary_v2_agri_product',
  iot_diary_v2_note: 'iot_diary_v2_note',
  iot_diary_v2_task: 'iot_diary_v2_task',
  iot_diary_v2_state: 'iot_diary_v2_state',
  iot_diary_v2_traceability_batch: 'iot_diary_v2_traceability_batch',
  iot_diary_v2_document: 'iot_diary_v2_document',
  iot_diary_v2_business_member: 'iot_diary_v2_business_member',
  iot_diary_v2_agri_process: 'iot_diary_v2_agri_process',
  iot_workshift: 'iot_workshift',
};
export const COLOR_HEX = {
  GREEN_TOOLTIP: '#41B06E',
};

export const TAG_COLOR = {
  PRIORITY: 'green',
  IMPORTANT: 'red',
  COMMON: 'blue',
};

export const STATUS_TAG_COLOR = {
  'To Bill': '#41B06E',
  'To Deliver and Bill': '#608BC1',
  'Delivered and Billed': 'green',
  'Delivered and Not Billed': 'orange',
  Cancelled: 'gray',
  Draft: 'brown',
};

export const DOC_STATUS_COLOR = ['brown', '#41B06E', 'gray'];
