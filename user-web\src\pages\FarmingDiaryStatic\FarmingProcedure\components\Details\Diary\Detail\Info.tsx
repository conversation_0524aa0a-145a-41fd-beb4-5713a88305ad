import FormUploadsPreviewable from '@/components/FormUploadsPreviewable';
import { Process } from '@/services/diary-2/process';
import { ProFormDigit, ProFormText, ProFormTextArea } from '@ant-design/pro-components';
import { useIntl } from '@umijs/max';
import { Card, Col, Row } from 'antd';
import { FC, ReactNode } from 'react';

interface InfoProps {
  children?: ReactNode;
  dataState?: Process['states'][0];
}

const Info: FC<InfoProps> = ({ children, dataState }) => {
  const { formatMessage } = useIntl();
  return (
    <Card title={formatMessage({ id: 'common.stage_detail' })}>
      <FormUploadsPreviewable
        fileLimit={10}
        label={formatMessage({ id: 'common.image' })}
        formItemName={'imgs'}
        initialImages={dataState?.image}
      />
      <Row gutter={24}>
        {' '}
        <Col span={8}>
          <ProFormText label={formatMessage({ id: 'common.name' })} name="label" />
        </Col>
        <Col span={8}>
          <ProFormDigit label={formatMessage({ id: 'common.task_count' })} name="task_count" />
        </Col>
        <Col span={8}>
          <ProFormDigit
            fieldProps={{
              // suffix: formatMessage({ id: 'common.start_date' }),
              suffix: formatMessage({ id: 'common.days' }),
            }}
            label={formatMessage({ id: 'common.stage_expire_time_in_days' })}
            name="expire_time_in_days"
          />
        </Col>
        <Col span={24}>
          <ProFormTextArea label="Ghi chú" name="description" />
        </Col>
      </Row>
    </Card>
  );
};

export default Info;
