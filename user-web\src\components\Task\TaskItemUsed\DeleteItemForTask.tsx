import ActionModalConfirm from '@/components/ActionModalConfirm';
import { generalDelete } from '@/services/sscript';
import { useAccess } from '@umijs/max';
import { message } from 'antd';

const DeleteItemForTask = (params: { refreshFnc: any; value: string }) => {
  const removeData = async () => {
    try {
      await generalDelete('iot_warehouse_item_task_used', params.value);
      try {
        params?.refreshFnc?.();
      } catch {}
      return true;
    } catch (error: any) {
      message.error(error.toString());
      return false;
    }
  };
  const access = useAccess();
  if (!access.canDeleteAllInPageAccess()) {
    return null;
  }
  return (
    <>
      <ActionModalConfirm
        isDelete
        modalProps={{
          onOk: removeData,
        }}
      />
      {/* <ActionPopConfirm
        actionCall={removeData}
        refreshData={params.refreshFnc}
        text={<DeleteFilled />}
        //buttonType={'dashed'}
        danger={true}
        size="small"
      ></ActionPopConfirm> */}
    </>
  );
};

export default DeleteItemForTask;
