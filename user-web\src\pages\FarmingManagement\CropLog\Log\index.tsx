import { myLazy } from '@/utils/lazy';
import { TableSkeleton } from '@ant-design/pro-components';
import { FC, ReactNode, Suspense } from 'react';

const CropNoteListTable = myLazy(() => import('@/pages/FarmingManagement/CropLog/Log/components/CropNoteListTable'));

interface LogProps {
  children?: ReactNode;
}

const Log: FC<LogProps> = () => {
  return (
    <Suspense fallback={<TableSkeleton active />}>
      <CropNoteListTable
        genLinkDetail={(itemId) => `/farming-management/crop-log/log/detail/${itemId}`}
        keepSearchParams
      />
    </Suspense>
  );
};

export default Log;
