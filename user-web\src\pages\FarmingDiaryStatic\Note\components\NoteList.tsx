import { DOCTYPE_ERP } from '@/common/contanst/constanst';
import ActionHover from '@/components/ActionHover';
import { getNoteList, Note } from '@/services/diary-2/note';
import { getParamsReqTable } from '@/services/utils';
import { ActionType, ProColumns, ProTable } from '@ant-design/pro-components';
import { useIntl } from '@umijs/max';
import { Button, Row } from 'antd';
import { FC, ReactNode, useEffect, useRef, useState } from 'react';
import DeleteNote from './DeleteNote';

interface StageCropListProps {
  children?: ReactNode;
  onSelect?: (noteId: string) => void;
  reloadKey?: string | null;
}

const NoteList: FC<StageCropListProps> = ({ children, onSelect, reloadKey }) => {
  const { formatMessage } = useIntl();
  const actionRef = useRef<ActionType>();
  const [data, setData] = useState<any[]>([]);

  const [selectedRowKey, setSelectedRowKey] = useState<string | null>(null);

  const handleReload = () => {
    actionRef.current?.reload?.();
  };
  useEffect(() => {
    if (reloadKey) {
      handleReload();
    }
  }, [reloadKey]);
  useEffect(() => {
    if (data.length > 0 && !selectedRowKey) {
      const firstRowKey = data[0].name;
      setSelectedRowKey(firstRowKey);
      onSelect?.(firstRowKey);
    }
  }, [data]);
  return (
    <ProTable
      actionRef={actionRef}
      search={false}
      scroll={{ x: 'max-content' }}
      pagination={{
        pageSize: 10,
        showSizeChanger: true,
        defaultPageSize: 10,
      }}
      toolBarRender={() => []}
      rowKey={'name'}
      form={{
        labelWidth: 'auto',
      }}
      request={async (params, sort, filter) => {
        const paramsReq = getParamsReqTable({
          doc_name: DOCTYPE_ERP.iot_diary_v2_note,
          tableReqParams: {
            params,
            sort,
            filter,
          },
          defaultSort: 'name asc',
        });
        const res = await getNoteList(paramsReq);
        setData(res.data);
        return {
          data: res.data,
          total: res.pagination.totalElements,
        };
      }}
      // rowClassName={'group/action'}
      rowClassName={(record) =>
        record.name === selectedRowKey ? 'bg-emerald-100 group/action' : 'group/action'
      }
      options={false}
      columns={
        [
          {
            title: formatMessage({
              id: 'common.note',
            }),
            dataIndex: 'label',
            render(dom, entity, index, action, schema) {
              return (
                <Button
                  style={{ padding: 0 }}
                  type="link"
                  onClick={() => {
                    onSelect?.(entity.name);
                    setSelectedRowKey(entity.name);
                  }}
                >
                  {dom}
                </Button>
              );
            },
            width: 100,
          },
          {
            title: formatMessage({
              id: 'common.product',
            }),
            dataIndex: 'product_label',
            width: 100,
          },
          {
            title: formatMessage({
              id: 'common.description',
            }),
            dataIndex: 'description',
            render: (dom: any, entity) => {
              const description = dom ? dom : '';
              return (
                <ActionHover
                  actions={() => (
                    <Row justify="center" align="middle">
                      <DeleteNote id={entity.name} onSuccess={handleReload} />
                    </Row>
                  )}
                >
                  {description.substring(0, 50)}
                </ActionHover>
              );
            },
            width: 100,
          },
        ] satisfies Array<ProColumns<Note>>
      }
    />
  );
};

export default NoteList;
