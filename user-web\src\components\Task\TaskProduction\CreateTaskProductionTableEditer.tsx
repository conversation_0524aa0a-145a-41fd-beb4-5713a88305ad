import ProductCategoryCard from '@/components/ProductCategory';
import { DeleteOutlined } from '@ant-design/icons';
import type { ProColumns } from '@ant-design/pro-components';
import { ProTable } from '@ant-design/pro-components';
import { FormattedMessage, useModel } from '@umijs/max';
import { Button } from 'antd';
import React from 'react';
import { v4 } from 'uuid';
import CreateTaskProduction from './CreateTaskProduction';
import UpdateTaskProduction from './UpdateTaskProduction';

type DataSourceType = {
  id: React.Key;
  title?: string;
  decs?: string;
  state?: string;
  created_at?: string;
  children?: DataSourceType[];
};

export default ({ dataSource, setDataSource }: { dataSource: any; setDataSource: any }) => {
  const { myProducts } = useModel('MyProducts');
  console.log('myProducts', myProducts);
  const columns: ProColumns<DataSourceType>[] = [
    {
      title: 'Sản phẩm thu hoạch',
      dataIndex: 'product_id',
      render: (dom: any, entity: any) => {
        return <>{myProducts?.find((el) => el.name === dom)?.label}</>;
      },
    },
    {
      title: 'Sản lượng/Số lượng dự kiến',
      dataIndex: 'exp_quantity',
    },
    {
      title: 'Sản lượng/Số lượng thực tế',
      dataIndex: 'quantity',
    },
    {
      title: <FormattedMessage id="category.material-management.unit" defaultMessage="unknown" />,
      dataIndex: 'product_id',
      render: (dom: any, entity: any) => {
        return <>{myProducts?.find((el) => el.name === dom)?.unit?.short_label}</>;
      },
    },
    {
      title: 'Mô tả',
      dataIndex: 'description',
      formItemProps: {
        rules: [
          {
            required: true,
            whitespace: true,
            message: 'Mô tả không được để trống',
          },
        ],
      },
    },
    {
      title: 'Hành động',
      dataIndex: 'name',
      render: (dom: any, entity: any) => {
        return (
          <>
            <UpdateTaskProduction data={entity} onFinish={handlerEdit} /> {'  '}
            <Button danger size="small" onClick={() => handlerRemove(dom)}>
              <DeleteOutlined />
            </Button>
          </>
        );
      },
    },
  ];

  const handlerAdd = async (values: any) => {
    try {
      values.name = v4();
      const _newData = dataSource;
      _newData.push(values);
      setDataSource([..._newData]);
    } catch (error) {
      console.log(error);
    }
  };

  const handlerEdit = async (values: any) => {
    try {
      const _newData = dataSource.map((d: any) => {
        return d.name === values.name ? values : d;
      });
      console.log('_newData', _newData);
      setDataSource([..._newData]);
    } catch (error) {
      console.log(error);
    }
  };
  const handlerRemove = async (name: any) => {
    try {
      const _newData = dataSource.filter((d: any) => {
        return d.name !== name;
      });
      setDataSource([..._newData]);
    } catch (error) {
      console.log(error);
    }
  };
  return (
    <>
      <ProTable
        headerTitle="Sản lượng"
        columns={columns}
        rowKey="name"
        dataSource={[...dataSource]}
        toolBarRender={() => {
          return [
            <ProductCategoryCard key="create-category" />,
            <CreateTaskProduction key="create-task-prod" onFinish={handlerAdd} />,
          ];
        }}
        search={false}
      />
    </>
  );
};
