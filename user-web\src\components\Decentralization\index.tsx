import {
changeIsAdmin,
createUser<PERSON><PERSON>,
deleteUser<PERSON><PERSON>,
getAllRole,
getUserRole
} from '@/services/role-manager';
import { IIotCustomerUser } from '@/types/auth.type';
import { ProFormGroup,ProFormSwitch } from '@ant-design/pro-components';
import { useRequest } from '@umijs/max';
import { App,Button,Checkbox,Col,Row,Spin } from 'antd';
import { SwitchChangeEventHandler } from 'antd/es/switch';
import { CheckboxProps } from 'antd/lib/checkbox';
import { useState } from 'react';

type IRoleState = {
  user_role_id?: string;
  role_id: string;
  description: string | null;
  checked: boolean;
};
export default function Decentralization(props: {
  user: IIotCustomerUser;
  onUpdateSuccess?: () => void;
}) {
  const { message } = App.useApp();
  const [isAdmin, setIsAdmin] = useState(!!props?.user.is_admin);
  const [roleState, setRoleState] = useState<IRoleState[]>([]);
  const { loading: loadingData, refresh: refreshLoadData } = useRequest(
    async (): Promise<{ data: IRoleState[] }> => {
      const listRole = await getAllRole();
      const userRole = await getUserRole({
        user_id: props?.user.name as string,
      });
      return {
        data: listRole.data.map<IRoleState>((item) => {
          const roleMapping = userRole.data.find((r) => r.role_id === item.role_id);
          return {
            user_role_id: roleMapping?.name,
            role_id: item.role_id,
            description: item.description,
            checked: !!roleMapping,
          };
        }),
      };
    },
    {
      onError: (err) => {
        message.error(err?.message?.toString());
      },
      onSuccess: (data) => {
        setRoleState(data);
      },
      refreshDeps: [props?.user.name],
    },
  );
  const { loading: loadingCreateRole, run: createUserRoleReq } = useRequest(createUserRole, {
    manual: true,
    onSuccess(data, params) {
      // update state
      refreshLoadData();
      // setRoleState(
      //   roleState.map((role) => ({
      //     ...role,
      //     user_role_id:
      //       role.user_role_id === params[0].user_role_id ? undefined : role.user_role_id,
      //   })),
      // );
      const role = roleState.find((r) => r.role_id === params[0].role_id);

      message.success({
        content: `Người dùng đã có role  (${role?.role_id || ''} - ${role?.description || ''}) `,
      });
    },
    onError(err) {
      message.error({
        content: `Không thể cập nhật role: ${err.message}`,
      });
    },
  });
  const { loading: loadingDeleteRole, run: deleteUserRoleReq } = useRequest(deleteUserRole, {
    manual: true,
    onSuccess(data, params) {
      // update state
      setRoleState(
        roleState.map((role) => ({
          ...role,
          user_role_id:
            role.user_role_id === params[0].user_role_id ? undefined : role.user_role_id,
        })),
      );
      message.success({
        content: `Xóa role thành công`,
      });
    },
    onError(err) {
      message.error({
        content: `Không thể xóa role: ${err.message}`,
      });
    },
  });
  const onRoleChange: CheckboxProps['onChange'] = (e) => {
    const checked = e.target.checked;
    const value = e.target.value;
    if (checked) {
      createUserRoleReq({
        role_id: value,
        user_id: props?.user.name as string,
      });
    } else {
      const roleMapping = roleState.find((r) => r.role_id === value);
      if (roleMapping?.user_role_id) {
        deleteUserRoleReq({
          user_role_id: roleMapping.user_role_id,
        });
      } else {
        message.error({
          content: 'Đã có lỗi xảy ra vui lòng thử lại',
        });
      }
    }
  };
  const { run: updateIsAdmin,loading:loadingUpdateIsAdmin } = useRequest(changeIsAdmin, {
    manual: true,
    onSuccess(data, params) {
      setIsAdmin(params[0].isAdmin);
      message.success({
        content: 'Thay đổi thành công',
      });
      props.onUpdateSuccess?.();
    },
    onError(err) {
      message.error({
        content: `Thay đổi không thành công: ${err.message}`,
      });
    },
  });
  const isAdminChange: SwitchChangeEventHandler = (val) => {
    updateIsAdmin({
      userId: props.user.name,
      isAdmin: val,
    });
  };
  return (
    <ProFormGroup title={'Phân quyền'}>
      <Spin
        spinning={loadingData || loadingCreateRole || loadingDeleteRole || loadingUpdateIsAdmin}
      >
        <Row gutter={[25, 25]}>
          <Col span={24}>
            <ProFormSwitch
              fieldProps={{
                checked: isAdmin,
                onChange: isAdminChange,
              }}
              label="Is admin"
            />
          </Col>
          {!isAdmin && (
            <Col span={24}>
              <Row style={{ width: '100%' }} gutter={[15, 15]}>
                {roleState?.map((item) => (
                  <Col span={24} sm={12} md={12} lg={12} xl={12} key={item.role_id}>
                    <Checkbox
                      checked={!!item.user_role_id}
                      onChange={onRoleChange}
                      value={item.role_id}
                    >{`${item.role_id} (${item.description})`}</Checkbox>
                  </Col>
                ))}
              </Row>
            </Col>
          )}
          <Col span={24}>
            <Button type="primary" htmlType="submit">
              Save
            </Button>
          </Col>
        </Row>
      </Spin>
    </ProFormGroup>
  );
}
