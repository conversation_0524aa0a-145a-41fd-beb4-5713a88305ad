import { DEFAULT_DATE_FORMAT_WITHOUT_TIME } from '@/common/contanst/constanst';
import { ProFormDatePicker, ProFormText } from '@ant-design/pro-components';
import { useIntl } from '@umijs/max';
import { Card, Col, Row } from 'antd';
import moment from 'moment';
import { FC, ReactNode } from 'react';

interface DetailedInfoProps {
  children?: ReactNode;
  initialImage?: string;
}
const w = 'md';
const DetailedInfo: FC<DetailedInfoProps> = ({ children, initialImage }) => {
  const { formatMessage } = useIntl();

  return (
    <Card
      title={formatMessage({
        id: 'task.detailed_info',
      })}
      bordered={false}
      style={{ boxShadow: 'none' }}
    >
      <Row gutter={24}>
        <Col span={24}>
          <ProFormText
            name={'label'}
            label={formatMessage({
              id: 'common.name',
            })}
            rules={[
              {
                required: true,
              },
            ]}
          />
        </Col>
        <Col span={12}>
          {' '}
          <ProFormDatePicker
            style={{ width: '100%' }}
            width={'xl'}
            name={'issue_date'}
            label={formatMessage({
              id: 'common.certification_date',
            })}
            rules={[
              {
                required: true,
              },
            ]}
            fieldProps={{
              format: (value: any) => moment(value).format(DEFAULT_DATE_FORMAT_WITHOUT_TIME),
            }}
          />
        </Col>
        <Col span={12}>
          {' '}
          <ProFormDatePicker
            style={{ width: '100%' }}
            width={'xl'}
            name={'expiry_date'}
            label={formatMessage({
              id: 'common.expiration_date',
            })}
            rules={[
              {
                required: true,
              },
            ]}
            fieldProps={{
              format: (value: any) => moment(value).format(DEFAULT_DATE_FORMAT_WITHOUT_TIME),
            }}
          />
        </Col>
      </Row>
    </Card>
  );
};

export default DetailedInfo;
