import { DEFAULT_PAGE_SIZE_ALL } from '@/common/contanst/constanst';
import FormUploadsPreviewable from '@/components/FormUploadsPreviewable';
import { getProductList } from '@/services/diary-2/product';
import { getStageList } from '@/services/diary-2/stage';
import { ProFormSelect, ProFormText, ProFormTextArea } from '@ant-design/pro-components';
import { useIntl } from '@umijs/max';
import { Card, Col, Row } from 'antd';
import { FC, ReactNode } from 'react';

interface DetailedInfoProps {
  children?: ReactNode;
}
const w = 'md';
const DetailedInfo: FC<DetailedInfoProps> = ({ children }) => {
  const { formatMessage } = useIntl();

  return (
    <Card
      title={formatMessage({
        id: 'task.detailed_info',
      })}
      bordered={false}
      style={{ boxShadow: 'none' }}
    >
      <FormUploadsPreviewable
        label={formatMessage({
          id: 'common.image',
        })}
        fileLimit={10}
        formItemName={'image'}
      />
      <Row gutter={24}>
        <Col span={24}>
          <ProFormText
            name={'label'}
            label={formatMessage({
              id: 'common.note',
            })}
            rules={[
              {
                required: true,
              },
            ]}
          />
        </Col>
        <Col span={12}>
          <ProFormSelect
            name={'state_id'}
            label={formatMessage({
              id: 'common.stage',
            })}
            request={async () => {
              const res = await getStageList({
                page: 1,
                size: DEFAULT_PAGE_SIZE_ALL,
              });
              return res.data.map((item) => ({
                label: item.label,
                value: item.name,
              }));
            }}
            showSearch
          />
        </Col>
        <Col span={12}>
          <ProFormSelect
            name={'product_id'}
            label={formatMessage({
              id: 'common.product',
            })}
            request={async () => {
              const res = await getProductList({
                page: 1,
                size: DEFAULT_PAGE_SIZE_ALL,
                order_by: 'name asc',
              });
              return res.data.map((item) => ({
                label: item.label,
                value: item.name,
              }));
            }}
            showSearch
          />
        </Col>
        <Col span={24}>
          <ProFormTextArea
            name={'description'}
            label={formatMessage({
              id: 'common.note',
            })}
          />
        </Col>
      </Row>
    </Card>
  );
};

export default DetailedInfo;
