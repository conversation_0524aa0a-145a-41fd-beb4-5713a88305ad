import { COLOR_HEX, DEFAULT_DATE_AND_HH_MM_FORMAT } from '@/common/contanst/constanst';
import { getCropByTask } from '@/services/crop';
import { getCustomerUserList } from '@/services/customerUser';
import { updateTaskProductionListSQL } from '@/services/products';
import { getItemGroupList, getItemList } from '@/services/stock/item';
import {
  getMaterialIssueTotal,
  getMaterialTotalBalanceForProduction,
  getMaterialTransferFromWIPTotal,
  saveStockEntry,
  submitStockEntry,
} from '@/services/stock/stockEntry';
import { getWarehouseList } from '@/services/stock/warehouse';
import { useTaskProductionStore } from '@/stores/TaskProductionUpdateStore';
import { IIotProductionQuantity } from '@/types/IIotProductionQuantity';
import { IStockItem, IStockItemGroup } from '@/types/warehouse.type';
import { ExclamationCircleOutlined, PlusOutlined } from '@ant-design/icons';
import {
  ModalForm,
  ProFormDateTimePicker,
  ProFormSelect,
  ProFormTextArea,
} from '@ant-design/pro-components';
import { FormattedMessage, useIntl, useModel } from '@umijs/max';
import { App, Button, Divider, Space, Tooltip } from 'antd';
import { useForm } from 'antd/es/form/Form';
import { ButtonType } from 'antd/lib/button';
import moment from 'moment';
import { useEffect, useState } from 'react';
import {
  createMaterialTransferFromWIPVoucher,
  createSubmittingMaterialTransferFromWIPVoucher,
  validateItems,
} from '../Helpers/StockEntryHelpers';
import { IMaterialTransferVoucherItem } from '../Interfaces/StockEntryInterfaces';
import MaterialTransferVoucherTable from './MaterialTransferVoucherTable';
interface ITreeNode {
  title: string;
  value: string;
  key: string;
  children?: ITreeNode[];
}
interface IFormData {
  doctype: 'Purchase Receipt';
  posting_date: string;
  company: 'VIIS';
  customer: string;
  warehouse: string;
  description: string;
  employee: string;
  iot_crop: string;
}
interface Props {
  onSuccess?: any;
  buttonType: ButtonType;
}

const fetchWarehouseList = async () => {
  const warehouse = await getWarehouseList();
  return warehouse.data.map((storage) => ({
    label: storage.label,
    value: storage.name,
  }));
};

const fetchCustomerUserList = async () => {
  const listUser = await getCustomerUserList();
  return listUser.data.map((item: any) => ({
    label: `${item.first_name} ${item.last_name}`,
    value: item.name,
  }));
};

const MaterialTransfer = ({ onSuccess, buttonType }: Props) => {
  //use zustand useTaskItemUsedStore
  const { dataSource, setDataSource } = useTaskProductionStore();
  const taskId = dataSource[0]?.task_id;
  const [submitting, setSubmitting] = useState<boolean>(false);
  const [treeData, setTreeData] = useState<ITreeNode[]>();
  const [selectedItems, setSelectedItems] = useState<IMaterialTransferVoucherItem[]>([]);
  const [items, setItems] = useState<IStockItem[]>([]);
  const [selectedWarehouse, setSelectedWarehouse] = useState<string>('');
  const [form] = useForm();
  //current user
  const { initialState } = useModel(`@@initialState`);
  const curentUser = initialState?.currentUser;
  const { message } = App.useApp();

  //get material-transfer-fromWIP-total
  const materialTransferFromWIPTotal = async () => {
    const realQuantityDataTransferFromWIP = await getMaterialTransferFromWIPTotal({
      task_id: taskId,
    });
    //map dataSource with realQuantityDataTransferFromWIP
    const data = dataSource.map((itemInTask) => {
      const realQuantity = realQuantityDataTransferFromWIP.data.find(
        (d) => d.item_id === itemInTask.item.item_code,
      );
      return {
        ...itemInTask,
        finished_quantity: realQuantity?.total_qty || 0,
      };
    });

    const realQuantityData = await getMaterialTotalBalanceForProduction({ task_id: taskId });
    //map dataSource with realQuantityData
    const data1 = data.map((itemInTask) => {
      const realQuantity = realQuantityData.data.find(
        (d) => d.item_id === itemInTask.item.item_code,
      );
      return {
        ...itemInTask,
        quantity: typeof realQuantity?.total_qty === 'number' ? realQuantity?.total_qty : 0,
      };
    });
    const realIssueQuantityData = await getMaterialIssueTotal({ task_id: taskId });
    //map dataSource with realIssueQuantityData
    const data2 = data1.map((itemInTask) => {
      const realQuantity = realIssueQuantityData.data.find(
        (d) => d.item_id === itemInTask.item.item_code,
      );
      return {
        ...itemInTask,
        issued_quantity: realQuantity?.total_qty || 0,
      };
    });
    await handleStoreItem(data2);
    setDataSource(data2);
  };

  const handleStoreItem = async (dataArr: any) => {
    try {
      const updateTaskProductionList: IIotProductionQuantity[] = [];
      for (const data of dataArr) {
        updateTaskProductionList.push({
          name: data.name,
          quantity: data.quantity,
          description: data.description,
          task_id: data.task_id,
          product_id: data.product_id,
          // exp_quantity: data.exp_quantity,
          lost_quantity: data.lost_quantity,
          finished_quantity: data.finished_quantity,
          issued_quantity: data.issued_quantity,
          // draft_quantity: data.draft_quantity,
        });
      }
      const request = await updateTaskProductionListSQL(updateTaskProductionList);
      message.success('Lưu vật tư liên quan thành công ');
    } catch (error: any) {
      message.error('Đã có lỗi xảy ra');
    }
  };
  //get list items in group
  const getItemTreeData = async () => {
    // Helper function
    function groupByItemGroupWithItemGroupData(
      stockItems: IStockItem[],
      stockItemGroups: IStockItemGroup[],
    ): Record<string, { items: IStockItem[]; itemGroup: IStockItemGroup }> {
      const groupedItems: Record<string, { items: IStockItem[]; itemGroup: IStockItemGroup }> = {};

      stockItems.forEach((item) => {
        const itemGroup = item.item_group;
        if (!groupedItems[itemGroup]) {
          groupedItems[itemGroup] = { items: [], itemGroup: {} as IStockItemGroup };
        }
        groupedItems[itemGroup].items.push(item);
      });

      // Now add item group data
      for (const itemGroup of stockItemGroups) {
        const itemGroupName = itemGroup.item_group_name;
        if (groupedItems[itemGroupName]) {
          groupedItems[itemGroupName].itemGroup = itemGroup;
        }
      }

      return groupedItems;
    }
    const data = await getItemList({});
    const dataGroup = await getItemGroupList({});
    const dataMap = groupByItemGroupWithItemGroupData(data.data, dataGroup.data);
    if (dataMap) {
      const generatedData = Object.entries(dataMap).map(([itemGroup, groupData]) => ({
        title: groupData.itemGroup?.label || '',
        value: itemGroup,
        key: itemGroup,
        children: groupData.items.map((item) => ({
          title: item.label || '',
          value: item.name || '',
          key: item.name || '',
        })),
      }));
      setTreeData(generatedData);
      setItems(data.data);
    }
  };
  const handleSelectWarehouse = (entity: any) => {
    setSelectedWarehouse(entity);

    setSelectedItems([]);
  };

  //only use loadData when  is true
  const loadData = async () => {
    const item_ids: any[] | undefined = dataSource;
    if (!item_ids) {
      message.error(`Vui lòng chọn hàng hóa`);
      return;
    }

    const formatted_items: IMaterialTransferVoucherItem[] = item_ids.map((obj, index) => ({
      conversion_factor: 1,
      item_code: obj.item ? obj.item.name : '',
      item_name: obj.item ? obj.item.item_name : '',
      actual_qty: obj.total_qty_in_crop || 0,
      finished_qty: obj.finished_quantity || 0,
      qty: obj.draft_quantity || 0,
      rate: obj.item ? obj.item.standard_rate : 0,
      total_price: 0,
      warehouse: selectedWarehouse,
      item_label: obj.item ? obj.item.label : '',
      key: index.toString(),
      uom_label: obj.item ? obj.item.uom_label : '',
      convert_uom_id: obj.item ? obj.item.stock_uom : '',
      convert_uom_label: obj.item ? obj.item.uom_label : '',
    }));
    setSelectedItems(formatted_items);
    form.setFieldValue('items', []);
    return;
  };

  //run when submit Form
  const handleFinish = async (values: IFormData) => {
    setSubmitting(true);
    try {
      if (!validateItems(selectedItems)) {
        return;
      }
      //verify in each item of selectedItems, if qty > actual_qty, return false and show error
      const verify = selectedItems.every((item) => {
        return item.qty <= item.actual_qty;
      });
      if (!verify) {
        message.error(`Lỗi SL < SL thực tế trong vụ mùa`);
        return;
      }
      let date = moment(values.posting_date, DEFAULT_DATE_AND_HH_MM_FORMAT);
      const posting_date = date.format('YYYY-MM-DD');
      // Lấy giờ và phút từ biến `date`
      const hoursAndMinutes = date.format('HH:mm');

      // Lấy giây tại thời điểm hiện tại
      const currentSeconds = moment().format('ss');

      // Kết hợp giờ, phút và giây để tạo `posting_time`
      const posting_time = `${hoursAndMinutes}:${currentSeconds}`;

      /**
       * Transfer Item from Warehouse to Work In Progress - V Warehouse
       */
      const transferVoucher = createMaterialTransferFromWIPVoucher(
        values,
        selectedItems,
        posting_date,
        posting_time,
        taskId,
      );
      const saveTransferRes = await saveStockEntry(transferVoucher);

      const submittingMaterialTransferVoucher = createSubmittingMaterialTransferFromWIPVoucher(
        saveTransferRes,
        taskId,
      );

      await submitStockEntry(submittingMaterialTransferVoucher);
      await materialTransferFromWIPTotal();
      message.success(`Success`);
      onSuccess?.();
      return true;
    } catch (error: any) {
      console.log({ error });
      if (error.code === 'ERR_BAD_REQUEST') {
        message.error(
          'Đã có lỗi xảy ra, có thể do số lượng item quá nhiều, hãy thử lại với số lượng ít hơn',
        );
      } else {
        message.error(`Error with Stock Transfer: ${error.message}`);
      }
      return;
    } finally {
      setSubmitting(false);
    }
  };

  useEffect(() => {
    const fetchData = async () => {
      getItemTreeData();
    };
    fetchData();

    loadData();
  }, [selectedWarehouse]);
  const intl = useIntl();
  return (
    <ModalForm<IFormData>
      title={'Thêm SL thu hoạch'}
      modalProps={{
        destroyOnClose: true,
        afterClose: () => form.resetFields(), // Reset form state when Modal closes
      }}
      trigger={
        <Space>
          <Button
            // ghost
            icon={<PlusOutlined />}
            type={buttonType}
            // style={{ backgroundColor: '#59B4C3' }}
          >
            {`Thêm SL thu hoạch`}
          </Button>
          <Tooltip
            color={COLOR_HEX.GREEN_TOOLTIP}
            key={COLOR_HEX.GREEN_TOOLTIP}
            title={intl.formatMessage({ id: 'tooltips.add_haverst_quantity_button' })}
          >
            <ExclamationCircleOutlined />
          </Tooltip>
        </Space>
      }
      width={1600}
      form={form}
      layout="vertical"
      rowProps={{
        gutter: [16, 0],
      }}
      submitter={{
        render: (props, defaultDoms) => {
          return [
            <Button
              key="submit-material-transfer"
              onClick={() => {
                props.submit();
              }}
              type="primary"
              style={{ width: '100%' }}
              loading={submitting}
            >
              {<FormattedMessage id="common.submit" />}
            </Button>,
          ];
        },
      }}
      autoFocusFirstInput
      grid
      // initialValues={{
      //   posting_date: moment().format(DEFAULT_DATE_FORMAT),
      // }}
      onFinish={handleFinish}
    >
      {/* <ProFormText
        name="document_code"
        label={<FormattedMessage id={'warehouse-management.import-voucher.document_code'} />}
        colProps={{ span: 8 }}
        width={'md'}
      /> */}
      <ProFormDateTimePicker
        name="posting_date"
        required
        rules={[{ required: true, message: 'Vui lòng chọn ngày hoạch toán' }]}
        label={<FormattedMessage id="warehouse-management.export-voucher.transaction_date" />}
        colProps={{ span: 8 }}
        width="md"
        fieldProps={{
          format: DEFAULT_DATE_AND_HH_MM_FORMAT,
        }}
      />
      {/* <ProFormText
        name="document_code"
        // rules={[{ required: true, message: 'Vui lòng chọn kho' }]}
        label={<FormattedMessage id="warehouse-management.export-voucher.document_code" />}
        colProps={{ span: 8 }}
        width={'md'}
      /> */}
      <ProFormSelect
        showSearch
        name="warehouse"
        rules={[{ required: true, message: 'Vui lòng chọn kho' }]}
        required
        label={<FormattedMessage id="warehouse-management.to-warehouse-name" />}
        onChange={(value) => handleSelectWarehouse(value)}
        colProps={{ span: 8 }}
        request={async () => {
          return await fetchWarehouseList();
        }}
        width={'md'}
      />
      <ProFormSelect
        showSearch
        name="iot_crop"
        required
        disabled
        label={<FormattedMessage id="common.crop" />}
        // onChange={(value) => handleSelectWarehouse(value)}
        colProps={{ span: 8 }}
        request={async () => {
          const res = await getCropByTask({ task_id: taskId });
          form.setFieldsValue({ iot_crop: res.data.name });
          form.setFieldsValue({
            description: `Chuyển sản lượng từ công việc "${res.data.task_label}" của mùa vụ "${res.data.label}" vào kho thành phẩm`,
          });
          return [
            {
              label: res.data.label,
              value: res.data.name,
            },
          ];
        }}
        width={'md'}
      />
      <ProFormSelect
        showSearch
        required
        disabled
        initialValue={curentUser?.user_id}
        label={<FormattedMessage id="warehouse-management.export-voucher.employee" />}
        request={async (option) => {
          return await fetchCustomerUserList();
        }}
        name="employee"
        // onChange={handleChooseItem}
        colProps={{ span: 8 }}
        width={'md'}
      />

      <ProFormTextArea
        name="description"
        label={<FormattedMessage id={'common.form.description'} />}
        colProps={{ span: 8 }}
        width={'md'}
      />
      <Divider />
      <MaterialTransferVoucherTable data={selectedItems} setData={setSelectedItems} />
    </ModalForm>
  );
};

export default MaterialTransfer;
