import useFormAddress from '@/components/Form/FormAddress';
import FormUploadsPreviewable from '@/components/FormUploadsPreviewable';
import QuillEditor from '@/components/QuillEditor';
import { createMember } from '@/services/diary-2/business';
import { ProForm, ProFormItem, ProFormText } from '@ant-design/pro-components';
import { history, useIntl } from '@umijs/max';
import { Card, Col, Row } from 'antd';
import { FC, ReactNode } from 'react';
import useCreate from '../../hooks/useCreate';
import Member from './Member';

interface CreateEnterpriseProps {
  children?: ReactNode;
}
const width = 'xl';
const CreateEnterprise: FC<CreateEnterpriseProps> = ({ children }) => {
  const { formatMessage } = useIntl();
  const { run: create } = useCreate({
    onSuccess() {
      history.push('/farming-diary-static/enterprise/list');
    },
  });
  const onFinish = async (values: any) => {
    const res = await create({
      avatar: values.avatar,
      qr: values.qr,
      business_code: values.business_code,
      label: values.label,
      phone: values.phone,
      email: values.email,
      image: values.image,
      province: values.province,
      district: values.district,
      ward: values.ward,
      address: values.address,
      description: values.description,
      link: values.link,
    });
    if (values.members) {
      await Promise.all(
        values.members?.map((record: any) =>
          createMember({
            // name: record.name,
            business_id: res.name,
            member_type: record.member_type,
            email: record.email,
            phone_number: record.phone_number,
            address: record.address,
            user_id: record.user_id,
          }),
        ),
      );
    }
    return true;
  };
  const [form] = ProForm.useForm();
  const { cityElement, districtElement, wardElement, detailsElement } = useFormAddress({
    form: form,
    formProps: {
      city: {
        name: 'province',
        width: width,
        rules: [{ required: true }],
      },
      district: {
        name: 'district',
        width: width,
        rules: [{ required: true }],
      },
      ward: {
        name: 'ward',
        width: width,
        rules: [{ required: true }],
      },
      address: {
        name: 'address',
        rules: [{ required: true }],
      },
    },
  });

  return (
    <ProForm onFinish={onFinish}>
      <div className="mb-4 space-y-4">
        <Card>
          <Row>
            <Col span={12}>
              <FormUploadsPreviewable
                isRequired={true}
                fileLimit={1}
                formItemName={'avatar'}
                label={'Logo'}
              />
            </Col>
            <Col span={12}>
              <FormUploadsPreviewable
                isRequired={true}
                fileLimit={1}
                formItemName={'qr'}
                label={formatMessage({ id: 'common.image_of_business_registration' })}
              />
            </Col>
          </Row>
          <Row gutter={24}>
            <Col span={12}>
              {' '}
              <ProFormText
                rules={[
                  {
                    required: true,
                  },
                ]}
                label={formatMessage({ id: 'common.business_code' })}
                width={width}
                name="business_code"
              />
            </Col>
            <Col span={12}>
              <ProFormText
                rules={[
                  {
                    required: true,
                  },
                ]}
                label={formatMessage({ id: 'common.business_name' })}
                width={width}
                name="label"
              />
            </Col>
            <Col span={12}>
              {' '}
              <ProFormText
                label={formatMessage({ id: 'common.number_phone' })}
                width={width}
                name="phone"
                rules={[
                  {
                    required: true,
                  },
                ]}
              />
            </Col>
            <Col span={12}>
              <ProFormText label="Email" width={width} name="email" />
            </Col>
            <Col span={8}>{cityElement}</Col>
            <Col span={8}> {districtElement}</Col>
            <Col span={8}>{wardElement}</Col>
            <Col span={24}> {detailsElement}</Col>
          </Row>

          <FormUploadsPreviewable
            fileLimit={10}
            formItemName={'image'}
            label={formatMessage({ id: 'common.other_images' })}
          />
          <ProFormText label={formatMessage({ id: 'common.web_link' })} name="link" />
          <ProFormItem
            name="description"
            label={formatMessage({
              id: 'common.introduce_business',
            })}
          >
            <QuillEditor />
          </ProFormItem>
        </Card>
        <Member />
      </div>
    </ProForm>
  );
};

export default CreateEnterprise;
