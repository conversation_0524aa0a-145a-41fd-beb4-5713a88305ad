import { DEFAULT_PAGE_SIZE_ALL, DOCTYPE_ERP } from '@/common/contanst/constanst';
import { getPestList } from '@/services/pandemic';
import { getListFileUrlFromString } from '@/services/utils';
import { useRequest } from '@umijs/max';
import { Spin } from 'antd';
import { FC, useEffect } from 'react';
import PandemicEmpty from './PandemicEmpty';
import { PandemicInfoProps } from './PandemicInfo';
import PandemicInfoList from './PandemicInfoList';

interface PandemicProps {
  cropId?: string;
  cacheKey?: string;
}

const Pandemic: FC<PandemicProps> = ({ cropId, cacheKey }) => {
  const { run, loading, data, refresh } = useRequest(
    ({ crop_id }: { crop_id?: string }) =>
      getPestList({
        page: 1,
        size: DEFAULT_PAGE_SIZE_ALL,
        filters: cropId ? [[DOCTYPE_ERP.iotPest, 'iot_crop', 'like', `${crop_id}`]] : undefined,
        order_by: 'creation desc',
      }),
    {
      manual: true,
    },
  );
  useEffect(() => {
    if (cropId) {
      run({
        crop_id: cropId,
      });
    }
  }, [cropId, cacheKey]);

  return (
    <Spin spinning={loading}>
      {!loading && (data || []).length === 0 ? (
        <PandemicEmpty
          cropId={cropId}
          onCreateSuccess={() => {
            refresh();
          }}
        />
      ) : (
        <PandemicInfoList
          data={data?.map<PandemicInfoProps['data']>((item) => ({
            id: item.name,
            title: item.label,
            time: item?.creation || item?.modified,
            description: item.description,
            category_list: item.category_list,
            involved_in_users: item.involved_in_users,
            state_list: item.state_list,
            listImg: getListFileUrlFromString({ arrUrlString: item.image }).map((url) => ({
              src: url,
            })),
          }))}
          onDeleteSuccess={() => {
            refresh();
          }}
        />
      )}
    </Spin>
  );
};

export default Pandemic;
