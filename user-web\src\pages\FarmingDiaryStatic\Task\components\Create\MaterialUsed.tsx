import { useProFormList } from '@/components/Form/Config/pro-form-list';
import { getProductItemV3 } from '@/services/InventoryManagementV3/product-item';
import { getUOM_v3 } from '@/services/InventoryManagementV3/uom';
import { ProFormDigit, ProFormList, ProFormSelect } from '@ant-design/pro-components';
import { useIntl } from '@umijs/max';
import { Card, Col, Row } from 'antd';
import { FC, ReactNode } from 'react';

interface MaterialUseProps {
  children?: ReactNode;
}
export type RelatedItemForm = {
  item_id: string;
  uom_id: string;
  quantity: number;
};
const MaterialUsed: FC<MaterialUseProps> = ({ children }) => {
  const { formatMessage } = useIntl();
  const proFormListProps = useProFormList();
  return (
    <Card
      title={formatMessage({
        id: 'common.materials_used',
      })}
    >
      <ProFormList name="related_items" {...proFormListProps}>
        {(d, index) => {
          return (
            <Row gutter={24}>
              <Col span={8}>
                {' '}
                <ProFormSelect
                  label={`${index + 1}. ${formatMessage({
                    id: 'common.select_supplies',
                  })}`}
                  name="item_id"
                  rules={[
                    {
                      required: true,
                    },
                  ]}
                  width="md"
                  showSearch
                  request={async () => {
                    const res = await getProductItemV3();
                    return res.data.map((item) => ({
                      label: item.label,
                      value: item.name,
                    }));
                  }}
                />
              </Col>
              <Col span={8}>
                <ProFormSelect
                  label={`${index + 1}. ${formatMessage({
                    id: 'common.unit',
                  })}`}
                  name="uom_id"
                  rules={[
                    {
                      required: true,
                    },
                  ]}
                  width="md"
                  showSearch
                  request={async () => {
                    const res = await getUOM_v3();
                    return res.data.map((item) => ({
                      label: `${item.uom_name}`,
                      value: item.name,
                    }));
                  }}
                />
              </Col>
              <Col span={8}>
                {' '}
                <ProFormDigit
                  label={`${index + 1}. ${formatMessage({
                    id: 'common.used_qty',
                  })}`}
                  name={'quantity'}
                />
              </Col>
            </Row>
          );
        }}
      </ProFormList>
    </Card>
  );
};

export default MaterialUsed;
