import { getCustomerUserList } from '@/services/customerUser';
import { generalUpdate } from '@/services/sscript';
import { EditOutlined } from '@ant-design/icons';
import { ProForm, ProFormSelect } from '@ant-design/pro-components';
import { FormattedMessage } from '@umijs/max';
import { Button, Col, Form, Input, message, Modal, Row } from 'antd';
import { useState } from 'react';
const { Item } = Form;

const UpdateTodoForTask = (params: { refreshFnc: any; task_id: string; data: any }) => {
  const [loading, setLoading] = useState(false);
  const [isOpen, setOpen] = useState(false);
  const [form] = ProForm.useForm();

  const showModal = () => {
    form.setFieldsValue(params.data);
    setOpen(true);
  };
  const hideModal = () => {
    setOpen(false);
  };
  const handleOk = () => {
    form.submit();
  };
  const handleCancel = () => {
    hideModal();
    form.resetFields();
  };

  return (
    <>
      <Button
        size="small"
        //type="dashed"
        style={{ display: 'flex', alignItems: 'center' }}
        onClick={showModal}
      >
        <EditOutlined></EditOutlined>
      </Button>
      <Modal
        title={`Chỉnh sửa công việc con`}
        open={isOpen}
        onOk={handleOk}
        onCancel={handleCancel}
        confirmLoading={loading}
      >
        <ProForm
          submitter={false}
          layout="horizontal"
          labelCol={{ span: 24 }}
          labelAlign="left"
          form={form}
          onFinish={async (value: any) => {
            try {
              value.farming_plan_task = params.task_id;
              value.name = params.data.name;
              const result = await generalUpdate('iot_todo', params.data.name, {
                data: value,
              });
              form.resetFields();
              hideModal();
              message.success('Success!');
              if (params.refreshFnc) {
                await params.refreshFnc();
              }
            } catch (error: any) {
              message.error(error.toString());
            } finally {
              setLoading(false);
            }
          }}
        >
          <Row gutter={5}>
            <Col className="gutter-row" md={24}>
              <Item
                label="Tên công việc"
                labelCol={{ span: 24 }}
                rules={[
                  {
                    required: true,
                    message: 'Bắt buộc điền',
                  },
                ]}
                name="label"
              >
                <Input />
              </Item>
            </Col>
            <Col className="gutter-row" md={24}>
              {/* <Item
                label={<FormattedMessage id={'common.form.customer_user_id'} />}
                labelCol={{ span: 24 }}
                name="customer_user_id"
              >
                <Input.TextArea />
              </Item> */}
              <ProFormSelect
                label={'Người thực hiện'}
                showSearch
                name="customer_user_id"
                request={async () => {
                  const result = await getCustomerUserList();
                  return result.data.map((item: any) => {
                    return {
                      label: item.last_name + ' ' + item.first_name,
                      value: item.name,
                    };
                  });
                  //return test options
                  // return [
                  //   { label: 'Nguyễn Văn A', value: 'Nguyễn Văn A' },
                  //   { label: 'Nguyễn Văn B', value: 'Nguyễn Văn B' },
                  //   { label: 'Nguyễn Văn C', value: 'Nguyễn Văn C' },
                  // ];
                }}
              />
            </Col>
            <Col className="gutter-row" md={24}>
              <Item
                label={<FormattedMessage id={'common.form.description'} />}
                labelCol={{ span: 24 }}
                name="description"
              >
                <Input.TextArea />
              </Item>
            </Col>
          </Row>
        </ProForm>
      </Modal>
    </>
  );
};

export default UpdateTodoForTask;
