import { sscriptGeneralList } from '@/services/sscript';
import { useAccess, useRequest } from '@umijs/max';
import { Space } from 'antd';
import { FC, ReactNode, useEffect, useState } from 'react';
import { useParams } from 'react-router-dom';
import TaskList from './TaskList';

interface CropPlanDetailProps {
  children?: ReactNode;
}

const getPlanId = async (cropId: string) => {
  const plan = await sscriptGeneralList({
    doc_name: 'iot_farming_plan',
    fields: ['name'],
    filters: [['iot_farming_plan', 'crop', 'like', cropId]],
  });
  return { data: plan.data[0].name }; // Return an object with a data property
};
const CropPlanDetail: FC<CropPlanDetailProps> = ({ children }) => {
  const paramsUrl = useParams();
  const cropId = paramsUrl.id as string;
  const [planId, setPlanId] = useState<string | null>(null);

  const planIdRequest = useRequest(() => getPlanId(cropId), {
    manual: true,
    onSuccess: (data) => {
      setPlanId(data);
    },
  });
  useEffect(() => {
    planIdRequest.run();
  }, [cropId]);

  const access = useAccess();
  const canCreateState = access.canCreateInStateManagement();
  const canReadState = access.canAccessPageStateManagement();
  return (
    <>
      <Space direction="vertical" style={{ display: 'flex' }} size={32}>
        {canReadState && <TaskList cropId={cropId} planId={planId || ''} />}
      </Space>
      {/* <TableCropPlanDetail planId={paramsUrl.id as string} /> */}
    </>
  );
};

export default CropPlanDetail;
