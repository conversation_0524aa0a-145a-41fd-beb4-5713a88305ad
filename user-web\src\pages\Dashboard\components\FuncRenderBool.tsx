import { controlDevice, getDataTimeSeries, getLatestDataFunction } from '@/services/devices';
import { sscript } from '@/services/sscript';
import { IIotProductionFunction } from '@/types/IIotProductionFunction';
import { DashboardOutlined } from '@ant-design/icons';
import { PageContainer, ProColumns, ProTable } from '@ant-design/pro-components';
import { AxiosError, useModel } from '@umijs/max';
import { Button, Card, Col, ConfigProvider, DatePicker, Input, message, Modal, Result, Row, Select, Spin, Switch, Tabs } from 'antd';
import React, { useEffect, useMemo, useRef, useState } from 'react';
import * as FileSaver from 'file-saver';
import * as XLSX from 'xlsx';
import moment from 'moment';

interface ActionType {
    reload: (resetPageIndex?: boolean) => void;
    reloadAndRest: () => void;
    reset: () => void;
    clearSelected?: () => void;
    startEditable: (rowKey: String) => boolean;
    cancelEditable: (rowKey: String) => boolean;
}

export const downloadExcelData = (reqData: any) => {

    if (reqData.length < 1)
        return;
    const heades = Object.keys(reqData[0]);
    let test = [];
    test.push(heades);
    for (let i = 0; i < reqData.length; i++) {
        test.push(Object.values(reqData[i]));
    }
    const fileType = 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet;charset=UTF-8';
    const fileExtension = '.xlsx';
    const ws = XLSX.utils.json_to_sheet(test, { skipHeader: true });
    const wb = { Sheets: { 'data': ws }, SheetNames: ['data'] };
    const excelBuffer = XLSX.write(wb, { bookType: 'xlsx', type: 'array' });
    const data = new Blob([excelBuffer], { type: fileType });
    FileSaver.saveAs(data, "Sensor Report" + fileExtension);
}


const FuncRenderBool = ({ fnc }: { fnc: IIotProductionFunction }) => {
    const [loading, setLoading] = useState(false);
    const [dataTable, setDataTable] = useState([]);
    const [downloading, setDownloading] = useState(false);

    const tableRef = useRef<ActionType>();
    const {
        latestDeviceValue,
        selectedDeviceThingsboard,
        timeRange,
        selectedTimeType
    } = useModel('MyResource');
    const [fncValue, setFncValue] = useState(false);
    const [isModalOpen, setIsModalOpen] = useState(false);
    const [exportTime, setExportTime] = useState(timeRange);

    const showModal = () => {
        switch (selectedTimeType) {
            case '5p-now':
                setExportTime([
                    moment().add(-5, 'm'),
                    moment()
                ]);
                break;
            case '1h-now':
                setExportTime([
                    moment().add(-1, 'h'),
                    moment()
                ]);
                break;
            case '2h-now':
                setExportTime([
                    moment().add(-2, 'h'),
                    moment()
                ]);
                break;
            case '1d-now':
                setExportTime([
                    moment().add(-1, 'd'),
                    moment()
                ]);
                break;
            case 'custom':
                setExportTime(timeRange);
                break;
            default:
                break;
        }
        setIsModalOpen(true);

    };

    const handleOk = () => {
        downloadData();

    };

    const handleCancel = () => {
        setIsModalOpen(false);
    };

    useMemo(() => {
        if (latestDeviceValue?.length) {
            const match_key = latestDeviceValue.filter((d: any) => {
                return d.key === fnc.identifier;
            });
            if (match_key.length) {
                let value = match_key[match_key.length - 1];
                value.value = value.value?.toString() === "true";
                setFncValue(value.value);
            }
        }
    }, [latestDeviceValue]);

    const setValueFnc = async (val: boolean) => {
        try {
            setLoading(true);
            console.log("val", val);
            let params = {};
            params[fnc.identifier || ""] = val;
            await controlDevice({
                device_id_thingsboard: selectedDeviceThingsboard,
                method: "set_state",
                params: params
            });
        } catch (error) {
            message.error("Có lỗi xảy ra, vui lòng thử lại!");
        } finally {
            setLoading(false);
        }
    }
    const columns: ProColumns<API.User>[] = [
        {
            title: 'Thời gian',
            dataIndex: 'Time'
        },
        {
            title: 'Giá trị',
            dataIndex: 'Value'
        }
    ];

    const reloadTable = async () => {
        tableRef.current?.reload();
    };
    const downloadData = async () => {
        try {
            setDownloading(true);

            if (dataTable.length) {
                downloadExcelData(dataTable);
            }
        } catch (error) {

        } finally {
            setDownloading(false);
        }
    };
    const getDataHistory = async () => {
        try {
            setDownloading(true);
            const result = await getDataTimeSeries({
                agg: 'NONE',
                startTs: exportTime[0].unix() * 1000,
                endTs: exportTime[1].unix() * 1000,
                keys: fnc.identifier || "",
                device_id_thingsboard: fnc.device_id_thingsboard || "",
                limit: 1000000
            });
            let data = result[fnc.identifier || ""];
            if (data.length) {
                data = data.map((d: any) => {
                    d.value = d.value.toString() === "true" ? (fnc.data_on_text || "On") : (fnc.data_off_text || "Off");
                    return {
                        "Label": fnc.label,
                        "Time": moment(d.ts).format("HH:mm:ss DD/MM/YYYY"),
                        "Value": d.value
                    }
                });
                data = data.filter((el: any, index: number) => {
                    if (index > 0) {
                        return el.Value !== data[index - 1].Value;
                    }
                    else {
                        return true;
                    }
                });
                setDataTable(data);
            }
            else {
                message.warning("Cảm biến không có dữ liệu trong thời gian này");
            }
        } catch (error) {

        } finally {
            setDownloading(false);
        }
    };

    useEffect(() => {
        if (exportTime)
            getDataHistory();
    }, [exportTime]);

    return (
        <Row gutter={[5, 5]}>
            <Col md={12}>
                <Button
                    type='link'
                    onClick={showModal}
                    icon={<DashboardOutlined></DashboardOutlined>}
                >
                    {fnc.label}
                </Button>
            </Col>
            <Col md={12}>
                <Switch
                    disabled={fnc.data_permission === "r"}
                    onChange={setValueFnc}
                    checked={fncValue}
                    checkedChildren={fnc.data_on_text ? fnc.data_on_text : "Đanng bật"}
                    unCheckedChildren={fnc.data_off_text ? fnc.data_off_text : "Đanng tắt"}
                    loading={loading}
                />
            </Col>
            <Modal
                okText={"Export Excel"}
                cancelText={"Đóng"}
                title="Lịch sử"
                open={isModalOpen}
                onOk={handleOk}
                onCancel={handleCancel}
            >
                <Row gutter={[5, 5]}>
                    <Col md={24}>
                        <DatePicker.RangePicker
                            picker='time'
                            value={exportTime}
                            format={"HH:mm DD/MM/YYYY"}
                            onChange={(v) => {
                                if (v) {
                                    setExportTime(v)
                                }
                            }}
                        />
                    </Col>
                    <Col md={24}>
                        <ProTable<API.User, API.PageParams >
                            size="small"
                            actionRef={tableRef}
                            rowKey="name"
                            loading={downloading}
                            dataSource={[...dataTable]}
                            columns={columns}
                            search={false}
                            headerTitle={""}
                            toolBarRender={false}
                            pagination={{ defaultPageSize: 20, showSizeChanger: true, pageSizeOptions: ['20', '50', '100'] }}
                        />
                    </Col>
                </Row>
            </Modal>
        </Row>
    );
};

export default FuncRenderBool;
