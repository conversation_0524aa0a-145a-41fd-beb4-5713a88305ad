import { TAG_COLOR } from '@/common/contanst/constanst';
import { useProFormList } from '@/components/Form/Config/pro-form-list';
import { getTaskList } from '@/services/diary-2/task';
import { PlusOutlined } from '@ant-design/icons';
import {
  ProForm,
  ProFormDigit,
  ProFormList,
  ProFormSelect,
  ProFormText,
} from '@ant-design/pro-components';
import { history, useIntl, useParams, useRequest } from '@umijs/max';
import { Button, Card, Col, Row, Tag } from 'antd';
import { FC, ReactNode, useCallback } from 'react';

interface TaskProps {
  children?: ReactNode;
}

const getTagColor = (item: string) => {
  switch (item) {
    case 'Priority':
      return TAG_COLOR.PRIORITY;
    case 'Important':
      return TAG_COLOR.IMPORTANT;
    case 'Common':
      return TAG_COLOR.COMMON;
    default:
      return 'default';
  }
};

const Task: FC<TaskProps> = () => {
  const { formatMessage } = useIntl();
  const { data: tasks, loading: tasksLoading } = useRequest(getTaskList);
  const form = ProForm.useFormInstance();
  const formListProps = useProFormList();
  const { id } = useParams();

  const handleTaskChange = useCallback(
    (selectedTaskName: string) => {
      const selectedTask = tasks?.find((task) => task.name === selectedTaskName);
      if (!selectedTask) return;

      const currentTasks = form.getFieldValue('tasks') || [];
      const updatedTasks = currentTasks.map((task: any, taskIndex: number) =>
        task.name === selectedTaskName
          ? {
              ...task,
              idx: taskIndex + 1, // Bổ sung idx nếu chưa có
              level: selectedTask.level,
              expire_time_in_days: selectedTask.expire_time_in_days,
              execution_day: selectedTask.execution_day,
              supplies: selectedTask.related_items
                .map(
                  (related_item) =>
                    ` ${related_item.quantity} ${related_item.uom_name} ${related_item.label}`,
                )
                .join(','),
            }
          : task,
      );

      form.setFieldsValue({ tasks: updatedTasks });
    },
    [tasks, form],
  );

  return (
    <Card
      title={formatMessage({ id: 'common.task' })}
      bordered={false}
      style={{ boxShadow: 'none' }}
      extra={
        <Button
          onClick={() => {
            history.push('/farming-diary-static/task/create', {
              fromStageEdit: true,
              id: id,
            });
          }}
          icon={<PlusOutlined />}
          type="default"
        >
          {formatMessage({ id: 'common.create-task' })}
        </Button>
      }
    >
      <ProFormList
        name="tasks"
        {...formListProps}
        creatorButtonProps={{
          style: { width: '100%' }, // Set the width to 100%
          className: 'mx-auto mt-4', // Center align the button and add margin-top
        }}
      >
        {(d, index) => (
          <Card key={index} style={{ margin: 6 }}>
            <Row gutter={24}>
              <Col span={8}>
                <ProFormSelect
                  required
                  onChange={handleTaskChange}
                  fieldProps={{
                    loading: tasksLoading,
                  }}
                  options={tasks?.map((task) => ({
                    label: task.label,
                    value: task.name,
                  }))}
                  name="name"
                  label={`${index + 1}. ${formatMessage({
                    id: 'common.task_name',
                  })}`}
                  showSearch
                />
              </Col>
              <Col span={8}>
                <ProFormSelect
                  disabled
                  label={formatMessage({ id: 'common.level' })}
                  name="level"
                  request={async () => {
                    return ['Common', 'Important', 'Priority'].map((item) => ({
                      label: <Tag color={getTagColor(item)}>{item}</Tag>,
                      value: item,
                    }));
                  }}
                  placeholder=""
                />
              </Col>
              <Col span={8}>
                <ProFormDigit
                  disabled
                  label={formatMessage({
                    id: 'common.execution_time',
                  })}
                  name="execution_day"
                  fieldProps={{
                    prefix: formatMessage({ id: 'common.days' }),
                  }}
                  placeholder=""
                />
              </Col>
              <Col span={8}>
                <ProFormDigit
                  disabled
                  label={formatMessage({
                    id: 'common.expire_time_in_days',
                  })}
                  name="expire_time_in_days"
                  fieldProps={{
                    suffix: formatMessage({ id: 'common.days' }),
                  }}
                  placeholder=""
                />
              </Col>
              <Col span={16}>
                <ProFormText
                  disabled
                  label={formatMessage({
                    id: 'common.supplies',
                  })}
                  name="supplies"
                  placeholder=""
                />
              </Col>
            </Row>
          </Card>
        )}
      </ProFormList>
    </Card>
  );
};

export default Task;
