import { getCropList } from '@/services/cropManager';
import { createCropTracing } from '@/services/tracing';
import { PlusOutlined } from '@ant-design/icons';
import { ProFormSelect } from '@ant-design/pro-components';
import { useIntl } from '@umijs/max';
import { Button, Col, DatePicker, Form, Input, message, Modal, Row } from 'antd';
import { useState } from 'react';

const { Item } = Form;

const Create = (params: { refreshFnc: any }) => {
  const [loading, setLoading] = useState(false);
  const [isOpen, setOpen] = useState(false);
  const [form] = Form.useForm();

  const [imageUrl, setImageUrl] = useState<string>();
  const [uploading, setUploading] = useState(false);
  const [fileList, setFileList] = useState<any[]>([]);

  const showModal = () => {
    setOpen(true);
  };
  const hideModal = () => {
    setOpen(false);
  };
  const handleOk = () => {
    form.submit();
  };
  const handleCancel = () => {
    hideModal();
    form.resetFields();
  };
  const intl = useIntl();
  return (
    <>
      <Button type="primary" onClick={showModal} style={{ display: 'flex', alignItems: 'center' }}>
        <PlusOutlined /> {intl.formatMessage({ id: 'action.add' })}
      </Button>
      <Modal
        title={intl.formatMessage({ id: 'action.add' })}
        open={isOpen}
        onOk={handleOk}
        onCancel={handleCancel}
        confirmLoading={loading}
      >
        <Form
          layout="horizontal"
          labelCol={{ span: 24 }}
          labelAlign="left"
          form={form}
          wrapperCol={{ span: 24 }}
          style={{ maxWidth: 600 }}
          onFinish={async (value: any) => {
            try {
              value.image = '';
              if (fileList.length) {
                value.image = fileList[0].raw_url;
              }
              //   console.log('value product', value);
              const result = await createCropTracing(value);
              form.resetFields();
              hideModal();
              message.success('Success!');
              if (params.refreshFnc) {
                await params.refreshFnc();
              }
            } catch (error: any) {
              message.error(error.toString());
            } finally {
              setLoading(false);
            }
          }}
        >
          <Row gutter={10}>
            <Col className="gutter-row" md={12} span={12}>
              <Item
                label={intl.formatMessage({ id: 'common.origin_name' })}
                labelCol={{ span: 24 }}
                rules={[
                  {
                    required: true,
                    message: 'Bắt buộc điền',
                  },
                ]}
                name="label"
              >
                <Input />
              </Item>
            </Col>

            <Col className="gutter-row" md={12} span={12}>
              <Item label={intl.formatMessage({ id: 'common.expired_time' })} name="expiry_time">
                <DatePicker
                  format="DD/MM/YYYY hh:mm A"
                  onChange={(date, dateString) => console.log(date, dateString)}
                  showTime={{ use12Hours: true }}
                  placeholder="Default 2 days"
                />
              </Item>
            </Col>
          </Row>
          <Row>
            <Col className="gutter-row" md={12} span={12}>
              <Item
                label={intl.formatMessage({ id: 'common.crop' })}
                required={true}
                name="crop_id"
              >
                <ProFormSelect
                  showSearch
                  request={async () => {
                    const data = await getCropList();
                    return data.data.map((item: any) => ({
                      label: item.label,
                      value: item.name,
                    }));
                  }}
                />
              </Item>
            </Col>
          </Row>
        </Form>
      </Modal>
    </>
  );
};

export default Create;
