export default {
  'menu.welcome': 'Welcome',
  'menu.more-blocks': 'More Blocks',
  // menu route
  'menu.home': 'Home',
  'menu.monitor': 'Monitor',
  'menu.report-analysis': 'Report analysis',
  'menu.report-analysis.revenue-overview': 'Revenue overview',
  'menu.report-analysis.site-detail': 'Site detail',
  'menu.report-analysis.performance-analysis': 'Performance analysis',
  'menu.report-analysis.operational-analysis': 'Operational analysis',
  'menu.report-analysis.retrieve-report': 'Retrieve report',
  'menu.maintenance-plan': 'Maintenance plan',
  'menu.incident-warning': 'Incident warning',
  'menu.alarm-and-fault': 'Alarm and fault',

  'menu.my-project': 'My Project',
  'menu.my-project.all': 'All',
  'menu.my-project.detail': 'Detail',

  'menu.customer-management': 'Customer management',
  'menu.customer-management.all': 'All customer ',
  'menu.customer-management.detail': 'Detail customer ',

  'menu.account-management': 'Account management',
  'menu.Employees': 'Employees',
  'menu.Employees.all': 'All Employees',
  'menu.Employees.detail': 'Detail Employees',

  'menu.admin': 'Admin',
  'menu.admin.sub-page': 'Sub-Page',
  'menu.login': 'Login',
  'menu.register': 'Register',
  'menu.register-result': 'Register Result',
  'menu.dashboard': 'Dashboard',
  'menu.dashboard.analysis': 'Analysis',
  'menu.dashboard.monitor': 'Monitor',
  'menu.dashboard.workplace': 'Workplace',
  'menu.exception.403': '403',
  'menu.exception.404': '404',
  'menu.exception.500': '500',
  'menu.form': 'Form',
  'menu.form.basic-form': 'Basic Form',
  'menu.form.step-form': 'Step Form',
  'menu.form.step-form.info': 'Step Form(write transfer information)',
  'menu.form.step-form.confirm': 'Step Form(confirm transfer information)',
  'menu.form.step-form.result': 'Step Form(finished)',
  'menu.form.advanced-form': 'Advanced Form',
  'menu.list': 'List',
  'menu.list.table-list': 'Search Table',
  'menu.list.basic-list': 'Basic List',
  'menu.list.card-list': 'Card List',
  'menu.list.search-list': 'Search List',
  'menu.list.search-list.articles': 'Search List(articles)',
  'menu.list.search-list.projects': 'Search List(projects)',
  'menu.list.search-list.applications': 'Search List(applications)',
  'menu.profile': 'Profile',
  'menu.profile.basic': 'Basic Profile',
  'menu.profile.advanced': 'Advanced Profile',
  'menu.result': 'Result',
  'menu.result.success': 'Success',
  'menu.result.fail': 'Fail',
  'menu.exception': 'Exception',
  'menu.exception.not-permission': '403',
  'menu.exception.not-find': '404',
  'menu.exception.server-error': '500',
  'menu.exception.trigger': 'Trigger',
  'menu.account.center': 'Account Center',
  'menu.account.settings': 'Account Settings',
  'menu.account.trigger': 'Trigger Error',
  'menu.account.logout': 'Logout',
  'menu.editor': 'Graphic Editor',
  'menu.editor.flow': 'Flow Editor',
  'menu.editor.mind': 'Mind Editor',
  'menu.editor.koni': 'Koni Editor',
  'menu.Plan': 'Plan',
  'menu.Plan.Mainternance Plan': 'Mainternance Plan',
  'menu.Plan.Reduction Plan': 'Reduction Plan',
  'menu.test': 'Test',
  'menu.daily-operation-logs': 'Daily Operation Logs',
  'menu.decentralization': 'Decentralization',
  'menu.my-account': 'My account',

  'menu.category': 'Category',
  'menu.category-old': 'Category (Old)',
  'menu.category.material': 'Supplies',
  'menu.category.products': 'Product',
  'menu.category.supplier': 'Producer',
  'menu.category.supplier.producer': 'Producer',
  'menu.category.supplier.producer_group': 'Producer group',
  'menu.warehouse-management': 'Warehouse',
  'menu.warehouse-management-old': 'Warehouse (Old)',
  'menu.warehouse-management.warehouse-list': 'Warehouse list',
  'menu.warehouse-management.inventory-material.storage-category': 'Category storage',
  'menu.warehouse-management.inventory-material.import-history': 'Import history',
  'menu.warehouse-management.inventory-material.export-history': 'Export history',
  'menu.warehouse-management.inventory-material.check-history': 'Check history',
  'menu.warehouse-management.inventory-material': 'Inventory supplies',
  'menu.warehouse-management.inventory-products': 'Inventory products',
  'menu.warehouse-management.inventory-products.storage-product': 'Product storage',
  'menu.warehouse-management.inventory-products.import-history': 'Import history',
  'menu.warehouse-management.inventory-products.export-history': 'Export history',
  'menu.warehouse-management.inventory-products.check-history': 'Check history',

  'menu.account-information': 'Account information',
  'menu.logout': 'Logout',
  //quản lý canh tác
  'menu.farming-management': 'Farming',
  'menu.farming-management.dashboard': 'Dashboard',

  //quản lý vụ mùa
  'menu.farming-management.season-management': 'Crop management',
  'menu.farming-management.season-management.create': 'Create',
  'menu.farming-management.season-management.edit': 'Edit',

  //chi tiết vụ mùa
  'menu.farming-management.season-management.detail': 'Detail',
  //quản lý công việc
  'menu.farming-management.work-management': 'Task',
  //chi tiết công việc
  'menu.farming-management.work-management.create': 'Create',
  'menu.farming-management.work-management.detail': 'Detail',
  'menu.farming-management.work-management.edit': 'Edit',

  //Thư viện cây trồng
  'menu.farming-management.plant-library': 'Plant library',
  // thông kê vụ mùa
  'menu.farming-management.crop-statistical': 'Crop Statistical',
  //
  'menu.farming-management.plant': 'Plant',
  'menu.farming-management.zone': 'Zone',

  'menu.project_management': 'Project',
  'menu.project_management.create': 'Create new project',
  'menu.project_management.detail': 'Project detail',
  'menu.project_management.edit': 'Edit project',

  'menu.manage_iot_devices': 'Manage IOT devices',

  'menu.employee_management': 'Employee',
  'menu.employee_management.list_of_employee': 'List of employee',
  'menu.employee_management.list_of_permissions': 'List of permissions',
  'menu.employee_management.attendance': 'Attendance',
  'menu.employee_management.attendance.history_in_and_out': 'Check in and check out history',

  'menu.employee_management.timekeeping': 'Timesheet',
  'menu.employee_management.timekeeping.history_in': 'History  check in',
  'menu.employee_management.timekeeping.history_out': 'History check out',
  'menu.employee_management.timekeeping.synthetic': 'Synthetic',
  'menu.employee_management.timekeeping.timesheets': 'Timesheets',

  'menu.employee_management.approval': 'Approval',
  'menu.employee_management.approval.details': 'Approval details',

  'menu.timekeeping_management': 'Timesheet management',

  'menu.in_and_out_management': 'Access control',
  'menu.in_and_out_management.statistical_tables': 'Statistical tables',
  'menu.in_and_out_management.visit_history': 'Visit history',
  'menu.in_and_out_management.visit_history.all': 'All',
  'menu.in_and_out_management.visit_history.detail': 'Detail',

  'menu.in_and_out_management.user': 'User',
  'menu.in_and_out_management.user.all': 'All',
  'menu.in_and_out_management.user.detail': 'Detail',

  'menu.in_and_out_management.card': 'Card',
  'menu.in_and_out_management.card.all': 'All',
  'menu.in_and_out_management.card.detail': 'Detail',

  'menu.in_and_out_management.location': 'Location',
  'menu.in_and_out_management.location.all': 'All',
  'menu.in_and_out_management.location.detail': 'Detail',

  'menu.notification': 'Notification',
  'menu.farming-diary': 'Diary',
  'menu.farming-diary.detail': 'Detail',
  'common.expired_time': 'Expired time',
  'menu.category-v3': 'Category',
  'menu.category-v3.product': 'Item',
  'menu.category-v3.customer': 'Customer',
  'menu.category-v3.customer.sales-order': 'Sales order',
  'menu.category-v3.supplier': 'Supplier',
  'menu.inventory-management-v3': 'Inventory management',
  'menu.inventory-management-v3.list': 'List',
  'menu.inventory-management-v3.inventory': 'Inventory',
  'menu.inventory-management-v3.inventory.stock-ledger': 'Stock ledger',
  'menu.inventory-management-v3.inventory.import-history': 'Import history',
  'menu.inventory-management-v3.inventory.export-history': 'Export history',
  'menu.inventory-management-v3.inventory.reconciliation-history': 'Reconciliation history',
  'menu.inventory-management-v3.inventory.stock-entry': 'Stock Entry history',
  'menu.inventory-management-v3.inventory.report': 'Report',
  'menu.inventory-management-v3.dashboard': 'Dashboard',
  'menu.to-pdf': 'Print Receipt',
  'menu.documents': 'Documents',
  'menu.project-zone': 'Project - Zone',
  'menu.project-zone.create': 'Create',
  'menu.project-zone.detail': 'Detail',

  'menu.seek-an-origin': 'Traceability',
  'menu.category-v3.bom': 'BOM',
  'menu.farming-diary-static': 'Cultivation diary',
  'menu.farming-diary-static.procedure': 'Procedure',
  'menu.farming-diary-static.procedure.list': 'Procedure list',
  'menu.farming-diary-static.procedure.detail': 'Procedure detail',
  'menu.farming-diary-static.procedure.create': 'Procedure create',
  'menu.farming-diary-static.stage-of-crop': 'Stage',
  'menu.farming-diary-static.stage-of-crop.list': 'Stage list',
  'menu.farming-diary-static.stage-of-crop.create': 'Stage create',
  'menu.farming-diary-static.task': 'Task',
  'menu.farming-diary-static.task.list': 'Task list',
  'menu.farming-diary-static.task.create': 'Task create',
  'menu.farming-diary-static.task.detail': 'Task detail',
  'menu.farming-diary-static.note': 'Note',
  'menu.farming-diary-static.note.list': 'Note list',
  'menu.farming-diary-static.note.create': 'Note create',
  'menu.farming-diary-static.certification': 'Certification',
  'menu.farming-diary-static.certification.list': 'Certification list',
  'menu.farming-diary-static.certification.create': 'Certification create',
  'menu.farming-diary-static.product-procedure': 'Product',
  'menu.farming-diary-static.product-procedure.list': 'Product list',
  'menu.farming-diary-static.product-procedure.create': 'Product create',
  'menu.farming-diary-static.product-procedure.detail': 'Product detail',
  'menu.farming-diary-static.enterprise': 'Enterprise',
  'menu.farming-diary-static.enterprise.list': 'Enterprise list',
  'menu.farming-diary-static.enterprise.create': 'Enterprise create',
  'menu.farming-diary-static.enterprise.detail': 'Enterprise detail',
  'menu.farming-diary-static.stamp': 'Stamp',
  'menu.farming-diary-static.stamp.list': 'Stamp list',
  'menu.farming-diary-static.stamp.create': 'Stamp create',
  'menu.farming-diary-static.stamp.detail': 'Stamp detail',
  'menu.farming-diary-static.trace': 'Traceability',
  'menu.farming-diary-static.trace.list': 'Trace list',
  'menu.farming-diary-static.trace.detail': 'Trace detail',
  'menu.farming-diary-static.trace.create': 'Trace create',
  'menu.employee_management.attendance.work-shift': 'Work shift',
  'menu.employee_management.attendance.approval': 'Approval list',
};
