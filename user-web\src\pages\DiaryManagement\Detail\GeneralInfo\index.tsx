import { DOCTYPE_ERP } from '@/common/contanst/constanst';
import { getCropDiary, updateCropDiary } from '@/services/diary';
import { uploadFile } from '@/services/fileUpload';
import { getImageBaseUrls, getListFileUrlFromString } from '@/services/utils';
import { ProForm, ProFormText } from '@ant-design/pro-components';
import { useAccess, useIntl, useRequest } from '@umijs/max';
import { App, Card, Divider, Space, Spin, UploadFile } from 'antd';
import { nanoid } from 'nanoid';
import { FC, ReactNode, useEffect, useState } from 'react';
import ItemsStatistic from './components/ItemsStatistic';
import ProductionQuantityStatistic from './components/ProductionQuantityStatistic';
import WorksheetStatistic from './components/WorksheetStatistic';
import DetailedInfo from './DetailedInfo';

interface GeneralInfoProps {
  children?: ReactNode;
  onSuccess?: () => void;
  cropId: string;
}
type IFormData = {
  label: string;
  diary_id: string;
  zone_id: string;
  date_range: [string, string];
  square: number;
  plant_id: string;
  img?: UploadFile[];
  avatar?: any;
  description?: string;
  quantity_estimate?: number;
  status?: string;
};

const GeneralInfo: FC<GeneralInfoProps> = ({ onSuccess, cropId }) => {
  const { message } = App.useApp();
  const [submitting, setSubmitting] = useState(false);
  const intl = useIntl();
  const onFinish = async (values: any) => {
    setSubmitting(true);
    try {
      console.log('values', values);
      if (values.images && values.images.length > 5) {
        console.log('values.images', values.images);
        message.error('Chỉ được upload tối đa 5 ảnh');
        return;
      }
      // create
      const dataUpdated = await updateCropDiary({
        crop_id: cropId,
        diary_id: values.diary_id,
        crop_owner: values.crop_owner,
        location: values.location,
        longitude: values.longitude,
        latitude: values.latitude,
        short_description: values.short_description,
        business_info: values.business_info,
        phone: values.phone,
        website: values.website,
        email: values.email,
        other_link: values.other_link,
      });

      // // upload file
      // if (values.img && (values.img || []).length > 0) {
      //   // kiểm tra các file đã upload

      //   const filesUploaded = values.img.filter((item) => !item.originFileObj);
      //   const filesNotUpload = values.img.filter((item) => item.originFileObj);
      //   // upload bất kể thành công hay ko
      //   const uploadListRes = await Promise.allSettled(
      //     filesNotUpload.map(async (item) => {
      //       return await uploadFile({
      //         docType: DOCTYPE_ERP.iotCrop,
      //         docName: dataUpdated.data.name,
      //         file: item.originFileObj as any,
      //       });
      //     }),
      //   );
      //   // check if() 1 vài upload failed
      //   const checkUploadFailed = uploadListRes.find((item) => item.status === 'rejected');
      //   if (checkUploadFailed) {
      //     message.error({
      //       content: 'Some file upload failed',
      //     });
      //   }

      //   // update img path
      //   const arrFileUrl = uploadListRes
      //     .reduce<string[]>(
      //       (prev, item) =>
      //         item.status === 'fulfilled' ? [...prev, item?.value?.data?.message?.file_url] : prev,
      //       [],
      //     )
      //     .filter((item) => typeof item === 'string')
      //     // thêm file đã upload
      //     .concat(filesUploaded.map((item) => item.url as string));

      //   console.log('arrFileUrl', arrFileUrl);
      //   if (arrFileUrl.length > 0) {
      //     await updateCrop({
      //       name: dataUpdated.data.name,
      //       image: arrFileUrl.join(','),
      //       zone_id: dataUpdated.data.zone_id,
      //     });
      //   }
      // }
      console.log('dataUpdated.data.name hi', dataUpdated);

      //upload images
      if (values.images && (values.images || []).length > 0) {
        // kiểm tra các file đã upload
        const filesUploaded = values.images.filter((item: any) => !item.originFileObj);
        const filesNotUpload = values.images.filter((item: any) => item.originFileObj);
        // upload bất kể thành công hay ko
        console.log('filesNotUpload', filesNotUpload);
        console.log('filesUploaded', filesUploaded);

        const uploadListRes = await Promise.allSettled(
          filesNotUpload.map(async (item: any) => {
            return await uploadFile({
              docType: DOCTYPE_ERP.iotCropDiary,
              docName: dataUpdated[0].name,
              file: item.originFileObj as any,
            });
          }),
        );
        // check if() 1 vài upload failed
        const checkUploadFailed = uploadListRes.find((item) => item.status === 'rejected');
        if (checkUploadFailed) {
          message.error({
            content: 'Some file upload failed',
          });
        }

        // update avatar path
        const arrFileUrl = uploadListRes
          .reduce<string[]>(
            (prev, item) =>
              item.status === 'fulfilled' ? [...prev, item?.value?.data?.message?.file_url] : prev,
            [],
          )
          .filter((item) => typeof item === 'string')
          // thêm file đã upload
          .concat(filesUploaded.map((item: any) => item.base as string));
        if (arrFileUrl.length > 0) {
          await updateCropDiary({
            crop_id: cropId,
            diary_id: values.diary_id,
            images: arrFileUrl.join(','),
          });
        }
      }

      //upload business_avatar
      if (values.business_avatar && (values.business_avatar || []).length > 0) {
        // kiểm tra các file đã upload
        const filesUploaded = values.business_avatar.filter((item: any) => !item.originFileObj);
        const filesNotUpload = values.business_avatar.filter((item: any) => item.originFileObj);
        // upload bất kể thành công hay ko
        const uploadListRes = await Promise.allSettled(
          filesNotUpload.map(async (item: any) => {
            return await uploadFile({
              docType: DOCTYPE_ERP.iotCropDiary,
              docName: dataUpdated[0].name,
              file: item.originFileObj as any,
            });
          }),
        );
        // check if() 1 vài upload failed
        const checkUploadFailed = uploadListRes.find((item) => item.status === 'rejected');
        if (checkUploadFailed) {
          message.error({
            content: 'Some file upload failed',
          });
        }

        // update avatar path
        const arrFileUrl = uploadListRes
          .reduce<string[]>(
            (prev, item) =>
              item.status === 'fulfilled' ? [...prev, item?.value?.data?.message?.file_url] : prev,
            [],
          )
          .filter((item) => typeof item === 'string')
          // thêm file đã upload
          .concat(filesUploaded.map((item: any) => item.base as string));
        if (arrFileUrl.length > 0) {
          await updateCropDiary({
            crop_id: cropId,
            diary_id: values.diary_id,
            business_avatar: arrFileUrl.join(','),
          });
        }
      }

      message.success({
        content: 'Updated successfully',
      });
      onSuccess?.();
      // history.push('/farming-management/seasonal-management');
      // window.location.reload();
      return true;
    } catch (error) {
      message.error({
        content: 'Error, please try again',
      });
      return false;
    } finally {
      setSubmitting(false);
    }
  };
  const [form] = ProForm.useForm();
  const { loading, run: getDetail } = useRequest(
    () =>
      getCropDiary({
        size: 1,
        page: 1,
        filters: JSON.stringify([[DOCTYPE_ERP.iotCropDiary, 'crop_id', '=', cropId]]),
      }),
    {
      manual: true,
      onError() {
        message.error({
          content: 'Can not get crop information, please try again',
        });
      },
      onSuccess(res: any) {
        const dataFound = res.data[0];
        console.log('dataFound', dataFound);
        if (!dataFound) return;

        form.setFieldsValue({
          diary_id: dataFound.diary_id,
          label: dataFound.label,
          zone_id: dataFound.zone_id,
          zone_name: dataFound.zone_name,
          date_range: [dataFound.start_date, dataFound.end_date],
          square: dataFound.square,
          plant_id: dataFound.plant_id,
          plant_name: dataFound.plant_name,
          status: dataFound.status === 'In progress' ? 'Đang diễn ra' : 'Hoàn tất',
          description: dataFound.description,
          quantity_estimate: dataFound.quantity_estimate,
          images: getListFileUrlFromString({
            arrUrlString: dataFound.images,
          }).map((item) => ({
            base: getImageBaseUrls(item),
            uid: nanoid(),
            status: 'done',
            url: item,
            type: 'image/*',
          })),
          business_avatar: getListFileUrlFromString({
            arrUrlString: dataFound.business_avatar,
          }).map((item) => ({
            uid: nanoid(),
            status: 'done',
            url: item,
            type: 'image/*',
          })),
          avatar: getListFileUrlFromString({
            arrUrlString: dataFound.avatar,
          }).map((item) => ({
            uid: nanoid(),
            status: 'done',
            url: item,
            type: 'image/*',
          })),
          crop_owner: dataFound.crop_owner,
          location: dataFound.location,
          longitude: dataFound.longitude,
          latitude: dataFound.latitude,
          short_description: dataFound.short_description,
          business_info: dataFound.business_info,
          phone: dataFound.phone,
          website: dataFound.website,
          email: dataFound.email,
          other_link: dataFound.other_link,
        });
      },
    },
  );
  useEffect(() => {
    if (cropId) {
      getDetail();
    }
  }, [cropId]);

  const access = useAccess();
  const canUpdate = access.canUpdateInSeasonalManagement();
  return (
    <>
      <Spin spinning={loading}>
        <ProForm<IFormData> onFinish={onFinish} form={form} submitter={false} grid={true}>
          <Space
            size={'large'}
            direction="vertical"
            style={{
              width: '100%',
            }}
          >
            <ProFormText hidden={true} name="diary_id" />
            <DetailedInfo cropId={cropId} form={form} callback={form.submit} />

            {/* <Card title="Hình ảnh / Video mô tả ">
              <ProFormUploadButton
                accept="image/*"
                listType="picture-card"
                icon={<CameraFilled />}
                title=""
                name="img"
              />
            </Card> */}
            <Card title={intl.formatMessage({ id: 'seasonalTab.basicStatistics' })}>
              <ItemsStatistic cropId={cropId} />
              <Divider />
              <ProductionQuantityStatistic cropId={cropId} />
              <Divider />
              <WorksheetStatistic cropId={cropId} />
            </Card>
          </Space>
        </ProForm>
      </Spin>
    </>
  );
};

export default GeneralInfo;
