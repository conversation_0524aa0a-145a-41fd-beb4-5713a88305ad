import { DOCTYPE_ERP } from '@/common/contanst/constanst';
import ActionHover from '@/components/ActionHover';
import { getStageList } from '@/services/diary-2/stage';
import { getParamsReqTable } from '@/services/utils';
import { ActionType, ProTable } from '@ant-design/pro-components';
import { useIntl } from '@umijs/max';
import { Button, Space } from 'antd';
import { FC, ReactNode, useEffect, useRef, useState } from 'react';
import DeleteStage from './DeleteStage';

interface StageCropListProps {
  children?: ReactNode;
  onSelect?: (stageId: string) => void;
  reloadKey?: string | null;
}

const StageCropList: FC<StageCropListProps> = ({ children, onSelect, reloadKey }) => {
  const { formatMessage } = useIntl();
  const actionRef = useRef<ActionType>();
  const [selectedRowKey, setSelectedRowKey] = useState<string | null>(null);
  const [data, setData] = useState<any[]>([]);

  const handleReload = () => {
    actionRef.current?.reload?.();
  };
  useEffect(() => {
    if (reloadKey) {
      handleReload();
    }
  }, [reloadKey]);
  useEffect(() => {
    if (data.length > 0 && !selectedRowKey) {
      const firstRowKey = data[0].name;
      setSelectedRowKey(firstRowKey);
      onSelect?.(firstRowKey);
    }
  }, [data]);
  return (
    <ProTable
      actionRef={actionRef}
      search={false}
      toolBarRender={() => []}
      rowKey={'name'}
      options={false} // Hide the entire toolbar
      form={{
        labelWidth: 'auto',
      }}
      scroll={{
        x: 'max-content',
      }}
      pagination={{
        pageSize: 10,
        showSizeChanger: true,
        defaultPageSize: 10,
      }}
      request={async (params, sort, filter) => {
        const paramsReq = getParamsReqTable({
          doc_name: DOCTYPE_ERP.iot_diary_v2_state,
          tableReqParams: {
            params,
            sort,
            filter,
          },
          defaultSort: 'name asc',
        });
        const res = await getStageList(paramsReq);
        setData(res.data);

        return {
          data: res.data,
          total: res.pagination.totalElements,
        };
      }}
      rowClassName={(record) =>
        record.name === selectedRowKey ? 'bg-emerald-100 group/action' : 'group/action'
      }
      columns={[
        {
          title: formatMessage({
            id: 'common.stage_name',
          }),
          dataIndex: 'label',
          render(dom, entity, index, action, schema) {
            return (
              <Button
                type="link"
                onClick={() => {
                  setSelectedRowKey(entity.name);
                  onSelect?.(entity.name);
                }}
              >
                {dom}
              </Button>
            );
          },
        },
        {
          title: formatMessage({
            id: 'common.task_count',
          }),
          dataIndex: 'task_count',
        },
        {
          title: formatMessage({
            id: 'common.time',
          }),
          dataIndex: 'expire_time_in_days',

          render: (dom, entity) => {
            return (
              <ActionHover
                actions={() => (
                  <Space>
                    <DeleteStage id={entity.name} onSuccess={handleReload} />
                  </Space>
                )}
              >
                {`${dom} ${formatMessage({
                  id: 'common.days',
                })}`}
              </ActionHover>
            );
          },
        },
      ]}
    />
  );
};

export default StageCropList;
