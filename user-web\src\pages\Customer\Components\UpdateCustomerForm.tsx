import { LoadingOutlined, PlusOutlined } from '@ant-design/icons';
import { Button, Col, Form, Input, message, Row, Select, Upload } from 'antd';
import type { UploadProps } from 'antd/es/upload/interface';
import { useEffect, useState } from 'react';
const { Item } = Form;

import vietnam_location from '@/helpers/tree.json';
import { generalUpdate } from '@/services/sscript';
import { removeFile, uploadFile } from '@/services/uploadFile';
import {
  generateAPIPath,
  getDistristCodeByName,
  getProvinceCodeByName,
  getWardCodeByName,
  toLowerCaseNonAccentVietnamese,
} from '@/services/utils';
const { TextArea } = Input;
const { Option } = Select;

const UpdateCustomerForm = (params: { company: any; refreshFnc: any }) => {
  const [loading, setLoading] = useState(false);
  const [form] = Form.useForm();
  const [stateOption, setStateOption] = useState([]);
  const [wardOption, setWardOption] = useState([]);
  const [imageUrl, setImageUrl] = useState();
  const [uploading, setUploading] = useState(false);
  const [fileList, setFileList] = useState<any[]>([]);

  useEffect(() => {
    if (Object.keys(params.company).length) {
      let temp_data = params.company;

      temp_data.province = temp_data.province
        ? getProvinceCodeByName(temp_data.province)
        : temp_data.province;
      temp_data.district = temp_data.district
        ? getDistristCodeByName(temp_data.province, temp_data.district)
        : temp_data.district;
      temp_data.ward =
        temp_data.ward && temp_data.province
          ? getWardCodeByName(temp_data.province, temp_data.district, temp_data.ward)
          : temp_data.ward;

      if (temp_data.province) {
        const new_state: any = Object.keys(vietnam_location[temp_data.province]['quan-huyen']).map(
          (key) => {
            return (
              <Option key={key} value={key}>
                {vietnam_location[temp_data.province]['quan-huyen'][key].name_with_type}
              </Option>
            );
          },
        );
        setStateOption(new_state);
      }
      if (temp_data.district) {
        const new_ward: any = Object.keys(
          vietnam_location[temp_data.province]['quan-huyen'][temp_data.district]['xa-phuong'],
        ).map((key) => {
          return (
            <Option key={key} value={key}>
              {
                vietnam_location[temp_data.province]['quan-huyen'][temp_data.district]['xa-phuong'][
                  key
                ].name_with_type
              }
            </Option>
          );
        });
        setWardOption(new_ward);
      }

      form.setFieldsValue(params.company);
      if (params.company.logo) {
        setFileList([
          {
            uid: 'raw-logo',
            url: generateAPIPath(
              'api/v2/file/download?file_url=' +
                params.company.logo +
                '&dt=' +
                'iot_customer' +
                '&dn=' +
                params.company.name,
            ),
            raw_url: params.company.logo,
          },
        ]);
        setImageUrl(
          generateAPIPath(
            'api/v2/file/download?file_url=' +
              params.company.logo +
              '&dt=' +
              'iot_customer' +
              '&dn=' +
              params.company.name,
          ),
        );
      }
    }
  }, [params.company]);

  const handleChangeCity = (value: any) => {
    if (value) {
      const new_state: any = Object.keys(vietnam_location[value]['quan-huyen']).map((key) => {
        return (
          <Option key={key} value={key}>
            {vietnam_location[value]['quan-huyen'][key].name_with_type}
          </Option>
        );
      });
      form.setFieldValue('district', null);
      form.setFieldValue('ward', null);
      setStateOption(new_state);
    } else {
      form.setFieldValue('district', null);
      form.setFieldValue('ward', null);
    }
  };

  const handleChangeState = (value: any) => {
    if (value) {
      const city = form.getFieldValue('province');
      if (city) {
        const new_ward: any = Object.keys(
          vietnam_location[city]['quan-huyen'][value]['xa-phuong'],
        ).map((key) => {
          return (
            <Option key={key} value={key}>
              {vietnam_location[city]['quan-huyen'][value]['xa-phuong'][key].name_with_type}
            </Option>
          );
        });
        form.setFieldValue('ward', null);
        setWardOption(new_ward);
      }
    } else {
      form.setFieldValue('ward', null);
    }
  };

  const handleRemove = async (file) => {
    if (file.status === 'done') {
      try {
        await removeFile({
          fid: file.uid,
          doctype: 'iot_customer',
          docname: params.company.name,
        });
      } catch (error) {
        message.error('Delete Error,try again!');
      } finally {
      }
    }
  };

  const handleChange: UploadProps['onChange'] = (info) => {
    let newFileList = [...info.fileList];
    // 1. Limit the number of uploaded files
    // Only to show two recent uploaded files, and old ones will be replaced by the new
    // 2. Read from response and show file link
    newFileList = newFileList.map((file: any) => {
      if (file.response) {
        // Component will show file.url as link
        file.uid = file.response.name;
        file.url = generateAPIPath(
          'api/v2/file/download?file_url=' +
            file.response.file_url +
            '&dt=' +
            'iot_customer' +
            '&dn=' +
            params.company.name,
        );
        file.raw_url = file.response.file_url;
      }
      return file;
    });
    setFileList(newFileList);
    if (newFileList.length) {
      setImageUrl(newFileList[0].url);
    }
  };

  const handleUpload = async (options: any) => {
    const { onSuccess, onError, file } = options;
    try {
      setUploading(true);
      const res = await uploadFile({
        file,
        doctype: 'iot_customer',
        docname: params.company.name,
        is_private: 1,
        folder: 'Home/Attachments',
        optimize: false,
      });
      onSuccess(res.message);
    } catch (err) {
      console.log('Eroor: ', err);
      onError({ err });
    } finally {
      setUploading(true);
    }
  };

  const uploadButton = (
    <div>
      {uploading ? <LoadingOutlined /> : <PlusOutlined />}
      <div style={{ marginTop: 8 }}>Upload</div>
    </div>
  );

  return (
    <>
      <Form
        layout="horizontal"
        labelCol={{ span: 24 }}
        labelAlign="left"
        form={form}
        onFinish={async (value: any) => {
          try {
            let { customer_name, email, phone, province, district, ward, address, description } =
              value;
            let province_str,
              district_str,
              ward_str = '';
            province_str = vietnam_location[province].name_with_type;
            if (district)
              district_str = vietnam_location[province]['quan-huyen'][district]['name_with_type'];

            if (ward)
              ward_str =
                vietnam_location[province]['quan-huyen'][district]['xa-phuong'][ward][
                  'name_with_type'
                ];

            province = province_str;
            district = district_str;
            ward = ward_str;
            let logo = '';

            if (fileList.length) {
              logo = fileList[0].raw_url;
            }

            await generalUpdate('iot_customer', params.company.name, {
              data: {
                name: params.company.name,
                customer_name,
                email,
                phone,
                province,
                district,
                ward,
                address,
                description,
                logo,
              },
            });
            message.success('Success!');
            if (params.refreshFnc) {
              await params.refreshFnc();
            }
          } catch (error: any) {
            message.error(error.toString());
          } finally {
            setLoading(false);
          }
        }}
      >
        <Row gutter={5}>
          <Col className="gutter-row" md={24}>
            <Item label="Company Logo" labelCol={{ span: 24 }} name="logo">
              <Upload
                name="upload-logo"
                listType="picture-card"
                className="avatar-uploader"
                showUploadList={false}
                customRequest={handleUpload}
                onRemove={handleRemove}
                onChange={handleChange}
                accept="image/png, image/jpeg, image/svg+xml"
              >
                {imageUrl ? (
                  <img src={imageUrl} alt="avatar" style={{ width: '100%' }} />
                ) : (
                  uploadButton
                )}
              </Upload>
            </Item>
          </Col>
          <Col className="gutter-row" md={6}>
            <Item
              label="Company ID"
              labelCol={{ span: 24 }}
              rules={[
                {
                  required: true,
                  message: 'Bắt buộc điền',
                },
              ]}
              name="name"
            >
              <Input disabled />
            </Item>
          </Col>
          <Col className="gutter-row" md={6}>
            <Item
              label="Tên"
              labelCol={{ span: 24 }}
              rules={[
                {
                  required: true,
                  message: 'Bắt buộc điền',
                },
              ]}
              name="customer_name"
            >
              <Input />
            </Item>
          </Col>

          <Col className="gutter-row" md={6}>
            <Item
              label="Email"
              labelCol={{ span: 24 }}
              // rules={[{
              //     required: true,
              //     message: 'Bắt buộc điền'
              // }]}
              name="email"
            >
              <Input />
            </Item>
          </Col>
          <Col className="gutter-row" md={6}>
            <Item
              label="Phone"
              labelCol={{ span: 24 }}
              // rules={[{
              //     required: true,
              //     message: 'Bắt buộc điền'
              // }]}
              name="phone"
            >
              <Input />
            </Item>
          </Col>
          <Col className="gutter-row" md={8}>
            <Item
              label="Province"
              labelCol={{ span: 24 }}
              rules={[
                {
                  required: true,
                  message: 'Requied',
                },
              ]}
              name="province"
            >
              <Select
                allowClear
                showSearch
                style={{
                  width: '100%',
                }}
                onChange={handleChangeCity}
                filterOption={(input, option) =>
                  toLowerCaseNonAccentVietnamese(option!.children as unknown as string).includes(
                    toLowerCaseNonAccentVietnamese(input),
                  )
                }
              >
                {Object.keys(vietnam_location).map((key) => {
                  return (
                    <Option key={key} value={key}>
                      {vietnam_location[key].name}
                    </Option>
                  );
                })}
              </Select>
            </Item>
          </Col>
          <Col className="gutter-row" md={8}>
            <Item label="District" labelCol={{ span: 24 }} name="district">
              <Select
                allowClear
                showSearch
                style={{
                  width: '100%',
                }}
                onChange={handleChangeState}
                filterOption={(input, option) =>
                  toLowerCaseNonAccentVietnamese(option!.children as unknown as string).includes(
                    toLowerCaseNonAccentVietnamese(input),
                  )
                }
              >
                {stateOption}
              </Select>
            </Item>
          </Col>
          <Col className="gutter-row" md={8}>
            <Item label="Ward" labelCol={{ span: 24 }} name="ward">
              <Select
                allowClear
                showSearch
                style={{
                  width: '100%',
                }}
                placeholder="Chọn phường/xã"
                filterOption={(input, option) =>
                  toLowerCaseNonAccentVietnamese(option!.children as unknown as string).includes(
                    toLowerCaseNonAccentVietnamese(input),
                  )
                }
              >
                {wardOption}
              </Select>
            </Item>
          </Col>
          <Col className="gutter-row" md={24}>
            <Item
              label="Address"
              labelCol={{ span: 24 }}
              rules={[
                {
                  required: true,
                  message: 'Nhập địa chỉ',
                },
              ]}
              name="address"
            >
              <Input />
            </Item>
          </Col>
          <Col className="gutter-row" md={24}>
            <Item label="Description" labelCol={{ span: 24 }} name="description">
              <TextArea rows={5} placeholder="maxLength is 100" maxLength={100} />
            </Item>
          </Col>
          <Col className="gutter-row" md={24}>
            <Button loading={loading} type="primary" htmlType="submit">
              Save
            </Button>
          </Col>
        </Row>
      </Form>
    </>
  );
};

export default UpdateCustomerForm;
