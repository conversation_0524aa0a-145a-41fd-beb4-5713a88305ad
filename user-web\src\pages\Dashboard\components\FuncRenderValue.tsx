import { getDataTimeSeries, getLatestDataFunction } from '@/services/devices';
import { sscript } from '@/services/sscript';
import { IIotProductionFunction } from '@/types/IIotProductionFunction';
import { DownloadOutlined } from '@ant-design/icons';
import { PageContainer } from '@ant-design/pro-components';
import { AxiosError, useModel } from '@umijs/max';
import { Button, Card, Col, ConfigProvider, DatePicker, Form, Input, InputNumber, message, Result, Row, Select, Spin, Switch, Tabs } from 'antd';
import React, { useEffect, useMemo, useState } from 'react';
import ContentCard from './ContentCard';
import * as FileSaver from 'file-saver';
import * as XLSX from 'xlsx';
import moment from 'moment';
import FuncConfigModal from './FuncConfigModal';
const { Item } = Form;

export const downloadExcelData = (reqData: any) => {

    if (reqData.length < 1)
        return;
    const heades = Object.keys(reqData[0]);
    let test = [];
    test.push(heades);
    for (let i = 0; i < reqData.length; i++) {
        test.push(Object.values(reqData[i]));
    }
    const fileType = 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet;charset=UTF-8';
    const fileExtension = '.xlsx';
    const ws = XLSX.utils.json_to_sheet(test, { skipHeader: true });
    const wb = { Sheets: { 'data': ws }, SheetNames: ['data'] };
    const excelBuffer = XLSX.write(wb, { bookType: 'xlsx', type: 'array' });
    const data = new Blob([excelBuffer], { type: fileType });
    FileSaver.saveAs(data, "Sensor Report" + fileExtension);
}

const FuncRenderValue = ({ fnc }: { fnc: IIotProductionFunction }) => {
    const [downloading, setDownloading] = useState(false);
    const {
        timeRange,
        latestDeviceValue
    } = useModel('MyResource');

    const downloadData = async () => {
        try {
            setDownloading(true);
            const result = await getDataTimeSeries({
                agg: 'NONE',
                startTs: timeRange[0].unix() * 1000,
                endTs: timeRange[1].unix() * 1000,
                keys: fnc.identifier || "",
                device_id_thingsboard: fnc.device_id_thingsboard || "",
                limit: 1000000
            });
            let data = result[fnc.identifier || ""];
            if (data.length) {
                data = data.map((d: any) => {
                    d.value = parseFloat(d.value.toString());
                    return {
                        "Label": d.label,
                        "Time": moment(d.ts).format("HH:mm:ss DD/MM/YYYY"),
                        "Value": d.value,
                        "Unit": d.unit
                    }
                });
                downloadExcelData(data);
            }
            else {
                message.warning("Cảm biến không có dữ liệu trong thời gian này");
            }
        } catch (error) {

        } finally {
            setDownloading(false);
        }
    }

    return (
        <Row gutter={[5, 5]}>
            <Card
                style={(fnc.show_chart || fnc.data_permission === "r") ? {
                    width: '100%',
                    height: '100%',
                    boxSizing: 'border-box',
                    minHeight: '320px',
                    position: 'relative',
                    maxHeight: '600px'
                } : {}}
                type="inner"
                size="small"
                title={fnc.label}
                extra={
                    <>
                        <Button
                            loading={downloading}
                            type="ghost"
                            size="small"
                            onClick={async (e) => {
                                console.log("download")
                                await downloadData()
                            }}
                        >
                            <DownloadOutlined />
                        </Button>
                        {/* <FuncConfigModal refreshFnc={() => { location.reload(); }} fnc={fnc} /> */}
                    </>
                }

            >
                <ContentCard
                    title={fnc.label}
                    initData={[]}
                    openDialog={false}
                    displayTable={{
                        display: true,
                        displayMin: true,
                        displayMax: true,
                        displayAvg: true,
                    }}
                    sensor={fnc}
                />
                {/* <button onClick={showModal}>Open Full Screen Modal</button> */}
            </Card>
        </Row>
    );

};

export default FuncRenderValue;
