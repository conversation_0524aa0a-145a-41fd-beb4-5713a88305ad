import { listAllItem } from '@/services/products';
import { listAllUnit } from '@/services/unit';
import { PlusOutlined } from '@ant-design/icons';
import { ProFormSelect } from '@ant-design/pro-components';
import { FormattedMessage } from '@umijs/max';
import { Button, Col, Form, Input, InputNumber, Modal, Row } from 'antd';
import { useState } from 'react';
const { Item } = Form;

const CreateTaskProduction = (params: { onFinish: any }) => {
  const [loading, setLoading] = useState(false);
  const [isOpen, setOpen] = useState(false);

  const [slectedUnit, setSlectedUnit] = useState();
  const [selectedPackingUnit, setSelectedPackingUnit] = useState();

  const [units, setAllUnits] = useState([]);
  const [packingUnits, setPackingsUnits] = useState([]);
  const [unitSelectes, setUnitSelectes] = useState();

  const [form] = Form.useForm();

  const showModal = () => {
    setOpen(true);
  };
  const hideModal = () => {
    setOpen(false);
  };
  const handleOk = () => {
    form.submit();
  };
  const handleCancel = () => {
    hideModal();
    form.resetFields();
  };

  return (
    <>
      <Button type="primary" onClick={showModal} style={{ display: 'flex', alignItems: 'center' }}>
        <PlusOutlined></PlusOutlined>Thêm sản lượng
      </Button>
      <Modal
        title={`Thêm công việc con`}
        open={isOpen}
        onOk={handleOk}
        onCancel={handleCancel}
        confirmLoading={loading}
      >
        <Form
          layout="horizontal"
          labelCol={{ span: 24 }}
          labelAlign="left"
          form={form}
          onFinish={async (values: any) => {
            values._unit = units.find((d: any) => d.name === values.unit);
            values._packingUnit = packingUnits.find((d: any) => d.name === values.packing_unit);
            console.log('values', values);
            await params?.onFinish(values);
            setOpen(false);
            form.resetFields();
          }}
        >
          <Row gutter={5}>
            <Col className="gutter-row" md={24}>
              <ProFormSelect
                rules={[
                  {
                    required: true,
                    message: 'Bắt buộc điền',
                  },
                ]}
                allowClear
                label="Sản phẩm thu hoạch"
                name="product_id"
                request={async () => {
                  const res = await listAllItem();
                  const unitsRes = await listAllUnit();
                  setAllUnits(unitsRes.data);
                  return res.data.map((item: any) => {
                    const unit = unitsRes.data?.find((d) => d.name === item.unit_id);

                    return {
                      label: item.label + `${unit ? ' - ' + unit?.label : ''}`,
                      value: item.name,
                    };
                  });
                }}
              />
            </Col>
            <Col className="gutter-row" md={12}>
              <Item label="Sản lượng/số lượng dự kiến" labelCol={{ span: 24 }} name="exp_quantity">
                <InputNumber style={{ width: '100%' }} min={0} />
              </Item>
            </Col>
            <Col className="gutter-row" md={12}>
              <Item label="Sản lượng/số lượng thực tế" labelCol={{ span: 24 }} name="quantity">
                <InputNumber style={{ width: '100%' }} min={0} />
              </Item>
            </Col>
            <Col className="gutter-row" md={24}>
              <Item
                label={<FormattedMessage id={'common.form.description'} />}
                labelCol={{ span: 24 }}
                name="description"
              >
                <Input.TextArea />
              </Item>
            </Col>
          </Row>
        </Form>
      </Modal>
    </>
  );
};

export default CreateTaskProduction;
