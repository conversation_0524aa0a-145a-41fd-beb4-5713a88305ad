import { DOCTYPE_ERP } from '@/common/contanst/constanst';
import { Business, getBusinessList } from '@/services/diary-2/business';
import { useRequest } from '@umijs/max';

export default function useDetail(
  { id, onSuccess } = {} as {
    onSuccess?: (data: Business) => void;
    id: string;
  },
) {
  return useRequest(
    async () => {
      if (!id) return null;
      const res = await getBusinessList({
        filters: [[DOCTYPE_ERP.iotDiaryV2Business, 'name', '=', id]],
        page: 1,
        size: 1,
      });
      const data = res?.data?.[0];
      if (!data) throw new Error('Not found');
      // const members = await getMemberList({
      //   page: 1,
      //   size: DEFAULT_PAGE_SIZE_ALL,
      //   filters: [[DOCTYPE_ERP.iot_diary_v2_business_member, 'business_id', '=', id]],
      // });
      return {
        data: {
          ...data,
          // members: members.data,
        },
      };
    },
    {
      onSuccess: (data) => {
        if (data) onSuccess?.(data);
      },
      refreshDeps: [id],
    },
  );
}
