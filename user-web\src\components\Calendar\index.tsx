import { FC, useCallback, useState } from 'react';

import { Calendar, CalendarProps, dateFnsLocalizer, Event, View, Views } from 'react-big-calendar';

import { format, getDay, parse, startOfWeek } from 'date-fns';
import './index.less';

const locales = {
  'en-US': require('date-fns'),
};
const localizer = dateFnsLocalizer({
  format,
  parse,
  startOfWeek: () => startOfWeek(new Date(), { weekStartsOn: 1 }),
  getDay,
  locales,
});

interface AxcelaBigCalendarProps extends Omit<CalendarProps, 'localizer'> {}

export interface AxcelaBigCalendarEvent extends Event {
  id?: string;
  resourceId?: string;
  color?: string;
  [key: string]: any;
}

const AxcelaBigCalendar: FC<AxcelaBigCalendarProps> = (props: AxcelaBigCalendarProps) => {
  const [view, setView] = useState<View>(Views.WEEK);

  const onView = useCallback((newView: View) => setView(newView), [setView]);

  return (
    <div className="lms-axcela-big-calendar">
      <Calendar
        min={new Date(new Date().getFullYear(), new Date().getMonth(), new Date().getDate(), 7)}
        onView={onView}
        view={view}
        {...props}
        style={{
          minHeight: '600px',
        }}
        localizer={localizer}
      />
    </div>
  );
};

export default AxcelaBigCalendar;
