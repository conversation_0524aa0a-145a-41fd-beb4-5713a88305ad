import ActionPopConfirm from '@/components/ActionPopConfirm';
import { removeTask } from '@/services/TaskAndTodo';
import { useAccess } from '@umijs/max';
import { message } from 'antd';

const DeleteMultiTask = (params: { refreshFnc: any; tasks_id: string[] }) => {
  const handlerDeleteData = async () => {
    try {
      for (let i = 0; i < params.tasks_id.length; i++) {
        await removeTask(params.tasks_id[i]);
      }
    } catch (error: any) {
      message.error(error.toString());
    } finally {
      if (params.refreshFnc) {
        await params.refreshFnc();
      }
    }
  };
  const access = useAccess();
  if (!access.canDeleteAllInPageAccess()) {
    return null;
  }
  return (
    <ActionPopConfirm
      actionCall={handlerDeleteData}
      refreshData={params.refreshFnc}
      text={'Delete'}
      buttonType={'primary'}
      danger={true}
    ></ActionPopConfirm>
  );
};

export default DeleteMultiTask;
