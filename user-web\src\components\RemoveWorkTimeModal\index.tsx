import { Modal } from 'antd';
import { FC, useState } from 'react';
type RemoveWorkTimeModalPropsTypes = {
  show: boolean;
  onClose?: () => void;
  onOk?: () => void;
  on: string;
  from: string;
  to: string;
};
const RemoveWorkTimeModal: FC<RemoveWorkTimeModalPropsTypes> = (props) => {
  const { show, onClose, on, from, to, onOk } = props;
  const [isOpen, setOpen] = useState<boolean>(show);
  return (
    <Modal
      open={isOpen}
      title="Remove working time"
      onCancel={() => {
        if (onClose) {
          onClose();
        }
        setOpen(false);
      }}
      onOk={() => {
        if (onOk) {
          onOk();
        }
        setOpen(false);
      }}
    >
      <div>
        Do you want to remove work time on {on} from {from} - {to}
      </div>
    </Modal>
  );
};

export default RemoveWorkTimeModal;
