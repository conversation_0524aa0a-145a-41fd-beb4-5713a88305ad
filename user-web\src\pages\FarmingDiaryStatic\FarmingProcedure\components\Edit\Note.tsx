import { useProFormList } from '@/components/Form/Config/pro-form-list';
import { getNoteList } from '@/services/diary-2/note';
import { PlusOutlined } from '@ant-design/icons';
import {
  ProForm,
  ProFormGroup,
  ProFormList,
  ProFormSelect,
  ProFormText,
} from '@ant-design/pro-components';
import { history, useIntl, useParams, useRequest } from '@umijs/max';
import { Button, Card } from 'antd';
import { FC, ReactNode, useCallback } from 'react';

interface NoteFormProps {
  children?: ReactNode;
}

const NoteContent = ({ form, index }: { form: any; index: number }) => {
  const { formatMessage } = useIntl();
  const { data: notes, loading: notesLoading } = useRequest(() =>
    getNoteList({
      page: 1,
      size: 10000,
      order_by: 'name asc',
    }),
  );

  const handleNoteChange = useCallback(
    (selectedNoteName: string) => {
      const selectedNote = notes?.find((note) => note.name === selectedNoteName);
      if (!selectedNote) return;

      const currentNotes = form.getFieldValue('notes') || [];
      const updatedNotes = currentNotes.map((note: any, noteIndex: number) =>
        note.name === selectedNoteName
          ? {
              ...note,
              idx: noteIndex + 1, // Add idx if it doesn't exist
              product_label: selectedNote.product_label,
              description: selectedNote.description,
            }
          : note,
      );

      form.setFieldsValue({ notes: updatedNotes });
    },
    [notes, form],
  );

  return (
    <ProFormGroup>
      <ProFormSelect
        width="md"
        required
        onChange={handleNoteChange}
        fieldProps={{ loading: notesLoading }}
        options={notes?.map((note) => ({
          label: note.label,
          value: note.name,
        }))}
        name={`name`}
        label={`${index + 1}. ${formatMessage({ id: 'common.note' })}`}
        showSearch
      />
      <ProFormText
        disabled
        label={formatMessage({ id: 'common.product' })}
        name={`product_label`}
        width="sm"
      />
      <ProFormText
        disabled
        label={formatMessage({ id: 'common.description' })}
        name={`description`}
        width="sm"
      />
    </ProFormGroup>
  );
};

const NoteForm: FC<NoteFormProps> = () => {
  const { formatMessage } = useIntl();
  const { id } = useParams();
  const form = ProForm.useFormInstance();
  const formListProps = useProFormList();

  const renderCreateNoteButton = () => (
    <Button
      onClick={() => {
        history.replace('/farming-diary-static/note/create', {
          fromProcedureEdit: true,
          id: id,
        });
      }}
      icon={<PlusOutlined />}
      type="default"
    >
      {formatMessage({ id: 'common.create-note' })}
    </Button>
  );

  return (
    <Card title={formatMessage({ id: 'common.note' })} extra={renderCreateNoteButton()}>
      <ProFormList name="notes" {...formListProps}>
        {({ key }) => <NoteContent form={form} index={key} />}
      </ProFormList>
    </Card>
  );
};

export default NoteForm;
