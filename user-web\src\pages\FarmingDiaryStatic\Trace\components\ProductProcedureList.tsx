import { DEFAULT_DATE_FORMAT_WITHOUT_TIME } from '@/common/contanst/constanst';
import { getTraceList } from '@/services/diary-2/trace';
import { getUOM_v3 } from '@/services/InventoryManagementV3/uom';
import { convertDateToDesireFormat } from '@/utils/moment';
import { QrcodeOutlined } from '@ant-design/icons';
import { ActionType, ProTable } from '@ant-design/pro-components';
import { Link, useIntl } from '@umijs/max';
import { Button, Col, Modal, QRCode, Row, Tooltip } from 'antd';
import { FC, ReactNode, useRef, useState } from 'react';
import DeleteTrace from './DeleteTrace';

interface ProductProcedureListProps {
  children?: ReactNode;
}

const ProductProcedureList: FC<ProductProcedureListProps> = ({ children }) => {
  const actionRef = useRef<ActionType>();
  const handleReload = () => {
    actionRef.current?.reload?.();
  };
  const { formatMessage } = useIntl();
  const [qrModalVisible, setQrModalVisible] = useState(false);
  const [qrCodeValue, setQrCodeValue] = useState('');

  const handleQrClick = (token: string) => {
    const serverURL = 'http://truyxuat.viis.tech'; // Replace with your actual server URL
    const qrValue = `${serverURL}?token=${token}`;
    setQrCodeValue(qrValue);
    setQrModalVisible(true);
  };

  return (
    <>
      <ProTable
        actionRef={actionRef}
        search={{
          labelWidth: 'auto',
        }}
        scroll={{ x: 1000 }}
        rowKey={'name'}
        columns={[
          {
            title: 'STT',
            index: 1,
            render(dom, entity: any, index) {
              return index + 1;
            },
            hideInSearch: true,
            width: 40,
          },
          {
            title: formatMessage({ id: 'common.batch_code' }),
            dataIndex: 'batch_code',
            render(dom, entity: any) {
              return (
                <Link to={`/farming-diary-static/trace/edit/${entity.name}`}>
                  <Button type="link" style={{ padding: 0 }}>
                    {' '}
                    {dom}
                  </Button>
                </Link>
              );
            },
            width: 40,
            fixed: 'left',
          },
          {
            title: formatMessage({ id: 'common.starting_number' }),
            dataIndex: 'start_number',
            width: 40,
            hideInSearch: true,
          },
          {
            title: formatMessage({ id: 'common.ending_number' }),
            dataIndex: 'end_number',
            width: 40,
            hideInSearch: true,
          },
          {
            title: formatMessage({ id: 'common.company_name' }),
            dataIndex: 'business_label',
            width: 80,
          },
          {
            title: formatMessage({ id: 'common.product_name' }),
            dataIndex: 'product_label',
            width: 80,
          },
          {
            title: formatMessage({ id: 'common.farming_process' }),
            dataIndex: 'agri_process_label',
            hideInSearch: true,
            width: 80,
          },
          {
            title: formatMessage({ id: 'common.quantity' }),
            dataIndex: 'quantity',
            render(dom) {
              return `${dom}`;
            },
            width: 40,
            hideInSearch: true,
          },
          {
            title: formatMessage({ id: 'common.packing_unit' }),
            dataIndex: 'packing_unit',
            render(dom, entity: any) {
              return `${entity.packing_unit_name}`;
            },
            valueType: 'select',
            request: async () => {
              const res = await getUOM_v3();
              return res.data.map((item) => ({
                label: item.uom_name,
                value: item.name,
              }));
            },
            fieldProps: {
              showSearch: true,
            },
            width: 40,
            // hideInSearch: true,
          },
          {
            title: formatMessage({ id: 'common.date_of_manufacture' }),
            dataIndex: 'production_date',
            valueType: 'dateRange',
            render(dom, entity: any) {
              return `${convertDateToDesireFormat({
                date: entity.production_date,
                toFormat: DEFAULT_DATE_FORMAT_WITHOUT_TIME,
              })}`;
            },
            width: 40,
          },
          {
            title: formatMessage({ id: 'common.expiration_date' }),
            dataIndex: 'expiration_date',
            valueType: 'dateRange',
            render(dom, entity: any) {
              return `${convertDateToDesireFormat({
                date: entity.expiration_date,
                toFormat: DEFAULT_DATE_FORMAT_WITHOUT_TIME,
              })}`;
            },
            width: 40,
          },
          {
            hideInSearch: true,
            render(dom, entity: any) {
              return (
                <Row gutter={24}>
                  <Col span={12}>
                    <DeleteTrace id={entity.name} onSuccess={handleReload} />
                  </Col>
                  <Col span={12}>
                    <Tooltip title={formatMessage({ id: 'common.origin_list' })}>
                      <Button
                        size="small"
                        type="default"
                        icon={<QrcodeOutlined size={100} className="text-emerald-600" />}
                        onClick={() => handleQrClick(entity.token)}
                      />
                    </Tooltip>
                  </Col>
                </Row>
              );
            },
            width: 20,
          },
        ]}
        toolBarRender={() => [
          <Link to="/farming-diary-static/trace/create" key="create">
            <Button type="primary">
              {formatMessage({
                id: 'common.create_traceability',
              })}
            </Button>
          </Link>,
        ]}
        request={async (params: any, sort, filter) => {
          const paramsReq: any = {
            page: params.current,
            size: params.pageSize,
            fields: '*',
            filters: [],
            or_filters: '',
            order_by: 'name asc',
            group_by: '',
          };
          if (params.business_label) {
            paramsReq.filters.push([
              'iot_diary_v2_business',
              'label',
              'like',
              `%${params.business_label}%`,
            ]);
          }
          if (params.product_label) {
            paramsReq.filters.push([
              'iot_diary_v2_agri_product',
              'label',
              'like',
              `%${params.product_label}%`,
            ]);
          }
          if (params.production_date) {
            paramsReq.filters.push([
              'iot_diary_v2_traceability_batch',
              'production_date',
              '>=',
              params.production_date[0],
            ]);
            paramsReq.filters.push([
              'iot_diary_v2_traceability_batch',
              'production_date',
              '<=',
              params.production_date[1],
            ]);
          }
          if (params.expiration_date) {
            paramsReq.filters.push([
              'iot_diary_v2_traceability_batch',
              'expiration_date',
              '>=',
              params.expiration_date[0],
            ]);
            paramsReq.filters.push([
              'iot_diary_v2_traceability_batch',
              'expiration_date',
              '<=',
              params.expiration_date[1],
            ]);
          }
          if (params.batch_code) {
            paramsReq.filters.push([
              'iot_diary_v2_traceability_batch',
              'batch_code',
              'like',
              `%${params.batch_code}%`,
            ]);
          }
          if (params.packing_unit) {
            paramsReq.filters.push([
              'iot_diary_v2_agri_product',
              'packing_unit',
              'like',
              `%${params.packing_unit}%`,
            ]);
          }
          const res = await getTraceList(paramsReq);

          return {
            data: res.data,
            total: res.pagination.totalElements,
          };
        }}
      />

      <Modal
        title="QR Code"
        open={qrModalVisible}
        onCancel={() => setQrModalVisible(false)}
        footer={null}
        centered
        style={{
          textAlign: 'center',
        }}
      >
        <div
          id="myqrcode"
          style={{
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center',
            flexDirection: 'column',
            height: '100%',
          }}
        >
          <QRCode size={350} value={qrCodeValue} bgColor="#fff" style={{ paddingBlock: 20 }} />
          <div style={{ marginTop: '10px' }}>
            <a href={qrCodeValue} target="_blank" rel="noopener noreferrer">
              {qrCodeValue.substring(0, 100)}
            </a>
          </div>
        </div>
      </Modal>
    </>
  );
};

export default ProductProcedureList;
