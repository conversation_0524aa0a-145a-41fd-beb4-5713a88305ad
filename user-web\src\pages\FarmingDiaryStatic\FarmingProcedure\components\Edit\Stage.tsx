import { useProFormList } from '@/components/Form/Config/pro-form-list';
import { getStageList } from '@/services/diary-2/stage';
import { PlusOutlined } from '@ant-design/icons';
import {
  ProForm,
  ProFormDigit,
  ProFormGroup,
  ProFormList,
  ProFormSelect,
} from '@ant-design/pro-components';
import { history, useIntl, useParams, useRequest } from '@umijs/max';
import { Button, Card } from 'antd';
import { FC, ReactNode, useCallback } from 'react';

interface StageProps {
  children?: ReactNode;
}

const StageFormContent = ({ form, index }: { form: any; index: number }) => {
  const { formatMessage } = useIntl();
  const { data: stages, loading: stagesLoading } = useRequest(getStageList);

  const handleStageChange = useCallback(
    (selectedStageName: string) => {
      const selectedStage = stages?.find((stage) => stage.name === selectedStageName);
      if (!selectedStage) return;

      const currentStates = form.getFieldValue('states') || [];
      const updatedStates = currentStates.map((state: any, stateIndex: number) =>
        state.name === selectedStageName
          ? {
              ...state,
              idx: stateIndex + 1, // Add idx if it doesn't exist
              task_count: selectedStage.task_count,
              expire_time_in_days: selectedStage.expire_time_in_days,
            }
          : state,
      );

      form.setFieldsValue({ states: updatedStates });
    },
    [stages, form],
  );

  return (
    <ProFormGroup>
      <ProFormSelect
        width="md"
        required
        onChange={handleStageChange}
        fieldProps={{ loading: stagesLoading }}
        options={stages?.map((stage) => ({
          label: stage.label,
          value: stage.name,
        }))}
        name={`name`}
        label={`${index + 1}. ${formatMessage({ id: 'common.stage' })}`}
        showSearch
      />
      <ProFormSelect
        disabled
        label={formatMessage({ id: 'common.task' })}
        name={`task_count`}
        width="sm"
      />
      <ProFormDigit
        disabled
        label={formatMessage({ id: 'common.expire_time_in_days' })}
        width="sm"
        name={`expire_time_in_days`}
        fieldProps={{
          suffix: formatMessage({ id: 'common.days' }),
        }}
      />
    </ProFormGroup>
  );
};

const Stage: FC<StageProps> = () => {
  const { formatMessage } = useIntl();
  const { id } = useParams();
  const form = ProForm.useFormInstance();
  const formListProps = useProFormList();

  const renderCreateStageButton = () => (
    <Button
      onClick={() => {
        history.replace('/farming-diary-static/stage-of-crop/create', {
          fromProcedureEdit: true,
          id: id,
        });
      }}
      icon={<PlusOutlined />}
      type="default"
    >
      {formatMessage({ id: 'common.create-stage' })}
    </Button>
  );

  return (
    <Card title={formatMessage({ id: 'common.stage' })} extra={renderCreateStageButton()}>
      <ProFormList name="states" {...formListProps}>
        {({ key }) => <StageFormContent form={form} index={key} />}
      </ProFormList>
    </Card>
  );
};

export default Stage;
