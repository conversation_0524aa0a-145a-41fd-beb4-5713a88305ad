import { isSubscribedStock } from '@/access';
import { COLOR_HEX } from '@/common/contanst/constanst';
import RealityColumnRender from '@/components/RealityColumnRender';
import MaterialIssue from '@/components/StockEntryButtons/ProductionTaskButtons/MaterialIssueVoucher';
import MaterialReceipt from '@/components/StockEntryButtons/ProductionTaskButtons/MaterialReceiptVoucher';
import MaterialTransfer from '@/components/StockEntryButtons/ProductionTaskButtons/MaterialTransferVoucher';
import QuickMaterialTransfer from '@/components/StockEntryButtons/ProductionTaskButtons/QuickMaterialTransferVoucher';
import { getProductItemV3 } from '@/services/InventoryManagementV3/product-item';
import { updateTaskProductionListSQL } from '@/services/products';
import { getAllTaskProduction } from '@/services/TaskAndTodo';
import { useTaskProductionStore } from '@/stores/TaskProductionUpdateStore';
import { IIotCategory } from '@/types/IIotCategory';
import { IIotProductionQuantity, UOM } from '@/types/IIotProductionQuantity';
import { DownOutlined, InfoCircleOutlined } from '@ant-design/icons';
import type { ActionType, ProColumns } from '@ant-design/pro-components';
import { EditableProTable } from '@ant-design/pro-components';
import { FormattedMessage, useAccess, useIntl } from '@umijs/max';
import {
  Button,
  Dropdown,
  InputNumber,
  MenuProps,
  message,
  Select,
  Skeleton,
  Space,
  Tooltip,
} from 'antd';
import React, { useEffect, useRef, useState } from 'react';
import CreateProductionUpdateView from './CreateProductionUpdateView';
import DeleteItemForTask from './DeleteItemForTask';

const fetchTaskProduction = async (
  task_id: string,
  items: IIotCategory[],
  setDataSource: any,
  setLoading: any,
) => {
  try {
    setLoading(true);
    const resData = await getAllTaskProduction(task_id);
    setDataSource(
      resData.data.map((d: any) => {
        d.item = items.find((el: any) => el.name === d.product_id);
        return {
          ...d,
          quantity: d.quantity,
          exp_quantity: d.exp_quantity,
          loss_quantity: d.loss_quantity,
          issued_quantity: d.issued_quantity,
          total_qty_in_crop: d.total_qty_in_crop,
        };
      }),
    );
  } catch (error: any) {
    message.error(error.toString());
  } finally {
    setLoading(false);
  }
};

const fetchProductItems = async (setItems: any, setLoading: any) => {
  try {
    setLoading(true);
    const resData = await getProductItemV3();
    setItems(resData.data);
  } catch (error: any) {
    message.error(error.toString());
  } finally {
    setLoading(false);
  }
};

const handleSaveItems = async (dataSource: any, setIsSaving: any, setRefreshKey: any) => {
  try {
    const updateTaskItemList: IIotProductionQuantity[] = dataSource.map((data: any) => ({
      name: data.name,
      description: data.description,
      task_id: data.task_id,
      product_id: data.product_id,
      exp_quantity: data.exp_quantity * data.conversion_factor,
      draft_quantity: data.draft_quantity * data.conversion_factor,
    }));
    await updateTaskProductionListSQL(updateTaskItemList);
    setRefreshKey((prev: any) => prev + 1); // Thêm dòng này để cập nhật refreshKey
    message.success('Lưu sản lượng thành công');
  } catch (error: any) {
    message.error('Đã có lỗi xảy ra');
  } finally {
    setIsSaving(false);
  }
};

const RealityColumnNumberRender = ({ value, onChange }: any) => {
  return (
    <RealityColumnRender>
      <InputNumber
        value={value}
        onChange={onChange}
        disabled
        style={{ width: '100%' }}
        formatter={(value) => `${value}`.replace(/\B(?=(\d{3})+(?!\d))/g, ',')}
        parser={(value) => value!.replace(/\$\s?|(,*)/g, '')}
        step="0.01"
      />
    </RealityColumnRender>
  );
};

export default ({ task_id, isTemplateTask }: { task_id: string; isTemplateTask: boolean }) => {
  const [refreshKey, setRefreshKey] = useState(0);
  const { dataSource, setDataSource } = useTaskProductionStore();
  const tempDataSourceRef = useRef(dataSource);
  const [isSaving, setIsSaving] = useState(false);
  const [items, setItems] = useState<IIotCategory[]>([]);
  const actionRef = useRef<ActionType>();
  const [editableKeys, setEditableRowKeys] = useState<React.Key[]>(
    dataSource.map((data) => data.name),
  );
  const [loading, setLoading] = useState(false);

  useEffect(() => {
    fetchProductItems(setItems, setLoading);
  }, []);
  useEffect(() => {
    if (items.length) fetchTaskProduction(task_id, items, setDataSource, setLoading);
  }, [items, refreshKey]); // Thêm refreshKey vào dependencies

  useEffect(() => {
    if (isSaving) handleSaveItems(dataSource, setIsSaving, setRefreshKey);
    setEditableRowKeys(dataSource.map((data) => data.name));
    tempDataSourceRef.current = [...dataSource];
    actionRef.current?.reload();
  }, [dataSource, isSaving]);

  const handleStoreItem = () => {
    setIsSaving(true);
    setDataSource(tempDataSourceRef.current);
  };

  const handleUOMChange = (value: string, record: IIotProductionQuantity, uoms: UOM[]) => {
    const selectedUOM = uoms.find((uom) => uom.uom_id === value);
    const baseUOM = uoms.find((uom) => uom.conversion_factor === 1);
    if (selectedUOM && baseUOM) {
      const newConversionFactor = selectedUOM.conversion_factor;
      const ratio = baseUOM.conversion_factor / selectedUOM.conversion_factor;

      if (record.original_quantity === undefined) {
        record.original_quantity = record.quantity;
      }
      if (record.original_exp_quantity === undefined) {
        record.original_exp_quantity = record.exp_quantity;
      }
      if (record.original_draft_quantity === undefined) {
        record.original_draft_quantity = record.draft_quantity;
      }
      if (record.original_issued_quantity === undefined) {
        record.original_issued_quantity = record.issued_quantity;
      }

      if (record.original_finished_quantity === undefined) {
        record.original_finished_quantity = record.finished_quantity;
      }
      if (record.original_total_qty_in_crop === undefined) {
        record.original_total_qty_in_crop = record.total_qty_in_crop;
      }

      const updatedRecord = {
        ...record,
        conversion_factor: newConversionFactor,
        uom_id: selectedUOM.uom_id,
        uom_label: selectedUOM.uom_label,
        quantity: record.original_quantity! * ratio,
        exp_quantity: record.original_exp_quantity! * ratio,
        draft_quantity: record.original_draft_quantity! * ratio,
        issued_quantity: record.original_issued_quantity! * ratio,
        finished_quantity: record.original_finished_quantity! * ratio,
        total_qty_in_crop: record.original_total_qty_in_crop! * ratio,
      };

      const updatedDataSource = tempDataSourceRef.current.map((item) =>
        item.name === record.name ? updatedRecord : item,
      );

      tempDataSourceRef.current = updatedDataSource;
      setDataSource(updatedDataSource);
    }
  };

  const access = useAccess();
  const canCreateTask = access.canCreateInWorkFlowManagement();
  const canUpdateTask = access.canUpdateInWorkFlowManagement();
  const canDeleteTask = access.canDeleteInWorkFlowManagement();

  const columns: ProColumns<IIotProductionQuantity>[] = [
    {
      title: <FormattedMessage id="category.material-management.category_code" />,
      editable: false,
      render: (_, entity: any) => <>{entity.item?.item_name}</>,
      width: 100,
      fixed: 'left',
    },
    {
      title: <FormattedMessage id="category.material-management.category_name" />,
      editable: false,
      render: (_, entity: any) => <>{entity.item?.label}</>,
      width: 100,
      fixed: 'left',
    },
    {
      title: <FormattedMessage id="common.unit" />,
      dataIndex: 'uom_id',
      renderFormItem: (_, record) => (
        <Select
          defaultValue={record.record?.uom_id}
          onChange={(value) => handleUOMChange(value, record.record!, record.record?.uoms || [])}
        >
          {record.record?.uoms?.map((uom) => (
            <Select.Option key={uom.uom_id} value={uom.uom_id}>
              {uom.uom_label}
            </Select.Option>
          ))}
        </Select>
      ),
      fixed: 'left',
      width: 100,
    },
    {
      title: (
        <Tooltip
          color={COLOR_HEX.GREEN_TOOLTIP}
          key={COLOR_HEX.GREEN_TOOLTIP}
          title={<FormattedMessage id="tooltips.production_task_exp_quantity_description" />}
        >
          <FormattedMessage id="storage-management.category-management.expected_quantity" />{' '}
          <InfoCircleOutlined />
        </Tooltip>
      ),
      dataIndex: 'exp_quantity',
      renderFormItem: () => (
        <InputNumber
          style={{ width: '100%' }}
          formatter={(value) => `${value}`.replace(/\B(?=(\d{3})+(?!\d))/g, ',')}
          parser={(value) => value!.replace(/\$\s?|(,*)/g, '')}
          step="0.01"
        />
      ),
      width: 100,
    },
    {
      title: (
        <Tooltip
          color={COLOR_HEX.GREEN_TOOLTIP}
          key={COLOR_HEX.GREEN_TOOLTIP}
          title={<FormattedMessage id="tooltips.item_task_draft_quantity_description" />}
        >
          <FormattedMessage id="storage-management.category-management.draft_quantity" />{' '}
          <InfoCircleOutlined />
        </Tooltip>
      ),
      dataIndex: 'draft_quantity',
      renderFormItem: () => (
        <InputNumber
          style={{ width: '100%' }}
          formatter={(value) => `${value}`.replace(/\B(?=(\d{3})+(?!\d))/g, ',')}
          parser={(value) => value!.replace(/\$\s?|(,*)/g, '')}
          step="0.01"
        />
      ),
      width: 100,
    },
    {
      title: (
        <Tooltip
          color={COLOR_HEX.GREEN_TOOLTIP}
          key={COLOR_HEX.GREEN_TOOLTIP}
          title={<FormattedMessage id="tooltips.production_task_finished_quantity_description" />}
        >
          <FormattedMessage id="storage-management.category-management.used_quantity" />{' '}
          <InfoCircleOutlined />
        </Tooltip>
      ),
      dataIndex: 'quantity',
      width: 100,
      hideInTable: true,
      renderFormItem: (schema, config, form, action) => (
        <InputNumber
          disabled
          style={{ width: '100%' }}
          formatter={(value) => `${value}`.replace(/\B(?=(\d{3})+(?!\d))/g, ',')}
          parser={(value) => value!.replace(/\$\s?|(,*)/g, '')}
          step="0.01"
        />
      ),
    },
    {
      title: (
        <Tooltip
          color={COLOR_HEX.GREEN_TOOLTIP}
          key={COLOR_HEX.GREEN_TOOLTIP}
          title={<FormattedMessage id="tooltips.production_task_issued_quantity_description" />}
        >
          <FormattedMessage id="storage-management.category-management.damaged_quantity" />{' '}
          <InfoCircleOutlined />
        </Tooltip>
      ),
      dataIndex: 'issued_quantity',
      width: 100,
      renderFormItem: (schema, config: any, form, action) => (
        <InputNumber
          disabled
          style={{ width: '100%' }}
          formatter={(value) => `${value}`.replace(/\B(?=(\d{3})+(?!\d))/g, ',')}
          step="0.01"
        />
      ),
      hideInTable: !isSubscribedStock() || isTemplateTask,
    },
    {
      title: (
        <Tooltip
          color={COLOR_HEX.GREEN_TOOLTIP}
          key={COLOR_HEX.GREEN_TOOLTIP}
          title={<FormattedMessage id="tooltips.production_task_harvested_quantity_description" />}
        >
          <FormattedMessage id="storage-management.category-management.harvested_quantity" />{' '}
          <InfoCircleOutlined />
        </Tooltip>
      ),
      dataIndex: 'finished_quantity',
      width: 100,
      renderFormItem: (schema, config: any, form, action) => (
        <InputNumber
          disabled
          style={{ width: '100%' }}
          formatter={(value) => `${value}`.replace(/\B(?=(\d{3})+(?!\d))/g, ',')}
        />
      ),
      hideInTable: !isSubscribedStock() || isTemplateTask,
    },
    {
      title: (
        <Tooltip
          color={COLOR_HEX.GREEN_TOOLTIP}
          key={COLOR_HEX.GREEN_TOOLTIP}
          title={<FormattedMessage id="tooltips.production_task_crop_total_quantity_description" />}
        >
          <FormattedMessage id="storage-management.category-management.real_quantity" />{' '}
          <InfoCircleOutlined />
        </Tooltip>
      ),
      dataIndex: 'total_qty_in_crop',
      width: 100,
      renderFormItem: (schema, config, form, action) => <RealityColumnNumberRender />,
      hideInTable: !isSubscribedStock() || isTemplateTask,
    },
    {
      title: <FormattedMessage id="common.description" />,
      dataIndex: 'description',
      width: 100,
      formItemProps: {
        rules: [
          {
            required: true,
            whitespace: true,
            message: 'Mô tả không được để trống',
          },
        ],
      },
    },
    {
      title: 'Hành động',
      dataIndex: 'name',
      width: 100,
      renderFormItem: (schema, config: any) => (
        <>
          {canDeleteTask && (
            <DeleteItemForTask
              refreshFnc={() => fetchTaskProduction(task_id, items, setDataSource, setLoading)}
              value={config.recordKey}
            />
          )}
        </>
      ),
    },
  ];

  const itemsDropdownMenu: MenuProps['items'] = [
    canCreateTask || canUpdateTask
      ? {
          key: '1',
          label: (
            <CreateProductionUpdateView
              key={'create-item'}
              buttonType="link"
              refreshFnc={() => fetchTaskProduction(task_id, items, setDataSource, setLoading)}
              task_id={task_id}
            />
          ),
        }
      : null,
    (canCreateTask || canUpdateTask) && isSubscribedStock() && !isTemplateTask
      ? {
          key: '2',
          label: (
            <MaterialReceipt
              key={'create-material-receipt'}
              buttonType="link"
              onSuccess={() => fetchTaskProduction(task_id, items, setDataSource, setLoading)}
            />
          ),
        }
      : null,
    (canCreateTask || canUpdateTask) && isSubscribedStock() && !isTemplateTask
      ? {
          key: '3',
          label: (
            <MaterialIssue
              key={'create-material-issue'}
              buttonType="link"
              onSuccess={() => fetchTaskProduction(task_id, items, setDataSource, setLoading)}
            />
          ),
        }
      : null,
    (canCreateTask || canUpdateTask) && isSubscribedStock() && !isTemplateTask
      ? {
          key: '4',
          label: (
            <MaterialTransfer
              key={'create-material-transfer'}
              buttonType="link"
              onSuccess={() => fetchTaskProduction(task_id, items, setDataSource, setLoading)}
            />
          ),
        }
      : null,
    (canCreateTask || canUpdateTask) && isSubscribedStock() && !isTemplateTask
      ? {
          key: '5',
          label: (
            <QuickMaterialTransfer
              key={'quick-create-material-transfer'}
              buttonType="link"
              onSuccess={() => fetchTaskProduction(task_id, items, setDataSource, setLoading)}
            />
          ),
        }
      : null,
  ].filter(Boolean);
  const intl = useIntl();
  return !loading ? (
    <>
      <Space direction="vertical" style={{ width: '100%', marginBottom: 16 }}>
        <EditableProTable
          scroll={{ x: 1200 }}
          actionRef={actionRef}
          headerTitle={
            <Tooltip
              color={COLOR_HEX.GREEN_TOOLTIP}
              key={COLOR_HEX.GREEN_TOOLTIP}
              title={
                <div
                  dangerouslySetInnerHTML={{
                    __html: intl.formatMessage({ id: 'tooltips.task_production_quantity_table' }),
                  }}
                />
              }
            >
              <FormattedMessage id="common.production" /> <InfoCircleOutlined />
            </Tooltip>
          }
          columns={columns}
          rowKey="name"
          key={JSON.stringify(dataSource)}
          request={async (params) => {
            return {
              data: dataSource,
              success: true,
              total: dataSource.length,
            };
          }}
          toolBarRender={() => {
            return [
              <Dropdown menu={{ items: itemsDropdownMenu }}>
                <Button>
                  <FormattedMessage id="common.action" /> <DownOutlined />
                </Button>
              </Dropdown>,
            ];
          }}
          editable={{
            type: 'multiple',
            editableKeys,
            actionRender: (row, config, defaultDoms) => {
              return [defaultDoms.delete];
            },
            onValuesChange: (record, recordList) => {
              const updateDataSource = recordList.map((item) => {
                return {
                  ...item,
                  quantity:
                    typeof item.quantity === 'string' ? parseFloat(item.quantity) : item.quantity,
                  exp_quantity:
                    typeof item.exp_quantity === 'string'
                      ? parseFloat(item.exp_quantity)
                      : item.exp_quantity,
                  lost_quantity:
                    typeof item.lost_quantity === 'string'
                      ? parseFloat(item.lost_quantity)
                      : item.lost_quantity,
                };
              });
              tempDataSourceRef.current = updateDataSource;
            },
            onChange: setEditableRowKeys,
          }}
          recordCreatorProps={false}
          search={false}
          pagination={{
            pageSizeOptions: [10, 20, 50, 100],
            showSizeChanger: true,
          }}
        />
        {canUpdateTask && (
          <Button loading={isSaving} onClick={handleStoreItem}>
            <FormattedMessage id={'common.save_production'} />
          </Button>
        )}
      </Space>
    </>
  ) : (
    <Skeleton active />
  );
};
