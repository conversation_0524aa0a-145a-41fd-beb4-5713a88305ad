import { DOCTYPE_ERP } from '@/common/contanst/constanst';
import { deleteParticipantsInCrop, getParticipantsInCrop } from '@/services/cropManager';
import { getParamsReqTable } from '@/services/utils';
import { DeleteOutlined } from '@ant-design/icons';
import { ActionType, ProColumns, ProTable } from '@ant-design/pro-components';
import { FormattedMessage } from '@umijs/max';
import { App, Button, Space } from 'antd';
import { FC, useRef } from 'react';
import AddParticipant from './AddParticipant';

interface ParticipantsProps {
  cropId: string;
}
interface DataTable {
  name: string;
  email?: string;
  last_name?: string;
  first_name?: string;
  phone_number?: string;
}
const Participants: FC<ParticipantsProps> = ({ cropId }) => {
  const tableRef = useRef<ActionType>();
  // const [searchParams, setSearchParams] = useSearchParams();
  const { modal, message } = App.useApp();
  const reloadTable = async () => {
    tableRef.current?.reload();
  };
  const columns: ProColumns<DataTable>[] = [
    {
      dataIndex: 'name',
      title: 'ID',
      width: 200,
      ellipsis: true,
      copyable: true,
    },
    {
      title: 'Email',
      dataIndex: 'email',
    },
    {
      title: 'Họ và tên đệm',
      dataIndex: 'last_name',
    },
    {
      title: <FormattedMessage id="storage-management.category-management.object_name" />,
      dataIndex: 'first_name',
    },
    {
      title: 'Phone',
      dataIndex: 'phone_number',
    },
    {
      width: 150,
      render: (dom: any, entity: any) => {
        return (
          <Space>
            {/* <EditParticipant
              id={entity.name}
              cropId={cropId}
              userId={entity.iot_customer_user}
              onSuccess={() => {
                message.success('Chỉnh sửa thành công');
                reloadTable();
              }}
            /> */}
            <Button
              type="primary"
              danger
              icon={<DeleteOutlined />}
              onClick={() => {
                modal.confirm({
                  title: 'Xóa khỏi danh sách người tham gia',
                  onOk: async () => {
                    try {
                      await deleteParticipantsInCrop(entity?.name);
                      message.success('Xóa thành công');
                      reloadTable();
                    } catch (error) {
                      message.error('Xóa thất bại');
                    }
                  },
                  okButtonProps: {
                    danger: true,
                  },
                });
              }}
            />
          </Space>
        );
      },
    },
  ];

  return (
    <ProTable<DataTable>
      size="small"
      actionRef={tableRef}
      rowKey="name"
      request={async (params, sort, filter) => {
        try {
          const paramsReq = getParamsReqTable({
            doc_name: DOCTYPE_ERP.iotEmployeeInCrop,
            tableReqParams: {
              params,
              sort,
              filter,
            },
            concatFilter: [[DOCTYPE_ERP.iotEmployeeInCrop, 'iot_crop', '=', cropId]],
          });
          const res = await getParticipantsInCrop(paramsReq);
          return {
            data: res.data,
            total: (res as any).pagination.totalElements,
          };
        } catch {
          return {
            success: false,
          };
        }
      }}
      bordered
      columns={columns}
      search={false}
      headerTitle={'Danh sách người tham gia'}
      toolBarRender={() => [
        <AddParticipant
          onSuccess={() => {
            message.success('Thêm thành công');
            reloadTable();
          }}
          cropId={cropId}
          key="create"
        />,
      ]}
      pagination={{
        defaultPageSize: 20,
        showSizeChanger: true,
        pageSizeOptions: ['20', '50', '100'],
      }}
    />
  );
};

export default Participants;
