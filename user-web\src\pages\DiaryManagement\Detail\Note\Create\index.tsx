import { DEFAULT_PAGE_SIZE_ALL, DOCTYPE_ERP } from '@/common/contanst/constanst';
import { createCropNote, getCropManagementInfoList, updateCropNote } from '@/services/cropManager';
import { uploadFile } from '@/services/fileUpload';
import { CameraFilled, PlusOutlined } from '@ant-design/icons';
import {
  ModalForm,
  ProForm,
  ProFormSelect,
  ProFormText,
  ProFormTextArea,
  ProFormUploadButton,
} from '@ant-design/pro-components';
import { FormattedMessage, useAccess } from '@umijs/max';
import { App, Button } from 'antd';
import { UploadFile } from 'antd/es/upload';
import { FC, ReactElement, useEffect } from 'react';

interface CreatePandemicModalProps {
  cropId?: string;
  onSuccess?: () => void;
  trigger?: ReactElement;
}
type IFormData = {
  label: string;
  crop: string;
  note?: string;
  img?: UploadFile[];
};
const CreateNoteModal: FC<CreatePandemicModalProps> = ({ cropId, onSuccess, trigger }) => {
  const { message } = App.useApp();
  const onFinish = async (values: IFormData) => {
    try {
      // create
      const dataCreate = await createCropNote({
        label: values.label,
        crop: values.crop,
        note: values.note,
      });

      try {
        // upload file
        if (values.img && (values.img || []).length > 0) {
          // upload bất kể thành công hay ko
          const uploadListRes = await Promise.allSettled(
            values.img.map(async (item) => {
              return await uploadFile({
                docType: DOCTYPE_ERP.iotPest,
                docName: dataCreate.data.name,
                file: item.originFileObj as any,
              });
            }),
          );
          // check if() 1 vài upload failed
          const checkUploadFailed = uploadListRes.find((item) => item.status === 'rejected');
          if (checkUploadFailed) {
            message.error({
              content: 'Some file upload failed',
            });
          }

          // update img path
          const arrFileUrl = uploadListRes
            .reduce<string[]>(
              (prev, item) =>
                item.status === 'fulfilled'
                  ? [...prev, item?.value?.data?.message?.file_url]
                  : prev,
              [],
            )
            .filter((item) => typeof item === 'string');
          if (arrFileUrl.length > 0) {
            await updateCropNote({
              name: dataCreate.data.name,
              crop: values.crop,
              image: arrFileUrl.join(','),
            });
          }
        }
      } catch (error) {}

      message.success({
        content: 'Created successfully',
      });
      onSuccess?.();
      return true;
    } catch (error) {
      message.error({
        content: 'Error, please try again',
      });
      return false;
    }
  };
  const [form] = ProForm.useForm();
  useEffect(() => {
    // default select crop
    if (cropId) {
      form.setFieldsValue({
        crop: cropId,
      });
    }
  }, [cropId]);
  const access = useAccess();
  const canCreateCrop = access.canCreateInSeasonalManagement();
  if (canCreateCrop) {
    return (
      <ModalForm<IFormData>
        modalProps={{
          destroyOnClose: true,
        }}
        name="crop:create-note"
        title="Tạo ghi chú"
        trigger={
          trigger || (
            <Button type="primary" icon={<PlusOutlined />}>
              Thêm ghi chú
            </Button>
          )
        }
        width={500}
        form={form}
        onFinish={onFinish}
        initialValues={{
          crop: cropId,
        }}
      >
        <ProFormText
          label="Tiêu đề"
          name="label"
          rules={[
            {
              required: true,
            },
          ]}
        />
        <ProFormSelect
          label={'Chọn vụ mùa'}
          name="crop"
          rules={[
            {
              required: true,
            },
          ]}
          request={async () => {
            const res = await getCropManagementInfoList({
              page: 1,
              size: DEFAULT_PAGE_SIZE_ALL,
            });
            return res.data.map((item) => ({
              label: item.label,
              value: item.name,
            }));
          }}
        />
        {/* <ProFormDatePicker
          label="Chọn ngày"
          rules={[
            {
              required: true,
            },
          ]}
        /> */}
        <ProFormTextArea
          label={<FormattedMessage id={'common.form.description'} />}
          name={'note'}
        />
        <ProFormUploadButton
          label="Upload"
          listType="picture-card"
          name="img"
          icon={<CameraFilled />}
          title=""
          accept="image/*"
          rules={[
            {
              required: true,
            },
          ]}
        />
      </ModalForm>
    );
  } else return <></>;
};

export default CreateNoteModal;
