import { useProFormList } from '@/components/Form/Config/pro-form-list';
import ProFormSelectUser from '@/components/Form/ProFormSelectUser';
import { ProFormGroup, ProFormItem, ProFormList } from '@ant-design/pro-components';
import { useIntl } from '@umijs/max';
import { Card } from 'antd';
import { FC, ReactNode } from 'react';
import SelectOrCreateMemberType from './SelectOrCreateMemberType';

interface MemberProps {
  children?: ReactNode;
}

const Member: FC<MemberProps> = ({ children }) => {
  const { formatMessage } = useIntl();
  const formListProps = useProFormList();
  return (
    <Card
      title={formatMessage({
        id: 'common.member',
      })}
    >
      <ProFormList {...formListProps} name="members">
        {() => {
          return (
            <ProFormGroup>
              <ProFormItem
                label={formatMessage({
                  id: 'common.member_group',
                })}
                name="member_type"
              >
                <SelectOrCreateMemberType
                  style={{
                    width: '320px',
                  }}
                />
                {/* <ExampleSelect /> */}
              </ProFormItem>
              <ProFormSelectUser
                label={formatMessage({
                  id: 'common.member',
                })}
                name="user_id"
                style={{
                  width: '320px',
                }}
              />
              {/* <ProFormText
                label={formatMessage({
                  id: 'common.number_phone',
                })}
                name="phone_number"
              />
              <ProFormText label={`Email`} name="email" />
              <ProFormText
                label={formatMessage({
                  id: 'common.address',
                })}
                name="address"
              /> */}
            </ProFormGroup>
          );
        }}
      </ProFormList>
    </Card>
  );
};

export default Member;
