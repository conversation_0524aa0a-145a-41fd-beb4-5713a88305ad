import { IIotCropNote } from '@/types/IIotCropNote';
import { ListPageSkeleton, PageContainer } from '@ant-design/pro-components';
import { useSearchParams } from '@umijs/max';
import { Form, Tabs } from 'antd';
import { FC, Suspense, useState } from 'react';
import DetailTaskInfoTab from './components/DetailTaskInfoTab';
const { Item } = Form;

const DetailTaskInfo: FC = () => {
  const [tabActive, setTabActive] = useState('1');

  const [searchParams, setSearchParams] = useSearchParams();
  const [loading, setLoading] = useState(false);
  const [cropNote, setCropNote] = useState<Partial<IIotCropNote>>({});
  const [cropList, setCropList] = useState<any[]>([]);
  const [imageLinks, setImageLinks] = useState<any[]>([]);
  const [form] = Form.useForm();

  const taskInfoName = searchParams.get('task_info_id') ?? '';

  return (
    <Suspense fallback={<ListPageSkeleton />}>
      <>
        <PageContainer>
          <Tabs
            activeKey={tabActive}
            onChange={(e) => {
              setTabActive(e);
            }}
          >
            <Tabs.TabPane tab="Thông tin chi tiết ghi chú" key="1">
              <DetailTaskInfoTab taskID={taskInfoName} />
            </Tabs.TabPane>
            {/* <Tabs.TabPane tab="Thông tin khác" key="2">
              Oh no
            </Tabs.TabPane> */}
          </Tabs>
        </PageContainer>
      </>
    </Suspense>
  );
};

export default DetailTaskInfo;
