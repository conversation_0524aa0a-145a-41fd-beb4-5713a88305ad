import FormUploadsPreviewable from '@/components/FormUploadsPreviewable';
import { ProFormText } from '@ant-design/pro-components';
import { useIntl } from '@umijs/max';
import { Card, Col, Row } from 'antd';
import { FC, ReactNode } from 'react';

interface DetailedInfoProps {
  children?: ReactNode;
}
const w = 'md';
const DetailedInfo: FC<DetailedInfoProps> = ({ children }) => {
  const { formatMessage } = useIntl();

  return (
    <Card
      title={formatMessage({
        id: 'task.detailed_info',
      })}
      bordered={false}
      style={{ boxShadow: 'none' }}
    >
      <FormUploadsPreviewable
        label={formatMessage({
          id: 'common.image',
        })}
        fileLimit={10}
        formItemName={'image'}
        isRequired={false}
      />
      <Row gutter={24}>
        <Col span={12}>
          <ProFormText
            name={'label'}
            label={formatMessage({
              id: 'common.stage_name',
            })}
            rules={[
              {
                required: true,
              },
            ]}
          />
        </Col>
        <Col span={12}>
          {' '}
          <ProFormText
            name={'description'}
            label={formatMessage({
              id: 'common.note',
            })}
          />
        </Col>
      </Row>
    </Card>
  );
};

export default DetailedInfo;
