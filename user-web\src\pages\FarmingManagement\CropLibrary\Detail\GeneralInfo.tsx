import { InfoTab } from '@/types/infoTab.type';
import { Alert } from 'antd';
import { createStyles } from 'antd-use-styles';
import { FC, ReactNode } from 'react';
import CollapsibleInfoCard from './components/CollapsibleInfoCard';

interface GeneralInfoProps {
  children?: ReactNode;
  infoTabs: InfoTab[];
}
const useStyles = createStyles(({ token }) => ({
  wrapper: {
    display: 'flex',
    flexDirection: 'column',
    gap: token.margin,
  },
}));
const GeneralInfo: FC<GeneralInfoProps> = ({ children, infoTabs }) => {
  const styles = useStyles();
  return (
    <div className={styles.wrapper}>
      {infoTabs && infoTabs.length ? (
        infoTabs.map((infoTab) => <CollapsibleInfoCard key={infoTab.name} cardInfo={infoTab} />)
      ) : (
        <Alert message="C<PERSON>y này chưa có thông tin gì" type="info" />
      )}
    </div>
  );
};

export default GeneralInfo;
