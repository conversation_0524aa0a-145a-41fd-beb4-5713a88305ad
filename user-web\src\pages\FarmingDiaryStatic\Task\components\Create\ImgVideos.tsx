import FormUploadsPreviewable from '@/components/FormUploadsPreviewable';
import { useIntl } from '@umijs/max';
import { Card } from 'antd';
import { FC, ReactNode } from 'react';

interface ImgVideosProps {
  children?: ReactNode;
}

const ImgVideos: FC<ImgVideosProps> = ({ children }) => {
  const { formatMessage } = useIntl();
  return (
    <Card title={formatMessage({ id: 'common.image_preview' })}>
      <FormUploadsPreviewable label={''} fileLimit={10} formItemName={'image'} />
    </Card>
  );
};

export default ImgVideos;
