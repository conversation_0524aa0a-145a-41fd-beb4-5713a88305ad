import ActionModalConfirm from '@/components/ActionModalConfirm';
import { FC, ReactNode } from 'react';
import useDelete from '../hooks/useDelete';

interface DeleteTraceProps {
  children?: ReactNode;
  onSuccess?: () => void;
  id: string;
}

const DeleteTrace: FC<DeleteTraceProps> = ({ children, onSuccess, id }) => {
  const { run } = useDelete({
    onSuccess,
  });
  return (
    <ActionModalConfirm
      isDelete={true}
      modalProps={{
        async onOk() {
          await run(id);
          onSuccess?.();
          return true;
        },
      }}
    />
  );
};

export default DeleteTrace;
