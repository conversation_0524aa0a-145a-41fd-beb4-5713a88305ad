import {
  DEFAULT_DATE_FORMAT_WITHOUT_TIME,
  DEFAULT_PAGE_SIZE_ALL,
  DOCTYPE_ERP,
} from '@/common/contanst/constanst';
import { getCropManagementInfoList } from '@/services/cropManager';
import {
  createFarmingPlan,
  createFarmingPlanFromCopy,
  getFarmingPlanList,
  updateFarmingPlan,
} from '@/services/farming-plan';
import { uploadFileHome } from '@/services/fileUpload';
import { CameraFilled, PlusOutlined } from '@ant-design/icons';
import {
  ModalForm,
  ProFormDateRangePicker,
  ProFormSelect,
  ProFormText,
  ProFormUploadButton,
} from '@ant-design/pro-components';
import { App, Button, UploadFile } from 'antd';
import { FC } from 'react';

interface CreatePlanProps {
  onSuccess?: () => void;
}

type IFormData = {
  label: string;
  dateRange: [string, string];
  crop: string;
  img: UploadFile[];
  copy_plan_id?: string;
};
const CreateCropPlanFromCrop: FC<CreatePlanProps> = ({ onSuccess }) => {
  const { message } = App.useApp();
  const onFinish = async (values: IFormData) => {
    try {
      // create
      const { copy_plan_id } = values;
      let dataCreate: any = [];
      if (copy_plan_id) {
        dataCreate = await createFarmingPlanFromCopy({
          copy_plan_id: copy_plan_id,
          label: values.label,
          crop: values.crop,
          start_date: values.dateRange[0],
          end_date: values.dateRange[1],
        });
      } else {
        dataCreate = await createFarmingPlan({
          label: values.label,
          crop: values.crop,
          start_date: values.dateRange[0],
          end_date: values.dateRange[1],
        });
      }

      console.log('dataCreate', dataCreate);
      // upload file
      if (values.img && (values.img || []).length > 0) {
        // upload bất kể thành công hay ko
        try {
          let fileObj: any = values.img[0].originFileObj;
          const dataUploadRes = await uploadFileHome({
            file: fileObj as any,
          });
          console.log('success upload', dataUploadRes);
          if (copy_plan_id) {
            await updateFarmingPlan({
              name: dataCreate[0][0].name,
              crop: values.crop,
              image: dataUploadRes.data.message.file_url,
            });
          } else {
            await updateFarmingPlan({
              name: dataCreate.data.name,
              crop: values.crop,
              image: dataUploadRes.data.message.file_url,
            });
          }
        } catch (error) {
          console.log('error', error);
          message.error({
            content: 'File upload failed',
          });
        }
      }

      message.success({
        content: 'Created successfully',
      });
      onSuccess?.();
      return true;
    } catch (error) {
      message.error({
        content: 'Error, please try again',
      });
      return false;
    }
  };
  return (
    <ModalForm<IFormData>
      onFinish={onFinish}
      name="crop-plan:create"
      trigger={
        <Button icon={<PlusOutlined />} type="primary">
          Tạo kế hoạch mới
        </Button>
      }
      modalProps={{
        destroyOnClose: true,
      }}
    >
      <ProFormText
        name="label"
        label={'Tên kế hoạch'}
        rules={[
          {
            required: true,
          },
        ]}
      />
      <ProFormSelect
        label="Chọn vụ mùa"
        showSearch
        request={async () => {
          const res = await getCropManagementInfoList({
            page: 1,
            size: DEFAULT_PAGE_SIZE_ALL,
          });
          return res.data.map((item) => ({
            label: item.label,
            value: item.name,
          }));
        }}
        name="crop"
        rules={[
          {
            required: true,
          },
        ]}
      />
      <ProFormDateRangePicker
        colSize={24}
        name="dateRange"
        label="Thời gian"
        rules={[
          {
            required: true,
          },
        ]}
        fieldProps={{
          format: DEFAULT_DATE_FORMAT_WITHOUT_TIME,
        }}
      />

      <ProFormSelect
        colSize={24}
        allowClear
        showSearch
        label="Sao chép từ kế hoạch khác"
        name="copy_plan_id"
        request={async ({ keyWords }) => {
          const res = await getFarmingPlanList({
            page: 1,
            size: DEFAULT_PAGE_SIZE_ALL,
            ...(keyWords && {
              filters: `[["${DOCTYPE_ERP.iotFarmingPlan}", "label", "like", "%${keyWords}%"]]`,
            }),
          });
          console.log('res', res);
          return res.data.map((item) => ({
            label: item.label,
            value: item.name,
          }));
        }}
      />

      <ProFormUploadButton
        max={1}
        accept="image/*"
        label="Hình ảnh / Video mô tả "
        listType="picture-card"
        icon={<CameraFilled />}
        title=""
        name="img"
      />
    </ModalForm>
  );
};

export default CreateCropPlanFromCrop;
