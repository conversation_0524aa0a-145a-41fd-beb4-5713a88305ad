import { COLOR_HEX, DEFAULT_DATE_AND_HH_MM_FORMAT } from '@/common/contanst/constanst';
import { getCropByTask } from '@/services/crop';
import { getCustomerUserList } from '@/services/customerUser';
import { getCustomerV3 } from '@/services/InventoryManagementV3/customer';
import { updateTaskProductionListSQL } from '@/services/products';
import {
  getMaterialIssueTotal,
  getMaterialTotalBalanceForProduction,
  getMaterialTransferFromWIPTotal,
  saveStockEntry,
  submitStockEntry,
} from '@/services/stock/stockEntry';
import { getWarehouseList } from '@/services/stock/warehouse';
import { useTaskProductionStore } from '@/stores/TaskProductionUpdateStore';
import { IIotProductionQuantity } from '@/types/IIotProductionQuantity';
import { IStockItem } from '@/types/warehouse.type';
import { ExclamationCircleOutlined, PlusOutlined } from '@ant-design/icons';
import {
  ModalForm,
  ProFormDateTimePicker,
  ProFormSelect,
  ProFormTextArea,
} from '@ant-design/pro-components';
import { FormattedMessage, useIntl, useModel } from '@umijs/max';
import { App, Button, Divider, Space, Tooltip } from 'antd';
import { useForm } from 'antd/es/form/Form';
import { ButtonType } from 'antd/lib/button';
import moment from 'moment';
import { useEffect, useState } from 'react';
import {
  createMaterialReceiptVoucher,
  createMaterialTransferFromWIPVoucher,
  createSubmittingMaterialReceiptVoucher,
  createSubmittingMaterialTransferFromWIPVoucher,
  validateItems,
} from '../Helpers/StockEntryHelpers';
import { IMaterialVoucherItem } from '../Interfaces/StockEntryInterfaces';
import QuickMaterialReceiptTable from './QuickMaterialTransferVoucherTable';
interface ITreeNode {
  title: string;
  value: string;
  key: string;
  children?: ITreeNode[];
}
interface IFormData {
  doctype: 'Purchase Receipt';
  posting_date: string;
  company: 'VIIS';
  customer: string;
  warehouse: string;
  description: string;
  employee: string;
  iot_crop: string;
}
interface Props {
  onSuccess?: any;
  transferFromTask?: boolean;
  buttonType: ButtonType;
}

const fetchWarehouseList = async () => {
  const warehouse = await getWarehouseList();
  return warehouse.data.map((storage) => ({
    label: storage.label,
    value: storage.name,
  }));
};

const fetchCustomerUserList = async () => {
  const listUser = await getCustomerUserList();
  return listUser.data.map((item: any) => ({
    label: `${item.first_name} ${item.last_name}`,
    value: item.name,
  }));
};

const fetchCustomerV3 = async () => {
  const supplier = await getCustomerV3();
  return supplier.data.map((item) => ({
    label: item.label,
    value: item.name,
  }));
};

const QuickMaterialTransfer = ({ onSuccess, buttonType }: Props) => {
  //use zustand useTaskItemUsedStore
  const { dataSource, setDataSource } = useTaskProductionStore();
  const taskId = dataSource[0]?.task_id;
  const [submitting, setSubmitting] = useState<boolean>(false);
  const [treeData, setTreeData] = useState<ITreeNode[]>();
  const [selectedItems, setSelectedItems] = useState<IMaterialVoucherItem[]>([]);
  const [items, setItems] = useState<IStockItem[]>([]);
  const [selectedWarehouse, setSelectedWarehouse] = useState<string>('');
  const [form] = useForm();
  //current user
  const { initialState } = useModel(`@@initialState`);
  const curentUser = initialState?.currentUser;
  const { message } = App.useApp();

  const handleSelectWarehouse = (entity: any) => {
    setSelectedWarehouse(entity);
  };
  const handleStoreItem = async (dataArr: any) => {
    try {
      const updateTaskProductionList: IIotProductionQuantity[] = [];
      for (const data of dataArr) {
        updateTaskProductionList.push({
          name: data.name,
          quantity: data.quantity,
          description: data.description,
          task_id: data.task_id,
          product_id: data.product_id,
          // exp_quantity: data.exp_quantity,
          lost_quantity: data.lost_quantity,
          finished_quantity: data.finished_quantity,
          issued_quantity: data.issued_quantity,
          // draft_quantity: data.draft_quantity,
        });
      }
      const request = await updateTaskProductionListSQL(updateTaskProductionList);
      message.success('Lưu sản lượng thực tế thành công ');
    } catch (error: any) {
      message.error('Đã có lỗi xảy ra');
    }
  };
  //get material-receipt-total
  const materialReceiptTotal = async () => {
    const realQuantityData = await getMaterialTotalBalanceForProduction({ task_id: taskId });
    //map dataSource with realQuantityData
    const data = dataSource.map((itemInTask) => {
      const realQuantity = realQuantityData.data.find(
        (d) => d.item_id === itemInTask.item.item_code,
      );
      return {
        ...itemInTask,
        quantity: typeof realQuantity?.total_qty === 'number' ? realQuantity?.total_qty : 0,
      };
    });
    const realIssueQuantityData = await getMaterialIssueTotal({ task_id: taskId });
    //map dataSource with realIssueQuantityData
    const data1 = dataSource.map((itemInTask) => {
      const realQuantity = realIssueQuantityData.data.find(
        (d) => d.item_id === itemInTask.item.item_code,
      );
      return {
        ...itemInTask,
        issued_quantity: realQuantity?.total_qty || 0,
      };
    });
    await handleStoreItem(data1);
    setDataSource(data1);
  };
  //get material-transfer-fromWIP-total
  const materialTransferFromWIPTotal = async () => {
    const realQuantityDataTransferFromWIP = await getMaterialTransferFromWIPTotal({
      task_id: taskId,
    });
    //map dataSource with realQuantityDataTransferFromWIP
    const data = dataSource.map((itemInTask) => {
      const realQuantity = realQuantityDataTransferFromWIP.data.find(
        (d) => d.item_id === itemInTask.item.item_code,
      );
      return {
        ...itemInTask,
        finished_quantity: realQuantity?.total_qty || 0,
      };
    });

    const realQuantityData = await getMaterialTotalBalanceForProduction({ task_id: taskId });
    //map dataSource with realQuantityData
    const data1 = data.map((itemInTask) => {
      const realQuantity = realQuantityData.data.find(
        (d) => d.item_id === itemInTask.item.item_code,
      );
      return {
        ...itemInTask,
        quantity: typeof realQuantity?.total_qty === 'number' ? realQuantity?.total_qty : 0,
      };
    });
    const realIssueQuantityData = await getMaterialIssueTotal({ task_id: taskId });
    //map dataSource with realIssueQuantityData
    const data2 = data1.map((itemInTask) => {
      const realQuantity = realIssueQuantityData.data.find(
        (d) => d.item_id === itemInTask.item.item_code,
      );
      return {
        ...itemInTask,
        issued_quantity: realQuantity?.total_qty || 0,
      };
    });
    await handleStoreItem(data2);
    setDataSource(data2);
  };

  //only use loadData when transferFromTask is true
  const loadData = async () => {
    const item_ids: any[] | undefined = dataSource;
    if (!item_ids) {
      message.error(`Vui lòng chọn hàng hóa`);
      return;
    }
    const formatted_items: IMaterialVoucherItem[] = item_ids.map((obj, index) => ({
      conversion_factor: 1,
      item_code: obj.item ? obj.item.name : '',
      item_name: obj.item ? obj.item.item_name : '',
      actual_qty: obj.item ? obj.item.total_qty_in_crop : 0,
      qty: obj.draft_quantity || 0,
      rate: obj.item ? obj.item.valuation_rate : 0,
      total_price: 0,
      warehouse: selectedWarehouse,
      item_label: obj.item ? obj.item.label : '',
      key: index.toString(),
      uom_label: obj.item ? obj.item.uom_label : '',
      convert_uom_id: obj.item ? obj.item.stock_uom : '',
      convert_uom_label: obj.item ? obj.item.uom_label : '',
    }));
    console.log('formatted_items', formatted_items);
    setSelectedItems(formatted_items);
    form.setFieldValue('items', []);
    return;
  };

  //run when submit Form
  const handleFinish = async (values: IFormData) => {
    setSubmitting(true);
    try {
      if (!validateItems(selectedItems)) {
        return;
      }

      let date = moment(values.posting_date, DEFAULT_DATE_AND_HH_MM_FORMAT);
      const posting_date = date.format('YYYY-MM-DD');
      // Lấy giờ và phút từ biến `date`
      const hoursAndMinutes = date.format('HH:mm');

      // Lấy giây tại thời điểm hiện tại
      const currentSeconds = moment().format('ss');

      // Kết hợp giờ, phút và giây để tạo `posting_time`
      const posting_time = `${hoursAndMinutes}:${currentSeconds}`;

      const exportVoucher = createMaterialReceiptVoucher(
        values,
        selectedItems,
        posting_date,
        posting_time,
        taskId,
      );
      const saveRes = await saveStockEntry(exportVoucher);

      const submittingMaterialIssueVoucher = createSubmittingMaterialReceiptVoucher(
        saveRes,
        taskId,
      );

      await submitStockEntry(submittingMaterialIssueVoucher);

      //update production in task
      await materialReceiptTotal();

      /**
       * Transfer Item from Warehouse to Work In Progress - V Warehouse
       */
      const transferVoucher = createMaterialTransferFromWIPVoucher(
        values,
        selectedItems,
        posting_date,
        posting_time,
        taskId,
      );
      const saveTransferRes = await saveStockEntry(transferVoucher);

      const submittingMaterialTransferVoucher = createSubmittingMaterialTransferFromWIPVoucher(
        saveTransferRes,
        taskId,
      );

      await submitStockEntry(submittingMaterialTransferVoucher);
      await materialTransferFromWIPTotal();
      message.success(`Success`);
      onSuccess?.();
      return true;
    } catch (error: any) {
      console.log({ error });
      if (error.code === 'ERR_BAD_REQUEST') {
        message.error(
          'Đã có lỗi xảy ra, có thể do số lượng item quá nhiều, hãy thử lại với số lượng ít hơn',
        );
      } else {
        message.error(`Error with Stock Receipt: ${error.message}`);
      }
      return;
    } finally {
      setSubmitting(false);
    }
  };

  useEffect(() => {
    loadData();
  }, [selectedWarehouse]);

  useEffect(() => {
    setSelectedWarehouse('Work In Progress - V');
  }, []);
  const intl = useIntl();
  return (
    <ModalForm<IFormData>
      title={'Thu hoạch nhanh'}
      modalProps={{
        destroyOnClose: true,
        afterClose: () => form.resetFields(), // Reset form state when Modal closes
      }}
      trigger={
        <Space>
          <Button
            // ghost
            //style={{ backgroundColor: '#59B4C3' }}
            icon={<PlusOutlined />}
            type={buttonType}
          >
            {`Thu hoạch nhanh`}
          </Button>
          <Tooltip
            color={COLOR_HEX.GREEN_TOOLTIP}
            key={COLOR_HEX.GREEN_TOOLTIP}
            title={intl.formatMessage({ id: 'tooltips.quick_add_product_to_crop_button' })}
          >
            <ExclamationCircleOutlined />
          </Tooltip>
        </Space>
      }
      width={1600}
      form={form}
      layout="vertical"
      rowProps={{
        gutter: [16, 0],
      }}
      submitter={{
        render: (props, defaultDoms) => {
          return [
            <Button
              key="submit-material-receipt"
              onClick={() => {
                props.submit();
              }}
              type="primary"
              style={{ width: '100%' }}
              loading={submitting}
            >
              {<FormattedMessage id="common.submit" />}
            </Button>,
          ];
        },
      }}
      autoFocusFirstInput
      grid
      // initialValues={{
      //   posting_date: moment().format(DEFAULT_DATE_FORMAT),
      // }}
      onFinish={handleFinish}
    >
      {/* <ProFormText
        name="document_code"
        label={<FormattedMessage id={'warehouse-management.import-voucher.document_code'} />}
        colProps={{ span: 8 }}
        width={'md'}
      /> */}
      <ProFormDateTimePicker
        name="posting_date"
        required
        rules={[{ required: true, message: 'Vui lòng chọn ngày hoạch toán' }]}
        label={<FormattedMessage id="warehouse-management.export-voucher.transaction_date" />}
        colProps={{ span: 8 }}
        width="md"
        fieldProps={{
          format: DEFAULT_DATE_AND_HH_MM_FORMAT,
        }}
      />
      {/* <ProFormText
        name="document_code"
        // rules={[{ required: true, message: 'Vui lòng chọn kho' }]}
        label={<FormattedMessage id="warehouse-management.export-voucher.document_code" />}
        colProps={{ span: 8 }}
        width={'md'}
      /> */}
      <ProFormSelect
        showSearch
        name="warehouse"
        rules={[{ required: true, message: 'Vui lòng chọn kho' }]}
        required
        label={<FormattedMessage id="warehouse-management.to-warehouse-name" />}
        onChange={(value) => handleSelectWarehouse(value)}
        colProps={{ span: 8 }}
        request={async () => {
          return await fetchWarehouseList();
        }}
        width={'md'}
      />
      <ProFormSelect
        showSearch
        name="iot_crop"
        required
        disabled
        label={<FormattedMessage id="common.crop" />}
        // onChange={(value) => handleSelectWarehouse(value)}
        colProps={{ span: 8 }}
        request={async () => {
          const res = await getCropByTask({ task_id: taskId });
          form.setFieldsValue({ iot_crop: res.data.name });
          form.setFieldsValue({
            description: `Thêm sản lượng thực tế vào công việc "${res.data.task_label}" của mùa vụ "${res.data.label} và chuyển thành thành phẩm`,
          });
          return [
            {
              label: res.data.label,
              value: res.data.name,
            },
          ];
        }}
        width={'md'}
      />
      <ProFormSelect
        showSearch
        required
        disabled
        initialValue={curentUser?.user_id}
        label={<FormattedMessage id="warehouse-management.export-voucher.employee" />}
        request={async (option) => {
          return await fetchCustomerUserList();
        }}
        name="employee"
        // onChange={handleChooseItem}
        colProps={{ span: 8 }}
        width={'md'}
      />
      <ProFormTextArea
        name="description"
        label={<FormattedMessage id={'common.form.description'} />}
        colProps={{ span: 8 }}
        width={'md'}
      />
      <Divider />
      <QuickMaterialReceiptTable data={selectedItems} setData={setSelectedItems} />
    </ModalForm>
  );
};

export default QuickMaterialTransfer;
