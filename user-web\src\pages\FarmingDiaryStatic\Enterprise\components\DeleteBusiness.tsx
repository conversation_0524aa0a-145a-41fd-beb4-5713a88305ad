import ActionModalConfirm from '@/components/ActionModalConfirm';
import { FC } from 'react';
import useDelete from '../hooks/useDelete';

interface DeleteBusinessProps {
  id: string;
  onSuccess?: () => void;
}

const DeleteBusiness: FC<DeleteBusinessProps> = ({ id, onSuccess }) => {
  const { run } = useDelete({
    onSuccess,
  });
  return (
    <ActionModalConfirm
      isDelete
      modalProps={{
        async onOk(...args) {
          await run(id);
          return true;
        },
      }}
    />
  );
};

export default DeleteBusiness;
