import { DOCTYPE_ERP } from '@/common/contanst/constanst';
import { getTaskManagerList } from '@/services/farming-plan';
import { dayjsUtil } from '@/utils/date';
import { Link, useIntl, useModel, useRequest } from '@umijs/max';
import { Typography } from 'antd';
import { FC, ReactNode } from 'react';
import { useGetTimeName } from '../../hooks/useGetTimeName';

interface GreetingsProps {
  children?: ReactNode;
}

const Greetings: FC<GreetingsProps> = ({ children }) => {
  const { timeName } = useGetTimeName();
  const { locale } = useIntl();
  const { initialState } = useModel('@@initialState');
  const greeting = (timeName?.greetings as any)?.[locale];
  const { data, loading } = useRequest(async () => {
    const today = dayjsUtil().format('YYYY-MM-DD');
    // const startDate = moment().startOf('date').toISOString();
    // const endDate = moment().endOf('date').toISOString();
    const filters = [
      [DOCTYPE_ERP.iotFarmingPlanTask, 'end_date', '>=', today],
      [DOCTYPE_ERP.iotFarmingPlanTask, 'start_date', '<=', today],
    ];

    const res: any = await getTaskManagerList({
      page: 1,
      size: 1,
      filters: filters,
      or_filters: [],
    });

    return {
      data: {
        data: res.data,
        total: res.pagination.totalElements,
      },
    };
  });
  const { formatMessage } = useIntl();
  return (
    <div>
      <Typography.Title level={3} className="!mb-2 ">
        {greeting}, {initialState?.currentUser?.full_name}!
      </Typography.Title>
      {data?.total ? (
        <Link to="/farming-management/workflow-management">
          <div className="text-gray-400 hover:underline">
            {formatMessage(
              {
                id: 'new-dashboard.notice-task',
              },
              {
                count: data.total,
              },
            )}
            {/* Bạn có {data?.total} công việc cần làm hôm nay */}
          </div>
        </Link>
      ) : null}
    </div>
  );
};

export default Greetings;
