export default {
  'storage-management.category-management.object_code': 'Item code',
  'storage-management.category-management.object_name': 'Item name',
  'storage-management.category-management.import_id': 'Import ID',
  'storage-management.category-management.export_id': 'Export ID',
  'storage-management.category-management.check_id': 'Check ID',
  'storage-management.category-management.inventory': 'Category Inventory',
  'storage-management.category-management.detail': 'Detail',
  'storage-management.category-management.quantity': 'qty',
  'storage-management.category-management.type': 'Type',
  'storage-management.category-management.category_storage': 'Material Storage',
  'storage-management.category-management.import_history': 'Import History',
  'storage-management.category-management.export_history': 'Export History',
  'storage-management.category-management.check_history': 'Inventory Check History',
  'storage-management.category-management.import': 'Import',
  'storage-management.category-management.export': 'Export',
  'storage-management.category-management.check': 'Inventory Check',
  'storage-management.category-management.select_storage': 'Select Storage',
  'storage-management.category-management.inventory_quantity': 'Inventory qty',
  'storage-management.category-management.inventory_price': 'Total Price',
  'storage-management.category-management.transaction_date': 'Transaction Date',
  'storage-management.category-management.document_date': 'Document Date',
  'storage-management.category-management.document_code': 'Document Code',
  'storage-management.category-management.employee': 'Employee',
  'storage-management.category-management.import_from_excel': 'Import from Excel',
  'storage-management.category-management.total_quantity': 'Total qty',
  'storage-management.category-management.total_price': 'Total Price',
  'storage-management.category-management.download': 'Download Form',
  'storage-management.category-management.update': 'Update Form',
  'storage-management.category-management.check_type': 'Check Type',
  'storage-management.category-management.check_date': 'Inventory Check Date',
  'storage-management.category-management.present_quantity': 'Present qty',
  //used qty
  'storage-management.category-management.used_quantity': 'Used qty',
  'storage-management.category-management.real_quantity': 'Real qty',
  'storage-management.category-management.expected_quantity': 'Expected qty',
  'storage-management.category-management.draft_quantity': 'Draft qty',

  'storage-management.category-management.loss_quantity': 'Difference in qty',
  //issue quantity
  'storage-management.category-management.issue_quantity': 'Issued qty',
  //Hỏng huỷ
  'storage-management.category-management.damaged_quantity': 'Damaged qty',
  //sản lượng thu hoạch
  'storage-management.category-management.harvested_quantity': 'Finished qty',
  'storage-management.category-management.quality': 'Quality',
  'storage-management.category-management.total_amount': 'Total Amount',
};
