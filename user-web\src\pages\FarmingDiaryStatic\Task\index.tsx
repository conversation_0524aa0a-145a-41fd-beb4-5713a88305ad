import { PlusOutlined } from '@ant-design/icons';
import { PageContainer } from '@ant-design/pro-components';
import { Link, useIntl } from '@umijs/max';
import { Button } from 'antd';
import { FC, ReactNode } from 'react';
import TaskList from './components/TaskList';

interface IndexProps {
  children?: ReactNode;
}

const Index: FC<IndexProps> = ({ children }) => {
  const { formatMessage } = useIntl();
  return (
    <PageContainer
      extra={
        <Link to="/farming-diary-static/task/create">
          <Button type="primary" icon={<PlusOutlined />}>
            {formatMessage({
              id: 'task.create_new_task',
            })}
          </Button>
        </Link>
      }
    >
      <TaskList />
    </PageContainer>
  );
};

export default Index;
