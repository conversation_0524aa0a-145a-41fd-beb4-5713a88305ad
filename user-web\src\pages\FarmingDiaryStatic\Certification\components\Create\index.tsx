import { ProForm } from '@ant-design/pro-components';
import { history, useIntl, useLocation } from '@umijs/max';
import { message } from 'antd';
import { FC, ReactNode, useEffect } from 'react';
import useCreate from '../../hooks/useCreate';
import Attachment from './Attachment';
import DetailedInfo from './DetailedInfo';

interface StageOfCountEditProps {
  children?: ReactNode;
  onSuccess?: () => void;
  setIsFormDirty: (dirty: boolean) => void;
}

const DocumentCreate: FC<StageOfCountEditProps> = ({
  children,
  onSuccess,
  setIsFormDirty = () => {},
}) => {
  const { formatMessage } = useIntl();
  const location = useLocation();
  const state: any = location.state;
  const { run } = useCreate({
    onSuccess: () => {
      if (state?.fromProcedureCreate) {
        history.replace('/farming-diary-static/procedure/create');
      }
      if (state?.fromProcedureEdit) {
        history.replace(`/farming-diary-static/procedure/edit/${state.id}`);
      }
      onSuccess && onSuccess();
    },
  });
  useEffect(() => {
    setIsFormDirty(false);
  }, []);
  return (
    <ProForm
      onValuesChange={() => setIsFormDirty(true)}
      submitter={{
        searchConfig: {
          // resetText: formatMessage({ id: 'common.reset' }),
          // submitText: formatMessage({ id: 'common.submit' }),
        },
        render: (_, dom) => {
          return (
            <div style={{ textAlign: 'right', margin: 24 }}>
              {dom.map((item, index) => (
                <span key={index} style={{ marginRight: index === 0 ? 8 : 0 }}>
                  {item}
                </span>
              ))}
            </div>
          );
        },
      }}
      onFinish={async (values) => {
        //cần check issue_date phải nhỏ hơn expiry_date
        if (values.issue_date > values.expiry_date) {
          message.error('Ngày hết hạn phải lớn hơn ngày chứng nhận');
          return;
        }
        await run({
          // name: string;
          label: values.label,
          issue_date: values.issue_date,
          expiry_date: values.expiry_date,
          document_path: values.document_path,
        });
        setIsFormDirty(false);
        onSuccess?.();
        return true;
      }}
    >
      <div className="mb-4 space-y-4">
        <DetailedInfo />
        {/* <Task /> */}
        <Attachment />
      </div>
    </ProForm>
  );
};

export default DocumentCreate;
