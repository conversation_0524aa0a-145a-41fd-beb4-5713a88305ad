import { ActionType, ProTable } from '@ant-design/pro-components';
import { Link, useIntl } from '@umijs/max';
import { Button } from 'antd';
import { FC, ReactNode, useRef } from 'react';

interface ProductProcedureListProps {
  children?: ReactNode;
}

const TemList: FC<ProductProcedureListProps> = ({ children }) => {
  const actionRef = useRef<ActionType>();
  const handleReload = () => {
    actionRef.current?.reload?.();
  };
  const { formatMessage } = useIntl();

  return (
    <ProTable
      actionRef={actionRef}
      form={{
        labelWidth: 'auto',
      }}
      //  headerTitle={'Danh sách doanh nghiệp'}
      columns={[
        {
          title: 'STT',
          index: 1,
        },

        {
          title: formatMessage({ id: 'common.name' }),
          dataIndex: 'name',
        },
        {
          title: 'Loại tem',
          dataIndex: 'number_phone',
        },
        {
          title: formatMessage({ id: 'common.time' }),
          dataIndex: 'email',
        },
        {
          title: formatMessage({ id: 'common.quantity' }),
          dataIndex: 'address',
        },
        {
          title: formatMessage({
            id: 'common.note',
          }),
          dataIndex: 'address',
        },
        //  {
        //   title:'Trạng thái',
        //   render(dom, entity, index, action, schema) {
        //       if(true){
        //           return <Tag color='success'>Đang hoạt động</Tag>
        //       }
        //       return <Tag color='warning'>Đã hủy</Tag>
        //   },
        //  },
        //  {
        //    title: 'Liên kết web',
        //    dataIndex: 'url',
        //  },
      ]}
      toolBarRender={() => [
        // <Link to="/farming-diary-static/trace/create" key="l">
        //   <Button>Danh sách công việc</Button>
        // </Link>,
        <Link to="/farming-diary-static/tem/create" key="create">
          <Button type="primary">Tạo truy đăng kí tem</Button>
        </Link>,
      ]}
      request={async () => {
        return {
          data: [],
        };
      }}
    />
  );
};

export default TemList;
