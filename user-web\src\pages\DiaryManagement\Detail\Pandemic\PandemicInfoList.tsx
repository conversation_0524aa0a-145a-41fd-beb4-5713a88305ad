import { Space } from 'antd';
import { FC } from 'react';
import PandemicEmpty from './PandemicEmpty';
import PandemicInfo,{ PandemicInfoProps } from './PandemicInfo';

interface PandemicInfoListProps {
  data?: PandemicInfoProps['data'][];
  onDeleteSuccess?:(id:string)=>void
}

const PandemicInfoList: FC<PandemicInfoListProps> = ({ data ,onDeleteSuccess}) => {
  if (!data || data.length === 0) return <PandemicEmpty />;
  return (
    <Space
      direction="vertical"
      size="large"
      style={{
        width: '100%',
      }}
    >
      {data.map((item, index) => (
        <PandemicInfo data={item} key={index} onDeleteSuccess={onDeleteSuccess} />
      ))}
    </Space>
  );
};

export default PandemicInfoList;
