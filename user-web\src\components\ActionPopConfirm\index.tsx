import { FormattedMessage } from '@umijs/max';
import { Button, message, Popconfirm } from 'antd';
import { useState } from 'react';

const ActionPopConfirm = ({ actionCall, refreshData, buttonType, text, danger, size }: any) => {
  const [loading, setLoading] = useState(false);

  const onConfirm = async () => {
    try {
      setLoading(true);
      if (actionCall) {
        await actionCall();
      }
      if (refreshData) {
        await refreshData();
      }
    } catch (error: any) {
      message.error(error.toString());
      console.log(error);
    } finally {
      setLoading(false);
    }
  };

  const onCancel = async () => {};

  return (
    <Popconfirm
      title={<FormattedMessage id="action.confirm" />}
      description={<FormattedMessage id="action.verify" />}
      onConfirm={onConfirm}
      onCancel={onCancel}
      okText="Đồng ý"
      cancelText="Huỷ"
    >
      <Button
        style={{
          display: 'flex',
          alignItems: 'center',
          borderRadius: 0, // Thiết lập góc vuông cho button
        }}
        size={size || 'middle'}
        danger={danger}
        type={buttonType}
        loading={loading}
      >
        {text}
      </Button>
    </Popconfirm>
  );
};

export default ActionPopConfirm;
