/**
 * Standardized UOM Types with Backward Compatibility
 * This file provides consistent type definitions for UOM-related data
 */

/**
 * Base UOM information interface
 */
export interface BaseUOM {
  /** UOM identifier */
  uom_id: string;
  /** UOM code/name */
  uom: string;
  /** Display name for UOM */
  uom_name?: string;
  /** Alternative display name (for backward compatibility) */
  uom_label?: string;
  /** Conversion factor relative to base unit */
  conversion_factor: number;
}

/**
 * Extended UOM information with additional metadata
 */
export interface UOMInfo extends BaseUOM {
  /** Whether UOM is enabled */
  enabled?: boolean;
  /** Whether values must be whole numbers */
  must_be_whole_number?: boolean;
  /** UOM group/category */
  uom_group?: string;
  /** Creation timestamp */
  creation?: string;
  /** Last modified timestamp */
  modified?: string;
}

/**
 * Active UOM data for items/tasks
 * Supports both new and legacy field names for backward compatibility
 */
export interface ActiveUOMData {
  /** Current active UOM identifier */
  active_uom?: string | null;
  /** Current active conversion factor */
  active_conversion_factor?: number | null;
  
  // Legacy fields for backward compatibility
  /** @deprecated Use active_uom instead */
  uom_id?: string;
  /** @deprecated Use active_uom instead */
  uom?: string;
  /** @deprecated Use active_conversion_factor instead */
  conversion_factor?: number;
  /** @deprecated Use getUOMDisplayName utility instead */
  uom_name?: string;
  /** @deprecated Use getUOMDisplayName utility instead */
  uom_label?: string;
}

/**
 * Task item with UOM information
 */
export interface TaskItemWithUOM extends ActiveUOMData {
  /** Item identifier */
  item_id: string;
  /** Task identifier */
  task_id?: string;
  /** Item name */
  name?: string;
  /** Item label */
  label?: string;
  /** Quantity */
  quantity: number;
  /** Expected quantity */
  exp_quantity?: number;
  /** Draft quantity */
  draft_quantity?: number;
  /** Issued quantity */
  issued_quantity?: number;
  /** Loss quantity */
  loss_quantity?: number;
  /** Total quantity in crop */
  total_qty_in_crop?: number;
  /** Description */
  description?: string;
  /** Available UOMs for this item */
  uoms?: UOMInfo[];
  
  // Original values for conversion tracking
  /** @internal Original quantity before UOM conversion */
  original_quantity?: number;
  /** @internal Original expected quantity before UOM conversion */
  original_exp_quantity?: number;
  /** @internal Original draft quantity before UOM conversion */
  original_draft_quantity?: number;
  /** @internal Original issued quantity before UOM conversion */
  original_issued_quantity?: number;
  /** @internal Original total quantity before UOM conversion */
  original_total_qty_in_crop?: number;
}

/**
 * Production item with UOM information
 */
export interface ProductionItemWithUOM extends ActiveUOMData {
  /** Product identifier */
  product_id: string;
  /** Task identifier */
  task_id?: string;
  /** Item name */
  name?: string;
  /** Item label */
  label?: string;
  /** Quantity */
  quantity: number;
  /** Expected quantity */
  exp_quantity?: number;
  /** Finished quantity */
  finished_quantity?: number;
  /** Lost quantity */
  lost_quantity?: number;
  /** Basic rate */
  basic_rate?: number;
  /** Description */
  description?: string;
  /** Available UOMs for this item */
  uoms?: UOMInfo[];
}

/**
 * Material item for transfers and operations
 */
export interface MaterialItemWithUOM extends ActiveUOMData {
  /** Item identifier */
  item_id: string;
  /** Item code */
  item_code?: string;
  /** Item name */
  name?: string;
  /** Item label */
  label?: string;
  /** Quantity */
  quantity: number;
  /** Description */
  description?: string;
  /** Available UOMs for this item */
  uoms?: UOMInfo[];
}

/**
 * UOM conversion result
 */
export interface UOMConversionResult {
  /** Whether conversion was successful */
  success: boolean;
  /** Converted quantities */
  quantities: {
    quantity?: number;
    exp_quantity?: number;
    draft_quantity?: number;
    issued_quantity?: number;
    total_qty_in_crop?: number;
  };
  /** Original quantities (for rollback) */
  originalQuantities?: {
    quantity?: number;
    exp_quantity?: number;
    draft_quantity?: number;
    issued_quantity?: number;
    total_qty_in_crop?: number;
  };
  /** New UOM data */
  uomData: {
    active_uom: string;
    active_conversion_factor: number;
    display_name: string;
  };
  /** Error message if conversion failed */
  error?: string;
  /** Warning messages */
  warnings?: string[];
}

/**
 * UOM selection option for dropdowns
 */
export interface UOMOption {
  /** Option label */
  label: string;
  /** Option value */
  value: string | number;
  /** UOM identifier */
  uom_id?: string;
  /** Conversion factor */
  conversion_factor?: number;
  /** Whether option is disabled */
  disabled?: boolean;
}

/**
 * UOM validation configuration
 */
export interface UOMValidationConfig {
  /** Allow empty UOM values */
  allowEmpty?: boolean;
  /** Default UOM if none specified */
  defaultUOM?: string;
  /** Default conversion factor */
  defaultConversionFactor?: number;
  /** Strict validation against available UOMs */
  strictMode?: boolean;
  /** Minimum allowed conversion factor */
  minConversionFactor?: number;
  /** Maximum allowed conversion factor */
  maxConversionFactor?: number;
  /** Decimal precision for calculations */
  precision?: number;
}

/**
 * Legacy type aliases for backward compatibility
 * @deprecated Use the new types above
 */
export type UOM_V3_DATA = UOMInfo;
export type UOM = BaseUOM;

/**
 * Type guards for UOM data
 */
export function isValidUOMData(data: any): data is ActiveUOMData {
  return data && (
    typeof data.active_uom === 'string' ||
    typeof data.uom_id === 'string' ||
    typeof data.uom === 'string'
  );
}

export function hasActiveUOM(data: any): data is Required<Pick<ActiveUOMData, 'active_uom'>> {
  return data && typeof data.active_uom === 'string' && data.active_uom.length > 0;
}

export function hasConversionFactor(data: any): data is Required<Pick<ActiveUOMData, 'active_conversion_factor'>> {
  return data && typeof data.active_conversion_factor === 'number' && data.active_conversion_factor > 0;
}

/**
 * Utility type for making UOM fields required
 */
export type WithRequiredUOM<T extends ActiveUOMData> = T & Required<Pick<T, 'active_uom' | 'active_conversion_factor'>>;

/**
 * Utility type for optional UOM fields
 */
export type WithOptionalUOM<T> = T & Partial<ActiveUOMData>;
