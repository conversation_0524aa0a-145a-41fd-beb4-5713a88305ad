﻿export default [
  {
    path: '/maintenance',
    component: './Maintenance',
  },
  {
    path: '/user',
    layout: false,
    routes: [
      {
        name: 'Login',
        path: '/user/login',
        component: './User/Login',
      },
      {
        name: 'Forgot Password',
        path: '/user/forgot-password',
        component: './User/ForgotPassword',
      },
      {
        name: 'Reset New Password',
        path: '/user/update-password',
        component: './User/ResetNewPassword',
      },
      {
        name: 'Reset New Password',
        path: '/user/update-password/:key',
        component: './User/ResetNewPassword',
      },
    ],
  },
  {
    path: '/home',
    name: 'home',
    icon: '/icons/menu/home-icon.svg',
    // access: 'false',
    component: './DashboardNew',
    // hideInMenu: true,
  },
  {
    path: '/farming-management',
    // hideInMenu: true,
    // name: 'monitor',
    name: 'farming-management',
    icon: '/icons/menu/tree-icon.svg',
    access: 'canAccessFarmingManagement',
    routes: [
      {
        path: '/farming-management/dashboard',
        name: 'dashboard',
        access: 'canAccessPageSeasonalManagement',
        component: './FarmingManagement/SeasonalManagement/Dashboard',
        icon: '/icons/menu/sun.svg',
      },
      {
        path: '/farming-management/seasonal-management',
        name: 'season-management',
        access: 'canAccessPageSeasonalManagement',
        component: './FarmingManagement/SeasonalManagement',
        icon: '/icons/menu/sun.svg',
      },
      {
        path: '/farming-management/seasonal-management/create',
        name: 'season-management.create',
        component: './FarmingManagement/SeasonalManagement/Create',
        hideInMenu: true,
      },
      {
        path: '/farming-management/seasonal-management/detail/:id',
        name: 'season-management.detail',
        component: './FarmingManagement/SeasonalManagement/Detail',
        hideInMenu: true,
      },
      {
        path: '/farming-management/seasonal-management/detail/:cropId/ticket/:id/history',
        name: 'season-management.detail',
        component: './FarmingManagement/SeasonalManagement/Detail/Ticket/components/TicketMessages',
        hideInMenu: true,
      },
      {
        path: '/farming-management/seasonal-management/detail/:cropId/workflow-management/detail/:id',
        name: 'work-management.detail',
        component: './FarmingManagement/WorkflowManagement/Detail',
        hideInMenu: true,
      },
      {
        path: '/farming-management/seasonal-management/edit/:id',
        name: 'workflow-management.edit',
        component: './FarmingManagement/SeasonalManagement/Edit',
        hideInMenu: true,
      },
      {
        path: '/farming-management',
        redirect: '/farming-management/workflow-management',
        hideInMenu: true,
        // icon: '/icons/menu/bag.svg',
        access: 'canAccessPageWorkFlowManagement',
      },
      {
        path: '/farming-management/workflow-management',
        name: 'work-management',
        component: './FarmingManagement/WorkflowManagement',
        icon: '/icons/menu/bag.svg',
      },
      {
        path: '/farming-management/workflow-management/create',
        name: 'work-management.create',
        component: './FarmingManagement/WorkflowManagement/Create',
        hideInMenu: true,
      },
      {
        path: '/farming-management/workflow-management/detail/:id',
        name: 'work-management.detail',
        component: './FarmingManagement/WorkflowManagement/Detail',
        hideInMenu: true,
      },
      {
        path: '/farming-management/workflow-management/edit/:id',
        name: 'work-management.edit',
        component: './FarmingManagement/WorkflowManagement/Edit',
        hideInMenu: true,
      },

      {
        path: '/farming-management/crop-management-plan',
        name: 'Kế hoạch canh tác',
        component: './FarmingManagement/CropPlan',
        hideInMenu: true,
        icon: '/icons/menu/calendar.svg',
      },
      {
        path: '/farming-management/crop-management-plan/detail/:id',
        name: 'Kế hoạch canh tác',
        component: './FarmingManagement/CropPlan/Detail',
        hideInMenu: true,
      },
      {
        path: '/farming-management/crop-log',
        name: 'Nhật kí canh tác',
        icon: '/icons/menu/log.svg',
        hideInMenu: true,
        routes: [
          {
            path: '/farming-management/crop-log',
            name: 'Nhật kí canh tác',
            redirect: '/farming-management/crop-log/overview',
            hideInMenu: true,
          },
          {
            path: '/farming-management/crop-log/overview',
            name: 'Tổng quan',
            // component: './FarmingManagement/CropLog/OverviewAllDiary',
            component: './FarmingManagement/CropLog',
            hideInMenu: true,
          },
          {
            path: '/farming-management/crop-log/crop-note-detail',
            name: 'Chi tiết ghi chú mùa vụ',
            component: './FarmingManagement/CropLog/DetailCropNote',
            hideInMenu: true,
          },
          {
            path: '/farming-management/crop-log/crop-pest-detail',
            name: 'Chi tiết ghi chú dịch hại',
            component: './FarmingManagement/CropLog/DetailCropPest',
            hideInMenu: true,
          },
          {
            path: '/farming-management/crop-log/task-info-detail',
            name: 'Chi tiết ghi chú công việc',
            component: './FarmingManagement/CropLog/DetailTaskInfo',
            hideInMenu: true,
          },
          // {
          //   path: '/farming-management/crop-log',
          //   name: 'Nhật kí canh tác',
          //   redirect: '/farming-management/crop-log/overview',
          //   hideInMenu: true,
          // },
          // {
          //   path: '/farming-management/crop-log/overview',
          //   name: 'Tổng quan',
          //   component: './FarmingManagement/CropLog/OverviewAllDiary',
          //   hideInMenu: true,
          // },
          {
            path: '/farming-management/crop-log/log',
            name: 'Nhật kí',
            component: './FarmingManagement/CropLog/Log',
            hideInMenu: true,
          },
          {
            path: '/farming-management/crop-log/info',
            name: 'Tổng quan',
            component: './FarmingManagement/CropLog/Info',
            hideInMenu: true,
          },
          {
            path: '/farming-management/crop-log/supplies',
            name: 'Vật liệu',
            component: './FarmingManagement/CropLog/Supplies',
            hideInMenu: true,
          },
        ],
      },

      {
        path: '/farming-management/crop-library',
        name: 'plant-library',
        icon: '/icons/menu/one-tree.svg',
        component: './FarmingManagement/CropLibrary',
      },
      {
        path: '/farming-management/crop-library/:id/detail',
        component: './FarmingManagement/CropLibrary/Detail',
        name: 'Chi tiết cây trồng',
        hideInMenu: true,
        routes: [
          {
            path: '/farming-management/crop-library/:id/detail',
            name: 'Chi tiết cây trồng',
            redirect: '/farming-management/crop-library/:id/detail/general-info',
          },
          {
            path: '/farming-management/crop-library/:id/detail/general-info',
            name: 'Thông tin chung',
            // component: './FarmingManagement/CropLibrary/Detail/GeneralInfo',
          },
          {
            path: '/farming-management/crop-library/:id/detail/care-instructions',
            name: 'Hướng dẫn chăm sóc',
            // component: './FarmingManagement/CropLibrary/Detail/CareInstructions',
          },
        ],
      },
      {
        path: '/farming-management/crop-library/:id/edit',
        component: './FarmingManagement/CropLibrary/Edit',
        name: 'Chi tiết cây trồng',
        hideInMenu: true,
        routes: [
          {
            path: '/farming-management/crop-library/:id/edit',
            name: 'Chi tiết cây trồng',
            redirect: '/farming-management/crop-library/:id/edit/general-info',
          },
          {
            path: '/farming-management/crop-library/:id/edit/general-info',
            name: 'Chỉnh sửa cây trồng',
            // component: './FarmingManagement/CropLibrary/Edit/GeneralInfo',
          },
          {
            path: '/farming-management/crop-library/:id/edit/care-instructions',
            name: 'Chỉnh sửa cây trồng',
            // component: './FarmingManagement/CropLibrary/Edit/CareInstructions',
          },
        ],
      },
      //

      //
      {
        path: '/farming-management/crop-statistical',
        name: 'crop-statistical',
        // access: 'canAccessPageStatisticalCrop',
        component: './FarmingManagement/CropStatisticV2',
      },
      // {
      //   path: '/farming-management/zone',
      //   name: 'zone',
      //   // access: 'canAccessPageStatisticalCrop',
      //   component: './FarmingManagement/Zone',
      // },
      // {
      //   path: '/farming-management/plant',
      //   name: 'plant',
      //   // access: 'canAccessPageStatisticalCrop',
      //   component: './FarmingManagement/Plant',
      // },
    ],
  },
  {
    path: '/farming-diary-static',
    name: 'farming-diary-static',
    icon: '/images/diary-static/diary.svg',

    // hideInMenu: true,
    routes: [
      {
        path: '/farming-diary-static/procedure',
        name: 'procedure',
        routes: [
          {
            path: '/farming-diary-static/procedure',
            index: true,
            redirect: '/farming-diary-static/procedure/list',
          },
          {
            path: '/farming-diary-static/procedure/list',
            name: 'list',
            component: './FarmingDiaryStatic/FarmingProcedure',
            hideInMenu: true,
          },
          {
            path: '/farming-diary-static/procedure/create',
            name: 'create',
            component: './FarmingDiaryStatic/FarmingProcedure/CreateProcedurePage',
            hideInMenu: true,
          },
          {
            path: '/farming-diary-static/procedure/detail/:id',
            name: 'detail',
            component: './FarmingDiaryStatic/FarmingProcedure/DetailProcedurePage',
            hideInMenu: true,
          },
          {
            path: '/farming-diary-static/procedure/edit/:id',
            name: 'detail',
            component: './FarmingDiaryStatic/FarmingProcedure/EditProcedurePage',
            hideInMenu: true,
          },
          // {
          //   path: '/farming-diary-static/procedure/detail/:id',
          //   name: 'Chi tiết quy trình',
          //   component: './FarmingDiaryStatic/FarmingProcedure/DetailProcedurePage',
          //   hideInMenu: true,
          // },
        ],
      },
      {
        path: '/farming-diary-static/stage-of-crop',
        name: 'stage-of-crop',
        routes: [
          {
            path: '/farming-diary-static/stage-of-crop',
            index: true,
            redirect: '/farming-diary-static/stage-of-crop/list',
          },
          {
            path: '/farming-diary-static/stage-of-crop/list',
            name: 'list',
            component: './FarmingDiaryStatic/StageOfCrop',
            hideInMenu: true,
          },
          {
            path: '/farming-diary-static/stage-of-crop/create',
            name: 'create',
            component: './FarmingDiaryStatic/StageOfCrop/CreateStageOfCropPage',
            hideInMenu: true,
          },
          // {
          //   path: '/farming-diary-static/stage-of-crop/edit/:id',
          //   name: 'Chi tiết quy trình',
          //   component: './FarmingDiaryStatic/FarmingProcedure/DetailProcedurePage',
          //   hideInMenu: true,
          // },
          // {
          //   path: '/farming-diary-static/procedure/detail/:id',
          //   name: 'Chi tiết quy trình',
          //   component: './FarmingDiaryStatic/FarmingProcedure/DetailProcedurePage',
          //   hideInMenu: true,
          // },
        ],
      },
      {
        path: '/farming-diary-static/task',
        name: 'task',
        routes: [
          {
            path: '/farming-diary-static/task',
            index: true,
            redirect: '/farming-diary-static/task/list',
          },
          {
            path: '/farming-diary-static/task/list',
            name: 'list',
            component: './FarmingDiaryStatic/Task',
            hideInMenu: true,
          },
          {
            path: '/farming-diary-static/task/create',
            name: 'create',
            component: './FarmingDiaryStatic/Task/CreateTaskPage',
            hideInMenu: true,
          },
          {
            path: '/farming-diary-static/task/edit/:id',
            name: 'detail',
            component: './FarmingDiaryStatic/Task/EditTaskPage',
            hideInMenu: true,
          },
        ],
      },
      {
        path: '/farming-diary-static/note',
        name: 'note',
        routes: [
          {
            path: '/farming-diary-static/note',
            index: true,
            redirect: '/farming-diary-static/note/list',
          },
          {
            path: '/farming-diary-static/note/list',
            name: 'list',

            component: './FarmingDiaryStatic/Note',
            hideInMenu: true,
          },
          {
            path: '/farming-diary-static/note/create',
            name: 'create',
            component: './FarmingDiaryStatic/Note/CreateNotePage',
            hideInMenu: true,
          },
          // {
          //   path: '/farming-diary-static/stage-of-crop/create',
          //   name: 'Thêm quy trình',
          //   component: './FarmingDiaryStatic/FarmingProcedure/CreateProcedurePage',
          //   hideInMenu: true,
          // },
          // {
          //   path: '/farming-diary-static/stage-of-crop/edit/:id',
          //   name: 'Chi tiết quy trình',
          //   component: './FarmingDiaryStatic/FarmingProcedure/DetailProcedurePage',
          //   hideInMenu: true,
          // },
          // {
          //   path: '/farming-diary-static/procedure/detail/:id',
          //   name: 'Chi tiết quy trình',
          //   component: './FarmingDiaryStatic/FarmingProcedure/DetailProcedurePage',
          //   hideInMenu: true,
          // },
        ],
      },
      {
        path: '/farming-diary-static/certification',
        name: 'certification',
        routes: [
          {
            path: '/farming-diary-static/certification',
            index: true,
            redirect: '/farming-diary-static/certification/list',
          },
          {
            path: '/farming-diary-static/certification/list',
            name: 'list',

            component: './FarmingDiaryStatic/Certification',
            hideInMenu: true,
          },
          {
            path: '/farming-diary-static/certification/create',
            name: 'create',
            component: './FarmingDiaryStatic/Certification/CreateCertificationPage',
            hideInMenu: true,
          },
          // {
          //   path: '/farming-diary-static/stage-of-crop/edit/:id',
          //   name: 'Chi tiết quy trình',
          //   component: './FarmingDiaryStatic/FarmingProcedure/DetailProcedurePage',
          //   hideInMenu: true,
          // },
          // {
          //   path: '/farming-diary-static/procedure/detail/:id',
          //   name: 'Chi tiết quy trình',
          //   component: './FarmingDiaryStatic/FarmingProcedure/DetailProcedurePage',
          //   hideInMenu: true,
          // },
        ],
      },
      {
        path: '/farming-diary-static/product-procedure',
        name: 'product-procedure',
        // hideInMenu: true,
        routes: [
          {
            path: '/farming-diary-static/product-procedure',
            index: true,
            redirect: '/farming-diary-static/product-procedure/list',
          },
          {
            path: '/farming-diary-static/product-procedure/list',
            name: 'list',
            component: './FarmingDiaryStatic/ProductProcedure',
            hideInMenu: true,
          },
          {
            path: '/farming-diary-static/product-procedure/create',
            name: 'create',
            component: './FarmingDiaryStatic/ProductProcedure/CreateProdProcedurePage',
            hideInMenu: true,
          },
          {
            path: '/farming-diary-static/product-procedure/edit/:id',
            name: 'detail',
            component: './FarmingDiaryStatic/ProductProcedure/EditProdProcedurePage',
            hideInMenu: true,
          },
        ],
      },

      {
        path: '/farming-diary-static/enterprise',
        name: 'enterprise',
        // hideInMenu: true,
        routes: [
          {
            path: '/farming-diary-static/enterprise',
            index: true,
            redirect: '/farming-diary-static/enterprise/list',
          },
          {
            path: '/farming-diary-static/enterprise/list',
            name: 'list',
            component: './FarmingDiaryStatic/Enterprise',
            hideInMenu: true,
          },
          {
            path: '/farming-diary-static/enterprise/create',
            name: 'create',
            component: './FarmingDiaryStatic/Enterprise/CreatePage',
            hideInMenu: true,
          },
          {
            path: '/farming-diary-static/enterprise/edit/:id',
            name: 'detail',
            component: './FarmingDiaryStatic/Enterprise/EditPage',
            hideInMenu: true,
          },
        ],
      },
      {
        path: '/farming-diary-static/tem',
        name: 'stamp',
        hideInMenu: true,
        routes: [
          {
            path: '/farming-diary-static/tem',
            index: true,
            redirect: '/farming-diary-static/tem/list',
          },
          {
            path: '/farming-diary-static/tem/list',
            name: 'list',
            component: './FarmingDiaryStatic/Tem',
            hideInMenu: true,
          },
          {
            path: '/farming-diary-static/tem/create',
            name: 'create',
            component: './FarmingDiaryStatic/Tem/CreateTemPage',
            hideInMenu: true,
          },
          {
            path: '/farming-diary-static/tem/edit/:id',
            name: 'detail',
            component: './FarmingDiaryStatic/Tem/EditTemPage',
            hideInMenu: true,
          },
        ],
      },
      {
        path: '/farming-diary-static/trace',
        name: 'trace',
        // hideInMenu: true,
        routes: [
          {
            path: '/farming-diary-static/trace',
            index: true,
            redirect: '/farming-diary-static/trace/list',
          },
          {
            path: '/farming-diary-static/trace/list',
            name: 'list',
            component: './FarmingDiaryStatic/Trace',
            hideInMenu: true,
          },
          {
            path: '/farming-diary-static/trace/create',
            name: 'create',
            component: './FarmingDiaryStatic/Trace/CreateTracePage',
            hideInMenu: true,
          },
          {
            path: '/farming-diary-static/trace/edit/:id',
            name: 'detail',
            component: './FarmingDiaryStatic/Trace/EditTracePage',
            hideInMenu: true,
          },
        ],
      },
    ],
  },
  {
    path: '/farming-diary',
    name: 'farming-diary',
    icon: '/icons/menu/calendar.svg',
    component: './DiaryManagement',
    hideInMenu: true,
  },
  {
    path: '/farming-diary/detail/:id',
    name: 'farming-diary.detail',
    component: './DiaryManagement/Detail',
    icon: '/icons/menu/sun.svg',
    hideInMenu: true,
  },
  {
    path: '/traceability',
    name: 'seek-an-origin',
    icon: 'SecurityScanOutlined',
    component: './DiaryManagement/TracingTable/page',
    hideInMenu: true,
  },
  {
    path: '/project-management',
    name: 'project-zone',
    icon: '/icons/menu/three-tree.svg',
    // component: './Project',
    access: 'canAccessPageProjectNewManagement',
    // hideInMenu: true,
    routes: [
      {
        path: '/project-management',
        component: './Project',

        // redirect: '/project-management',
        hideInMenu: true,
      },
      {
        path: '/project-management/create',
        name: 'create',
        component: './Project/Create',
        hideInMenu: true,
      },
      {
        path: '/project-management/detail/:id',
        name: 'detail',
        component: './Project/Detail',
        hideInMenu: true,
      },
      {
        path: '/project-management/update/:id',
        name: 'edit',
        component: './Project/Update',
        hideInMenu: true,
      },
    ],
  },

  // {
  //   path: '/my-project/detail',
  //   name: 'Quản lý dự án',
  //   icon: '/icons/menu/three-tree.svg',
  //   component: './MyProject/Detail',
  //   hideInMenu: true,
  // },

  {
    path: '/inventory-management-v3',
    name: 'category-v3',
    // hideInMenu: process.env.NODE_ENV === 'production' ? true : false,
    // locale: false,
    // icon: '/icons/menu/home-icon.svg',
    icon: '/icons/menu/turtle-car.svg',

    // component: './InventoryManagementV3',

    access: 'canAccessTabCategory',
    routes: [
      // {
      //   path: '/inventory-management-v3',
      //   redirect: '/inventory-management-v3',
      //   hideInMenu: true,
      // },
      {
        // index: true,
        path: '/inventory-management-v3/products',
        // locale: false,
        name: 'product',
        component: './InventoryManagementV3',
        access: 'isSubscribedPrimary',
      },
      {
        path: '/inventory-management-v3/bom',
        name: 'bom',
        component: './InventoryManagementV3/ProductManagement/BOM',
        access: 'isSubscribedPrimary',
        // hideInMenu: true,
      },
      {
        // index: true,
        path: '/inventory-management-v3/customers',
        // locale: false,
        name: 'customer',
        component: './InventoryManagementV3/ProductManagement/Customer',
        access: 'isSubscribedStock',

      },
      //SalesOrderListPage
      {
        // index: true,
        path: '/inventory-management-v3/customers-sales-order',
        // locale: false,
        name: 'customer.sales-order',
        component: './InventoryManagementV3/ProductManagement/Customer/SalesOrderList',
        access: 'isSubscribedStock',

      },
      {
        path: '/inventory-management-v3/customers/to-pdf',
        name: 'customer-to-pdf',
        headerRender: false,
        footerRender: false,
        menuRender: false,
        menuHeaderRender: false,
        hideInMenu: true,
        component: './InventoryManagementV3/ProductManagement/Customer/Report/Print',
      },
      {
        // index: true,
        path: '/inventory-management-v3/suppliers',
        // locale: false,
        name: 'supplier',
        component: './InventoryManagementV3/ProductManagement/Supplier',
        access: 'isSubscribedStock',
      },
      {
        path: '/inventory-management-v3/suppliers/to-pdf',
        name: 'supplier-to-pdf',
        headerRender: false,
        footerRender: false,
        menuRender: false,
        menuHeaderRender: false,
        hideInMenu: true,
        component: './InventoryManagementV3/ProductManagement/Supplier/Report/Print',
      },
      {
        // index: true,
        hideInMenu: true,
        path: '/inventory-management-v3/uom',
        locale: false,
        name: 'Đơn vị',
        component: './InventoryManagementV3/ProductManagement/UOM/UOMList',
      },
    ],
  },
  {
    path: '/warehouse-management-v3',
    name: 'inventory-management-v3',
    // hideInMenu: process.env.NODE_ENV === 'production' ? true : false,
    access: 'canAccessTabInventory',
    icon: '/icons/menu/home-icon.svg',
    routes: [
      {
        path: 'warehouse-management-v3/dashboard',
        name: 'dashboard',
        component: './WarehouseManagementV3/Dashboard',
      },
      {
        path: '/warehouse-management-v3/warehouse',
        name: 'list',
        component: './WarehouseManagementV3/Warehouse',
      },
      {
        path: '/warehouse-management-v3/inventory',
        name: 'inventory',
        component: './WarehouseManagementV3/Inventory',
        routes: [
          {
            path: '/warehouse-management-v3/inventory/dashboard',
            name: 'Dashboard',
            component: './WarehouseManagementV3/Inventory/Dashboard',
            hideInMenu: true,
          },
          {
            path: '/warehouse-management-v3/inventory/inventory-list',
            name: 'stock-ledger',
            component: './WarehouseManagementV3/Inventory/InventoryListTable',
            hideInMenu: true,
          },
          {
            path: '/warehouse-management-v3/inventory/import-history',
            name: 'import-history',
            component: './WarehouseManagementV3/Inventory/ImportHistory',
            hideInMenu: true,
          },
          {
            path: '/warehouse-management-v3/inventory/export-history',
            name: 'export-history',
            component: './WarehouseManagementV3/Inventory/ExportHistory',
            hideInMenu: true,
          },
          {
            path: '/warehouse-management-v3/inventory/reconciliation-history',
            name: 'reconciliation-history',
            component: './WarehouseManagementV3/Inventory/ReconciliationHistory',
            hideInMenu: true,
          },
          {
            path: '/warehouse-management-v3/inventory/stock-entry',
            name: 'stock-entry',
            component: './WarehouseManagementV3/Inventory/StockEntryHistory',
            hideInMenu: true,
          },
          {
            path: '/warehouse-management-v3/inventory/report',
            name: 'report',
            component: './WarehouseManagementV3/Inventory/Report',
            hideInMenu: true,
          },
        ],
      },
    ],
  },
  {
    path: '/warehouse-management-v3/to-pdf',
    name: 'to-pdf',
    headerRender: false,
    footerRender: false,
    menuRender: false,
    menuHeaderRender: false,
    hideInMenu: true,
    component: './WarehouseManagementV3/Inventory/ExportPage',
  },
  {
    path: '/iot-device-management',
    name: 'manage_iot_devices',
    icon: '/icons/menu/iot.svg',
    // hideInMenu: true,
    component: './IoTDeviceMangement',
    access: 'canAccessPageIotDeviceManagement',
    routes: [
      {
        path: '/iot-device-management',
        redirect: '/iot-device-management',
        hideInMenu: true,
      },
    ],
  },
  {
    path: '/expense-management',
    name: 'Quản lý duyệt chi',
    icon: '/icons/menu/bill.svg',
    hideInMenu: true,
    routes: [
      {
        path: '/expense-management',
        redirect: '/expense-management',
        hideInMenu: true,
      },
    ],
  },
  {
    path: '/employee-management',
    // hideInMenu: true,
    name: 'employee_management',
    icon: '/icons/menu/staff.svg',
    access: 'canAccessTabEmployeeManagement',
    routes: [
      {
        path: '/employee-management/employee-list',
        name: 'list_of_employee',
        access: 'isSubscribed',
        component: './MyUser/UserList',
      },
      {
        path: '/employee-management/dynamic-role',
        name: 'list_of_permissions',
        access: 'isSubscribed',
        component: './MyUser/DynamicRole',
      },
      {
        path: '/employee-management/timekeeping',
        name: 'attendance',
        access: 'isSubscribedEnterpriseOrEmployee',
        component: './MyUser/Timekeeping',
        routes: [
          {
            path: '/employee-management/timekeeping/checkin-history',
            name: 'history_in_and_out',
            component: './MyUser/Timekeeping/Checkin',
            hideInMenu: true,
          },
          {
            path: '/employee-management/timekeeping/general',
            name: 'synthetic',
            component: './MyUser/Timekeeping/General',
            hideInMenu: true,
          },
          {
            path: '/employee-management/timekeeping/report',
            name: 'report',
            component: './MyUser/Timekeeping/Report',
            hideInMenu: true,
          },
          {
            path: '/employee-management/timekeeping/work-shift',
            name: 'work-shift',
            component: './MyUser/Timekeeping/WorkShift',
            hideInMenu: true,
          },
          {
            path: '/employee-management/timekeeping/approval',
            name: 'approval',
            component: './MyUser/Timekeeping/Approval',
            hideInMenu: true,
          },
        ],
      },
      {
        path: '/employee-management/timekeepingV2',
        name: 'timekeeping',
        access: 'isSubscribedEnterpriseOrEmployee',
        routes: [
          {
            index: true,
            component: './MyUser/TimekeepingV2',
            hideInMenu: true,
          },
          {
            path: '/employee-management/timekeepingV2/:id',
            name: 'timesheets',
            component: './MyUser/TimekeepingV2/components/DetailTimesheet',
            hideInMenu: true,
          },
          // {
          //   path: '/employee-management/timekeepingV2/general',
          //   name: 'synthetic',
          //   component: './MyUser/Timekeeping/General',
          //   hideInMenu: true,
          // },
        ],
      },
      {
        path: '/employee-management/approval',
        name: 'approval',
        hideInMenu: true,
        routes: [
          {
            path: '/employee-management/approval',
            index: true,
            component: './MyUser/Approval',
            hideInMenu: true,
          },
          {
            name: 'details',
            path: '/employee-management/approval/details/:id',
            component: './MyUser/Approval/Details',
            hideInMenu: true,
          },
        ],
      },
    ],
  },
  {
    path: '/employee-management/timekeeping/to-pdf',
    name: 'timekeeping/to-pdf',
    headerRender: false,
    footerRender: false,
    menuRender: false,
    menuHeaderRender: false,
    hideInMenu: true,
    component: './MyUser/ExportPage',
  },
  {
    path: '/employee-management/visitor-management',
    name: 'in_and_out_management',
    // hideInMenu: true,
    access: 'isSubscribedVisitor',
    icon: '/icons/menu/log.svg',
    // access: 'canAccessPageVisitorManagement',
    routes: [
      {
        name: 'statistical_tables',
        icon: 'BarChartOutlined',
        path: '/employee-management/visitor-management/dashboard-visitor',
        component: './Visitor/Dashboard',
      },
      {
        name: 'visit_history',
        icon: 'HomeOutlined',
        path: '/employee-management/visitor-management/history',
        routes: [
          {
            path: '/employee-management/visitor-management/history',
            redirect: '/employee-management/visitor-management/history/all',
          },
          {
            name: 'all',
            path: '/employee-management/visitor-management/history/all',
            component: './Visitor/History',
            hideInMenu: true,
          },
          {
            name: 'detail',
            path: '/employee-management/visitor-management/history/detail',
            component: './Visitor/History/Detail',
            hideInMenu: true,
          },
        ],
      },
      {
        name: 'user',
        icon: 'UsergroupAddOutlined',
        path: '/employee-management/visitor-management/member',
        routes: [
          {
            path: '/employee-management/visitor-management/member',
            redirect: '/employee-management/visitor-management/member/all',
          },
          {
            name: 'all',
            path: '/employee-management/visitor-management/member/all',
            component: './Visitor/PakingCustomer',
            hideInMenu: true,
          },
          {
            name: 'detail',
            path: '/employee-management/visitor-management/member/detail',
            component: './Visitor/PakingCustomer/Detail',
            hideInMenu: true,
          },
        ],
      },
      {
        name: 'card',
        icon: 'CreditCardOutlined',
        path: '/employee-management/visitor-management/card',
        routes: [
          {
            path: '/employee-management/visitor-management/card',
            redirect: '/employee-management/visitor-management/card/all',
          },
          {
            name: 'all',
            path: '/employee-management/visitor-management/card/all',
            component: './Visitor/MemberCard',
            hideInMenu: true,
          },
          {
            name: 'detail',
            path: '/employee-management/visitor-management/card/detail',
            component: './Visitor/MemberCard/Detail',
            hideInMenu: true,
          },
        ],
      },
      {
        name: 'location',
        icon: 'EnvironmentOutlined',
        path: '/employee-management/visitor-management/location',
        routes: [
          {
            path: '/employee-management/visitor-management/location',
            redirect: '/employee-management/visitor-management/location/all',
          },
          {
            name: 'all',
            path: '/employee-management/visitor-management/location/all',
            component: './Visitor/Location',
            hideInMenu: true,
          },
          {
            name: 'detail',
            path: '/employee-management/visitor-management/location/detail',
            component: './Visitor/Location/Detail',
            hideInMenu: true,
          },
        ],
      },
    ],
  },
  {
    path: '/time-attendance-management',
    name: 'timekeeping_management',
    icon: '/icons/menu/timekeeping.svg',
    hideInMenu: true,
    routes: [
      {
        path: '/time-attendance-management',
        redirect: '/time-attendance-management',
        hideInMenu: true,
      },
    ],
  },
  {
    name: 'notification',
    icon: '/icons/menu/notification.svg',
    path: '/user-notification',
    hideInMenu: true,
    component: './Notification',
  },
  // {
  //   path: '/category',
  //   name: 'category-old',
  //   icon: '/icons/menu/turtle-car.svg',
  //   // //access: 'canAccessPageCategory',

  //   routes: [
  //     {
  //       path: '/category',
  //       name: 'material',
  //       redirect: '/category/material-management',
  //       hideInMenu: true,
  //     },
  //     {
  //       path: '/category/material-management',
  //       name: 'material',
  //       icon: '/icons/menu/turtle-car.svg',
  //       component: './ItemManagement',
  //     },
  //     {
  //       path: '/category/product',
  //       name: 'products',
  //       icon: '/icons/menu/turtle-car.svg',
  //       component: './FarmProduct',
  //     },
  //     {
  //       path: '/category/producer',
  //       name: 'supplier',
  //       component: './IoTProducer',
  //       routes: [
  //         {
  //           path: '/category/producer/producer-management',
  //           name: 'producer',
  //           component: './IoTProducer',
  //           hideInMenu: true,
  //         },
  //         {
  //           path: '/category/producer/producer-group-management',
  //           name: 'producer_group',
  //           component: './IoTProducer',
  //           hideInMenu: true,
  //         },
  //       ],
  //     },
  //   ],
  // },
  // {
  //   path: '/storage-management',
  //   name: 'warehouse-management-old',
  //   icon: '/icons/menu/home-icon.svg',
  //   //access: 'canAccessPageStorageManagement',
  //   routes: [
  //     {
  //       path: '/storage-management/storage-detail',
  //       name: 'warehouse-list',
  //       component: './StorageManagement/StorageDetail',
  //     },
  //     {
  //       path: '/storage-management/category-management',
  //       name: 'inventory-material',
  //       icon: '/icons/menu/home-icon.svg',
  //       // redirect: '/inventory-management/details',
  //       component: './StorageManagement/CategoryManagement',
  //       routes: [
  //         {
  //           path: '/storage-management/category-management/details',
  //           name: 'storage-category',
  //           component: './StorageManagement/CategoryManagement',
  //           hideInMenu: true,
  //         },
  //         {
  //           path: '/storage-management/category-management/import-history',
  //           name: 'import-history',
  //           component: './StorageManagement/CategoryManagement/ImportHistory',
  //           hideInMenu: true,
  //         },
  //         {
  //           path: '/storage-management/category-management/export-history',
  //           name: 'export-history',
  //           component: './StorageManagement/CategoryManagement/ExportHistory',
  //           hideInMenu: true,
  //         },
  //         {
  //           path: '/storage-management/category-management/check-history',
  //           name: 'check-history',
  //           component: './StorageManagement/CategoryManagement/CheckHistory',
  //           hideInMenu: true,
  //         },
  //       ],
  //     },
  //     {
  //       path: '/storage-management/product-management',
  //       name: 'inventory-products',
  //       icon: '/icons/menu/home-icon.svg',
  //       component: './StorageManagement/ProductManagement',
  //       routes: [
  //         {
  //           path: '/storage-management/product-management/details',
  //           name: 'storage-product',
  //           component: './StorageManagement/ProductManagement',
  //           hideInMenu: true,
  //         },
  //         {
  //           path: '/storage-management/product-management/import-history',
  //           name: 'import-history',
  //           component: './StorageManagement/ProductManagement/ImportHistory',
  //           hideInMenu: true,
  //         },
  //         {
  //           path: '/storage-management/product-management/export-history',
  //           name: 'export-history',
  //           component: './StorageManagement/ProductManagement/ExportHistory',
  //           hideInMenu: true,
  //         },
  //         {
  //           path: '/storage-management/product-management/check-history',
  //           name: 'check-history',
  //           component: './StorageManagement/ProductManagement/CheckHistory',
  //           hideInMenu: true,
  //         },
  //       ],
  //     },
  //   ],
  // },
  {
    path: '/documents',
    name: 'documents',
    icon: 'QuestionCircleOutlined',
    component: './Docs/CropLibrary',
  },
  {
    path: '/documents/:id/detail',
    component: './Docs/CropLibrary/Detail',
    name: 'Chi tiết tài liệu',
    hideInMenu: true,
    routes: [
      {
        path: '/documents/:id/detail',
        name: 'Chi tiết tài liệu',
        redirect: '/documents/:id/detail/general-info',
      },
      {
        path: '/documents/:id/detail/general-info',
        name: 'Thông tin chung',
        // component: './FarmingManagement/CropLibrary/Detail/GeneralInfo',
      },
      {
        path: '/documents/:id/detail/care-instructions',
        name: 'Hướng dẫn chăm sóc',
        // component: './FarmingManagement/CropLibrary/Detail/CareInstructions',
      },
    ],
  },
  {
    path: '/documents/:id/edit',
    component: './Docs/CropLibrary/Edit',
    name: 'Chi tiết tài liệu',
    locale: 'false',
    hideInMenu: true,
    routes: [
      {
        path: '/documents/:id/edit',
        name: 'Chi tiết tài liệu',
        redirect: '/documents/:id/edit/general-info',
      },
      {
        path: '/documents/:id/edit/general-info',
        name: 'Chỉnh sửa tài liệu',
        // component: './FarmingManagement/CropLibrary/Edit/GeneralInfo',
      },
      {
        path: '/documents/:id/edit/care-instructions',
        name: 'Chỉnh sửa tài liệu',
        // component: './FarmingManagement/CropLibrary/Edit/CareInstructions',
      },
    ],
  },
  {
    path: '/account-information',
    name: 'account-information',
    icon: '/icons/menu/farmer.svg',
    component: './User/MyAccount',
  },
  {
    path: '/log-out',
    name: 'logout',
    icon: '/icons/menu/log-out.svg',
    component: './User/Logout',

    // routes: [
    //   {
    //     path: '/account-information',
    //     redirect: '/account-information',
    //     hideInMenu: true,
    //   },
    // ],
  },
  // {
  //   path: '/dashboard',
  //   name: 'Home',
  //   icon: 'HomeOutlined',
  //   //access: 'isIoTUser',
  //   component: './Dashboard',
  // },
  // {
  //   path: '/customer',
  //   name: 'Khách hàng',
  //   icon: 'UsergroupAddOutlined',
  //   //access: 'isAdmin',
  //   component: './Customer',
  // },
  // {
  //   name: 'Chi tiết khách hàng',
  //   path: '/customer/detail',
  //   component: './Customer/Detail',
  //   hideInMenu: true,
  // },
  // {
  //   path: '/product',
  //   name: 'Sản phẩm và giải pháp',
  //   icon: 'SlackOutlined',
  //   //access: 'isAdmin',
  //   component: './Products',
  // },
  // {
  //   path: '/product/detail',
  //   name: 'Chi tiết sản phẩm',
  //   icon: 'SlackOutlined',
  //   //access: 'isAdmin',
  //   component: './Products/Detail',
  //   hideInMenu: true,
  // },
  // {
  //   path: '/device',
  //   name: 'Thiết bị',
  //   icon: 'Table',
  //   //access: 'isAdmin',
  //   component: './Dashboard',
  // },
  // {
  //   name: 'My Account',
  //   icon: 'UserOutlined',
  //   path: '/my-account',
  //   component: './User/MyAccount',
  //   //access: 'isIoTUser',
  // },

  { path: '/', redirect: '/home' },
  { path: '*', component: './404' },
];
