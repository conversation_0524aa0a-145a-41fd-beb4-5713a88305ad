import { getTagColor } from '@/pages/FarmingDiaryStatic/FarmingProcedure/helper';
import { Process } from '@/services/diary-2/process';
import { Task } from '@/services/diary-2/task';
import { createEmptyArray } from '@/utils/array';
import { ActionType, ProColumns, ProTable } from '@ant-design/pro-components';
import { Link, useIntl } from '@umijs/max';
import { Tag } from 'antd';
import { FC, ReactNode, useRef } from 'react';

interface TaskListProps {
  children?: ReactNode;
  dataStage?: Process['states'][0];
}
type ItemType = Process['states'][0]['tasks'][0]['items'][0];
const TaskList: FC<TaskListProps> = ({ children, dataStage }) => {
  const tasks = dataStage?.tasks || [];
  const maxItem = tasks.reduce((acc, cur) => (acc > cur.items.length ? acc : cur.items.length), 0);
  const actionRef = useRef<ActionType>();
  const handleReload = () => {
    actionRef.current?.reload?.();
  };
  const { formatMessage } = useIntl();
  return (
    <ProTable
      actionRef={actionRef}
      form={{
        labelWidth: 'auto',
      }}
      search={false}
      scroll={{ x: 'max-content' }}
      dataSource={tasks as any}
      columns={
        [
          {
            title: 'STT',
            render(dom, entity, index, action, schema) {
              return index + 1;
            },
            hideInSearch: true,
          },
          {
            title: formatMessage({
              id: 'common.task_name',
            }),
            dataIndex: 'label',
            render(dom, entity, index, action, schema) {
              return <Link to={`/farming-diary-static/task/edit/${entity.name}`}>{dom}</Link>;
            },
          },
          {
            title: formatMessage({
              id: 'common.assigned_to',
            }),
            render(dom, entity, index, action, schema) {
              return <>{`${entity.first_name} ${entity.last_name}`}</>;
            },
          },
          {
            title: formatMessage({
              id: 'common.level',
            }),
            dataIndex: 'level',
            render(dom, entity, index, action, schema) {
              const colorMap = {
                Priority: 'warning',
                Important: 'error',
                Common: 'success',
              };
              return <Tag color={getTagColor(entity.level)}>{dom}</Tag>;
            },
          },
          {
            title: formatMessage({
              id: 'common.execution_time',
            }),
            dataIndex: 'execution_day',
            render(dom, entity, index, action, schema) {
              return `${formatMessage({
                id: 'common.date',
              })} ${dom}`;
            },
          },
          ...createEmptyArray(maxItem).map<ProColumns<Task>>((item) => ({
            title: `${formatMessage({
              id: 'common.supplies',
            })} ${item + 1}`,
            render(dom, entity, index, action, schema) {
              const items = (entity as any).items as ItemType[];
              const data = items?.[item];
              return `${data?.quantity || ''} ${data?.label || ''}`;
            },
          })),
          {
            title: formatMessage({
              id: 'common.note',
            }),
            dataIndex: 'description',
          },
          // {
          //   hideInSearch: true,
          //   hideInForm: true,
          //   render(dom, entity, index, action, schema) {
          //     return (
          //       <Space>
          //         <DeleteTask id={entity.name} onSuccess={handleReload} />
          //       </Space>
          //     );
          //   },
          // },
        ] satisfies Array<ProColumns<Task>>
      }
    />
  );
};

export default TaskList;
