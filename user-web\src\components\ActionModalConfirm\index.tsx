import { DeleteOutlined } from '@ant-design/icons';
import { useIntl } from '@umijs/max';
import { App, Button, ButtonProps, ModalFuncProps } from 'antd';
import { FC, useCallback } from 'react';

interface ActionModalConfirmProps {
  btnProps?: ButtonProps;
  modalProps?: ModalFuncProps;
  isDelete?: boolean;
}

const ActionModalConfirm: FC<ActionModalConfirmProps> = ({ modalProps, btnProps, isDelete }) => {
  const { modal } = App.useApp();
  const { formatMessage } = useIntl();
  const onClick = useCallback(() => {
    modal.confirm({
      ...modalProps,
      title: isDelete
        ? formatMessage({
            id: 'common.sentences.confirm-delete',
          })
        : formatMessage({ id: 'action.confirm' }),
      okButtonProps: {
        danger: true,

        ...modalProps?.okButtonProps,
      },
    });
  }, [modal, modalProps, btnProps]);
  return <Button danger icon={<DeleteOutlined />} size="small" onClick={onClick} {...btnProps} />;
};

export default ActionModalConfirm;
