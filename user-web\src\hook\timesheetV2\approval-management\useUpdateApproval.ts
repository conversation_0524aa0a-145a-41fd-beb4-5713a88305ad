import { editTimeSheetApprove } from '@/services/timesheetsV2';
import { useIntl,useRequest } from '@umijs/max';
import { App } from 'antd';


export function useUpdateApproval({ onSuccess } = {} as { onSuccess?: () => void }) {
  const { message } = App.useApp();
  const { formatMessage } = useIntl();

  return useRequest(editTimeSheetApprove, {
    manual: true,
    onError: (err) => {
      message.error(err.message);
    },
    onSuccess: () => {
      message.success(
        formatMessage({
          id: 'common.success',
        }),
      );
      onSuccess?.();
    },
  });
}
