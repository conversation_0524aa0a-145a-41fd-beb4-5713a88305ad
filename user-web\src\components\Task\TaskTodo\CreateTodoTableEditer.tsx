import { getCustomerUserList } from '@/services/customerUser';
import { DeleteOutlined } from '@ant-design/icons';
import type { ProColumns } from '@ant-design/pro-components';
import { ProTable } from '@ant-design/pro-components';
import { useIntl } from '@umijs/max';
import { Button } from 'antd';
import React from 'react';
import { v4 } from 'uuid';
import CreateTodo from './CreateTodo';
import UpdateTodo from './UpdateTodo';

type DataSourceType = {
  id: React.Key;
  title?: string;
  decs?: string;
  state?: string;
  created_at?: string;
  children?: DataSourceType[];
};

export default ({
  dataSource,
  setDataSource,
  customerUserOptions,
}: {
  dataSource: any;
  setDataSource: any;
  customerUserOptions: any;
}) => {
  const intl = useIntl();
  const columns: ProColumns<DataSourceType>[] = [
    {
      title: intl.formatMessage({ id: 'common.label' }),
      dataIndex: 'label',
    },
    {
      title: intl.formatMessage({ id: 'common.executor' }), // 'Người thực hiện'
      dataIndex: 'customer_user_id',
      width: 250,
      render: (dom: any, entity: any) => {
        const customerUserName =
          entity.customer_user_name || `${entity.user_last_name} ${entity.user_first_name}`;
        return <>{customerUserName}</>;
      },
    },
    {
      title: intl.formatMessage({ id: 'common.description' }), // 'Mô tả'
      dataIndex: 'description',
      formItemProps: {
        rules: [
          {
            required: true,
            whitespace: true,
            message: intl.formatMessage({ id: 'common.description_required' }), // 'Mô tả không được để trống'
          },
        ],
      },
    },
    {
      title: intl.formatMessage({ id: 'common.action' }), // 'Hành động'
      dataIndex: 'name',
      render: (dom: any, entity: any) => {
        return (
          <>
            <UpdateTodo data={entity} onFinish={handlerEdit} /> {'  '}
            <Button
              danger
              size="small"
              style={{ display: 'flex', alignItems: 'center' }}
              onClick={() => handlerRemove(dom)}
            >
              <DeleteOutlined />
            </Button>
          </>
        );
      },
    },
  ];

  const handlerAddTodo = async (values: any) => {
    try {
      values.name = v4();
      const userInfo = await getCustomerUserList({
        filters: [['iot_customer_user', 'name', 'like', values.customer_user_id]],
      });
      values.user_first_name = userInfo.data[0].first_name;
      values.user_last_name = userInfo.data[0].last_name;
      values.customer_user_id = userInfo.data[0].name;
      const _newData = dataSource;
      _newData.push(values);
      setDataSource([..._newData]);
    } catch (error) {
      console.log(error);
    }
  };

  const handlerEdit = async (values: any) => {
    try {
      const userInfo = await getCustomerUserList({
        filters: [['iot_customer_user', 'name', 'like', values.customer_user_id]],
      });
      values.user_first_name = userInfo.data[0].first_name;
      values.user_last_name = userInfo.data[0].last_name;
      values.customer_user_id = userInfo.data[0].name;
      const _newData = dataSource.map((d: any) => {
        return d.name === values.name ? values : d;
      });
      setDataSource([..._newData]);
    } catch (error) {
      console.log(error);
    }
  };
  const handlerRemove = async (name: any) => {
    try {
      const _newData = dataSource.filter((d: any) => {
        return d.name !== name;
      });
      setDataSource([..._newData]);
    } catch (error) {
      console.log(error);
    }
  };
  return (
    <>
      <ProTable
        headerTitle={intl.formatMessage({ id: 'common.sub_task' })} // 'Công việc con'
        columns={columns}
        rowKey="name"
        dataSource={[...dataSource]}
        toolBarRender={() => {
          return [<CreateTodo onFinish={handlerAddTodo} />];
        }}
        search={false}
      />
    </>
  );
};
