import ActionModalConfirm from '@/components/ActionModalConfirm';
import { FC, ReactNode } from 'react';
import useDelete from '../hooks/useDelete';

interface DeleteNoteProps {
  children?: ReactNode;
  id: string;
  onSuccess?: () => void;
}

const DeleteNote: FC<DeleteNoteProps> = ({ children, id, onSuccess }) => {
  const { run } = useDelete({
    onSuccess,
  });

  return (
    <ActionModalConfirm
      isDelete={true}
      modalProps={{
        async onOk(...args) {
          await run(id);
          return true;
        },
      }}
    />
  );
};

export default DeleteNote;
