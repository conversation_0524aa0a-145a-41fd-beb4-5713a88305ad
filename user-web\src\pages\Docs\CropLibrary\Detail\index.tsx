import PageContainerTabsWithPath from '@/components/PageContainerTabsWithPath';
import { deletePlantAllResources } from '@/services/plants';
import { Guide } from '@/types/guide.type';
import { InfoTab } from '@/types/infoTab.type';
import { myLazy } from '@/utils/lazy';
import { DeleteOutlined, EditOutlined } from '@ant-design/icons';
import { DescriptionsSkeleton } from '@ant-design/pro-components';
import { history, Link, useAccess, useIntl, useModel, useParams } from '@umijs/max';
import { Button, message, Popconfirm } from 'antd';
import { FC, Fragment, ReactNode, useEffect, useState } from 'react';

const GeneralInfo = myLazy(() => import('./GeneralInfo'));
const CareInstructions = myLazy(() => import('./CareInstructions'));

interface DetailProps {
  children?: ReactNode;
}

const Detail: FC<DetailProps> = ({ children }) => {
  const { id } = useParams();

  const { myPlant, setMyPlant, isAccessEditDocs } = useModel('Docs');
  const [guides, setGuides] = useState<Guide[]>([]);
  const [inforTabs, setInforTabs] = useState<InfoTab[]>([]);
  const intl = useIntl();
  useEffect(() => {
    const selectedPlant = myPlant.find((plant) => plant.name === id);
    if (selectedPlant === undefined) {
      history.back();
      message.error(`Không tìm thấy tài liệu!`);
    } else {
      selectedPlant.guide_list = selectedPlant?.guide_list
        ?.sort((a, b) => (a.sort_index || 0) - (b.sort_index || 0))
        ?.map((guide, index) => ({
          ...guide,
          sort_index: index,
        }));
      selectedPlant.infor_tab_list = selectedPlant?.infor_tab_list
        ?.sort((a, b) => (a.sort_index || 0) - (b.sort_index || 0))
        ?.map((inforTab, index) => ({
          ...inforTab,
          sort_index: index,
        }));
      if (selectedPlant.guide_list) setGuides(selectedPlant.guide_list);
      if (selectedPlant.infor_tab_list) setInforTabs(selectedPlant.infor_tab_list);
    }
  }, [myPlant]);
  const handleRemove = async () => {
    await deletePlantAllResources(id)
      .then((res) => {
        setMyPlant((prev) => prev.filter((item) => item.name !== id));
        message.success(intl.formatMessage({ id: 'common.success' }));
        history.push(`/documents`);
      })
      .catch((error) => {
        message.error(`Lỗi khi xoá tài liệu: ${error}`, 5);
      });
  };
  const access = useAccess();
  const canUpdatePlant = isAccessEditDocs;
  const canDeletePlant = isAccessEditDocs;

  const RemovePlantButton = (
    <Fragment key="delete">
      {access.canDeleteAllInPageAccess() && (
        <Popconfirm
          title={intl.formatMessage({ id: 'plantTab.deletePlant' })}
          description={intl.formatMessage({ id: 'plantTab.deletePlantConfirm' })}
          onConfirm={() => handleRemove()}
          key="delete"
        >
          <Button icon={<DeleteOutlined />}> {intl.formatMessage({ id: 'common.delete' })}</Button>
        </Popconfirm>
      )}
    </Fragment>
  );
  const EditPlantButton = (
    <Link to={`/documents/${id}/edit`} key={'edit'}>
      <Button icon={<EditOutlined />} type="primary">
        {intl.formatMessage({ id: 'common.edit' })}
      </Button>
    </Link>
  );
  const extraPage = [];
  if (canDeletePlant) {
    extraPage.push(RemovePlantButton);
  }
  if (canUpdatePlant) {
    extraPage.push(EditPlantButton);
  }
  return (
    <PageContainerTabsWithPath
      tabItems={[
        {
          tab: intl.formatMessage({ id: 'plantTab.generalInfo' }),
          key: 'general-info',
          fallback: <DescriptionsSkeleton active />,
          extraPage: extraPage,
          children: <GeneralInfo infoTabs={inforTabs} />,
        },
        // {
        //   tab: intl.formatMessage({ id: 'plantTab.careGuide' }),
        //   key: 'care-instructions',
        //   fallback: <DescriptionsSkeleton active />,
        //   extraPage: extraPage,
        //   children: <CareInstructions guides={guides} />,
        // },
      ]}
      generalPath={`/farming-management/crop-library/${id}/detail`}
    />
  );
};

export default Detail;
