import { useModel } from '@umijs/max';
import { message } from 'antd';
import { useEffect, useState } from 'react';
import { createItem, listAllItem, updateItem, removeItem } from '@/services/workType';
import { IIotWorkType } from '@/services/workType/type';

export default () => {
    const [myWorkType, setMyWorkType] = useState<IIotWorkType[]>([]);
    const [loadingResource, setLoadingResource] = useState<any>(false);
    const { initialState } = useModel('@@initialState');

    const fetchAllWorkType = async () => {
        try {
            setLoadingResource(true);
            const data = await listAllItem();
            setMyWorkType(data.data || []);
        } catch (error: any) {
            message.error(error?.toString());
        } finally {
            setLoadingResource(false);
        }
    };

    useEffect(() => {
        if (initialState?.currentUser) {
            fetchAllWorkType();
        }
    }, [initialState]);

    return {
        myWorkType,
        loadingResource,
        fetchAllWorkType
    };
};