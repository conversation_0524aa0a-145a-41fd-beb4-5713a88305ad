import { deletePlanState } from '@/services/farming-plan';
import { IFarmPlanRes } from '@/types/IFarmPlanRes.type';
import { useAccess,useIntl } from '@umijs/max';
import { App,Collapse,DatePicker,Row,Space,theme,Typography } from 'antd';
import dayjs from 'dayjs';
import { useEffect } from 'react';
import StateTaskTable from './StateTaskTable';
const { RangePicker } = DatePicker;

interface Props {
  planId: string;
  onSuccess?: () => void;
  stateListRequest: any;
}
const StateCollapsableList = ({ planId, onSuccess, stateListRequest }: Props) => {
  const { token } = theme.useToken();
  const { modal, message } = App.useApp();
  const { run, data, loading, refresh } = stateListRequest;
  const intl = useIntl();
  // const { run, data, loading, refresh } = useRequest(() =>
  //   getFarmingPlanStates({
  //     filters: `[["${DOCTYPE_ERP.iotFarmingPlanState}", "farming_plan", "like", "${planId}"]]`,
  //   }),
  // );
  const onDeletePlanState = ({ stateId }: { stateId: string }) => {
    modal.confirm({
      content: 'Are you sure you want to delete this state?',
      onOk: async () => {
        try {
          await deletePlanState({
            name: stateId,
          });
          message.success({
            content: 'Delete successfully',
          });
          refresh();
          return true;
        } catch (error) {
          message.error({
            content: 'Delete error, please try again',
          });
          return false;
        }
      },
      okButtonProps: {
        danger: true,
      },
    });
  };
  const panelStyle: React.CSSProperties = {
    marginBottom: 24,
    background: token.colorBgContainer,
    borderRadius: token.borderRadiusLG,
    // border: 'none',
  };

  useEffect(() => {
    const fetchData = () => {
      if (planId) run();
      console.log('data state', data);
    };
    fetchData();
  }, [planId]);

  const access = useAccess();
  const canDeleteState = access.canDeleteInStateManagement();
  const canUpdateState = access.canUpdateInStateManagement();
  const dateFormat = 'YYYY/MM/DD';

  return (
    <Collapse
      accordion // One active at a time
      bordered={false}
      // defaultActiveKey={[0]}
      style={{ background: 'none' }}
      collapsible="header"
      items={
        (data as IFarmPlanRes[])?.map((state, index) => ({
          label: <Typography.Title level={5}>{state.label}</Typography.Title>,
          key: index,
          style: panelStyle,
          children: <StateTaskTable stateId={state.name} />,
          extra: (
            <Row justify="center">
              <Space align="center">
                {data[index] && data[index].start_date && data[index].end_date ? (
                  <RangePicker
                    size={'middle'}
                    value={[
                      dayjs(data[index].start_date, dateFormat),
                      dayjs(data[index].end_date, dateFormat),
                    ]}
                    disabled={true}
                    format={dateFormat}
                  />
                ) : null}
                {/* {canUpdateState && (
                  <EditPlanState
                    key="edit"
                    id={state.name}
                    onSuccess={() => {
                      refresh();
                    }}
                  />
                )}

                {canDeleteState && (
                  <Button
                    size="middle"
                    icon={<DeleteOutlined />}
                    key={'delete'}
                    danger
                    onClick={(e) => {
                      e.stopPropagation();
                      onDeletePlanState({ stateId: state.name });
                    }}
                  >
                    {intl.formatMessage({ id: 'common.delete' })}
                  </Button>
                )} */}
              </Space>
            </Row>
          ),
        })) || []
      }
    ></Collapse>
  );
};

export default StateCollapsableList;
