import { createItem } from '@/services/diary-2/task';
import { ProForm } from '@ant-design/pro-components';
import { history, useIntl, useLocation } from '@umijs/max';
import { App } from 'antd';
import { FC, ReactNode } from 'react';
import useCreate from '../../hooks/useCreate';
import DetailedInfo from './DetailedInfo';
import ImgVideos from './ImgVideos';
import MaterialUsed, { RelatedItemForm } from './MaterialUsed';

interface CreateTaskProps {
  children?: ReactNode;
}

const CreateTask: FC<CreateTaskProps> = ({ children }) => {
  const { formatMessage } = useIntl();
  const { message } = App.useApp();

  const location = useLocation();
  const state: any = location.state;
  const { run, loading } = useCreate({
    onSuccess: () => {
      if (state?.fromStageCreate) {
        history.replace('/farming-diary-static/stage-of-crop/list');
      } else if (state?.fromStageEdit) {
        history.replace(`/farming-diary-static/stage-of-crop/list`);
      } else {
        history.push('/farming-diary-static/task/list');
      }
    },
  });
  const onFinish = async (values: any) => {
    const res = await run({
      label: values.label,
      description: values.description,
      assigned_to: values.assigned_to,
      image: values.image,
      level: values.level,
      expire_time_in_days: values.expire_time_in_days,
      execution_day: values.execution_day,
    });
    if (values.related_items && values.related_items.length > 0) {
      await Promise.all(
        values.related_items.map(async (item: RelatedItemForm) => {
          return createItem({
            item_id: item.item_id,
            task_id: res.name,
            quantity: item.quantity,
            uom_id: item.uom_id,
          });
        }),
      );
    }
    message.success(
      formatMessage({
        id: 'common.success',
      }),
    );

    return true;
  };
  return (
    <ProForm onFinish={onFinish} loading={loading}>
      <div className="mb-4 space-y-4">
        <DetailedInfo />
        <MaterialUsed />
        <ImgVideos />
      </div>
    </ProForm>
  );
};

export default CreateTask;
