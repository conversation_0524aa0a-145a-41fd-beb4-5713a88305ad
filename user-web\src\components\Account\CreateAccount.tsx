import { sscript } from '@/services/sscript';
import { allAxcelaUserTypeValue } from '@/services/valueEnums';
import {
  <PERSON>ert,
  Button,
  Card,
  Checkbox,
  Col,
  ConfigProvider,
  Form,
  Input,
  message,
  Row,
  Select,
} from 'antd';
import { useEffect, useState } from 'react';
import { v4 as uuidv4 } from 'uuid';
const { Item } = Form;
const { Option } = Select;

export const CreateAccount = (params: { entity: any; role: string }) => {
  const [loading, setLoading] = useState(false);
  const [account, setAccount] = useState({});
  const [newPass, setNewpass] = useState('');

  const [form] = Form.useForm();

  const fetchAccountInfor = async () => {
    let { email } = params.entity;
    if (params.role === 'Axcela Student') {
      email = params.entity.student_email;
    } else if (params.role === 'Customer Business HR') {
      email = params.entity.businesshr_email;
    } else {
      email = params.entity.employee_email;
    }
    try {
      setLoading(true);
      const params = {
        doctype: 'User',
        name: email,
      };
      const result = await sscript('frappe.desk.form.load.getdoc', params, 'GET');
      if (result.docs.length) {
        setAccount(result.docs[0]);
        form.setFieldsValue(result.docs[0]);
      }
    } catch (error) {
      console.log(error);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchAccountInfor();
  }, [params.entity]);

  const createNewAccount = async () => {
    let { first_name, last_name, email, user_type, new_password } = params.entity;
    new_password = uuidv4().split('-')[[0]];
    if (params.role === 'Axcela Student') {
      first_name = params.entity.student_first_name;
      last_name = params.entity.student_last_name;
      email = params.entity.student_email;
      user_type = params.role;
    } else if (params.role === 'Customer Business HR') {
      first_name = params.entity.businesshr_first_name;
      last_name = params.entity.businesshr_last_name;
      email = params.entity.businesshr_email;
      user_type = params.role;
    } else {
      first_name = params.entity.employee_first_name;
      last_name = params.entity.employee_last_name;
      email = params.entity.employee_email;
      user_type = params.role;
    }
    try {
      setLoading(true);
      let req_params: any = {
        doc: {
          docstatus: 0,
          doctype: 'User',
          name: email,
          __islocal: 1,
          __unsaved: 1,
          owner: 'Administrator',
          enabled: 1,
          language: 'en',
          send_welcome_email: 0,
          unsubscribed: 0,
          desk_theme: 'Light',
          mute_sounds: 0,
          logout_all_sessions: 1,
          document_follow_notify: 0,
          document_follow_frequency: 'Daily',
          follow_created_documents: 0,
          follow_commented_documents: 0,
          follow_liked_documents: 0,
          follow_assigned_documents: 0,
          follow_shared_documents: 0,
          thread_notify: 1,
          send_me_a_copy: 0,
          allowed_in_mentions: 1,
          simultaneous_sessions: 1,
          user_type: user_type,
          bypass_restrict_ip_check_if_2fa_enabled: 0,
          time_zone: 'Asia/Kolkata',
          email: email,
          first_name: first_name,
          last_name: last_name,
          new_password: new_password,
        },
        action: 'Save',
      };
      req_params.doc = JSON.stringify(req_params.doc);
      const result = await sscript('frappe.desk.form.save.savedocs', req_params, 'POST');
      setNewpass(new_password);
    } catch (error) {
    } finally {
      setLoading(false);
    }
  };
  const resetPasswordNewAccount = async () => {
    const new_password = uuidv4().split('-')[[0]];
    try {
      setLoading(true);
      let req_params: any = {
        doc: account,
        action: 'Save',
      };
      req_params.doc.new_password = new_password;
      req_params.doc = JSON.stringify(req_params.doc);
      const result = await sscript('frappe.desk.form.save.savedocs', req_params, 'POST');
      setNewpass(new_password);
    } catch (error: any) {
      message.error(error.toString());
    } finally {
      setLoading(false);
    }
  };
  return (
    <ConfigProvider>
      <Row gutter={5}>
        <Col className="gutter-row" md={24}>
          {newPass ? (
            <>
              <Alert
                message={
                  <>
                    <h3>New password</h3>
                    <h1>{newPass}</h1>
                  </>
                }
                type="success"
              />
              ;
            </>
          ) : (
            ''
          )}
        </Col>
        {Object.keys(account).length ? (
          <>
            <Col className="gutter-row" md={12}>
              <Card loading={loading} title="Account Infor">
                <Form
                  // size='small'
                  layout="horizontal"
                  labelCol={{ span: 24 }}
                  labelAlign="left"
                  form={form}
                  onFinish={async (value: any) => {
                    try {
                      setLoading(true);
                      let req_params: any = {
                        doc: account,
                        action: 'Save',
                      };
                      req_params.doc.user_type = value.user_type;
                      req_params.doc.enabled = value.enabled;
                      req_params.doc = JSON.stringify(req_params.doc);
                      const result = await sscript(
                        'frappe.desk.form.save.savedocs',
                        req_params,
                        'POST',
                      );
                      await fetchAccountInfor();
                    } catch (error: any) {
                      message.error(error.toString());
                    } finally {
                      setLoading(false);
                    }
                  }}
                >
                  <Row gutter={5}>
                    <Col className="gutter-row" md={24}>
                      <Item
                        label="Email"
                        labelCol={{ span: 24 }}
                        rules={[
                          {
                            required: true,
                            message: 'Bắt buộc điền',
                          },
                        ]}
                        name="name"
                      >
                        <Input disabled />
                      </Item>
                    </Col>
                    <Col className="gutter-row" md={24}>
                      <Item label="User Role" labelCol={{ span: 24 }} name="user_type">
                        <Select options={allAxcelaUserTypeValue}></Select>
                      </Item>
                    </Col>
                    <Col className="gutter-row" md={24}>
                      <Item
                        label="Enable"
                        labelCol={{ span: 24 }}
                        name="enabled"
                        valuePropName="checked"
                      >
                        <Checkbox></Checkbox>
                      </Item>
                    </Col>
                  </Row>
                  <Col className="gutter-row" md={6}>
                    <Button htmlType="submit" type="primary">
                      Save
                    </Button>
                  </Col>
                </Form>
              </Card>
            </Col>
            <Col className="gutter-row" md={12}>
              <Card loading={loading} title="Action">
                <Button type="primary" onClick={resetPasswordNewAccount}>
                  Reset password
                </Button>
              </Card>
            </Col>
          </>
        ) : (
          <>
            <Col className="gutter-row" md={12}>
              <Card loading={loading} title="Account Infor">
                {Object.keys(account).length ? (
                  <></>
                ) : (
                  <>
                    {newPass ? (
                      <></>
                    ) : (
                      <Button type="primary" onClick={createNewAccount}>
                        Create Account
                      </Button>
                    )}
                  </>
                )}
              </Card>
            </Col>
          </>
        )}
      </Row>
    </ConfigProvider>
  );
};
