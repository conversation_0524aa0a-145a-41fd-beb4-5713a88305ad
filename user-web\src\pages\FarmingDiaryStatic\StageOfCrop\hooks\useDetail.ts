import { DOCTYPE_ERP } from '@/common/contanst/constanst';
import { getStageList, Stage } from '@/services/diary-2/stage';
import { useRequest } from '@umijs/max';

export default function useDetail(
  { id, onSuccess } = {} as {
    onSuccess?: (data: Stage) => void;
    id: string;
  },
) {
  return useRequest(
    async () => {
      if (!id)
        return {
          data: null,
        };
      const res = await getStageList({
        filters: [[DOCTYPE_ERP.iot_diary_v2_state, 'name', '=', id]],
        order_by: 'name asc',
        page: 1,
        size: 1,
      });
      const data = res?.data?.[0];
      if (!data) throw new Error('Not found');
      return {
        data: data,
      };
    },
    {
      onSuccess: (data) => {
        if (data) onSuccess?.(data);
      },
      refreshDeps: [id],
    },
  );
}
