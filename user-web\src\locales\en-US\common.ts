export default {
  'common.upload-error-file-big': 'File size must be less than',
  'common.total-site': 'Total site',
  'common.total-design-capacity': 'Total design capacity (kWp)',
  'common.installed-capacity-(kWp)': 'Installed capacity (kWp)',
  'common.capacity-hdmb': 'Capacity HDMB',
  'common.total-capacity-hdmb': 'Total HDMB capacity',
  'common.yesterday-revenue-reference': "Yesterday 's revenue (Reference)",
  'common.total': 'Total',

  'common.all': 'All',
  'common.select-site': 'Select site',
  'common.select-inverter': 'Select inverter',
  'common.overview': 'Overview',
  'common.revenue': 'Revenue',
  'common.by-hour': 'By hour',
  'common.by-date': 'By date',
  'common.by-week': 'By week',
  'common.by-month': 'By month',
  'common.by-quarterly': 'By quarterly',
  'common.by-year': 'By year',
  'common.today': 'Today',
  'common.two_days_ago': '2 days ago',
  'common.seven_days_ago': '7 days ago',

  'common.total-revenue': 'Total revenue',
  'common.total-cost': 'Total cost',
  'common.detailed-revenue-by-site': 'Detailed revenue by site',
  'common.total-revenue-of-sites': 'Total revenue of sites',
  'common.quantity': 'Quantity',
  'common.detailed-output-by-site': 'Detailed output by site',
  'common.total-output-of-sites': 'Total output of sites',
  'common.total-output(kWh)': 'Total output (kWh)',
  'common.average-performance': 'Average performance',
  'common.activity-data-for-the-day': 'Activity data for the day',
  'common.revenue-reference': 'Revenue (for reference)',
  'common.site_power_output_from_to': 'Site power output from {start} to {end}',
  'common.operation_plan': 'Operation plan',
  'common.control': 'Control',

  'common.download-all-site-performance-reports': 'Download all site performance reports',
  'common.download-detailed-solar-power-report-file-at-site':
    'Download detailed solar power report file at site',
  'common.this_week_s_tasks': "This week's tasks",
  'common.more': 'More',
  'common.all_site': 'All site',

  'common.project': 'Project',
  'common.project-name': 'Project name',
  'common.location': 'Location',
  'common.area': 'Zone',
  'common.pagination-show-total': '{start}-{end} out of {total} {name}',
  'common.general-information': 'General information',
  'common.list-of-devices': 'List of devices',
  'common.warranty-maintenance': 'Warranty / maintenance',

  'common.device-code': 'Device code',
  'common.product-line': 'Product line',
  'common.dc-installed-power-(kwp)': 'DC installed power (kWp)',
  'common.status': 'Status',
  'common.other-assets': 'Other assets',
  'common.categories': 'Items',
  'common.click-to-experience-the-feature': 'Click to experience the feature',
  'common.feature-in-progress,-please-come-back-later~~':
    'Feature-in-progress,-please-come-back-later~~"',
  'common.notifications': 'Notifications',

  'common.fullname': 'Full name',
  'common.email': 'Email',
  'common.avatar': 'Avatar',
  'common.permission': 'Permission',
  'common.is-blocked': 'Is blocked',
  'common.yes': 'Yes',
  'common.no': 'No',
  'common.add': 'Add',
  'common.details': 'Details',
  'common.action': 'Action',
  'common.title': 'Title',
  'common.description': 'Description',
  'common.time': 'Time',
  'common.level': 'Level',
  'common.serious': 'Serious',
  'common.info': 'Information',
  'common.warning': 'Warning',
  'common.download-daily-report': 'Download daily report',
  'common.download-monthly-report': 'Download monthly report',
  'common.select-day': 'Select day',
  'common.select-month': 'Select month',
  'common.topic': 'Topic',
  'common.daily': 'Daily',
  'common.monthly': 'Monthly',
  'common.error': 'An error has occurred. Please try again!',
  'common.success': 'Success',
  'common.site-name': 'Site Name',
  'common.site-code': 'Site code',
  'common.image': 'Image',
  'common.choice-image': 'Choice image',
  'common.site-installed-power': 'Installed power (kWp)',
  'common.site-wattage-hdmb': 'Wattage HDMB',
  'common.site-internet-info': 'Internet info',
  'common.address': 'Address',
  'common.create-user': 'Create user',
  'common.update-user': 'Update user',
  'common.first-name': 'First Name',
  'common.middle-name': 'Middle name',
  'common.last-name': 'Last name',
  'common.time-zone': 'Time zone',
  'common.language': 'Language',
  'common.username': 'Username',
  'common.user-type': 'User type',
  'common.send-email-welcome': 'Send email  welcome',
  'common.name': 'Name',
  'common.gender': 'Gender',
  'common.number-phone': 'Number phone',
  'common.position': 'Position',
  'common.date-of-birth': 'Date of birth',
  'common.province/city': 'Province/City',
  'common.district': 'District',
  'common.ward': 'Ward',
  'common.delete': 'Delete',
  'common.copy': 'Copy',
  'common.edit': 'Edit',
  'common.enabled': 'Enabled',
  'common.disabled': 'Disabled',
  'common.date-created': 'Date created',
  'common.type': 'Type',
  'common.daily-operation-log': 'Daily operation log',
  'common.password': 'Password',
  'common.retry': 'Retry',
  'common.customer': 'Customer',
  'common.customer/supplier': 'Customer/ Supplier',
  'common.electricity-price': 'Electricity price',
  'common.company': 'Company',
  'common.staff-in-charge': 'Staff in charge',
  'common.owner': 'Owner',
  'common.attachments': 'Attachments',
  'common.save': 'Save',
  'common.inverter': 'Inverter',
  'common.role': 'Role',
  'common.contact-info': 'Contact information',
  'common.reload': 'Reload',
  'common.account': 'Account',
  'common.monitor': 'Monitor',
  'common.number-of-inverter': 'Number of inverter',
  'common.plan': 'Plan',
  'common.log': 'Log',
  'common.classify': 'Classify',
  'common.refresh': 'Refresh',
  'common.total_power_capacity': 'Total power capacity',
  'common.my_site': 'My site',
  'common.reduction': 'Reduction',
  'common.subject': 'Subject',
  'common.start': 'Start',
  'common.end': 'End',
  'common.reduction_power': 'Reduction Power',
  'common.reduction_enable': 'Reduction enable',
  'common.device_family': 'Device family',
  'common.device_sn': 'Device serial number',
  'common.dc_power_design': 'DC Power Design',
  'common.on_off': 'On/Off',
  'common.connections_status': 'Connection Status',
  'common.deselect_all': 'Deselect all',
  'common.table': 'Table',
  'common.calendar': 'Calendar',
  'common.year': 'Year',
  'common.month': 'Month',
  'common.week': 'Week',
  'common.day': 'Day',
  'common.list': 'List',
  'common.priority': 'Priority',
  'common.time_range': 'Time range',
  'common.progress': 'Progress',
  'common.allow_automatic_reduction': 'Allow automatic reduction',
  'common.is_group': 'Is Group',
  'common.pendingAlarm': 'Pending Alarm',
  'common.alertAt': 'Alert At',
  'common.entityType': 'Entity Type',
  'common.entity': 'Entity',
  'common.site': 'Site',
  'common.alertCode': 'Alert Code',
  'common.alertDescription': 'Alert Description',
  'common.clear_alarm': 'Clear Alarm',
  'common.confirm_clear_description': 'Confirm Clear Description',
  'common.description_confirmation': 'Description Confirmation',
  'common.because_the_mouse_bit_the_network_wire,_the_connection_was_lost':
    'Because the mouse bit the network wire, the connection was lost',
  'common.example': 'Example',
  'common.clear_at': 'Clear At',
  'common.clear_by': 'Clear By',
  'common.new_password': 'New Password',
  'common.confirm_password': 'Confirm Password',
  'common.informations': 'Informations',
  'common.full_name': 'Full Name',
  'common.user_type': 'User Type',
  'common.creation': 'Creation',
  'common.customer_name': 'Customer Name',
  'common.customer_type': 'Customer Type',
  'common.phone': 'Phone',
  'common.province': 'Province',
  'common.add_customer': 'Add Customer',
  'common.addEmployee': 'Add Employee',
  'common.personalInformation': 'Personal Information',
  'common.firstName': 'First name',
  'common.lastName': 'Last name',
  'common.dateOfBirth': 'Date of birth',
  'common.idPassport': 'ID passport',
  'common.department': 'Department',
  'common.jobTitlePosition': 'Job Tittle/ Position',
  'common.contactDetail': 'Contact detail',
  'common.is_active': 'Is active',
  'common.add_employee': 'Add employee',
  'common.persional_information': 'Persional Information',
  'common.contract_detail': 'Contract detail',
  'common.decentralization': 'Decentralization',
  'common.account_info': 'Account Info',
  'common.reset_password': 'Reset password',
  'common.create_site': 'Create Site',
  'common.alarms_pending': 'Alarms pending',
  'common.upload_document': 'Upload document',

  // sentences
  'common.sentences.confirm-delete':
    'Do you want to delete this item, this action cannot be undone!',
  'common.sentences.confirm-password': 'Enter password to confirm',
  'common.sentences.no-result-and-try-again': 'No results found, please try again!',
  'common.sentences.error-occurred-please-try-again': 'An error occurred, please try again!',
  'common.please_select_site': 'Please select site',
  'common.password_not_match': 'Password and confirm password do not match!',
  'common.change_password': 'Change Password',
  'common.error_try_again': 'Error, please try again!',
  'common.site_ connected': 'Site connected',
  'common.map': 'Map',
  'common.checking': 'Checking',
  'common.no_signal_yet': 'No signal yet',
  'common.connected': 'Connected',
  'common.disconnected': 'Disconnected',
  'common.power_rated': 'Power rated',
  'common.priority_level': 'Priority level',
  'common.creator': 'Creator',
  'common.people_involved': 'People involved',
  'common.related_equipment': 'Related equipment',
  'common.inverter_code': 'Inverter code',
  'common.device_code': 'Device code',
  'common.device_name': 'Device name',
  'common.serial_number': 'Serial number',
  'common.set_the_rate_of_set_on_per_inverter': 'Set the rate of set on per inverter',
  'common.number_string_was_connect': 'Number string was connect',
  'common.gateway_code': 'Gateway code',
  'common.sensor_code': 'Sensor code',
  'common.send_report_to_my_email': 'Send report to my email',
  'common.download_report_pdf': 'Download report PDF',
  'common.log_out': 'Log out',
  'common.search': 'Search',
  'common.delete_all_warning': 'Delete all warning',
  'common.selected': 'Selected',
  'common.cancel_selection': 'Cancel selection',
  'common.pending_warning': 'Pending warning',
  'common.clear_all_alarm_selected': 'Clear all alarm selected',
  'common.alert_at': 'Alert at',
  'common.entity_type': 'Entity type',
  'common.alert_description': 'Alert description',
  'common.alert_code': 'Alert code',
  'common.clear': 'Clear',
  'common.clear_alarms': 'Clear alarms',
  'common.not_found': 'Not found',
  'common.loading_data': 'Loading data',
  'common.finished': 'Finished',
  'common.total_alarm_pending': 'Total Alarm pending',
  'common.no_alarm_data_to_clear': 'No alarm data to clear',
  'common.total_loop': 'Total loop',
  'common.loop': 'Loop',
  'common.clear_all_alarm_successfully': 'Clear all alarm successfully',
  'common.clear_all_alarm': 'Clear all alarm',
  'common.logs': 'Logs',
  'common.error_message': 'Error',
  'common.clear_message': 'Clear message',
  'common.alarm_history': 'Alarm history',
  'common.viewed_all': 'Viewed all',
  'common.no_data': 'No data',
  'common.alarms': 'Alarms',
  'common.other': 'Other',
  'common.see_more': 'See more',
  'common.all_data_has_been_downloaded': 'All data has been downloaded',
  'common.site_code': 'Site code',
  'common.inverter_connected': 'Inverter connected',
  'common.alarm_pending': 'Alarm pending',
  'common.production_of_the_day': 'Production of the day',
  'common.revenue_of_the_day': 'Revenue of the day',
  'common.average_play_time': 'Average play time',

  // form
  'common.form.description': 'Description',
  'common.form.image': 'Image',
  'common.form.image-limit': 'Image (max 5 images)',
  'common.form.email': 'Email',
  'common.form.password': 'Password',
  'common.form.confirm_password': 'Confirm password',
  'common.form.new_password': 'New password',
  'common.form.company': 'Company',
  'common.form.tax_code': 'Tax ID',
  'common.form.address': 'Address',
  'common.form.phone': 'Phone',
  'common.form.district': 'District',
  'common.form.document': 'Document',
  //mẫu excel
  'common.form.excel_template': 'Excel template',
  'common.form.name': 'Name',
  'common.form.account_info': 'Account info',
  'common.form.account': 'Account',
  //tương đương
  'common.form.equivalent': 'equivalent',
  'common.zone_name': 'Zone name',
  'common.image_preview': 'Image preview',
  'common.add_new_zone': 'Add new zone',
  'common.note': 'Note',
  //Chỉnh sửa lúc
  'common.edit_at': 'Edit at',
  'common.to': 'To',
  'common.download_qr_code': 'Download QR',
  'common.index': 'Index',
  'common.start_date': 'Start date',
  'common.crop_start_date': 'Crop start date',
  'common.end_date': 'End date',
  'common.crop_end_date': 'Crop end date',
  'common.completion_level': 'Completion level',
  //vụ mùa
  'common.crop': 'Crop',
  'common.add_new_state': 'Add new state',
  'common.state': 'State',

  'common.all_zone': 'All zone',
  'common.value': 'Value',
  'common.is_on': 'Is on',
  'common.is_off': 'Is off',
  'common.history': 'History',
  'common.close': 'Close',
  'common.sensor_no_data_in_time': 'The sensor has no data during this time',
  'common.sending_failed_device_make_online':
    'Sending failed, please make sure your device is online!',
  'common.current_set_value': 'Current value',
  'common.new_set_value': 'New set value',
  'common.set_the_set_value': 'Set the set value',
  'common.send_to_device': 'Send to device',
  'common.device_currently_has_no_data': 'The device currently has no data',
  'common.time_period_less_than_or_equal_to_one_month':
    'Time period less than or equal to one month',
  'common.sensor_config': 'Sensor configuration',
  'common.block_size': 'Block size',
  'common.even_number_valid_from_1_24':
    'An even number, valid from 1 - 24, dividing the screen into 24 parts',

  'common.last_name': 'Last name',
  'common.first_name': 'First name',
  'common.confirm': 'Confirm',
  'common.does_not_have_an_account_create_a_new_account':
    'User does not have an account, create a new account',
  'common.create_an_account': 'Create a new account',
  'common.delete_user': 'Delete user',
  'common.add_new_user': 'Add new user',
  'common.add_new_role': 'Add new role',
  'common.role_name': 'Role name',
  'common.history_in': 'History in',
  'common.history_out': 'History out',
  'common.date': 'Date',
  'common.in_time': 'In time',
  'common.out_time': 'Out time',
  'common.location_in': 'Location in',
  'common.location_out': 'Location out',
  'common.image_in': 'Image in',
  'common.image_out': 'Image out',
  'common.zone': 'Zone',
  'common.employee': 'Employee',
  'common.number_of_working_days': 'Number of working days',
  'common.select-category': 'Select item',
  'common.qr_code_for_task': 'Task QR code',
  'common.category': 'Category',
  'diary.add_document': 'Add new document',
  'common.required_error': 'Please provide required information',
  'common.diary_list': 'Diary list',
  'common.origin_list': 'Origin tracing list',
  'common.diary_name': 'Diary name',
  'common.plant_name': 'Plant name',
  'common.origin_name': 'Origin tracing name',
  //mã đợt truy xuất
  'common.origin_code': 'Origin tracing code',
  'common.scan_times': 'Scan times',
  'common.created_at': 'Created at',
  'common.modified': 'Modified',
  'diary.crop_owner': 'Crop Owner',
  'diary.location': 'Location',
  'diary.latitude': 'Latitude',
  'diary.longitude': 'Longitude',
  'diary.short_description': 'Short description',
  'diary.business_info': 'Business information',
  'diary.phone': 'Phone',
  'diary.website': 'Website',
  'diary.email': 'Email',
  'diary.other_link': 'Other links',
  'common.form.avatar': 'Avatar',
  'common.participant_list': 'Participant list',
  'common.remove_from_participant_list': 'Remove from participant list',
  'common.add_participant': 'Add participant',
  'common.participant': 'Participant',
  'common.personnel_statistics': 'Personnel statistics',
  'common.personnel': 'Personnel',
  'common.total_task': 'Total task',
  'common.total_related_task': 'Total related task',
  'common.total_sub_task': 'Total sub task',
  'common.task': 'Task',
  'common.task_child': 'Task child',
  'common.business_image': 'Business image',
  'common.add_origin': 'Add origin tracing',
  'common.category_group': 'Category group',
  'common.assigned_to': 'Assigned to',
  'common.missing_info': 'Missing info',
  'common.done': 'Done',
  'common.task_name': 'Task Name',
  'common.add_new_task': 'Add new task',
  'common.copy_last_week': 'Copy last week',
  'common.product': 'Product',
  //approval date
  'common.approval_date': 'Approval date',
  'common.approval_status': 'Approval status',
  'common.comment': 'Comment',
  'common.timesheet_id': 'Timesheet ID',
  'common.add_approval': 'Add approval',
  'common.create_approval': 'Create approval',

  'common.approver': 'Approver',
  'common.refused': 'Refused',

  'common.approved': 'Approved',
  'common.approver_id': 'Approver ID',
  'common.request_date': 'Request date',
  'common.request_user': 'Request user',
  'common.detail': 'Detail',

  'common.qr_code_for_device': 'Device QR code',
  'common.completion_schedule': 'Completion schedule',
  'common.add_timesheet': 'Add timesheet',
  'common.timesheet': 'Timesheet',

  'common.refuse': 'Refuse',
  'common.accept': 'Accept',
  'common.approval': 'Approve',
  'common.approval_list': 'Approval list',
  'common.approval_list_need_to_check': 'Approval list',
  'common.update_pest_info': 'Update pest infomation',
  //tên dịch hại
  'common.pest_name': 'Pest name',
  //chọn giai đoạn
  'common.select_state': 'Select state',
  //Tạo thông tin dịch hại
  'common.create-pest': 'Create pest infomation',
  //Thêm dịch hại
  'common.add-pest': 'Add pest',
  'common.add-health-info': 'Add health infomation',

  //Thêm ghi chú
  'common.add-note': 'Add note',
  'common.export_excel': 'Export excel',
  'common.zone_list': 'Zone list',
  'common.disable': 'Disable',
  'common.active': 'Active',
  'common.range': 'Range',
  'common.range-download': 'Range download',

  'common.default-price': 'Sell price (default)',
  'common.default-purchase-price': 'Purchase price (default)',
  'common.unit': 'Unit',
  'common.purchase-price': 'Unit price',
  'common.the-harvest-season-is-finished': 'The harvest season is finished',
  'common.the-harvest-season-ends-in': 'The season ends in',
  'common.the-harvest-season-is-overdue': 'The harvest season is overdue',
  'common.group': 'Group',
  'common.item_name': 'Item code',
  'common.item_label': 'Item label',
  'common.item_group': 'Item group',
  'common.inventory': 'Inventory',
  'common.inventory_quantity': 'Inventory quantity',
  'common.total_price': 'Total price',
  'common.number_of_days_of_implementation': 'Number of days of implementation',
  'common.uom': 'UOM',
  'common.valuation_rate': 'Purchase price (default)',
  'common.standard_rate': 'Sell price (default)',
  'common.unit-conversion-table': 'Unit conversion table',
  'common.add-uom': 'Add UOM',
  'common.attribute': 'Attribute',
  'common.supplier': 'Supplier',
  'common.index_key': 'Index',
  'common.voucher_id': 'Voucher ID',
  'common.inventory-voucher': 'Inventory voucher',
  'common.customer_code': 'Customer code',
  'common.phone_number': 'Phone number',
  //birthday
  'common.birthday': 'Birthday',
  //customer group
  'common.customer_group': 'Customer group',
  //mã số thuế
  'common.tax_code': 'Tax code',
  //facebook
  'common.facebook': 'Facebook',
  //created_by
  'common.created_by': 'Created by',
  'common.add-customer-group': 'Add customer group',
  //lịch sử xuất hàng
  'common.export-history': 'Export history',
  'common.import-history': 'Import history',
  //thanh toán
  'common.payment': 'Payment',
  //supplier code
  'common.supplier_code': 'Supplier code',
  'common.supplier_name': 'Supplier name',
  'common.supplier_group': 'Supplier group',
  'common.warehouse': 'Warehouse',
  'common.inventory_price': 'Inventory price',
  'common.inventory_price_difference': 'Inventory price difference',
  'common.in_quantity': 'In quantity',
  'common.out_quantity': 'Out quantity',
  'common.transaction_qty': 'Transaction quantity',
  'common.qty_after_transaction': 'Qty after transaction',
  'common.import_voucher': 'Import voucher',
  'common.export_voucher': 'Export voucher',
  'common.transfer_voucher': 'Transfer voucher',
  'common.material_receipt': 'Material receipt',
  'common.material_issue': 'Material issue',
  'common.material-transfer': 'Material transfer',
  'common.stock_reconciliation': 'Stock reconciliation',
  'common.stock-entry': 'Stock entry',
  'common.cancel': 'Cancel',
  'common.migrate': 'Migrate to',
  'common.supplier_list': 'Supplier List',
  'common.supplier_category': 'Supplier Category',
  'common.customer_list': 'Customer List',
  'common.customer_category': 'Customer Category',
  'common.id': 'ID',
  'common.create': 'Create',
  'common.card_id': 'Card ID',
  'common.identity_card': 'Identity card',
  'common.country': 'Country',
  'common.join_date': 'Join date',
  'common.expire_date': 'Expire date',
  //Lưu vật tư liên quan
  'common.save_related_material': 'Save related material',
  'common.save_production': 'Save production',
  'common.crop_product': 'Crop product',
  'common.production': 'Production',
  //Thêm sản lượng
  'common.add_production': 'Add production',
  //Thêm vật tư
  'common.add_material': 'Add material',
  'common.old_quantity': 'Old quantity',
  'common.reconciled_quantity': 'Reconciled quantity',
  'common.old_rate': 'Old rate',
  'common.reconciled_rate': 'Reconciled rate',
  'common.reconciled_amount': 'Reconciled amount',
  'common.select_plan': 'Select plan',
  'common.select_plant': 'Select plant',
  'common.add_item_to_crop': 'Add use qty',
  'common.actual_item_used': 'Actual item used',
  'common.add_relative_item': 'Add expected qty',
  'common.upload': 'Upload',
  'common.submit': 'Submit',
  'common.disposed-quantity': 'Issued qty',
  'common.total_harvested_quantity': 'Total harvested qty',
  'common.harvested_quantity': 'Harvested qty',
  'common.transaction_date': 'Transaction date',
  'common.crop_item_transactions_table': 'Crop item transactions table',
  'common.purpose': 'Purpose',
  'common.task_label': 'Task label',
  'common.t_wh_label': 'Target warehouse',
  'common.s_wh_label': 'Source warehouse',
  'common.item_code': 'Item code',
  'common.qty': 'Qty',
  'common.actual_qty': 'Actual qty',
  'common.balance': 'Balance',
  'common.basic_rate': 'Basic rate',
  'common.basic_amount': 'Amount',
  'common.print_receipt': 'Print receipt',
  'common.print_receipt_qty_only': 'Print receipt qty only',

  //số lượng mặt hàng
  'common.number_of_items': 'Number of items',
  //giá trị kho
  'common.stock_value': 'Stock value',
  //Nhập kho
  'common.import': 'Import',
  //Xuất kho
  'common.export': 'Export',
  //Nhận trực tiếp
  'common.receive_directly': 'Material receipt',
  //Xuất huỷ
  'common.export_cancellation': 'Material issue',
  'common.min_stock_level': 'Min stock level',
  'common.max_stock_level': 'Max stock level',
  //còn hàng
  'common.in_stock': 'Available',
  'common.out_stock': 'Out of stock',
  //sắp hết hàng
  'common.nearly_out_of_stock': 'Nearly out of stock',
  //quá tải
  'common.overload': 'Overload',
  'common.dashboard': 'Dashboard',
  'common.number_of_crops_in_the_zone': 'Number of crops in the zone',
  'common.number_of_crops_by_plant': 'Number of crops by plant',
  'common.crop_list': 'Crops list',
  'common.plant_list': 'Plants list',
  'common.voucher_code': 'Voucher code',
  'common.plant': 'Plant',
  'common.product_status': 'Product Status',
  'common.overstock': 'Over Stock',
  'common.docs': 'Documents',
  'common.create_doc': 'Create document',
  'common.report': 'Report',
  'common.import_export_report': 'Import & Export Report',
  'common.begin_qty': 'Before Qty',
  'common.end_qty': 'End Qty',
  'common.import_qty': 'Import Qty',
  'common.export_qty': 'Export Qty',
  'common.print_report': 'Print report',
  'common.subscription_table_title': 'List of subscriptions',
  'common.total_out_qty': 'Total out qty',
  'common.total_in_qty': 'Total in qty',
  'common.employee_id': 'Employee ID',
  'common.ticket': 'Ticket',
  'common.images': 'Images',
  'common.back_to_tickets': 'Back to tickets',
  'common.send_message': 'Send message',
  'common.involve_users': 'Involve users',
  'common.crop_name': 'Crop name',
  'common.bom-management': 'BOM Management',
  'common.added_item_qty': 'Add expected qty for items',
  'common.attendance_report': 'Attendance Report',
  'common.timesheet_report': 'Timesheet Report',
  'common.day_of_week': 'Day of Week',
  'common.check_in': 'In',
  'common.check_out': 'Out',
  'common.work_hour': 'Work Hour',
  'common.item_list': 'Item list',
  'common.item_list_with_bom': 'Item list with BOM',
  'common.config': 'Config',
  'common.notification': 'Notification',
  'common.value_type': 'Value type',

  'common.condition': 'Condition',
  'common.bom.detail': 'BOM Detail',
  'common.copy_from_task': 'Copy from task',
  'common.related_members': 'Related members',
  'common.repeat_task': 'Repeat task',
  'common.creation_data_type': 'Creation data type  ',
  'common.deletion_data_type': 'Deletion data type',
  'common.deletion_value': 'Deletion value',
  'common.creation_value': 'Creation value',
  'common.creation_message': 'Creation message',
  'common.deletion_message': 'Deletion message',
  'common.activated': 'Activated',

  'common.creation_condition': 'Creation condition  ',
  'common.deletion_condition': 'Deletion condition',
  'common.alarm_name': 'Alarm name',
  'common.Monday': 'Monday',
  'common.Tuesday': 'Tuesday',
  'common.Wednesday': 'Wednesday',
  'common.Thursday': 'Thursday',
  'common.Friday': 'Friday',
  'common.Saturday': 'Saturday',
  'common.Sunday': 'Sunday',
  'common.filter': 'Filter',
  'common.ok': 'OK',
  'common.monthly_report': 'Monthly Report',
  'common.my_devices': 'My devices',
  'common.shared_devices': 'Shared devices',
  'common.list_of_shared_users': 'Danh sách người dùng chia sẻ',
  'common.add_user_to_share': 'Add user to share',
  'common.view_permission': 'View permission',
  'common.edit_permission': 'Edit permission',
  'common.share': 'Share',
  'warehouse-management.dashboard': 'Warehouse Dashboard',
  'common.tag': 'Tag',
  'common.each': 'Each',
  'common.time_type': 'Range type',
  'common.interval_range': 'Interval range',
  'common.enable_origin_tracing': 'Enable origin tracing',
  'common.label': 'Label',
  'common.executor': 'Executor',
  'common.description_required': 'Description cannot be empty',
  'common.sub_task': 'Sub Task',
  'common.add_sub_task': 'Add sub task',
  'common.required': 'This field is required',
  'category.material_management.category_code': 'Material Code',
  'category.material_management.category_name': 'Material Name',
  'storage_management.category_management.expected_quantity': 'Expected Quantity',
  'seasonalTab.suppliesList': 'Material List',
  'production.material_management.production_code': 'Production Code',
  'production.material_management.production_name': 'Production Name',
  'production_management.production_management.expected_quantity': 'Expected Quantity',
  'task.detailed_info': 'Detailed Information',
  'task.description_images': 'Description Images',
  'task.task_name': 'Task Name',
  'task.select_plan': 'Select Plan',
  'task.select_stage': 'Select Stage',
  'task.start_time': 'Start Time',
  'task.end_time': 'End Time',
  'task.assigned_to': 'Assigned To',
  'task.related_members': 'Related Members (if any)',
  'task.status': 'Status',
  'task.status.plan': 'Plan',
  'task.status.in_progress': 'In Progress',
  'task.status.done': 'Done',
  'task.status.pending': 'Pending',
  'task.notes': 'Notes',
  'task.enable_tracing': 'Enable Origin Tracing',
  'task.save_info': 'Save Information',

  'workflowTab.complete': 'Complete',
  'workflowTab.work_name': 'Work Name',
  'workflowTab.executor': 'Executor',

  'task.executor': 'Executor',

  'common.confirm_delete_title': 'Are you sure you want to delete this task?',
  'common.confirm_delete_content': 'If you agree, all related data will be changed',
  'common.confirm_delete_ok': "I'm sure",
  'common.delete_task': 'Delete Task',

  'task.create_new_task': 'Create New Task',
  'task.select_crop': 'Select Crop',
  'menu.employee_management.attendance.report': 'Report',
  'common.message': 'Message',
  'common.device': 'Device',
  'common.select_function': 'Select function',
  'common.select_data_type': 'Select data type',
  'common.select_condition': 'Select condition',
  'common.enable': 'Enable',

  // "common.alarm_name":"Alarm name",
  'seasonalTab.totalDraft': 'Total input quantity',
  'common.production_group': 'Production group',
  'common.import_export_report_value': 'Import Export Value report',
  'common.begin_price': 'Begin Price',
  'common.end_price': 'End Price',
  'common.import_price': 'Import Price',
  'common.export_price': 'Export Price',
  'common.grand_total': 'Grand Total',
  'common.paid_amount': 'Pay Amount',
  'common.add_taxes': 'Add Taxes',
  'common.other_charges': 'Other Charges',
  'common.outstanding_amount': 'Outstanding Amount',
  'common.supplier_total_outstanding_amount': 'Old Outstanding Amount',
  'common.customer_total_outstanding_amount': 'Customer Outstanding Amount',
  'common.voucher_amount': 'Voucher Amount',
  'common.total_amount': 'Total Amount',
  'common.payment-entry': 'Payment Entry',
  'common.payment_quantity': 'Payment Quantity',
  'common.payment_date': 'Payment Date',
  'common.old_paid': 'Paid',
  'common.total_outstanding_amount': 'Total Outstanding Amount:',
  'common.batch_code': 'Batch code',
  'common.company_name': 'Company name',
  'common.farming_season': 'Farming season',
  'common.date_of_manufacture': 'Manufacturing date',
  'common.expiration_date': 'Expiration date',

  'common.work_list': 'Work list',
  'common.create_traceability': 'Create traceability',
  'common.select_product': 'Select product',
  'common.expiry': 'Expiry',
  'common.production_batch_code': 'Production batch code',
  'common.starting_number': 'Starting number',
  'common.ending_number': 'Ending number',
  'common.select_region': 'Select region',
  'common.select_farming_process': 'Select farming process',
  'common.farming_process': 'Farming process',

  'common.select_business': 'Select business',
  'common.confirmation_tem': 'Confirmation Tem',
  'common.production_tem': 'Production Tem',
  'common.business_list': 'Business list',
  'common.add_new_business': 'Add new business',
  'common.business_name': 'Business name',
  'common.business_code': 'Business code',
  'common.number_phone': 'Number phone',
  'common.detailed_address': 'Detailed address',
  'common.web_link': 'Web link',
  'common.introduce_business': 'Introduce business',
  'common.image_of_business_registration': 'Image of business registration',
  'common.other_images': 'Other images',
  'common.stage_name': 'Stage name',
  'common.task_count': 'Task count',
  'common.supplies': 'Supplies',
  'common.execution_time': 'Execution time',
  'common.product_name': 'Product name',
  'common.create_stage': 'Create stage',
  'common.from_the_start_of_the_crop': 'From the start of the crop',
  'common.materials_used': 'Materials used',
  'common.select_supplies': 'Items used',
  'common.pest': 'Pest',
  'common.stage': 'Stage',
  'common.member_group': 'Member group',
  'common.member': 'Member',
  'common.contact': 'Contact',
  'common.expiry_date': 'Expiry date',
  'common.net_weight': 'Net weight',
  'common.packaging_unit': 'Packaging unit',
  'common.product_avatar': 'Product avatar',
  'common.image_of_product_registration': 'Image of product registration',
  'common.product_introduction': 'Product introduction',
  'common.user_manual': 'User manual',
  'common.add_product': 'Add product',
  'common.product_list': 'Product list',
  'common.certification_date': 'Certification date',
  'common.add_certification': 'Add certification',
  'common.certification': 'Certification',
  'common.diary': 'Diary',
  'common.asc': 'Asc',
  'common.desc': 'Desc',
  'common.expected_qty': 'Expected qty',
  'common.history_in_and_out': 'Check in and check out history',
  'common.export_data': 'Export data',
  'common.unknown': 'Unknown',
  'common.delete_confirm': 'Are you sure you want to delete this item?',
  'common.time_unit': 'Time unit',
  'common.expiry_time': 'Expiry time',
  'common.days': 'days',
  'common.only_one_line_editor_alert_message': 'Only one line editor is allowed',
  'common.only_add_one_line_alert_message': 'Only add one line',
  'common.stage_detail': 'Stage detail',
  'common.stage_expire_time_in_days': 'Stage completion time',
  'common.expire_time_in_days': 'Execution days',
  'common.packing_unit': 'Packing unit',
  'common.execute_time': 'Execution time',
  'common.you_need_to_complete': 'You need to complete',
  'common.task_lower_case': 'task',
  'common.used_qty': 'Used quantity',
  'common.windSpeed': 'Wind speed',
  'common.humidity': 'Humidity',
  'common.temperature': 'Temperature',
  'common.precipitation': 'Precipitation',
  'common.uvIndex': 'UV index',
  'common.create-stage': 'Create stage',
  'common.create-ceritification': 'Create ceritification',
  'common.create-note': 'Create note',
  'common.create-task': 'Create task',
  'common.important': 'Important',
  'common.common': 'Common',
  'common.unsaved_changes': 'You have unsaved changes',
  'common.confirm_leave': 'Are you sure you want to leave?',
  'common.template-crop': 'Crop template',
  'common.copy-from-other-crop': 'Copy from other crop',
  'common.is_template': 'Is template',
  'common.template_task': 'Template task',
  'common.work_shift': 'Work shift',
  'common.create_work_shift': 'Create work shift',
  'common.work_shift_label': 'Work shift label',
  'common.work_shift_code': 'Work shift code',
  'common.allowed_lates_earlys': 'Allowed lates/earlys',
  'common.start_time': 'Start time',
  'common.end_time': 'End time',
  'common.times': 'Times',
  'common.work_shift_time': 'Work shift time',
  'common.working_hours': 'Working hours',
  'common.duration': 'Duration',
  'common.rest_start_time': 'Rest start time',
  'common.rest_end_time': 'Rest end time',
  'common.exclude_working_time': 'Exclude working time',
  'common.overtime_before': 'Overtime before',
  'common.overtime_after': 'Overtime after',
  'common.hours': 'Hours',
  'common.employee-applicable': 'Employee applicable',
  'common.reset': 'Reset',
  'common.confirm_overtime_before': 'Is overtime before',
  'common.confirm_overtime_after': 'Is overtime after',
  //đi trễ về sớm
  'common.late_early': 'Late/Early',
  'common.minutes': 'Minutes',
  'common.compare': 'Compare',
  'common.date-range': 'Date range',
  'common.total_crop': 'Total crop',
  'common.total_used_item_value': 'Total used item value',
  'common.total_used_product_value': 'Total used product value',
  'common.profit': 'Profit',
  'common.cost-and-revenue-statistic': 'Cost and revenue statistic',
  'common.supplies-cost-statistic': 'Supplies cost statistic',
  'common.used-cost': 'Used cost',
  'common.exp_value': 'Expected value',
  'common.real_value': 'Real value',
  'common.agri_product': 'Agri product',
  'common.agri_product_profit_statistic': 'Agri product profit statistic',
  'common.with': 'With',
  'common.diff': 'Difference',
  'common.total_value_diff': 'Total value difference',
  'common.collapse': 'Collapse',
  'common.expand': 'Expand',
  'common.late': 'Late',
  'common.early_leave': 'Early leave',
  'common.unlink_device_successfully': 'Unlink device successfully',
  //Failed to unlink device
  'common.failed_to_unlink_device': 'Failed to unlink device',
  //Are you sure you want to unlink this device?
  'common.confirm_unlink_device': 'Are you sure you want to unlink this device?',
  //This action cannot be undone.
  'common.cannot_be_undone': 'This action cannot be undone',
  //Yes, unlink it
  'common.yes_unlink': 'Yes, unlink it',
  'common.unlink_device': 'Unlink device',
  'common.overtime_type': 'Overtime type',
  'common.before': 'Before',
  'common.after': 'After',
  'common.overtime_date': 'Overtime date',
  'common.reply_comment': 'Reply comment',
  'common.request_comment': 'Request comment',
  'common.creator_name': 'Creator name',
  'common.approver_name': 'Approver name',
  'common.deny': 'Deny',
  'common.approve': 'Approve',
  'common.upload-excel-error-missing-keys': 'Excel file is missing keys, please check again',
  'common.debt_adjustment': 'Debt adjustment',
  'common.adjust_paid_amount': 'Adjustment amount',
  'common.adjustment_amount_tool_tip': 'Positive number for increase, negative number for decrease',
  'common.adjustment_history': 'Adjustment history',
  'common.sales-order-list': 'Sales order list',
  'common.sales-order': 'Sales order',
  'common.po_date': 'Purchase order date',
  'common.delivery_date': 'Delivery date',
  'common.paid_amount.error': 'Paid amount must be less than total amount',
  'common.new_paid_amount': 'New paid amount',
  'common.discount': 'Discount',
  'common.exported_voucher': 'Exported voucher',
  'common.create_delivery_note': 'Create delivery note',
  'common.to_deliver_and_bill': 'To deliver and bill',
  'common.to_bill': 'To bill',
  'common.draft': 'Draft',
  'common.confirm_submit': 'Are you sure you want to submit this?',
  'common.submitted': 'Submitted',
  'common.discount_old': 'Discount old',
  'common.discount_new': 'Discount new',
  'common.customer_totalPayment_report': 'Customer total payment report',
  'common.customer_detailPayment_report': 'Customer detail payment report',
  'common.supplier_totalPayment_report': 'Supplier total payment report',
  'common.supplier_detailPayment_report': 'Supplier detail payment report',
  'common.total_outstanding_amount_at_begin': 'Total outstanding amount at begin',
  'common.total_debt_amount_at_middle': 'Total debt amount at middle',
  'common.total_paid_amount_at_middle': 'Total paid amount at middle',
  'common.total_outstanding_amount_at_end': 'Total outstanding amount at end',
  'common.adrress': 'Address',
  'commond.amend': 'Amend',
  'common.doc_status.0': 'Draft',
  'common.doc_status.1': 'Submitted',
  'common.doc_status.2': 'Cancelled',
  'common.customer_detailItem_report': 'Customer detail item report',
  'common.supplier_detailItem_report': 'Supplier detail item report',
  'common.add_supplier': 'Add supplier',
  'common.payment_amount': 'Payment amount',
  'common.bulk_payment': 'Bulk payment',
  'common.auto_allocate': 'Auto allocate',
  'common.total_payment_amount': 'Total payment amount',
  'common.total_outstanding_amount_of_selected_voucher':
    'Total outstanding amount of selected voucher',
  'common.voucher_amount_old': 'Voucher amount old',
  'common.add_taxes_old': 'Add taxes old',
  'common.other_charges_old': 'Other charges old',
  'common.voucher_amount_new': 'Voucher amount new',
  'common.add_taxes_new': 'Add taxes new',
  'common.other_charges_new': 'Other charges new',
  'common.adjust_discount': 'Adjust discount',
  'common.adjust_voucher_amount': 'Adjust voucher amount',
  'common.adjust_add_taxes': 'Adjust add taxes',
  'common.adjust_other_charges': 'Adjust other charges',
  'common.remaining_debt': 'Remaining debt',
  'common.rate': 'Rate',
  'common.add_item': 'Add item',
  'common.source_warehouse': 'Source warehouse',
  'common.message.error.source_warehouse.empty': 'Please select a source warehouse',
  'common.target_warehouse': 'Target warehouse',
  'common.message.error.target_warehouse.empty': 'Please select a target warehouse',
  'common.reconciliated_quantity': 'Reconciliated quantity',
  'common.quick-action': 'Quick action',
  'common.material-receipt': 'Material receipt',
  'common.material-issue': 'Material issue',
  'common.purchase-receipt': 'Purchase receipt',
  'common.delivery-note': 'Delivery note',
  'common.stock-reconciliation': 'Stock reconciliation',
};
