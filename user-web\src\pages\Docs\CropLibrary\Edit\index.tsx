import { DOCTYPE_ERP } from '@/common/contanst/constanst';
import { uploadFile } from '@/services/fileUpload';
import { deleteGuide } from '@/services/guide';
import { deleteInforTab } from '@/services/inforTab';
import { updatePlantAllResource } from '@/services/plants';
import { Guide } from '@/types/guide.type';
import { InfoTab } from '@/types/infoTab.type';
import { Plant } from '@/types/plant.type';
import { myLazy } from '@/utils/lazy';
import { CheckOutlined } from '@ant-design/icons';
import { PageContainer, ProForm } from '@ant-design/pro-components';
import { history, Link, useModel, useParams } from '@umijs/max';
import { Button, message, UploadFile } from 'antd';
import { FC, Fragment, ReactNode, useEffect, useState } from 'react';
import CreateGenericInfo from './components/CreateGenericInfo';

// TODO create a generic component because these two work the same
const GeneralInfo = myLazy(() => import('./GeneralInfo'));
const CareInstructions = myLazy(() => import('./CareInstructions'));

interface DetailProps {
  children?: ReactNode;
}

interface UpdatingGuide extends Omit<Guide, 'name'> {
  is_new?: boolean;
  is_deleted?: boolean;
  name?: string;
  imageFiles?: UploadFile[];
  iconFiles?: UploadFile[];
}

interface UpdatingInforTab extends Omit<InfoTab, 'name'> {
  is_new?: boolean;
  is_deleted?: boolean;
  name?: string;
  imageFiles?: UploadFile[];
  iconFiles?: UploadFile[];
}
interface UpdatingPlant extends Omit<Plant, 'infor_tab_list' | 'guide_list'> {
  infor_tab_list: UpdatingInforTab[];
  guide_list: UpdatingGuide[];
}

const Detail: FC<DetailProps> = ({ children }) => {
  const { id } = useParams();
  const { myPlant, setMyPlant, isAccessEditDocs } = useModel('Docs');
  const [curPlant, setCurPlant] = useState<Plant>({} as Plant);
  const [submitting, setSubmitting] = useState(false);
  const [curTab, setCurTab] = useState<string>();
  const [form] = ProForm.useForm<UpdatingPlant>();
  useEffect(() => {
    const selectedPlant = myPlant.find((plant) => plant.name === id);
    if (selectedPlant === undefined) {
      history.back();
      message.error(`Không tìm thấy tài liệu!`);
    } else {
      // console.log({ selectedPlant });
      selectedPlant.guide_list = selectedPlant?.guide_list
        ?.sort((a, b) => (a.sort_index || 0) - (b.sort_index || 0))
        ?.map((guide, index) => ({
          ...guide,
          sort_index: index,
        }));
      selectedPlant.infor_tab_list = selectedPlant?.infor_tab_list
        ?.sort((a, b) => (a.sort_index || 0) - (b.sort_index || 0))
        ?.map((inforTab, index) => ({
          ...inforTab,
          sort_index: index,
        }));
      // console.log({ processedPlant: selectedPlant });
      setCurPlant(selectedPlant);
      setCurTab('general-info');
    }
  }, []);
  const processRawGenericList = async (
    rawUpdatingGenericList: UpdatingGuide[] | UpdatingInforTab[],
  ) => {
    const upsertGenericList: UpdatingGuide[] | UpdatingInforTab[] = [];
    const deleteGenericList: UpdatingGuide[] | UpdatingInforTab[] = [];
    await Promise.all(
      rawUpdatingGenericList.map(async (generic) => {
        if (generic.is_deleted) {
          // if guide.is_new ?
          deleteGenericList.push(generic);
          return;
        } else {
          if (generic.iconFiles && generic.iconFiles.length) {
            const iconFile = generic.iconFiles[0];
            let iconUrl: string | undefined;
            if (iconFile?.url) {
              iconUrl = iconFile.url.split('file_url=').at(-1);
            } else {
              try {
                const uploadIconRes = await uploadFile({
                  docType: DOCTYPE_ERP.iotPlant,
                  docName:
                    iconFile.name + Math.random().toString(4) + iconFile.lastModified?.toString(4),
                  file: iconFile.originFileObj as any,
                });
                iconUrl = uploadIconRes.data.message.file_url;
              } catch (error: any) {
                message.error(
                  `Đã có lỗi khi upload icon của ${generic.label}: ${JSON.stringify(error)}`,
                );
              }
            }
            generic.icon = iconUrl;
            delete generic.iconFiles;
          }
          if (generic.imageFiles) {
            const uploadListRes = await Promise.allSettled(
              generic.imageFiles.map(async (item) => {
                if (item.url) {
                  return {
                    data: {
                      message: {
                        file_url: item.url.split('file_url=').at(-1),
                      },
                    },
                  };
                }
                return await uploadFile({
                  docType: DOCTYPE_ERP.iotPlant,
                  docName: item.name + Math.random().toString(4) + item.lastModified?.toString(4),
                  file: item.originFileObj as any,
                });
              }),
            );
            // check if() 1 vài upload failed
            const checkUploadFailed = uploadListRes.find((item) => item.status === 'rejected');
            if (checkUploadFailed) {
              message.error({
                content: `Đã có ảnh của ${generic.label} upload không thành công`,
              });
            }

            // update img path
            const arrFileUrl = uploadListRes
              .reduce<string[]>(
                (prev, item) =>
                  item.status === 'fulfilled' && item?.value?.data?.message?.file_url
                    ? [...prev, item?.value?.data?.message?.file_url]
                    : prev,
                [],
              )
              .filter((item) => typeof item === 'string');
            if (arrFileUrl) {
              generic.image = arrFileUrl.join(',');
            }
          }

          if (generic.is_new) delete generic.name;
          upsertGenericList.push(generic);
          delete generic.imageFiles;
        }
      }),
    );

    return { upsertGenericList, deleteGenericList };
  };

  const handleSave = async () => {
    setSubmitting(true);
    try {
      // Run validation
      await form.validateFields();

      // Getting raww values from form
      const formValues = form.getFieldsValue();
      // console.log({ formValues });

      // Getting raw guidelist from the resulted form
      const rawUpdatingGuideList = Object.values(
        formValues?.guide_list || curPlant.guide_list || [],
      );

      const rawUpdatingInforTabList = Object.values(
        formValues?.infor_tab_list || curPlant.infor_tab_list || [],
      );

      // Preprocess updating Guide list and InforTab list
      const { deleteGenericList: deleteGuideList, upsertGenericList: upsertGuideList } =
        await processRawGenericList(rawUpdatingGuideList);
      const { deleteGenericList: deleteInforTabList, upsertGenericList: upsertInforTabList } =
        await processRawGenericList(rawUpdatingInforTabList);

      const upsertPlant = {
        ...curPlant,
        infor_tab_list: upsertInforTabList,
        guide_list: upsertGuideList,
      };
      // console.log({ upsertPlant });
      const promiseDeleteGuideList = deleteGuideList.map(async (guide) => {
        if (guide?.is_new) {
          return;
        }
        return deleteGuide(guide.name || '');
      });
      const promiseDeleteInforTabList = deleteInforTabList.map(async (inforTab) => {
        if (inforTab?.is_new) {
          return;
        }
        return deleteInforTab(inforTab.name || '');
      });
      const promiseUpdatePlant = updatePlantAllResource(upsertPlant);
      const [resPlant, _] = await Promise.all([
        promiseUpdatePlant,
        promiseDeleteGuideList,
        promiseDeleteInforTabList,
      ]);

      // Update to global state
      const updatedPlants = myPlant.map((plant) => {
        if (plant.name === resPlant.name) {
          return resPlant;
        }
        return plant;
      });
      setMyPlant(updatedPlants);
      message.success('Cập nhật thành công', 5);
      history.push(`/documents/${id}/detail`);
      setSubmitting(false);
      return true;
    } catch (error: any) {
      // TODO phân biệt lỗi validation và lỗi BE
      // TODO redirect hay vẫn ở lại?
      // console.log({ error });
      message.error(`Đã có lỗi trong quá trình lưu thông tin`);
      setSubmitting(false);
    }
  };

  const handleTabChange = (e: any) => {
    setCurTab(e);
  };
  const handleNewGeneralInfo = (data: any) => {
    data.sort_index = curPlant.infor_tab_list?.length || 0;
    // console.log('new infor_tab', data);
    setCurPlant({
      ...curPlant,
      infor_tab_list: curPlant.infor_tab_list
        ? [data as InfoTab, ...curPlant.infor_tab_list]
        : [data as InfoTab],
    });
  };
  const handleNewCareInstruction = (data: any) => {
    data.sort_index = curPlant.guide_list?.length || 0;
    // console.log('new guide', data);
    setCurPlant({
      ...curPlant,
      guide_list: curPlant.guide_list ? [data as Guide, ...curPlant.guide_list] : [data as Guide],
    });
  };
  const routes = [
    {
      key: 'general-info',
      extra: [
        <Link to={`/documents/${id}/detail/general-info`} key={'cancel'}>
          <Button key={'cancel'}>Huỷ</Button>
        </Link>,
        <Fragment key={'create'}>
          {isAccessEditDocs && (
            <CreateGenericInfo
              key={'create'}
              triggerLabel="Thêm thông tin"
              handleNewGenericInfo={handleNewGeneralInfo}
            />
          )}
        </Fragment>,

        <Fragment key="save">
          {isAccessEditDocs && (
            <Button
              key={'save'}
              type="primary"
              icon={<CheckOutlined />}
              onClick={handleSave}
              loading={submitting}
            >
              Lưu
            </Button>
          )}
        </Fragment>,
      ],
    },
    {
      key: 'care-instructions',
      extra: [
        <Link to={`/documents/${id}/detail/general-info`} key={'cancel'}>
          <Button key={'cancel'}>Huỷ</Button>
        </Link>,
        <Fragment key={'create'}>
          {isAccessEditDocs && (
            <CreateGenericInfo
              triggerLabel="Thêm hướng dẫn"
              handleNewGenericInfo={handleNewCareInstruction}
            />
          )}
        </Fragment>,

        <Fragment key="save">
          {isAccessEditDocs && (
            <Button
              key={'save'}
              type="primary"
              icon={<CheckOutlined />}
              onClick={handleSave}
              loading={submitting}
            >
              Lưu
            </Button>
          )}
        </Fragment>,
      ],
    },
  ];
  return (
    <ProForm form={form} style={{ width: '100%' }} submitter={false}>
      <PageContainer
        tabList={[
          {
            tab: 'Thông tin chung',
            key: 'general-info',
            children: <GeneralInfo curPlant={curPlant} />,
          },
          // {
          //   tab: 'Hướng dẫn chăm sóc',
          //   key: 'care-instructions',
          //   children: <CareInstructions curPlant={curPlant} />,
          // },
        ]}
        onTabChange={handleTabChange}
        extra={routes.find((route) => route.key === curTab)?.extra}
      ></PageContainer>
    </ProForm>
  );
};

export default Detail;
