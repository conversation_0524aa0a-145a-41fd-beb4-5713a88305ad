import FallbackComponent from '@/components/FallbackContent';
import { PageContainer } from '@ant-design/pro-components';
import { Access, useAccess, useIntl } from '@umijs/max';
import { DatePicker } from 'antd';
import { FC, ReactNode } from 'react';
import DiaryTable from './DiaryTable';
const { RangePicker } = DatePicker;

interface WorkflowManagementProps {
  children?: ReactNode;
}

const WorkflowManagement: FC<WorkflowManagementProps> = ({ children }) => {
  const intl = useIntl();

  const access = useAccess();
  return (
    <Access accessible={access.canAccessPageSeasonalManagement()} fallback={<FallbackComponent />}>
      <PageContainer>
        <DiaryTable />

        {/* <Col>
          <Space direction="vertical" size="middle" style={{ display: 'flex' }}>
            <Space direction="vertical" size="middle" style={{ display: 'flex' }}>
              <Tabs defaultActiveKey="active">
                <Tabs.TabPane tab={intl.formatMessage({ id: 'common.diary_list' })} key="active">
                  <Suspense fallback={<TableSkeleton active />}>
                    <DiaryTable />
                  </Suspense>
                </Tabs.TabPane>

                <Tabs.TabPane tab={intl.formatMessage({ id: 'common.origin_list' })} key="2">
                  <Suspense fallback={<TableSkeleton active />}>
                    <TracingTable />
                  </Suspense>
                </Tabs.TabPane>
              </Tabs>
            </Space>
          </Space>
        </Col> */}
      </PageContainer>
    </Access>
  );
};

export default WorkflowManagement;
