import { getCustomerUserList } from '@/services/customerUser';
import { ProFormSelect, ProFormSelectProps } from '@ant-design/pro-components';
import { FC } from 'react';

type ProFormSelectUserProps = ProFormSelectProps<any>;

const ProFormSelectUser: FC<ProFormSelectUserProps> = ({ ...props }) => {
  return (
    <ProFormSelect
      showSearch
      request={async (option) => {
        const listUser = await getCustomerUserList();
        return listUser.data.map((product: any) => ({
          label: `${product.first_name} ${product.last_name}`,
          value: product.name,
        }));
      }}
      {...props}
    />
  );
};

export default ProFormSelectUser;
